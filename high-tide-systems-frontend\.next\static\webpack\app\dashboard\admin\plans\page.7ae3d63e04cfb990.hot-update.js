"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/components/users/UserModulesTab.js":
/*!************************************************!*\
  !*** ./src/components/users/UserModulesTab.js ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n\n\n\nconst UserModulesTab = (param)=>{\n    let { user, savedUserId, selectedModules, setSelectedModules, isAdmin, isLoading } = param;\n    const toggleModule = (moduleId)=>{\n        if (moduleId === \"ADMIN\" && !isAdmin) return;\n        if (selectedModules.includes(moduleId)) {\n            setSelectedModules((prev)=>prev.filter((m)=>m !== moduleId));\n        } else {\n            setSelectedModules((prev)=>[\n                    ...prev,\n                    moduleId\n                ]);\n        }\n    };\n    // Função para alternar todos os módulos (igual nas permissões)\n    const toggleAllModules = ()=>{\n        const allModules = [\n            \"ADMIN\",\n            \"SCHEDULING\",\n            \"PEOPLE\"\n        ];\n        const availableModules = isAdmin ? allModules : allModules.filter((m)=>m !== \"ADMIN\");\n        // Verificar se todos os módulos disponíveis estão selecionados\n        const allSelected = availableModules.every((moduleId)=>selectedModules.includes(moduleId));\n        if (allSelected) {\n            // Desmarcar todos (manter apenas BASIC que é obrigatório)\n            setSelectedModules([\n                \"BASIC\"\n            ]);\n        } else {\n            // Selecionar todos os disponíveis (incluindo BASIC)\n            setSelectedModules([\n                \"BASIC\",\n                ...availableModules\n            ]);\n        }\n    };\n    // Verificar se todos os módulos disponíveis estão selecionados\n    const allModules = [\n        \"ADMIN\",\n        \"SCHEDULING\",\n        \"PEOPLE\"\n    ];\n    const availableModules = isAdmin ? allModules : allModules.filter((m)=>m !== \"ADMIN\");\n    const allSelected = availableModules.every((moduleId)=>selectedModules.includes(moduleId));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white p-4 rounded-lg mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 bg-white/20 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: (user === null || user === void 0 ? void 0 : user.fullName) || (savedUserId ? \"Novo Usuário\" : \"\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-white/80\",\n                                            children: \"Gerenciar m\\xf3dulos de acesso\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                children: \"Selecione os m\\xf3dulos que este usu\\xe1rio ter\\xe1 acesso:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAllModules,\n                                className: \"px-3 py-1 rounded text-sm font-medium \".concat(allSelected ? \"bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700\" : \"bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700\"),\n                                disabled: isLoading,\n                                children: allSelected ? \"Desmarcar todos\" : \"Selecionar todos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border bg-white dark:bg-white/20 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: true,\n                                        onChange: ()=>{},\n                                        disabled: true,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"B\\xe1sico\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Acesso b\\xe1sico ao sistema, visualiza\\xe7\\xe3o limitada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"M\\xf3dulo obrigat\\xf3rio para todos os usu\\xe1rios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"ADMIN\") ? \"bg-gray-200 dark:bg-gray-800/60 text-gray-800 dark:text-gray-300 border-gray-300 dark:border-gray-700/60\" : \"border-neutral-200 dark:border-gray-700\", \" \").concat(!isAdmin ? \"opacity-70\" : \"cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"ADMIN\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"ADMIN\"),\n                                        onChange: ()=>{},\n                                        disabled: !isAdmin || isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"ADMIN\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: [\n                                                        \"Administra\\xe7\\xe3o\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded\",\n                                                            children: \"Acesso Administrativo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Acesso completo ao sistema, incluindo configura\\xe7\\xf5es e gerenciamento de usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Apenas administradores podem conceder este acesso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"SCHEDULING\") ? \"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"SCHEDULING\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"SCHEDULING\"),\n                                        onChange: ()=>{},\n                                        disabled: isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"SCHEDULING\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"Agendamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Gerenciamento de compromissos, reuni\\xf5es e aloca\\xe7\\xe3o de recursos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 171,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"PEOPLE\") ? \"bg-orange-300 dark:bg-orange-700/60 text-orange-900 dark:text-orange-200 border-orange-400 dark:border-orange-600/70\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"PEOPLE\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"PEOPLE\"),\n                                        onChange: ()=>{},\n                                        disabled: isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"PEOPLE\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"Pessoas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Cadastro e gerenciamento de pacientes, clientes e conv\\xeanios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = UserModulesTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserModulesTab);\nvar _c;\n$RefreshReg$(_c, \"UserModulesTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/UserModulesTab.js\n"));

/***/ })

});