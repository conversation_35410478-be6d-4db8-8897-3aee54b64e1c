"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import TutorialManager from "@/components/tutorial/TutorialManager";
import ModuleHeader from "@/components/ui/ModuleHeader";
import { ModuleTable, ModuleCheckbox } from "@/components/ui";
import {
  Plus,
  Filter,
  Edit,
  Trash,
  User,
  Power,
  CheckCircle,
  XCircle,
  Mail,
  Phone,
  CreditCard,
  Calendar,
  Users,
  Eye,
  RefreshCw,
} from "lucide-react";
import { personsService } from "@/app/modules/people/services/personsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import PersonFormModal from "@/components/people/PersonFormModal.js";
import ExportMenu from "@/components/ui/ExportMenu";
import ShareButton from "@/components/common/ShareButton";
import { PersonsFilters } from "@/components/people/PersonsFilters";
import { 
  SensitiveEmail, 
  SensitivePhone, 
  SensitiveCpfCnpj, 
  SensitiveFullName, 
  SensitiveAvatar,
  SensitiveBirthDate
} from '@/components/ui/SensitiveField';
import { useDataPrivacy } from '@/hooks/useDataPrivacy';

import { formatDate } from "@/utils/dateUtils";
import Link from "next/link";

const personsTutorialSteps = [
  {
    title: "Pacientes",
    content: "Esta tela permite gerenciar o cadastro de pacientes no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Paciente",
    content: "Clique aqui para adicionar um novo paciente.",
    selector: "button:has(span:contains('Novo Paciente'))",
    position: "left"
  },
  {
    title: "Filtrar Pacientes",
    content: "Use esta barra de pesquisa para encontrar pacientes específicos pelo nome, email ou CPF.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtros Avançados",
    content: "Clique no botão Filtros para acessar opções avançadas de filtragem.",
    selector: "button:has(span:contains('Filtros'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de pacientes em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Pacientes",
    content: "Visualize, edite, ative/desative ou exclua pacientes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const PersonsPage = () => {
  const { user: currentUser } = useAuth();
  const { applyListPrivacyMasks } = useDataPrivacy();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [persons, setPersons] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPersons, setTotalPersons] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [personFormOpen, setPersonFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(persons.map(p => p.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };

  const [filters, setFilters] = useState({
    search: "",
    companies: [],
    status: "",
    relationship: "",
    persons: [],
    dateFrom: "",
    dateTo: ""
  });

  const [itemsPerPage, setItemsPerPage] = useState(10);

  const loadPersons = async (
    page = currentPage,
    filtersToUse = filters,
    sortField = 'fullName',
    sortDirection = 'asc',
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      const pageNumber = parseInt(page, 10);
      setCurrentPage(pageNumber);

      const response = await personsService.getPersons({
        page: pageNumber,
        limit: perPage,
        search: filtersToUse.search || undefined,
        personIds: filtersToUse.persons.length > 0 ? filtersToUse.persons : undefined,
        active: filtersToUse.status === "" ? undefined : filtersToUse.status === "active",
        relationship: filtersToUse.relationship || undefined,
        companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,
        dateFrom: filtersToUse.dateFrom || undefined,
        dateTo: filtersToUse.dateTo || undefined,
        sortField,
        sortDirection,
      });

      const personsData = response?.persons || response?.people || response?.data || [];
      
      // Aplicar máscaras de privacidade aos dados das pessoas
      const personsWithPrivacy = applyListPrivacyMasks('patient', personsData);
      console.log('🔒 Máscaras de privacidade aplicadas às pessoas/pacientes');
      
      setPersons(personsWithPrivacy);
      setTotalPersons(response?.total || 0);
      setTotalPages(response?.pages || 1);
    } catch (error) {
      console.error("Erro ao carregar pessoas:", error);
      setPersons([]);
      setTotalPersons(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPersons(1, filters, 'fullName', 'asc');
  }, []);

  useEffect(() => {
    const personId = searchParams.get('personId');
    const openModal = searchParams.get('openModal');

    if (personId && openModal === 'true') {
      const person = persons.find(p => p.id === personId);
      if (person) {
        setSelectedPerson(person);
        setPersonFormOpen(true);
      } else {
        personsService.getPerson(personId).then(personData => {
          setSelectedPerson(personData);
          setPersonFormOpen(true);
        }).catch(error => {
          console.error("Erro ao buscar pessoa:", error);
        });
      }
    }
  }, [searchParams, persons]);

  const handleSearch = (searchFilters) => {
    loadPersons(1, searchFilters, 'fullName', 'asc');
  };

  const handlePageChange = (page) => {
    loadPersons(page, filters, 'fullName', 'asc');
  };

  const handleEditPerson = async (person) => {
    try {
      const personData = await personsService.getPerson(person.id);
      setSelectedPerson(personData);
      setPersonFormOpen(true);
    } catch (error) {
      console.error('Erro ao buscar dados do paciente:', error);
      setSelectedPerson(person);
      setPersonFormOpen(true);
    }
  };

  const handleToggleStatus = (person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "toggle-status",
      message: `${person.active ? "Desativar" : "Ativar"} o paciente ${person.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeletePerson = (person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente o paciente ${person.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await personsService.togglePersonStatus(selectedPerson.id);
        loadPersons(currentPage, filters, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao alterar status do paciente:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await personsService.deletePerson(selectedPerson.id);
        loadPersons(currentPage, filters, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao excluir paciente:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      await personsService.exportPersons({
        search: filters.search || undefined,
        personIds: filters.persons.length > 0 ? filters.persons : undefined,
        active: filters.status === "" ? undefined : filters.status === "active",
        relationship: filters.relationship || undefined,
        companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,
        dateFrom: filters.dateFrom || undefined,
        dateTo: filters.dateTo || undefined,
      }, format);
    } catch (error) {
      console.error("Erro ao exportar pessoas:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const formatCPF = (cpf) => {
    if (!cpf) return "N/A";
    const cpfNumbers = cpf.replace(/\D/g, "");
    return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (phone) => {
    if (!phone) return "N/A";
    const phoneNumbers = phone.replace(/\D/g, "");
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  };

  const handleClosePersonModal = () => {
    setPersonFormOpen(false);
    setSelectedPerson(null);
    const params = new URLSearchParams(window.location.search);
    params.delete('personId');
    params.delete('openModal');
    router.replace(`/dashboard/people/persons${params.toString() ? '?' + params.toString() : ''}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Users size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Pacientes
        </h1>

        <div className="flex items-center gap-2">
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir pacientes selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || persons.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          <button
            onClick={() => {
              setSelectedPerson(null);
              setPersonFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Paciente</span>
          </button>
        </div>
      </div>

      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie o cadastro de pacientes no sistema. Utilize os filtros abaixo para encontrar pacientes específicos."
        tutorialSteps={personsTutorialSteps}
        tutorialName="persons-overview"
        moduleColor="people"
        filters={
          <PersonsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
          />
        }
      />

      <ModuleTable
        moduleColor="people"
        title="Lista de Pacientes"
        headerContent={
          <button
            onClick={() => loadPersons()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          { header: 'Paciente', field: 'fullName', width: '25%' },
          { header: 'Contato', field: 'contact', width: '8%' },
          { header: 'CPF', field: 'cpf', width: '15%' },
          { header: 'Relação', field: 'relationship', width: '12%' },
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Cadastro', field: 'createdAt', width: '15%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={persons}
        isLoading={isLoading}
        emptyMessage="Nenhum paciente encontrado"
        emptyIcon={<User size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalPersons}
        onPageChange={handlePageChange}
        showPagination={true}
        tableId="people-persons-table"
        enableColumnToggle={true}
        defaultSortField="fullName"
        defaultSortDirection="asc"
        onSort={(field, direction) => {
          loadPersons(currentPage, filters, field, direction);
        }}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadPersons(1, filters, 'fullName', 'asc', newItemsPerPage);
        }}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        renderRow={(person, index, moduleColors, visibleColumns) => (
          <tr key={person.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="people"
                  checked={selectedIds.includes(person.id)}
                  onChange={(e) => handleSelectOne(person.id, e.target.checked)}
                  name={`select-person-${person.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('fullName') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <SensitiveAvatar
                    entityType="patient"
                    src={person.profileImageFullUrl}
                    alt={person.fullName}
                    size={40}
                    className="flex-shrink-0"
                  />
                  <div className="ml-3">
                    <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                      <Link
                        href={`/dashboard/people/persons/${person.id}`}
                        className="hover:text-primary-600 dark:hover:text-primary-400 hover:underline"
                      >
                        <SensitiveFullName
                          entityType="patient"
                          value={person.fullName}
                          data={person}
                          showToggle={true}
                        />
                      </Link>
                    </div>
                    <div className="flex items-center text-xs text-neutral-500 dark:text-neutral-400">
                      <Calendar className="h-3 w-3 mr-1" />
                      <SensitiveBirthDate
                        entityType="patient"
                        value={person.birthDate}
                        emptyText="Sem data"
                      />
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('contact') && (
              <td className="px-3 py-4">
                <div className="text-xs text-neutral-600 dark:text-neutral-300">
                  {person.email && (
                    <div className="flex items-center gap-1 mb-1">
                      <Mail className="h-3 w-3 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                      <span className="truncate text-xs">
                        <SensitiveEmail
                          entityType="patient"
                          value={person.email}
                          data={person}
                          showToggle={true}
                        />
                      </span>
                    </div>
                  )}
                  {person.phone && (
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                      <span className="truncate text-xs">
                        <SensitivePhone
                          entityType="patient"
                          value={person.phone}
                          data={person}
                          showToggle={true}
                        />
                      </span>
                    </div>
                  )}
                  {!person.email && !person.phone && (
                    <span className="text-neutral-400 dark:text-neutral-500 text-xs">Sem contato</span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('cpf') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300">
                <div className="flex items-center gap-1">
                  <CreditCard className="h-3 w-3 text-neutral-400 dark:text-neutral-500" />
                  <SensitiveCpfCnpj
                    entityType="patient"
                    value={person.cpf}
                    data={person}
                    emptyText="Não informado"
                    showToggle={true}
                  />
                </div>
              </td>
            )}

            {visibleColumns.includes('relationship') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {person.clientPersons && person.clientPersons.length > 0 ? (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1" />
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400">
                      {person.relationship || "Titular"}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">
                    Sem cliente
                  </span>
                )}
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                    person.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                  }`}
                >
                  {person.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300">
                {formatDate(person.createdAt)}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                <ShareButton
                  itemType="person"
                  itemId={person.id}
                  itemTitle={person.fullName}
                  size="xs"
                  variant="ghost"
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                />
                
                <Link
                  href={`/dashboard/people/persons/${person.id}`}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors inline-flex items-center justify-center"
                  title="Visualizar"
                >
                  <Eye size={18} />
                </Link>
                <button
                  onClick={() => handleEditPerson(person)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  title="Editar"
                >
                  <Edit size={18} />
                </button>

                <button
                  onClick={() => handleToggleStatus(person)}
                  className={`p-1 transition-colors ${
                    person.active
                      ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                  }`}
                  title={person.active ? "Desativar" : "Ativar"}
                >
                  <Power size={18} />
                </button>

                <button
                  onClick={() => handleDeletePerson(person)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="Excluir"
                >
                  <Trash size={18} />
                </button>
              </div>
            </td>
            )}
          </tr>
        )}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        moduleColor="people"
      />

      {personFormOpen && (
        <PersonFormModal
          isOpen={personFormOpen}
          onClose={handleClosePersonModal}
          person={selectedPerson}
          onSuccess={() => {
            handleClosePersonModal();
            loadPersons(1, filters, 'fullName', 'asc');
          }}
        />
      )}

      <TutorialManager />
    </div>
  );
};

export default PersonsPage;