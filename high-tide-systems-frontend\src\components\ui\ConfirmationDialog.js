"use client";

import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { AlertTriangle, X } from "lucide-react";
import { cn } from "@/lib/utils";

const ConfirmationDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirmar ação",
  message = "Tem certeza que deseja continuar?",
  confirmText = "Confirmar",
  cancelText = "Cancelar",
  variant = "warning", // warning, danger, info
  moduleColor = "scheduling", // scheduling, people, etc.
  appointmentData = null // Dados do agendamento para exibição detalhada
}) => {
  // Definir estilos com base na variante e módulo
  const getVariantStyles = () => {
    switch (variant) {
      case "danger":
        return {
          iconColor: "text-red-500 dark:text-red-400",
          iconBg: "bg-red-100 dark:bg-red-900/30",
          confirmBg: "bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600",
          borderColor: "border-red-400 dark:border-red-500",
          textColor: "text-red-600 dark:text-red-300",
          titleColor: "text-red-800 dark:text-white",
        };
      case "info":
        if (moduleColor === "people") {
          return {
            iconColor: "text-orange-500 dark:text-orange-400",
            iconBg: "bg-orange-100 dark:bg-orange-900/30",
            confirmBg: "bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 dark:from-orange-700 dark:to-amber-700 dark:hover:from-orange-600 dark:hover:to-amber-600",
            borderColor: "border-orange-400 dark:border-orange-500",
            textColor: "text-orange-600 dark:text-orange-300",
            titleColor: "text-orange-800 dark:text-white",
          };
        }
        if (moduleColor === "admin") {
          return {
            iconColor: "text-gray-500 dark:text-gray-400",
            iconBg: "bg-gray-100 dark:bg-gray-800/30",
            confirmBg: "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 dark:from-gray-600 dark:to-gray-700 dark:hover:from-gray-500 dark:hover:to-gray-600",
            borderColor: "border-gray-400 dark:border-gray-500",
            textColor: "text-gray-600 dark:text-gray-300",
            titleColor: "text-gray-800 dark:text-white",
          };
        }
        if (moduleColor === "scheduler") {
          return {
            iconColor: "text-purple-500 dark:text-purple-400",
            iconBg: "bg-purple-100 dark:bg-purple-900/30",
            confirmBg: "bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600",
            borderColor: "border-purple-400 dark:border-purple-500",
            textColor: "text-purple-600 dark:text-purple-300",
            titleColor: "text-purple-800 dark:text-white",
          };
        }
        return {
          iconColor: "text-purple-500 dark:text-purple-400",
          iconBg: "bg-purple-100 dark:bg-purple-900/30",
          confirmBg: "bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600",
          borderColor: "border-purple-400 dark:border-purple-500",
          textColor: "text-purple-600 dark:text-purple-300",
          titleColor: "text-purple-800 dark:text-white",
        };
      case "warning":
      default:
        if (moduleColor === "people") {
          return {
            iconColor: "text-orange-500 dark:text-orange-400",
            iconBg: "bg-orange-100 dark:bg-orange-900/30",
            confirmBg: "bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 dark:from-orange-700 dark:to-amber-700 dark:hover:from-orange-600 dark:hover:to-amber-600",
            borderColor: "border-orange-400 dark:border-orange-500",
            textColor: "text-orange-600 dark:text-orange-300",
            titleColor: "text-orange-800 dark:text-white",
          };
        }
        if (moduleColor === "admin") {
          return {
            iconColor: "text-gray-500 dark:text-gray-400",
            iconBg: "bg-gray-100 dark:bg-gray-800/30",
            confirmBg: "bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 dark:from-gray-600 dark:to-gray-700 dark:hover:from-gray-500 dark:hover:to-gray-600",
            borderColor: "border-gray-400 dark:border-gray-500",
            textColor: "text-gray-600 dark:text-gray-300",
            titleColor: "text-gray-800 dark:text-white",
          };
        }
        if (moduleColor === "scheduler") {
          return {
            iconColor: "text-purple-500 dark:text-purple-400",
            iconBg: "bg-purple-100 dark:bg-purple-900/30",
            confirmBg: "bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600",
            borderColor: "border-purple-400 dark:border-purple-500",
            textColor: "text-purple-600 dark:text-purple-300",
            titleColor: "text-purple-800 dark:text-white",
          };
        }
        return {
          iconColor: "text-purple-500 dark:text-purple-400",
          iconBg: "bg-purple-100 dark:bg-purple-900/30",
          confirmBg: "bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600",
          borderColor: "border-purple-400 dark:border-purple-500",
          textColor: "text-purple-600 dark:text-purple-300",
          titleColor: "text-purple-800 dark:text-white",
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Estado para controlar a montagem do componente no cliente
  const [mounted, setMounted] = useState(false);

  // Efeito para garantir que o portal só seja criado no lado do cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Efeito para prevenir scroll quando o modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !mounted) return null;

  // Usar createPortal para renderizar o modal no nível mais alto do DOM
  const modalContent = (
    <div className={cn(
      "fixed inset-0 z-[15000] flex items-center justify-center overflow-y-auto",
      "pointer-events-auto" // Garantir que os eventos de clique funcionem
    )} onClick={(e) => {
      e.stopPropagation();
    }}>
      {/* Overlay de fundo escuro */}
      <div
        className="fixed inset-0 bg-black/60"
        onClick={(e) => {
          e.stopPropagation(); // Impedir propagação do evento para o modal principal
          e.preventDefault(); // Adicionado preventDefault para garantir
          onClose();
        }}
      ></div>

      <div
        className={`relative bg-background rounded-xl shadow-xl dark:shadow-black/50 w-full max-w-2xl z-[15050] border-2 ${variantStyles.borderColor}`}
        onClick={(e) => {
          e.stopPropagation(); // Impedir propagação do evento para o modal principal
        }}
      >
        <div className="absolute top-4 right-4">
          <button
            onClick={(e) => {
              e.stopPropagation(); // Impedir propagação do evento
              e.preventDefault(); // Adicionado preventDefault para garantir
              onClose();
            }}
            className="text-neutral-400 dark:text-gray-500 hover:text-neutral-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
            <div className={`p-3 rounded-full ${variantStyles.iconBg}`}>
              <AlertTriangle className={`h-6 w-6 ${variantStyles.iconColor}`} />
            </div>
            <div className="flex-1">
              <h3 className="text-xl font-bold text-slate-800 dark:text-white">{title}</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Confirme os detalhes antes de prosseguir</p>
            </div>
          </div>

          <div className="text-neutral-600 dark:text-gray-300 mb-6 max-h-[60vh] overflow-y-auto pr-2">
            {appointmentData ? (
              <div className="space-y-4">
                <p className="text-base font-medium text-slate-800 dark:text-white mb-4">
                  {message}
                </p>
                
                <div className={`bg-background rounded-lg p-4 border-2 ${variantStyles.borderColor}`}>
                  <h4 className={`font-semibold ${variantStyles.titleColor} mb-3`}>Detalhes do Agendamento:</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    {appointmentData.provider && (
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${variantStyles.textColor}`}>Profissional:</span>
                        <span className="text-gray-800 dark:text-white font-medium">{appointmentData.provider}</span>
                      </div>
                    )}

                    {appointmentData.patient && (
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${variantStyles.textColor}`}>Paciente:</span>
                        <span className="text-gray-800 dark:text-white font-medium">{appointmentData.patient}</span>
                      </div>
                    )}
                    
                    {appointmentData.location && (
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${variantStyles.textColor}`}>Local:</span>
                        <span className="text-gray-800 dark:text-white font-medium">{appointmentData.location}</span>
                      </div>
                    )}

                    {appointmentData.service && (
                      <div className="flex items-center gap-2">
                        <span className={`font-medium ${variantStyles.textColor}`}>Serviço:</span>
                        <span className="text-gray-800 dark:text-white font-medium">{appointmentData.service}</span>
                      </div>
                    )}
                  </div>
                  
                  {appointmentData.schedules && appointmentData.schedules.length > 0 && (
                    <div className="mt-4">
                      <span className={`font-medium ${variantStyles.textColor} block mb-3`}>Horários:</span>
                      <div className="space-y-2">
                        {appointmentData.schedules.map((schedule, index) => (
                          <div key={index} className="bg-white dark:bg-gray-600 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-500 shadow-sm">
                            <span className="text-gray-800 dark:text-white font-medium text-sm">{schedule}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div>
                {message.split('\n').map((line, index) => (
                  <React.Fragment key={index}>
                    {line.startsWith('•') ? (
                      <span className="block pl-2 py-0.5 text-purple-600 dark:text-purple-400 font-medium">{line}</span>
                    ) : line.match(/^\d+\./) ? (
                      <span className="block pl-2 py-0.5 font-medium">{line}</span>
                    ) : (
                      <span className={line.trim() === '' ? 'block py-1' : 'block py-0.5'}>{line}</span>
                    )}
                  </React.Fragment>
                ))}
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={(e) => {
                e.stopPropagation(); // Impedir propagação do evento
                e.preventDefault(); // Adicionado preventDefault para garantir
                onClose();
              }}
              className="px-6 py-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 font-medium"
            >
              {cancelText}
            </button>
            <button
              onClick={(e) => {
                console.log('[CONFIRMATION-DIALOG] Botão confirmar clicado!');
                console.log('[CONFIRMATION-DIALOG] onConfirm function:', typeof onConfirm);
                e.stopPropagation();
                e.preventDefault();
                if (onConfirm) {
                  console.log('[CONFIRMATION-DIALOG] Chamando onConfirm...');
                  onConfirm();
                } else {
                  console.error('[CONFIRMATION-DIALOG] onConfirm é undefined!');
                }
              }}
              className={`px-6 py-2.5 text-white rounded-lg transition-all duration-200 font-medium shadow-lg ${variantStyles.confirmBg}`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Renderizar o modal usando um portal para garantir que ele fique acima de tudo
  // Usamos um z-index maior que o do modal principal para garantir que ele fique por cima
  return createPortal(modalContent, document.body);
};

export default ConfirmationDialog;