"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/settings/CompanyManagementTab.js":
/*!*********************************************************!*\
  !*** ./src/components/settings/CompanyManagementTab.js ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Building,CheckCircle,Clock,CreditCard,Edit,Eye,Filter,Globe,Mail,MapPin,Phone,Plus,Power,RefreshCw,Search,Trash,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyLogoService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyLogoService.js\");\n/* harmony import */ var _CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CompanyFormModal */ \"(app-pages-browser)/./src/components/settings/CompanyFormModal.js\");\n/* harmony import */ var _CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./CompanyDetailsModal */ \"(app-pages-browser)/./src/components/settings/CompanyDetailsModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CompanyManagementTab = ()=>{\n    _s();\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalCompanies, setTotalCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyModalOpen, setCompanyModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companyDetailsModalOpen, setCompanyDetailsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCompany, setSelectedCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    // Check user roles\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // If user is company admin or has a company ID but is not system admin, we should only show their company\n    const isCompanyAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'COMPANY_ADMIN' || (user === null || user === void 0 ? void 0 : user.companyId) && (user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN';\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CompanyManagementTab.useEffect\": ()=>{\n            loadCompanies();\n        }\n    }[\"CompanyManagementTab.useEffect\"], []);\n    const loadCompanies = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage;\n        setIsLoading(true);\n        try {\n            // If company admin, just get their specific company\n            if (isCompanyAdmin && (user === null || user === void 0 ? void 0 : user.companyId)) {\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies/\".concat(user.companyId));\n                if (response.data) {\n                    setCompanies([\n                        response.data\n                    ]);\n                    setTotalCompanies(1);\n                    setTotalPages(1);\n                } else {\n                    setCompanies([]);\n                    setTotalCompanies(0);\n                    setTotalPages(1);\n                }\n            } else {\n                // System admin sees all companies\n                const params = {\n                    page,\n                    limit: 10,\n                    search: search || undefined,\n                    active: statusFilter || undefined\n                };\n                const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/companies\", {\n                    params\n                });\n                setCompanies(response.data.companies || []);\n                setTotalCompanies(response.data.total || 0);\n                setTotalPages(response.data.pages || 1);\n            }\n            setCurrentPage(page);\n        } catch (error) {\n            console.error(\"Error loading companies:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        loadCompanies(1);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        loadCompanies(1);\n    };\n    const handlePageChange = (page)=>{\n        setCurrentPage(page);\n        loadCompanies(page);\n    };\n    const handleAddCompany = ()=>{\n        setSelectedCompany(null);\n        setCompanyModalOpen(true);\n    };\n    const handleEditCompany = (company)=>{\n        setSelectedCompany(company);\n        setCompanyModalOpen(true);\n    };\n    const handleViewCompany = (companyId)=>{\n        setSelectedCompanyId(companyId);\n        setCompanyDetailsModalOpen(true);\n    };\n    const handleToggleCompanyStatus = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can change status\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(company.active ? \"Desativar\" : \"Ativar\", \" a empresa \").concat(company.name, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteCompany = (company)=>{\n        if (!isSystemAdmin) return; // Only system admins can delete\n        setSelectedCompany(company);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente a empresa \".concat(company.name, \"?\"),\n            variant: \"danger\"\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const confirmAction = async ()=>{\n        if (!selectedCompany || !isSystemAdmin) return;\n        try {\n            if (actionToConfirm.type === \"toggle-status\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.patch(\"/companies/\".concat(selectedCompany.id, \"/status\"));\n            } else if (actionToConfirm.type === \"delete\") {\n                await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.delete(\"/companies/\".concat(selectedCompany.id));\n            }\n            loadCompanies();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error performing action:\", error);\n            alert(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Ocorreu um erro ao executar esta ação\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined),\n                            isSystemAdmin ? \"Gerenciamento de Empresas\" : \"Minha Empresa\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleAddCompany,\n                        className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Nova Empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome, nome fantasia ou CNPJ...\",\n                                    value: search,\n                                    onChange: (e)=>setSearch(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-300 dark:focus:ring-gray-500 dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>setStatusFilter(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"true\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"false\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 214,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    className: \"px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 222,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    className: \"px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"sm:hidden h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-gray-500 dark:border-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, undefined) : companies.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-500 dark:text-gray-400\",\n                        children: \"Nenhuma empresa encontrada\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, undefined) : isCompanyAdmin ? // Company Admin sees just their company as a card\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-16 w-16 rounded-full bg-gray-100 dark:bg-gray-700/30 flex items-center justify-center text-gray-600 dark:text-gray-400\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-8 w-8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-neutral-800 dark:text-white\",\n                                                        children: companies[0].name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                                        children: companies[0].tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center mt-1 text-xs text-neutral-500 dark:text-gray-400\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"px-2 py-0.5 rounded-full \".concat(companies[0].active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                                            children: companies[0].active ? \"Ativa\" : \"Inativa\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(companies[0].id),\n                                                className: \"px-3 py-1.5 bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-md hover:bg-neutral-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Detalhes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 272,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(companies[0]),\n                                                className: \"px-3 py-1.5 bg-gray-50 dark:bg-gray-700/30 text-gray-600 dark:text-gray-400 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Editar\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 252,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CNPJ:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].cnpj ? companies[0].cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].phone2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Telefone 2:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].phone2\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 290,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Endere\\xe7o\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Endere\\xe7o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].address || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Cidade/Estado:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].city && companies[0].state ? \"\".concat(companies[0].city, \", \").concat(companies[0].state) : \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"CEP:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].postalCode || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Online\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Website:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].website || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 348,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Email:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].contactEmail || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-500 dark:text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Outras Informa\\xe7\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Moeda:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].defaultCurrency || \"BRL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 365,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm flex justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400\",\n                                                                children: \"Fuso Hor\\xe1rio:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200 font-medium\",\n                                                                children: companies[0].timeZone || \"America/Sao_Paulo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    companies[0].description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-500 dark:text-gray-400 block mb-1\",\n                                                                children: \"Descri\\xe7\\xe3o:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-neutral-800 dark:text-gray-200\",\n                                                                children: companies[0].description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 362,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 251,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 250,\n                    columnNumber: 11\n                }, undefined) : // System Admin sees a table of all companies\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Empresas\",\n                    columns: [\n                        {\n                            header: 'Empresa',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'CNPJ',\n                            field: 'cnpj',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Plano',\n                            field: 'plan',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Pagamento',\n                            field: 'payment',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'status',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: companies,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma empresa encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                        lineNumber: 399,\n                        columnNumber: 24\n                    }, void 0),\n                    currentPage: currentPage,\n                    totalPages: totalPages,\n                    totalItems: totalCompanies,\n                    onPageChange: handlePageChange,\n                    showPagination: totalPages > 1,\n                    tableId: \"admin-companies-table\",\n                    enableColumnToggle: true,\n                    renderRow: (company, index, moduleColors, visibleColumns)=>{\n                        var _company_subscriptionInfo, _company_subscriptionInfo1, _company_subscriptionInfo2, _company_subscriptionInfo3, _company_subscriptionInfo4, _company_subscriptionInfo5, _company_subscriptionInfo6, _company_subscriptionInfo7, _company_subscriptionInfo8;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 flex items-center justify-center\",\n                                                children: company.documents && company.documents[0] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        className: \"h-10 w-10 rounded-full object-cover border border-neutral-200 dark:border-gray-600\",\n                                                        src: company.documents && company.documents[0] && company.documents[0].path ? _app_modules_admin_services_companyLogoService__WEBPACK_IMPORTED_MODULE_5__.companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : '',\n                                                        alt: company.name,\n                                                        onLoad: ()=>console.log(\"[CompanyManagementTab] Imagem da empresa \".concat(company.id, \" carregada com sucesso\")),\n                                                        onError: (e)=>{\n                                                            console.error(\"[CompanyManagementTab] Erro ao carregar imagem da empresa \".concat(company.id, \":\"), e.target.src);\n                                                            e.target.onerror = null;\n                                                            e.target.style.display = 'none';\n                                                            e.target.parentNode.innerHTML = '<div class=\"h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700/30 flex items-center justify-center text-gray-600 dark:text-gray-400\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><rect width=\"20\" height=\"14\" x=\"2\" y=\"7\" rx=\"2\" ry=\"2\"/><path d=\"M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16\"/></svg></div>';\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 rounded-full bg-gray-100 dark:bg-gray-700/30 flex items-center justify-center text-gray-600 dark:text-gray-400\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 29\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100\",\n                                                        children: company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 25\n                                                    }, void 0),\n                                                    company.tradingName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-neutral-500 dark:text-neutral-400\",\n                                                        children: company.tradingName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 435,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 411,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 410,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('cnpj') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                    children: company.cnpj ? company.cnpj.replace(/^(\\d{2})(\\d{3})(\\d{3})(\\d{4})(\\d{2})$/, \"$1.$2.$3/$4-$5\") : \"N/A\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('plan') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-neutral-800 dark:text-neutral-100\",\n                                                children: ((_company_subscriptionInfo = company.subscriptionInfo) === null || _company_subscriptionInfo === void 0 ? void 0 : _company_subscriptionInfo.planName) || 'Sem Plano'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 458,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-neutral-500 dark:text-neutral-400\",\n                                                children: [\n                                                    ((_company_subscriptionInfo1 = company.subscriptionInfo) === null || _company_subscriptionInfo1 === void 0 ? void 0 : _company_subscriptionInfo1.moduleCount) || 0,\n                                                    \" m\\xf3dulos\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 461,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 456,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('payment') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            ((_company_subscriptionInfo2 = company.subscriptionInfo) === null || _company_subscriptionInfo2 === void 0 ? void 0 : _company_subscriptionInfo2.paymentStatus) === 'up_to_date' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-green-600 dark:text-green-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Em dia\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 471,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo3 = company.subscriptionInfo) === null || _company_subscriptionInfo3 === void 0 ? void 0 : _company_subscriptionInfo3.paymentStatus) === 'overdue' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-red-600 dark:text-red-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Atrasado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 477,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo4 = company.subscriptionInfo) === null || _company_subscriptionInfo4 === void 0 ? void 0 : _company_subscriptionInfo4.paymentStatus) === 'expired' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-orange-600 dark:text-orange-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Vencido\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 483,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo5 = company.subscriptionInfo) === null || _company_subscriptionInfo5 === void 0 ? void 0 : _company_subscriptionInfo5.isNearExpiry) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-yellow-600 dark:text-yellow-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Pr\\xf3x. venc.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 489,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            ((_company_subscriptionInfo6 = company.subscriptionInfo) === null || _company_subscriptionInfo6 === void 0 ? void 0 : _company_subscriptionInfo6.paymentStatus) === 'no_subscription' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Sem plano\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 469,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 468,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_company_subscriptionInfo7 = company.subscriptionInfo) === null || _company_subscriptionInfo7 === void 0 ? void 0 : _company_subscriptionInfo7.userCount) || 0,\n                                                    ((_company_subscriptionInfo8 = company.subscriptionInfo) === null || _company_subscriptionInfo8 === void 0 ? void 0 : _company_subscriptionInfo8.userLimit) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-400 dark:text-neutral-500\",\n                                                        children: [\n                                                            \"/\",\n                                                            company.subscriptionInfo.userLimit\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                        lineNumber: 510,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 505,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 504,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full \".concat(company.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300\"),\n                                        children: company.active ? \"Ativa\" : \"Inativa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 521,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 520,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-4 py-4 text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewCompany(company.id),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-gray-500 dark:hover:text-gray-400 transition-colors\",\n                                                title: \"Visualizar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 535,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 543,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleCompanyStatus(company),\n                                                className: \"p-1 transition-colors \".concat(company.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                title: company.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 551,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteCompany(company),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Building_CheckCircle_Clock_CreditCard_Edit_Eye_Filter_Globe_Mail_MapPin_Phone_Plus_Power_RefreshCw_Search_Trash_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                                lineNumber: 563,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                        lineNumber: 534,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                                    lineNumber: 533,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, company.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                            lineNumber: 408,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                    lineNumber: 384,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, undefined),\n            companyModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyFormModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: companyModalOpen,\n                onClose: ()=>setCompanyModalOpen(false),\n                company: selectedCompany,\n                onSuccess: ()=>{\n                    setCompanyModalOpen(false);\n                    loadCompanies();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 581,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CompanyDetailsModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: companyDetailsModalOpen,\n                onClose: ()=>setCompanyDetailsModalOpen(false),\n                companyId: selectedCompanyId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 592,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.variant) || \"warning\",\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n                lineNumber: 598,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\CompanyManagementTab.js\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CompanyManagementTab, \"9dHN0xgRBQS8/O5mdeEH5tqyYdQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth\n    ];\n});\n_c = CompanyManagementTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompanyManagementTab);\nvar _c;\n$RefreshReg$(_c, \"CompanyManagementTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/CompanyManagementTab.js\n"));

/***/ })

});