"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/permissions/PermissionsModal.js":
/*!********************************************************!*\
  !*** ./src/components/permissions/PermissionsModal.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Calendar,CheckSquare,ChevronDown,ChevronRight,DollarSign,Info,Loader2,Search,Settings,Shield,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/permissionConfig */ \"(app-pages-browser)/./src/utils/permissionConfig.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst PermissionsModal = (param)=>{\n    let { isOpen, onClose, user, onSuccess } = param;\n    var _currentUser_modules, _user_modules;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [selectedPermissions, setSelectedPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [expandedModules, setExpandedModules] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredPermissions, setFilteredPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Carregar permissões do usuário ao abrir o modal\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsModal.useEffect\": ()=>{\n            if (user && isOpen) {\n                setIsLoading(true);\n                // Reset completo do estado primeiro\n                setExpandedModules({});\n                setSearchTerm(\"\");\n                setError(\"\");\n                setFilteredPermissions((0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getAllPermissions)());\n                // Se usuário já tem módulos/permissões, configuramos o estado inicial\n                if (user.permissions) {\n                    setSelectedPermissions(user.permissions);\n                } else {\n                    // Se não tiver permissões, inicializamos com array vazio\n                    setSelectedPermissions([]);\n                }\n                setIsLoading(false);\n            }\n        }\n    }[\"PermissionsModal.useEffect\"], [\n        user,\n        isOpen\n    ]);\n    // Filtragem de permissões baseada na busca\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PermissionsModal.useEffect\": ()=>{\n            if (!searchTerm.trim()) {\n                setFilteredPermissions((0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getAllPermissions)());\n                return;\n            }\n            const lowerSearch = searchTerm.toLowerCase();\n            const filtered = (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getAllPermissions)().filter({\n                \"PermissionsModal.useEffect.filtered\": (permission)=>permission.name.toLowerCase().includes(lowerSearch) || permission.description.toLowerCase().includes(lowerSearch) || permission.id.toLowerCase().includes(lowerSearch) || _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[permission.moduleId].name.toLowerCase().includes(lowerSearch)\n            }[\"PermissionsModal.useEffect.filtered\"]);\n            setFilteredPermissions(filtered);\n            // Expande módulos apenas quando há busca ativa\n            const modulesToExpand = {};\n            filtered.forEach({\n                \"PermissionsModal.useEffect\": (permission)=>{\n                    modulesToExpand[permission.moduleId] = true;\n                }\n            }[\"PermissionsModal.useEffect\"]);\n            setExpandedModules(modulesToExpand);\n        }\n    }[\"PermissionsModal.useEffect\"], [\n        searchTerm\n    ]);\n    const isAdmin = currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_modules = currentUser.modules) === null || _currentUser_modules === void 0 ? void 0 : _currentUser_modules.includes(\"ADMIN\");\n    // Verificar se o usuário tem uma permissão específica\n    const hasPermission = (permissionId)=>{\n        return selectedPermissions.includes(permissionId);\n    };\n    // Alternar uma permissão específica\n    const togglePermission = (permissionId)=>{\n        setSelectedPermissions((prev)=>{\n            if (prev.includes(permissionId)) {\n                return prev.filter((id)=>id !== permissionId);\n            } else {\n                return [\n                    ...prev,\n                    permissionId\n                ];\n            }\n        });\n    };\n    // Alternar todas as permissões de um módulo - usando a mesma lógica do UserPermissionsTab\n    const toggleModulePermissions = (moduleId)=>{\n        let modulePermissions = filteredPermissions.filter((p)=>p.moduleId === moduleId && !p.id.startsWith('notifications.'));\n        // Se for BASIC, excluir permissões que também existem em ADMIN\n        if (moduleId === 'BASIC') {\n            const adminPermissionIds = filteredPermissions.filter((p)=>p.moduleId === 'ADMIN').map((p)=>p.id);\n            modulePermissions = modulePermissions.filter((p)=>!adminPermissionIds.includes(p.id));\n        }\n        const modulePermissionIds = modulePermissions.map((p)=>p.id);\n        const allSelected = modulePermissionIds.every((id)=>selectedPermissions.includes(id));\n        if (allSelected) {\n            setSelectedPermissions((prev)=>prev.filter((id)=>!modulePermissionIds.includes(id)));\n        } else {\n            setSelectedPermissions((prev)=>[\n                    ...new Set([\n                        ...prev,\n                        ...modulePermissionIds\n                    ])\n                ]);\n        }\n    };\n    // Alternar a expansão de um módulo\n    const toggleModuleExpansion = (moduleId)=>{\n        setExpandedModules((prev)=>({\n                ...prev,\n                [moduleId]: !prev[moduleId]\n            }));\n    };\n    // Salvar permissões\n    const handleSave = async ()=>{\n        setIsSaving(true);\n        setError(\"\");\n        try {\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_4__.userService.updatePermissions(user.id, selectedPermissions);\n            onSuccess();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao atualizar permissões:\", error);\n            setError(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao atualizar permissões\");\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    // Obter o ícone do módulo\n    const getModuleIcon = (moduleId)=>{\n        const icons = {\n            ADMIN: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 169,\n                columnNumber: 14\n            }, undefined),\n            RH: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 170,\n                columnNumber: 11\n            }, undefined),\n            FINANCIAL: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 171,\n                columnNumber: 18\n            }, undefined),\n            SCHEDULING: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 172,\n                columnNumber: 19\n            }, undefined),\n            PEOPLE: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 173,\n                columnNumber: 15\n            }, undefined),\n            BASIC: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 174,\n                columnNumber: 14\n            }, undefined)\n        };\n        return icons[moduleId] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n            lineNumber: 177,\n            columnNumber: 31\n        }, undefined);\n    };\n    // Renderizar as permissões de um módulo\n    const renderModulePermissions = (moduleId)=>{\n        const module = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId];\n        if (!module) return null;\n        // Se estiver filtrando, mostrar apenas as permissões que correspondem à busca\n        let permissions = searchTerm ? module.permissions.filter((p)=>filteredPermissions.some((fp1)=>fp1.id === p.id)) : module.permissions;\n        // Se for BASIC, remover permissões que também existem em ADMIN ou NOTIFICATIONS\n        if (moduleId === 'BASIC') {\n            var _PERMISSIONS_CONFIG_ADMIN;\n            const adminPermissionIds = ((_PERMISSIONS_CONFIG_ADMIN = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG['ADMIN']) === null || _PERMISSIONS_CONFIG_ADMIN === void 0 ? void 0 : _PERMISSIONS_CONFIG_ADMIN.permissions.map((p)=>p.id)) || [];\n            const notificationPermissionIds = filteredPermissions.filter((p)=>p.id.startsWith('notifications.')).map((p)=>p.id);\n            permissions = permissions.filter((p)=>!adminPermissionIds.includes(p.id) && !notificationPermissionIds.includes(p.id));\n        }\n        if (permissions.length === 0) return null;\n        const allPermissionsSelected = permissions.every((p)=>selectedPermissions.includes(p.id));\n        const somePermissionsSelected = permissions.some((p)=>selectedPermissions.includes(p.id));\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 flex items-center justify-between cursor-pointer border-b dark:border-gray-700 \".concat(moduleId === \"ADMIN\" ? \"bg-gray-200 dark:bg-gray-800/60\" : moduleId === \"PEOPLE\" ? \"bg-orange-300 dark:bg-orange-700/60\" : moduleId === \"SCHEDULING\" ? \"bg-purple-100 dark:bg-purple-900/30\" : moduleId === \"BASIC\" ? \"bg-gray-100 dark:bg-gray-800/40\" : \"bg-neutral-50 dark:bg-gray-800\"),\n                    onClick: ()=>toggleModuleExpansion(moduleId),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-2 rounded-full \".concat(somePermissionsSelected ? allPermissionsSelected ? \"bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\" : \"bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400\" : \"bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\"),\n                                    children: getModuleIcon(moduleId)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-neutral-800 dark:text-gray-200\",\n                                            children: module.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                            children: somePermissionsSelected ? \"\".concat(selectedPermissions.filter((p)=>permissions.some((mp)=>mp.id === p)).length, \" de \").concat(permissions.length, \" permiss\\xf5es selecionadas\") : \"Nenhuma permissão selecionada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        toggleModulePermissions(moduleId);\n                                    },\n                                    className: \"px-3 py-1 rounded text-sm font-medium \".concat(allPermissionsSelected ? \"bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700\" : \"bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700\"),\n                                    children: allPermissionsSelected ? \"Desmarcar todas\" : \"Selecionar todas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, undefined),\n                                expandedModules[moduleId] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"text-neutral-600 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 270,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"text-neutral-600 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, undefined),\n                expandedModules[moduleId] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 divide-y dark:divide-gray-700 dark:bg-gray-850\",\n                    children: permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-3 first:pt-0 last:pb-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 mt-0.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: permission.id,\n                                            checked: hasPermission(permission.id),\n                                            onChange: ()=>togglePermission(permission.id),\n                                            className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 283,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: permission.id,\n                                                className: \"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\",\n                                                children: permission.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-neutral-600 dark:text-gray-400\",\n                                                children: permission.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-1 text-xs text-neutral-500 dark:text-gray-500\",\n                                                children: [\n                                                    \"ID: \",\n                                                    permission.id\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 282,\n                                columnNumber: 17\n                            }, undefined)\n                        }, permission.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                            lineNumber: 281,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, moduleId, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, undefined);\n    };\n    if (false) {}\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 dark:bg-black/70\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-gray-300 dark:border-gray-600 bg-background shadow-lg duration-200 rounded-xl max-w-5xl max-h-[90vh] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center px-6 py-4 border-b-2 border-gray-400 dark:border-gray-500 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg text-white\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-slate-800 dark:text-white border-l-4 border-gray-400 dark:border-gray-500 pl-3\",\n                                                children: \"Gerenciar Permiss\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3\",\n                                                children: \"Configure as permiss\\xf5es espec\\xedficas do usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"text-neutral-500 hover:text-neutral-700 dark:text-gray-400 dark:hover:text-gray-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 340,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto p-6 bg-background min-h-0\",\n                        children: [\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-center gap-2 dark:bg-red-900/20 dark:border-red-800/50 dark:text-red-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3\",\n                                        children: user === null || user === void 0 ? void 0 : user.fullName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-neutral-600 dark:text-gray-400 mb-4\",\n                                        children: \"Configure as permiss\\xf5es espec\\xedficas que este usu\\xe1rio ter\\xe1 acesso dentro de cada m\\xf3dulo:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-amber-50 border border-amber-200 p-2 rounded-lg flex items-start gap-2 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 text-amber-500 dark:text-amber-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                        className: \"text-sm font-medium text-amber-800 dark:text-amber-300\",\n                                                        children: \"Importante\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-amber-700 dark:text-amber-400\",\n                                                        children: \"As permiss\\xf5es s\\xf3 ser\\xe3o aplicadas se o usu\\xe1rio tamb\\xe9m tiver acesso ao m\\xf3dulo correspondente. Certifique-se de que o usu\\xe1rio tenha os m\\xf3dulos necess\\xe1rios atribu\\xeddos.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 371,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-neutral-400 dark:text-gray-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Buscar permiss\\xf5es...\",\n                                                value: searchTerm,\n                                                onChange: (e)=>setSearchTerm(e.target.value),\n                                                className: \"w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 dark:placeholder-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 386,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            searchTerm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                onClick: ()=>setSearchTerm(\"\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-neutral-400 hover:text-neutral-600 dark:text-gray-500 dark:hover:text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-8 w-8 animate-spin text-primary-500 dark:text-primary-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-transparent border border-gray-300 dark:border-gray-600 p-3 rounded-lg mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-neutral-700 dark:text-gray-300 mb-2\",\n                                                children: \"M\\xf3dulos Atribu\\xeddos\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: [\n                                                    user === null || user === void 0 ? void 0 : (_user_modules = user.modules) === null || _user_modules === void 0 ? void 0 : _user_modules.filter((moduleId)=>(0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getActiveModules)().includes(moduleId)).map((moduleId)=>{\n                                                        var _PERMISSIONS_CONFIG_moduleId;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-1.5 border rounded-full flex items-center gap-2 \".concat(moduleId === \"ADMIN\" ? \"bg-gray-200 dark:bg-gray-800/60 border-gray-300 dark:border-gray-700/60\" : moduleId === \"SCHEDULING\" ? \"bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700\" : moduleId === \"PEOPLE\" ? \"bg-orange-300 dark:bg-orange-700/60 border-orange-400 dark:border-orange-600/70\" : moduleId === \"BASIC\" ? \"bg-gray-100 dark:bg-gray-800/40 border-gray-300 dark:border-gray-600\" : \"bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600\"),\n                                                            children: [\n                                                                getModuleIcon(moduleId),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm dark:text-gray-300\",\n                                                                    children: ((_PERMISSIONS_CONFIG_moduleId = _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId]) === null || _PERMISSIONS_CONFIG_moduleId === void 0 ? void 0 : _PERMISSIONS_CONFIG_moduleId.name) || moduleId\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, moduleId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 21\n                                                        }, undefined);\n                                                    }),\n                                                    (!(user === null || user === void 0 ? void 0 : user.modules) || user.modules.filter((m)=>(0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getActiveModules)().includes(m)).length === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                        children: \"Nenhum m\\xf3dulo atribu\\xeddo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 411,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    searchTerm ? (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getActiveModules)().filter((moduleId)=>_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId] && _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId].permissions.some((p)=>filteredPermissions.some((fp1)=>fp1.id === p.id) && !fp.id.startsWith('notifications.'))).map((moduleId)=>renderModulePermissions(moduleId)) : (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getActiveModules)().filter((moduleId)=>_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.PERMISSIONS_CONFIG[moduleId]).map((moduleId)=>renderModulePermissions(moduleId)),\n                                    (()=>{\n                                        const notificationPermissions = filteredPermissions.filter((p)=>p.id.startsWith('notifications.'));\n                                        if (notificationPermissions.length > 0) {\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6 border rounded-lg overflow-hidden dark:border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer overflow-hidden\",\n                                                        onClick: ()=>toggleModuleExpansion('NOTIFICATIONS'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 rounded-full \".concat(notificationPermissions.some((p)=>selectedPermissions.includes(p.id)) ? notificationPermissions.every((p)=>selectedPermissions.includes(p.id)) ? \"bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400\" : \"bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400\" : \"bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                            lineNumber: 479,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-medium text-neutral-800 dark:text-gray-200\",\n                                                                                children: \"Notifica\\xe7\\xf5es\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                lineNumber: 482,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                                                children: notificationPermissions.some((p)=>selectedPermissions.includes(p.id)) ? \"\".concat(selectedPermissions.filter((p)=>p.startsWith('notifications.')).length, \" de \").concat(notificationPermissions.length, \" permiss\\xf5es selecionadas\") : \"Nenhuma permissão selecionada\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 relative z-10\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        type: \"button\",\n                                                                        onClick: (e)=>{\n                                                                            e.stopPropagation();\n                                                                            const notificationPermissionIds = notificationPermissions.map((p)=>p.id);\n                                                                            const allSelected = notificationPermissionIds.every((id)=>selectedPermissions.includes(id));\n                                                                            if (allSelected) {\n                                                                                setSelectedPermissions((prev)=>prev.filter((id)=>!notificationPermissionIds.includes(id)));\n                                                                            } else {\n                                                                                setSelectedPermissions((prev)=>[\n                                                                                        ...new Set([\n                                                                                            ...prev,\n                                                                                            ...notificationPermissionIds\n                                                                                        ])\n                                                                                    ]);\n                                                                            }\n                                                                        },\n                                                                        className: \"px-3 py-1 rounded text-sm font-medium \".concat(notificationPermissions.every((p)=>selectedPermissions.includes(p.id)) ? \"bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700\" : \"bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700\"),\n                                                                        children: notificationPermissions.every((p)=>selectedPermissions.includes(p.id)) ? \"Desmarcar todas\" : \"Selecionar todas\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                        lineNumber: 491,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    expandedModules['NOTIFICATIONS'] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neutral-500 dark:text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                        lineNumber: 517,\n                                                                        columnNumber: 29\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-neutral-500 dark:text-gray-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    expandedModules['NOTIFICATIONS'] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 divide-y divide-gray-200 dark:divide-gray-700 dark:bg-gray-850\",\n                                                        children: notificationPermissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"py-3 first:pt-0 last:pb-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-shrink-0 mt-0.5\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                id: permission.id,\n                                                                                checked: selectedPermissions.includes(permission.id),\n                                                                                onChange: ()=>togglePermission(permission.id),\n                                                                                className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:checked:bg-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: permission.id,\n                                                                                    className: \"block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer\",\n                                                                                    children: permission.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                    lineNumber: 538,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"mt-1 text-sm text-neutral-600 dark:text-gray-400\",\n                                                                                    children: permission.description\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                    lineNumber: 544,\n                                                                                    columnNumber: 35\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-1 text-xs text-neutral-500 dark:text-gray-500\",\n                                                                                    children: [\n                                                                                        \"ID: \",\n                                                                                        permission.id\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                                    lineNumber: 547,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                            lineNumber: 537,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, permission.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 29\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, undefined);\n                                        }\n                                        return null;\n                                    })(),\n                                    searchTerm && filteredPermissions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-neutral-500 dark:text-gray-400\",\n                                            children: [\n                                                'Nenhuma permiss\\xe3o encontrada para \"',\n                                                searchTerm,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                            lineNumber: 565,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 564,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center border-t-2 border-gray-300 dark:border-gray-600 pt-4 px-6 pb-4 flex-shrink-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-neutral-600 dark:text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: (()=>{\n                                            // Contar apenas permissões dos módulos ativos\n                                            const activeModules = (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_5__.getActiveModules)();\n                                            const activePermissions = selectedPermissions.filter((permId)=>{\n                                                // Incluir permissões de notificação\n                                                if (permId.startsWith('notifications.')) {\n                                                    return true;\n                                                }\n                                                // Incluir apenas permissões de módulos ativos\n                                                return filteredPermissions.some((p)=>p.id === permId && activeModules.includes(p.moduleId));\n                                            });\n                                            return activePermissions.length;\n                                        })()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    \"permiss\\xf5es selecionadas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: onClose,\n                                        className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                        disabled: isSaving,\n                                        children: \"Cancelar\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleSave,\n                                        className: \"px-4 py-2 bg-slate-500 hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700 text-white rounded-lg transition-colors flex items-center gap-2\",\n                                        disabled: isSaving,\n                                        children: isSaving ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    size: 16,\n                                                    className: \"animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvando...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 614,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Calendar_CheckSquare_ChevronDown_ChevronRight_DollarSign_Info_Loader2_Search_Settings_Shield_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 618,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Salvar Permiss\\xf5es\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                                lineNumber: 596,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n                lineNumber: 324,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\permissions\\\\PermissionsModal.js\",\n        lineNumber: 320,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PermissionsModal, \"bAeQaxmiSAKYMfKURw7MVm+i1Ck=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = PermissionsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PermissionsModal);\nvar _c;\n$RefreshReg$(_c, \"PermissionsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/permissions/PermissionsModal.js\n"));

/***/ })

});