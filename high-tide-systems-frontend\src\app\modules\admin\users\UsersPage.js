"use client";

import React, { useState, useEffect, useMemo, useCallback } from "react";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Shield,
  Power,
  CheckCircle,
  XCircle,
  Lock,
  UserCog,
  Briefcase,
  User,
  Mail
} from "lucide-react";
import ExportMenu from "@/components/ui/ExportMenu";
import { useAuth } from "@/contexts/AuthContext";
import { usePermissions } from "@/hooks/usePermissions";
import { Protected } from "@/components/permissions/Protected";
import UserFormModal from "@/components/users/UserFormModal";
import ModulesModal from "@/components/users/ModulesModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import PermissionsModal from "@/components/permissions/PermissionsModal";
import RoleModal from "@/components/users/RoleModal";
import { userService } from "@/app/modules/admin/services/userService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { subscriptionService } from "@/services/subscriptionService";
import { ModuleSelect, ModuleTable, MultiSelect, ModuleCheckbox } from "@/components/ui";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import { useRouter, useSearchParams } from 'next/navigation';
import { UsersFilters } from '@/components/admin/UsersFilters';
import { 
  SensitiveEmail, 
  SensitiveFullName, 
  SensitiveAvatar 
} from '@/components/ui/SensitiveField';
import { usePreferences } from '@/hooks/usePreferences';
import { useDataPrivacy } from '@/hooks/useDataPrivacy';
import { getActiveModules } from '@/utils/permissionConfig';

const UsersPage = () => {
  const { user: currentUser } = useAuth();
  const { can } = usePermissions();
  const { preferences } = usePreferences();
  const { applyListPrivacyMasks } = useDataPrivacy();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: "",
    companies: [],
    status: "",
    module: "",
    users: []
  });
  const [userOptions, setUserOptions] = useState([]);
  const [isLoadingUserOptions, setIsLoadingUserOptions] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [userFormOpen, setUserFormOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [modulesModalOpen, setModulesModalOpen] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [permissionsModalOpen, setPermissionsModalOpen] = useState(false);
  const [roleModalOpen, setRoleModalOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [isExporting, setIsExporting] = useState(false);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [isLoadingSubscription, setIsLoadingSubscription] = useState(false);
  const [showUserLimitModal, setShowUserLimitModal] = useState(false);
  const [selectedActiveUsers, setSelectedActiveUsers] = useState([]);
  const [savingUserLimit, setSavingUserLimit] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  // Funções para seleção múltipla
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(users.map(u => u.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };

  // Estado para controlar itens por página
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Estados para controlar ordenação
  const [sortField, setSortField] = useState("fullName");
  const [sortDirection, setSortDirection] = useState("asc");
  
  // Obter módulos ativos e seus labels
  const activeModules = getActiveModules();
  const MODULE_LABELS = {
    ADMIN: "Administração",
    RH: "RH",
    FINANCIAL: "Financeiro",
    SCHEDULING: "Agendamento",
    PEOPLE: "Pessoas",
    BASIC: "Básico",
  };

  // Verificar se o usuário atual é um system_admin ou company_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const isAdmin = currentUser?.role === "SYSTEM_ADMIN" || currentUser?.role === "COMPANY_ADMIN";

  // Verificar se pode adicionar usuários baseado no limite da subscription
  const canAddUsers = subscriptionData?.usage?.canAddUsers !== false;

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar dados da subscription
  const loadSubscriptionData = async () => {
    if (isSystemAdmin) return; // System admin não tem limite de usuários

    setIsLoadingSubscription(true);
    try {
      const response = await subscriptionService.getSubscription();
      setSubscriptionData(response);
    } catch (error) {
      console.error("Erro ao carregar dados da subscription:", error);
      // Se não conseguir carregar, assume que pode adicionar usuários
      setSubscriptionData({ usage: { canAddUsers: true } });
    } finally {
      setIsLoadingSubscription(false);
    }
  };

  // Função para carregar opções de usuários para o multi-select
  const loadUserOptions = useCallback(async () => {
    setIsLoadingUserOptions(true);
    try {
      // ✅ CORREÇÃO: Aplicar mesmo filtro de empresa para as opções
      const filters = {
        active: true, // Apenas usuários ativos por padrão
        // Company admin só vê usuários da sua empresa
        companyId: isSystemAdmin
          ? undefined // System admin pode ver usuários de todas as empresas
          : (currentUser?.companyId || undefined), // Company admin só vê usuários da sua empresa
        excludeSystemAdmin: !isSystemAdmin
      };

      console.log('🔍 Filtros aplicados para loadUserOptions:', {
        userRole: currentUser?.role,
        isSystemAdmin,
        companyId: filters.companyId,
        filters
      });

      // Carregar todos os usuários para o multi-select (com limite maior)
      const response = await userService.list(1, 100, filters);

      console.log('📊 Opções de usuários carregadas:', {
        totalOptions: response.users?.length,
        firstOption: response.users?.[0]?.fullName
      });

      // Transformar os dados para o formato esperado pelo MultiSelect
      const options = response.users.map(user => ({
        value: user.id,
        label: user.fullName
      }));

      setUserOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de usuários:", error);
    } finally {
      setIsLoadingUserOptions(false);
    }
  }, [isSystemAdmin, currentUser?.companyId]);

  const loadUsers = async (
    page = currentPage,
    filtersToUse = filters,
    sortField = "fullName",
    sortDirection = "asc",
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      const apiFilters = {
        search: filtersToUse.search || undefined,
        active: filtersToUse.status === "" ? undefined : filtersToUse.status === "active",
        module: filtersToUse.module || undefined,
        excludeSystemAdmin: !isSystemAdmin,
        userIds: filtersToUse.users.length > 0 ? filtersToUse.users : undefined,
        sortField: sortField,
        sortDirection: sortDirection
      };
      
      // Adicionar companyId apenas quando necessário
      if (isSystemAdmin) {
        if (filtersToUse.companies.length > 0) {
          apiFilters.companyId = filtersToUse.companies[0];
          console.log('🏢user SYSTEM_ADMIN com empresa selecionada:', filtersToUse.companies[0]);
        } else {
          console.log('🌍🏢user SYSTEM_ADMIN sem empresa - buscando em todas as empresas');
        }
      } else if (currentUser?.companyId) {
        apiFilters.companyId = currentUser.companyId;
        console.log('🏢🏢user Usuário normal - empresa:', currentUser.companyId);
      }
      
      console.log('📋🏢user Filtros finais enviados para API:', apiFilters);

      const response = await userService.list(page, perPage, apiFilters);
      
      console.log('📊🏢user Resposta da API:', {
        total: response.total,
        usersCount: response.users?.length,
        pages: response.pages
      });

      console.log('📊🏢user Resposta do userService.list:', {
        totalUsers: response.total,
        usersCount: response.users?.length,
        firstUser: response.users?.[0]?.fullName
      });

      console.log('🎯 Definindo users no estado:', response.users?.length, 'usuários');
      console.log('🎯 Primeiro usuário:', response.users?.[0]?.fullName);
      
      // Aplicar máscaras de privacidade aos dados dos usuários
      const usersWithPrivacy = applyListPrivacyMasks('user', response.users || []);
      console.log('🔒 Máscaras de privacidade aplicadas aos usuários');
      
      setUsers(usersWithPrivacy);
      setTotalUsers(response.total);
      setTotalPages(response.pages);
      setCurrentPage(page);
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
      console.error("Detalhes do erro:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadUsers(1, filters, "fullName", "asc");
    loadSubscriptionData();
  }, []);

  // Efeito para abrir o modal automaticamente baseado nos parâmetros da URL
  useEffect(() => {
    const userId = searchParams.get('userId');
    const openModal = searchParams.get('openModal');

    if (userId && openModal === 'true') {
      // Verifica se o usuário já está carregado
      const user = users.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setUserFormOpen(true);
      } else {
        // Se não estiver carregado, busca o usuário
        userService.get(userId).then(userData => {
          // Aplicar máscaras de privacidade ao usuário específico
          const userWithPrivacy = applyListPrivacyMasks('user', [userData])[0];
          setSelectedUser(userWithPrivacy);
          setUserFormOpen(true);
        }).catch(error => {
          console.error("Erro ao buscar usuário:", error);
        });
      }
    }
  }, [searchParams, users]);

  const handleSearch = (searchFilters) => {
    loadUsers(1, searchFilters, "fullName", "asc");
  };

  const handlePageChange = (page) => {
    // Manter a ordenação atual ao mudar de página
    const currentSortField = sortField || "fullName";
    const currentSortDirection = sortDirection || "asc";
    console.log('📄 Mudando página:', { page, currentSortField, currentSortDirection });
    loadUsers(page, filters, currentSortField, currentSortDirection, itemsPerPage);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setUserFormOpen(true);
  };

  const handleEditModules = (user) => {
    setSelectedUser(user);
    setModulesModalOpen(true);
  };

  const handleManageRole = (user) => {
    setSelectedUser(user);
    setRoleModalOpen(true);
  };

  const handleToggleStatus = (user) => {
    setSelectedUser(user);
    setActionToConfirm({
      type: "toggle-status",
      message: `${user.active ? "Desativar" : "Ativar"} o usuário ${user.fullName
        }?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteUser = (user) => {
    setSelectedUser(user);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente o usuário ${user.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleManagePermissions = (user) => {
    setSelectedUser(user);
    setPermissionsModalOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      await userService.exportUsers({
        search: filters.search || undefined,
        userIds: filters.users.length > 0 ? filters.users : undefined,
        active: filters.status === "" ? undefined : filters.status === "active",
        module: filters.module || undefined,
        companyId: filters.companies.length > 0 ? filters.companies[0] : undefined
      }, format, preferences);
    } catch (error) {
      console.error("Erro ao exportar usuários:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await userService.toggleStatus(selectedUser.id, !selectedUser.active);
        loadUsers();
        loadSubscriptionData(); // Recarregar dados da subscription
      } catch (error) {
        console.error("Erro ao alterar status do usuário:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await userService.delete(selectedUser.id);
        loadUsers();
        loadSubscriptionData(); // Recarregar dados da subscription
      } catch (error) {
        console.error("Erro ao excluir usuário:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  // Import tutorial steps from tutorialMapping
  const admUsersTutorialSteps = useMemo(() => {
    // Import dynamically to avoid circular dependencies
    const tutorialMap = require('@/tutorials/tutorialMapping').default;
    return tutorialMap['/dashboard/admin/users'] || [];
  }, []);

  // Função para abrir o modal e pré-selecionar os primeiros permitidos
  const openUserLimitModal = () => {
    // Filtra o usuário atual da lista de usuários disponíveis
    const availableUsers = users.filter(u => u.active && u.id !== currentUser?.id);

    // Seleciona os primeiros (userLimit - 1) usuários, já que o usuário atual sempre ficará ativo
    if (availableUsers.length > 0 && subscriptionData?.usage) {
      const usersToSelect = Math.min(subscriptionData.usage.userLimit - 1, availableUsers.length);
      setSelectedActiveUsers(availableUsers.slice(0, usersToSelect).map(u => u.id));
    }
    setShowUserLimitModal(true);
  };
  // Função para salvar seleção e desativar excedentes
  const handleSaveUserLimit = async () => {
    setSavingUserLimit(true);
    try {
      // Desativa todos os usuários ativos que não estão selecionados E que não são o usuário atual
      const usersToDeactivate = users.filter(u =>
        u.active &&
        !selectedActiveUsers.includes(u.id) &&
        u.id !== currentUser?.id // Garantir que o usuário atual nunca seja desativado
      );

      for (const user of usersToDeactivate) {
        await userService.toggleStatus(user.id, false);
      }
      setShowUserLimitModal(false);
      loadUsers();
      loadSubscriptionData();
    } catch (err) {
      alert('Erro ao atualizar usuários: ' + (err.message || ''));
    } finally {
      setSavingUserLimit(false);
    }
  };

  // Função para fechar o modal e limpar a URL
  const handleCloseUserModal = () => {
    setUserFormOpen(false);
    setSelectedUser(null);
    // Remove userId e openModal da URL
    const params = new URLSearchParams(window.location.search);
    params.delete('userId');
    params.delete('openModal');
    router.replace(`/dashboard/admin/users${params.toString() ? '?' + params.toString() : ''}`);
  };

  return (
    <div className="space-y-6">
      {/* ALERTA DE LIMITE DE USUÁRIOS*/}
      {subscriptionData?.usage &&
        subscriptionData.usage.currentUsers > subscriptionData.usage.userLimit && (
          <div className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-l-4 border-module-admin-primary dark:border-module-admin-primary-dark rounded-lg shadow-sm mb-6">
            <div className="flex items-center justify-between p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full flex items-center justify-center">
                    <User size={20} className="text-module-admin-icon dark:text-module-admin-icon-dark" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-semibold text-red-800 dark:text-red-200 mb-1">
                    Limite de usuários excedido
                  </h3>
                  <div className="text-sm text-red-700 dark:text-red-300">
                    <p className="mb-2">
                      Você está usando <span className="font-semibold">{subscriptionData.usage.currentUsers}</span> de <span className="font-semibold">{subscriptionData.usage.userLimit}</span> usuários permitidos pelo seu plano.
                    </p>
                    <p className="text-xs">
                      Selecione quais usuários permanecerão ativos para estar em conformidade com o limite da sua assinatura.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex-shrink-0 ml-4">
                <button
                  onClick={openUserLimitModal}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-module-admin-primary to-module-admin-accent hover:from-module-admin-primary-dark hover:to-module-admin-accent-dark dark:from-module-admin-primary-dark dark:to-module-admin-accent-dark dark:hover:from-slate-700 dark:hover:to-slate-600 text-white text-sm font-medium rounded-lg shadow-sm transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-module-admin-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                >
                  <UserCog size={16} />
                  <span className="whitespace-nowrap">Gerenciar usuários</span>
                </button>
              </div>
            </div>

            {/* Barra de progresso visual */}
            <div className="px-4 pb-4">
              <div className="flex items-center gap-2 text-xs text-red-600 dark:text-red-400 mb-1">
                <span>Uso atual:</span>
                <span className="font-semibold">
                  {Math.round((subscriptionData.usage.currentUsers / subscriptionData.usage.userLimit) * 100)}%
                </span>
              </div>
              <div className="w-full bg-red-200 dark:bg-red-800/30 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.min((subscriptionData.usage.currentUsers / subscriptionData.usage.userLimit) * 100, 100)}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

      {/* MODAL DE SELEÇÃO DE USUÁRIOS ATIVOS*/}
      {subscriptionData?.usage && (
        <Modal
          isOpen={showUserLimitModal}
          onClose={() => setShowUserLimitModal(false)}
          title="Gerenciar usuários ativos"
          size="lg"
        >
          <div className="p-6 space-y-6">
            {/* Header com informações */}
            <div className="bg-module-admin-bg dark:bg-module-admin-bg-dark rounded-lg p-4 border border-module-admin-border dark:border-module-admin-border-dark">
              <div className="flex items-center gap-3 mb-3">
                <div className="w-8 h-8 bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full flex items-center justify-center">
                  <User size={16} className="text-module-admin-icon dark:text-module-admin-icon-dark" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-module-admin-text dark:text-module-admin-text-dark">
                    Limite da assinatura
                  </h3>
                  <p className="text-xs text-module-admin-muted dark:text-module-admin-muted-dark">
                    Selecione até {subscriptionData?.usage?.userLimit - 1} usuários adicionais (você permanecerá ativo automaticamente)
                  </p>
                </div>
              </div>

              {/* Contador e barra de progresso */}
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-module-admin-muted dark:text-module-admin-muted-dark">
                    Selecionados: {selectedActiveUsers.length + 1} de {subscriptionData?.usage?.userLimit}
                  </span>
                  <span className={`font-semibold ${selectedActiveUsers.length + 1 === subscriptionData?.usage?.userLimit
                      ? 'text-green-600 dark:text-green-400'
                      : selectedActiveUsers.length + 1 > subscriptionData?.usage?.userLimit
                        ? 'text-red-600 dark:text-red-400'
                        : 'text-module-admin-accent dark:text-module-admin-accent-dark'
                    }`}>
                    {selectedActiveUsers.length + 1 === subscriptionData?.usage?.userLimit
                      ? '✓ Completo'
                      : selectedActiveUsers.length + 1 > subscriptionData?.usage?.userLimit
                        ? '⚠ Excedido'
                        : '○ Incompleto'
                    }
                  </span>
                </div>
                <div className="w-full bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${selectedActiveUsers.length + 1 === subscriptionData?.usage?.userLimit
                        ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                        : 'bg-gradient-to-r from-red-500 to-red-600'
                      }`}
                    style={{
                      width: `${Math.min(((selectedActiveUsers.length + 1) / subscriptionData?.usage?.userLimit) * 100, 100)}%`
                    }}
                  ></div>
                </div>
              </div>

              {/* Informação sobre o usuário atual */}
              <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center gap-2 text-sm">
                  <CheckCircle size={16} className="text-green-600 dark:text-green-400 flex-shrink-0" />
                  <span className="text-green-800 dark:text-green-300 font-medium">
                    Você ({currentUser?.fullName}) permanecerá ativo automaticamente
                  </span>
                </div>
              </div>
            </div>

            {/* Lista de usuários */}
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-module-admin-text dark:text-module-admin-text-dark px-1">
                Outros usuários ativos disponíveis ({users.filter(u => u.active && u.id !== currentUser?.id).length})
              </h4>

              <div className="max-h-80 overflow-y-auto border border-module-admin-border dark:border-module-admin-border-dark rounded-lg bg-module-admin-card dark:bg-module-admin-card-dark">
                <div className="divide-y divide-module-admin-border dark:divide-module-admin-border-dark">
                  {users.filter(u => u.active && u.id !== currentUser?.id).map((user, index) => {
                    const isSelected = selectedActiveUsers.includes(user.id);
                    const isDisabled = !isSelected && selectedActiveUsers.length >= (subscriptionData.usage.userLimit - 1);

                    return (
                      <label
                        key={user.id}
                        className={`flex items-center gap-3 p-4 cursor-pointer transition-all duration-150 ${isDisabled
                            ? 'opacity-50 cursor-not-allowed bg-module-admin-secondary/20 dark:bg-module-admin-secondary-dark/20'
                            : isSelected
                              ? 'bg-module-admin-secondary/30 dark:bg-module-admin-secondary-dark/30'
                              : 'bg-module-admin-card dark:bg-module-admin-card-dark hover:bg-module-admin-secondary/20 dark:hover:bg-module-admin-secondary-dark/20'
                          }`}
                      >
                        <div className="flex-shrink-0">
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={(e) => {
                              if (e.target.checked) {
                                if (selectedActiveUsers.length < (subscriptionData.usage.userLimit - 1)) {
                                  setSelectedActiveUsers([...selectedActiveUsers, user.id]);
                                }
                              } else {
                                setSelectedActiveUsers(selectedActiveUsers.filter(id => id !== user.id));
                              }
                            }}
                            disabled={isDisabled}
                            className="w-4 h-4 text-module-admin-primary border-module-admin-border dark:border-module-admin-border-dark rounded focus:ring-module-admin-primary dark:focus:ring-module-admin-primary-dark disabled:opacity-50"
                          />
                        </div>

                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          {/* Avatar do usuário */}
                          <SensitiveAvatar
                            entityType="user"
                            src={user.profileImageFullUrl}
                            alt={user.fullName}
                            size={32}
                            className="flex-shrink-0"
                          />

                          {/* Informações do usuário */}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-module-admin-text dark:text-module-admin-text-dark truncate">
                              <SensitiveFullName
                                entityType="user"
                                value={user.fullName}
                                showToggle={false}
                              />
                            </p>
                            {/* ✅ CORREÇÃO: Email e profissão em linha separada com ícones */}
                            <div className="flex items-center gap-2 text-xs text-module-admin-muted dark:text-module-admin-muted-dark mt-1">
                              <div className="flex items-center gap-1 truncate">
                                <Mail size={12} className="flex-shrink-0" />
                                <span className="truncate">
                                  <SensitiveEmail
                                    entityType="user"
                                    value={user.email}
                                    data={user}
                                    showToggle={true}
                                  />
                                </span>
                              </div>
                              {user.professionObj && (
                                <>
                                  <span>•</span>
                                  <div className="flex items-center gap-1 truncate">
                                    <Briefcase size={12} className="flex-shrink-0" />
                                    <span className="truncate">{user.professionObj.name}</span>
                                  </div>
                                </>
                              )}
                            </div>
                          </div>

                          {/* Badge de seleção */}
                          {isSelected && (
                            <div className="flex-shrink-0">
                              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full">
                                <CheckCircle size={12} />
                                Selecionado
                              </span>
                            </div>
                          )}
                        </div>
                      </label>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Footer com ações */}
            <div className="flex items-center justify-between pt-4 border-t border-module-admin-border dark:border-module-admin-border-dark">
              <div className="text-xs text-module-admin-muted dark:text-module-admin-muted-dark">
                {(selectedActiveUsers.length + 1) !== subscriptionData?.usage?.userLimit && (
                  <span className="flex items-center gap-1">
                    <XCircle size={12} className="text-module-admin-accent" />
                    Selecione até {subscriptionData?.usage?.userLimit - 1} usuários adicionais para continuar
                  </span>
                )}
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={() => setShowUserLimitModal(false)}
                  variant="secondary"
                  className="text-sm"
                >
                  Cancelar
                </Button>
                <Button
                  onClick={handleSaveUserLimit}
                  isLoading={savingUserLimit}
                  disabled={(selectedActiveUsers.length + 1) > subscriptionData?.usage?.userLimit}
                  variant="primary"
                  className="text-sm"
                >
                  {savingUserLimit ? (
                    <>
                      <RefreshCw size={14} className="animate-spin mr-2" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <CheckCircle size={14} className="mr-2" />
                      Salvar seleção
                    </>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      )}

      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <User size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Usuários
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exclusão em massa */}
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir usuários selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || users.length === 0}
            className="text-slate-700 dark:text-slate-300"
          />

          {/* Botão de adicionar */}
          {can("admin.users.create") && (
            <div className="relative group">
              <button
                onClick={() => {
                  if (canAddUsers) {
                    setSelectedUser(null);
                    setUserFormOpen(true);
                  }
                }}
                disabled={!canAddUsers}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-all ${canAddUsers
                  ? "bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800"
                  : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  }`}
              >
                <Plus size={18} />
                <span className="font-medium">Novo Usuário</span>
              </button>

              {/* Tooltip quando o botão está desabilitado */}
              {!canAddUsers && subscriptionData?.usage && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                  Limite de usuários atingido ({subscriptionData.usage.currentUsers}/{subscriptionData.usage.userLimit})
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        description="Gerencie os usuários do sistema. Utilize os filtros abaixo para encontrar usuários específicos."
        moduleColor="admin"
        tutorialSteps={admUsersTutorialSteps}
        tutorialName="admin-users-overview"
        filters={
          <UsersFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
          />
        }
      />

      {/* Tabela de Usuários */}
      <ModuleTable
        moduleColor="admin"
        title="Lista de Usuários"
        headerContent={
          <button
            onClick={() => loadUsers()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-admin-primary dark:hover:text-module-admin-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          { header: 'Usuário', field: 'fullName', width: '20%' },
          { header: 'Email', field: 'email', width: '15%' },
          { header: 'Profissão', field: 'profession', width: '10%', maxWidth: '160px' },
          { header: 'Grupo', field: 'group', width: '10%', maxWidth: '140px' },
          { header: 'Módulos', field: 'modules', width: '10%' },
          { header: 'Função', field: 'role', width: '10%' },
          { header: 'Status', field: 'active', width: '8%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '14%', sortable: false }
        ]}
        data={users}
        // DEBUG: Verificar se users tem dados
        {...(console.log('📋 ModuleTable recebendo users:', users?.length, 'itens') || {})}
        isLoading={isLoading}
        emptyMessage="Nenhum usuário encontrado"
        emptyIcon={<User size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalUsers}
        onPageChange={handlePageChange}
        showPagination={true}
        tableId="admin-users-table"
        enableColumnToggle={true}
        defaultSortField={sortField}
        defaultSortDirection={sortDirection}
        onSort={(field, direction) => {
          console.log('🔄 Ordenando:', { field, direction });
          setSortField(field);
          setSortDirection(direction);
          loadUsers(currentPage, filters, field, direction, itemsPerPage);
        }}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadUsers(1, filters, "fullName", "asc", newItemsPerPage);
        }}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        renderRow={(user, index, moduleColors, visibleColumns) => {
          console.log(`🔄 Renderizando linha ${index}:`, user.fullName);
          return (
          <tr key={user.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="admin"
                  checked={selectedIds.includes(user.id)}
                  onChange={(e) => handleSelectOne(user.id, e.target.checked)}
                  name={`select-user-${user.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('fullName') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <SensitiveAvatar
                    entityType="user"
                    src={user.profileImageFullUrl}
                    alt={user.fullName}
                    size={40}
                    className="flex-shrink-0"
                  />
                  <div className="ml-3 min-w-0 flex-1">
                    {/* ✅ CORREÇÃO: Nome em uma linha */}
                    <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      <SensitiveFullName
                        entityType="user"
                        value={user.fullName}
                        showToggle={false}
                      />
                    </div>
                    {/* ✅ CORREÇÃO: Login em linha separada com ícone */}
                    <div className="flex items-center gap-1 text-xs text-neutral-500 dark:text-neutral-400 truncate mt-1">
                      <User size={12} className="flex-shrink-0" />
                      <span className="truncate">{user.login}</span>
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('email') && (
              <td className="px-4 py-4">
                {/* ✅ CORREÇÃO: Email com ícone */}
                <div className="flex items-center gap-1 text-sm text-neutral-600 dark:text-neutral-300 truncate">
                  <Mail size={14} className="flex-shrink-0 text-neutral-400 dark:text-neutral-500" />
                  <span className="truncate">
                    <SensitiveEmail
                      entityType="user"
                      value={user.email}
                      data={user}
                      showToggle={true}
                    />
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('profession') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 max-w-[160px] truncate">
                {user.professionObj ? (
                  <div className="flex items-center gap-1">
                    <Briefcase size={14} className="text-neutral-500 dark:text-neutral-400 flex-shrink-0" />
                    <span className="truncate">
                      {user.professionObj.name}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 italic">Sem profissão</span>
                )}
              </td>
            )}

            {visibleColumns.includes('group') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 max-w-[140px] truncate">
                {user.professionObj && user.professionObj.group ? (
                  <div className="flex items-center gap-1">
                    <User size={14} className="text-neutral-500 dark:text-neutral-400 flex-shrink-0" />
                    <span className="truncate">{user.professionObj.group.name}</span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 italic">Sem grupo</span>
                )}
              </td>
            )}

            {visibleColumns.includes('modules') && (
              <td className="px-4 py-4">
                <div className="flex flex-wrap gap-1">
                  {user.modules.filter(module => activeModules.includes(module)).slice(0, 2).map((module) => (
                    <span
                      key={module}
                      className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300"
                    >
                      {MODULE_LABELS[module]}
                    </span>
                  ))}
                  {user.modules.filter(module => activeModules.includes(module)).length > 2 && (
                    <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300">
                      +{user.modules.filter(module => activeModules.includes(module)).length - 2}
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('role') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 ${user.role === "SYSTEM_ADMIN"
                    ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    : user.role === "COMPANY_ADMIN"
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400"
                      : "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                    }`}
                >
                  <UserCog size={12} className="flex-shrink-0" />
                  <span className="truncate">
                    {user.role === "SYSTEM_ADMIN"
                      ? "Admin Sistema"
                      : user.role === "COMPANY_ADMIN"
                        ? "Admin Empresa"
                        : "Funcionário"}
                  </span>
                </span>
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${user.active
                    ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                    : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    }`}
                >
                  {user.active ? (
                    <>
                      <CheckCircle size={12} className="flex-shrink-0" />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} className="flex-shrink-0" />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right text-sm font-medium">
                <div className="flex justify-end gap-1">
                  <Protected permission="admin.users.edit">
                    <button
                      onClick={() => handleEditUser(user)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                      id="edicaoUsuario"
                      title="Editar"
                    >
                      <Edit size={16} />
                    </button>
                  </Protected>

                  <Protected permission="admin.permissions.manage">
                    <button
                      onClick={() => handleEditModules(user)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                      id="gerenciarModulo"
                      title="Gerenciar módulos"
                    >
                      <Shield size={16} />
                    </button>
                  </Protected>

                  <Protected permission="admin.permissions.manage">
                    <button
                      onClick={() => handleManagePermissions(user)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors"
                      id="gerenciarPermissoes"
                      title="Gerenciar permissões"
                    >
                      <Lock size={16} />
                    </button>
                  </Protected>

                  {/* Não permitir gerenciar funções de SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                  {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                    <Protected permission="admin.users.edit">
                      <button
                        onClick={() => handleManageRole(user)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors"
                        id="gerenciarFuncao"
                        title="Alterar função"
                      >
                        <UserCog size={16} />
                      </button>
                    </Protected>
                  )}

                  {/* Não permitir alterar status de SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                  {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                    <Protected permission="admin.users.edit">
                      <button
                        onClick={() => handleToggleStatus(user)}
                        className={`p-1 transition-colors ${user.active
                          ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                          : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                          }`}
                        id="desativarUsuario"
                        title={user.active ? "Desativar" : "Ativar"}
                      >
                        <Power size={16} />
                      </button>
                    </Protected>
                  )}

                  {/* Não permitir excluir SYSTEM_ADMIN se o usuário não for SYSTEM_ADMIN */}
                  {(user.role !== "SYSTEM_ADMIN" || isSystemAdmin) && (
                    <Protected permission="admin.users.delete">
                      <button
                        onClick={() => handleDeleteUser(user)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                        id="excluirUsuario"
                        title="Excluir"
                      >
                        <Trash size={16} />
                      </button>
                    </Protected>
                  )}
                </div>
              </td>
            )}
          </tr>
        );
        }}
      />

      {/* Modais */}
      <UserFormModal
        isOpen={userFormOpen}
        onClose={handleCloseUserModal}
        user={selectedUser}
        onSuccess={() => {
          handleCloseUserModal();
          loadUsers();
          loadSubscriptionData(); // Recarregar dados da subscription
        }}
        currentUser={currentUser}
      />

      <ModulesModal
        isOpen={modulesModalOpen}
        onClose={() => setModulesModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setModulesModalOpen(false);
          loadUsers();
        }}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        moduleColor="admin"
      />

      <PermissionsModal
        isOpen={permissionsModalOpen}
        onClose={() => setPermissionsModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setPermissionsModalOpen(false);
          loadUsers();
        }}
      />
      <RoleModal
        isOpen={roleModalOpen}
        onClose={() => setRoleModalOpen(false)}
        user={selectedUser}
        onSuccess={() => {
          setRoleModalOpen(false);
          loadUsers();
        }}
      />
    </div>
  );
};

export default UsersPage;