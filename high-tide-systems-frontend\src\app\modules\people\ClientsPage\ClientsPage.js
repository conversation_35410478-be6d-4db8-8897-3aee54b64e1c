"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import ModuleHeader from "@/components/ui/ModuleHeader";
import { ModuleTable, ModuleCheckbox } from "@/components/ui";
import ExportMenu from "@/components/ui/ExportMenu";
import {
  Filter,
  Edit,
  Trash,
  Power,
  CheckCircle,
  XCircle,
  Mail,
  Users,
  UserPlus,
  Eye,
  Plus,
  RefreshCw,
} from "lucide-react";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ClientFormModal from "@/components/people/ClientFormModal";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useRouter, useSearchParams } from "next/navigation";
import ShareButton from "@/components/common/ShareButton";
import { ClientsFilters } from "@/components/people/ClientsFilters";
import { 
  SensitiveEmail, 
  SensitiveFullName 
} from '@/components/ui/SensitiveField';
import { useDataPrivacy } from '@/hooks/useDataPrivacy';

// Tutorial steps para a página de clientes
const clientsTutorialSteps = [
  {
    title: "Clientes",
    content: "Esta tela permite gerenciar o cadastro de clientes no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Cliente",
    content: "Clique aqui para adicionar um novo cliente.",
    selector: "button:has(span:contains('Novo Cliente'))",
    position: "left"
  },
  {
    title: "Filtrar Clientes",
    content: "Use esta barra de pesquisa para encontrar clientes específicos pelo login ou email.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtros Avançados",
    content: "Clique no botão Filtros para acessar opções avançadas de filtragem.",
    selector: "button:has(span:contains('Filtros'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de clientes em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Clientes",
    content: "Visualize, edite, ative/desative ou exclua clientes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const ClientsPage = () => {
  const { user: currentUser } = useAuth();
  const { applyListPrivacyMasks } = useDataPrivacy();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [clients, setClients] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalClients, setTotalClients] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [clientFormOpen, setClientFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  // Funções para seleção múltipla
  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(clients.map(c => c.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };

  // Estado para os filtros
  const [filters, setFilters] = useState({
    search: "",
    companies: [],
    status: "",
    clients: [],
    dateFrom: "",
    dateTo: ""
  });

  // Estado para controlar itens por página
  const [itemsPerPage, setItemsPerPage] = useState(10);

  const loadClients = async (
    page = currentPage,
    filtersToUse = filters,
    sortField = 'fullName',
    sortDirection = 'asc',
    perPage = itemsPerPage
  ) => {
    console.log('loadClients chamado com parâmetros:', {
      page,
      filters: filtersToUse,
      sortField,
      sortDirection,
      perPage
    });
    
    setIsLoading(true);
    try {
      const pageNumber = parseInt(page, 10);
      setCurrentPage(pageNumber);

      const requestParams = {
        page: pageNumber,
        limit: perPage,
        search: filtersToUse.search || undefined,
        clientIds: filtersToUse.clients.length > 0 ? filtersToUse.clients : undefined,
        active: filtersToUse.status === "" ? undefined : filtersToUse.status === "active",
        companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,
        dateFrom: filtersToUse.dateFrom || undefined,
        dateTo: filtersToUse.dateTo || undefined,
        sortField,
        sortDirection,
        include_persons: 'true',
      };

      console.log('ClientsPage - Parâmetros enviados para API:', requestParams);

      const response = await clientsService.getClients(requestParams);

      const clientsData = response?.clients || response?.data || [];

      console.log("ClientsPage - Resposta da API:", response);
      console.log("ClientsPage - Dados dos clientes (primeiros 3):", clientsData.slice(0, 3).map(c => ({ id: c.id, fullName: c.fullName, login: c.login })));

      if (!Array.isArray(clientsData)) {
        setClients([]);
      } else {
        // Aplicar máscaras de privacidade aos dados dos clientes
        const clientsWithPrivacy = applyListPrivacyMasks('client', clientsData);
        console.log('🔒 Máscaras de privacidade aplicadas aos clientes');
        setClients(clientsWithPrivacy);
      }

      setTotalClients(response?.total || 0);
      setTotalPages(response?.pages || 1);
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      setClients([]);
      setTotalClients(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    console.log('ClientsPage - Carregamento inicial com ordenação por fullName asc');
    loadClients(1, filters, 'fullName', 'asc');
  }, []);

  // Efeito para abrir o modal automaticamente baseado nos parâmetros da URL
  useEffect(() => {
    const clientId = searchParams.get('clientId');
    const openModal = searchParams.get('openModal');

    if (clientId && openModal === 'true') {
      const client = clients.find(c => c.id === clientId);
      if (client) {
        setSelectedClient(client);
        setClientFormOpen(true);
      } else {
        clientsService.getClient(clientId).then(clientData => {
          setSelectedClient(clientData);
          setClientFormOpen(true);
        }).catch(error => {
          console.error("Erro ao buscar cliente:", error);
        });
      }
    }
  }, [searchParams, clients]);

  const handleSearch = (searchFilters) => {
    loadClients(1, searchFilters, 'fullName', 'asc');
  };

  const handlePageChange = (page) => {
    loadClients(page, filters, 'fullName', 'asc');
  };

  const handleEditClient = (client) => {
    setSelectedClient(client);
    setClientFormOpen(true);
  };

  const handleToggleStatus = (client) => {
    setSelectedClient(client);
    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
      ? client.clientPersons[0].person.fullName
      : client.login;

    setActionToConfirm({
      type: "toggle-status",
      message: `${client.active ? "Desativar" : "Ativar"} o cliente ${clientName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteClient = (client) => {
    setSelectedClient(client);
    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
      ? client.clientPersons[0].person.fullName
      : client.login;

    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente o cliente ${clientName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      await clientsService.exportClients({
        search: filters.search || undefined,
        clientIds: filters.clients.length > 0 ? filters.clients : undefined,
        active: filters.status === "" ? undefined : filters.status === "active",
        companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,
        dateFrom: filters.dateFrom || undefined,
        dateTo: filters.dateTo || undefined,
        include_persons: 'true'
      }, format);
    } catch (error) {
      console.error("Erro ao exportar clientes:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await clientsService.toggleClientStatus(selectedClient.id);
        loadClients(currentPage, filters, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao alterar status do cliente:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await clientsService.deleteClient(selectedClient.id);
        loadClients(currentPage, filters, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao excluir cliente:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  const handleCloseClientModal = () => {
    setClientFormOpen(false);
    setSelectedClient(null);
    const params = new URLSearchParams(window.location.search);
    params.delete('clientId');
    params.delete('openModal');
    router.replace(`/dashboard/people/clients${params.toString() ? '?' + params.toString() : ''}`);
  };

  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <UserPlus size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Clientes
        </h1>

        <div className="flex items-center gap-2">
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir clientes selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || clients.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          <button
            onClick={() => {
              setSelectedClient(null);
              setClientFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Cliente</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie o cadastro de clientes no sistema. Utilize os filtros abaixo para encontrar clientes específicos."
        tutorialSteps={clientsTutorialSteps}
        tutorialName="clients-overview"
        moduleColor="people"
        filters={
          <ClientsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
          />
        }
      />

      {/* Tabela de Clientes */}
      <ModuleTable
        moduleColor="people"
        title="Lista de Clientes"
        headerContent={
          <button
            onClick={() => loadClients()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          { header: 'Cliente', field: 'fullName', width: '20%' },
          { header: 'Email', field: 'email', width: '20%' },
          { header: 'Pessoas', field: 'persons', width: '15%' },
          { header: 'Status', field: 'active', width: '15%' },
          { header: 'Cadastro', field: 'createdAt', width: '15%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={clients}
        isLoading={isLoading}
        emptyMessage="Nenhum cliente encontrado"
        emptyIcon={<Users size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalClients}
        onPageChange={handlePageChange}
        showPagination={true}
        tableId="people-clients-table"
        enableColumnToggle={true}
        defaultSortField="fullName"
        defaultSortDirection="asc"
        onSort={(field, direction) => {
          console.log('ClientsPage - Ordenação solicitada:', { field, direction });
          loadClients(currentPage, filters, field, direction);
        }}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadClients(1, filters, 'fullName', 'asc', newItemsPerPage);
        }}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        renderRow={(client, _, moduleColors, visibleColumns) => (
          <tr key={client.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="people"
                  checked={selectedIds.includes(client.id)}
                  onChange={(e) => handleSelectOne(client.id, e.target.checked)}
                  name={`select-client-${client.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('fullName') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden">
                    {(client.fullName || client.login).charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-3 min-w-0">
                    <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      <SensitiveFullName
                        entityType="client"
                        value={client.fullName || client.login}
                        data={client}
                        showToggle={true}
                      />
                    </div>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                      {client.login}
                    </p>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('email') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="truncate">
                    <SensitiveEmail
                      entityType="client"
                      value={client.email}
                      data={client}
                      showToggle={true}
                    />
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('persons') && (
              <td className="px-4 py-4">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="text-neutral-600 dark:text-neutral-300 text-sm font-medium">
                    {client.clientPersons?.length || 0}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${client.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    }`}
                >
                  {client.active ? (
                    <>
                      <CheckCircle size={12} className="flex-shrink-0" />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} className="flex-shrink-0" />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                {formatDate(client.createdAt)}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right text-sm font-medium">
                <div className="flex justify-end gap-1">
                  <ShareButton
                    itemType="client"
                    itemId={client.id}
                    itemTitle={client.fullName}
                    size="xs"
                    variant="ghost"
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                  />
                  
                  <button
                    onClick={() => router.push(`/dashboard/people/clients/${client.id}`)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Ver detalhes"
                  >
                    <Eye size={16} />
                  </button>

                  <button
                    onClick={() => handleEditClient(client)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>

                  <button
                    onClick={() => handleToggleStatus(client)}
                    className={`p-1 transition-colors ${client.active
                        ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                        : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                      }`}
                    title={client.active ? "Desativar" : "Ativar"}
                  >
                    <Power size={16} />
                  </button>

                  <button
                    onClick={() => handleDeleteClient(client)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        moduleColor="people"
      />

      {clientFormOpen && (
        <ClientFormModal
          isOpen={clientFormOpen}
          onClose={handleCloseClientModal}
          client={selectedClient}
          onSuccess={() => {
            handleCloseClientModal();
            loadClients(1, filters, 'fullName', 'asc');
          }}
        />
      )}

      <TutorialManager />
    </div>
  );
};

export default ClientsPage;