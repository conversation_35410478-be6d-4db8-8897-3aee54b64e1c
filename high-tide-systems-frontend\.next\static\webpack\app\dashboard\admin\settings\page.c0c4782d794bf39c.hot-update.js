"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/settings/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/settings/SettingsPage.js":
/*!********************************************************!*\
  !*** ./src/app/modules/admin/settings/SettingsPage.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_settings_BranchesTab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/settings/BranchesTab */ \"(app-pages-browser)/./src/components/settings/BranchesTab.js\");\n/* harmony import */ var _components_settings_CompanyManagementTab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/settings/CompanyManagementTab */ \"(app-pages-browser)/./src/components/settings/CompanyManagementTab.js\");\n/* harmony import */ var _components_settings_EmailSettingsTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/settings/EmailSettingsTab */ \"(app-pages-browser)/./src/components/settings/EmailSettingsTab.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_PreferencesPanel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/PreferencesPanel */ \"(app-pages-browser)/./src/components/admin/PreferencesPanel.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Componente SecurityTab\nconst SecurityTab = (param)=>{\n    let { isSystemAdmin, isCompanyAdmin } = param;\n    _s();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [securitySettings, setSecuritySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        requireUppercase: true,\n        requireNumber: true,\n        requireSpecialChar: false,\n        minPasswordLength: 8,\n        passwordExpirationDays: 90,\n        limitLoginAttempts: true,\n        maxLoginAttempts: 5,\n        enforceIpTracking: true,\n        enforceSessionTimeout: true,\n        sessionTimeoutMinutes: 30\n    });\n    const [originalSettings, setOriginalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [securityLogs, setSecurityLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingSettings, setIsLoadingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingLogs, setIsLoadingLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalLogs, setTotalLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Carregar configurações de segurança\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SecurityTab.useEffect\": ()=>{\n            loadSecuritySettings();\n            loadSecurityLogs();\n        }\n    }[\"SecurityTab.useEffect\"], []);\n    const loadSecuritySettings = async ()=>{\n        try {\n            var _currentUser;\n            // Para system_admin, verificar se há empresa selecionada\n            const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');\n            if (((_currentUser = currentUser) === null || _currentUser === void 0 ? void 0 : _currentUser.role) === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {\n                // Se system_admin não tem empresa selecionada, não carregar configurações\n                setSecuritySettings({\n                    requireUppercase: true,\n                    requireNumber: true,\n                    requireSpecialChar: false,\n                    minPasswordLength: 8,\n                    passwordExpirationDays: 90,\n                    limitLoginAttempts: true,\n                    maxLoginAttempts: 5,\n                    enforceIpTracking: true,\n                    enforceSessionTimeout: true,\n                    sessionTimeoutMinutes: 30\n                });\n                setOriginalSettings({});\n                return;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get('/settings/security');\n            if (response.data.success) {\n                setSecuritySettings(response.data.data);\n                setOriginalSettings(response.data.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar configurações de segurança:', error);\n            // Em caso de erro, usar configurações padrão\n            setSecuritySettings({\n                requireUppercase: true,\n                requireNumber: true,\n                requireSpecialChar: false,\n                minPasswordLength: 8,\n                passwordExpirationDays: 90,\n                limitLoginAttempts: true,\n                maxLoginAttempts: 5,\n                enforceIpTracking: true,\n                enforceSessionTimeout: true,\n                sessionTimeoutMinutes: 30\n            });\n            setOriginalSettings({});\n        } finally{\n            setIsLoadingSettings(false);\n        }\n    };\n    const loadSecurityLogs = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/settings/security/logs?page=\".concat(page, \"&limit=\").concat(perPage));\n            if (response.data.success) {\n                setSecurityLogs(response.data.data.logs);\n                setTotalLogs(response.data.data.pagination.total);\n                setTotalPages(response.data.data.pagination.pages);\n                setCurrentPage(response.data.data.pagination.page);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar logs de segurança:', error);\n            setSecurityLogs([]);\n        } finally{\n            setIsLoadingLogs(false);\n        }\n    };\n    const saveSecuritySettings = async ()=>{\n        try {\n            var _currentUser;\n            setIsLoadingSettings(true);\n            // Para system_admin, verificar se há empresa selecionada\n            const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');\n            if (((_currentUser = currentUser) === null || _currentUser === void 0 ? void 0 : _currentUser.role) === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {\n                toast_error('Selecione uma empresa para salvar as configurações de segurança');\n                return;\n            }\n            // Identificar apenas os campos que mudaram\n            const changedSettings = {};\n            Object.keys(securitySettings).forEach((key)=>{\n                if (securitySettings[key] !== originalSettings[key]) {\n                    changedSettings[key] = securitySettings[key];\n                }\n            });\n            // Se nada mudou, não fazer requisição\n            if (Object.keys(changedSettings).length === 0) {\n                toast_success('Nenhuma alteração detectada');\n                return;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.put('/settings/security', changedSettings);\n            if (response.data.success) {\n                toast_success('Configurações de segurança salvas com sucesso!');\n                setOriginalSettings(securitySettings);\n                loadSecurityLogs(1, itemsPerPage);\n            }\n        } catch (error) {\n            console.error('Erro ao salvar configurações:', error);\n            toast_error('Erro ao salvar configurações de segurança');\n        } finally{\n            setIsLoadingSettings(false);\n        }\n    };\n    const handleSettingChange = (key, value)=>{\n        setSecuritySettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Configura\\xe7\\xf5es de Seguran\\xe7a\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        moduleColor: \"admin\",\n                                        children: \"Pol\\xedtica de Senhas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireUppercase\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireUppercase,\n                                                        onChange: (e)=>handleSettingChange('requireUppercase', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireUppercase\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos uma letra mai\\xfascula\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireNumber\",\n                                                        className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireNumber,\n                                                        onChange: (e)=>handleSettingChange('requireNumber', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireNumber\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos um n\\xfamero\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireSpecialChar\",\n                                                        className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireSpecialChar,\n                                                        onChange: (e)=>handleSettingChange('requireSpecialChar', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireSpecialChar\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos um caractere especial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"minPasswordLength\",\n                                        moduleColor: \"admin\",\n                                        children: \"Tamanho m\\xednimo da senha\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"minPasswordLength\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"6\",\n                                        max: \"32\",\n                                        value: securitySettings.minPasswordLength,\n                                        onChange: (e)=>handleSettingChange('minPasswordLength', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"passwordExpiration\",\n                                        moduleColor: \"admin\",\n                                        children: \"Expira\\xe7\\xe3o de senha (dias)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"passwordExpiration\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"365\",\n                                        value: securitySettings.passwordExpirationDays,\n                                        onChange: (e)=>handleSettingChange('passwordExpirationDays', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-xs text-neutral-500 dark:text-neutral-400\",\n                                        children: \"0 = Sem expira\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        moduleColor: \"admin\",\n                                        children: \"Seguran\\xe7a de Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"limitLoginAttempts\",\n                                                        className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.limitLoginAttempts,\n                                                        onChange: (e)=>handleSettingChange('limitLoginAttempts', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"limitLoginAttempts\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Limitar tentativas de login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"enforceIpTracking\",\n                                                        className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.enforceIpTracking,\n                                                        onChange: (e)=>handleSettingChange('enforceIpTracking', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"enforceIpTracking\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Rastrear IPs de login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"enforceSessionTimeout\",\n                                                        className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.enforceSessionTimeout,\n                                                        onChange: (e)=>handleSettingChange('enforceSessionTimeout', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"enforceSessionTimeout\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Encerrar sess\\xf5es inativas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"maxLoginAttempts\",\n                                        moduleColor: \"admin\",\n                                        children: \"M\\xe1ximo de tentativas de login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"maxLoginAttempts\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"3\",\n                                        max: \"10\",\n                                        value: securitySettings.maxLoginAttempts,\n                                        onChange: (e)=>handleSettingChange('maxLoginAttempts', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"sessionTimeout\",\n                                        moduleColor: \"admin\",\n                                        children: \"Tempo de inatividade at\\xe9 logout (minutos)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"sessionTimeout\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"5\",\n                                        max: \"240\",\n                                        value: securitySettings.sessionTimeoutMinutes,\n                                        onChange: (e)=>handleSettingChange('sessionTimeoutMinutes', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: saveSecuritySettings,\n                    className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                    disabled: !isSystemAdmin && !isCompanyAdmin || isLoadingSettings,\n                    children: [\n                        isLoadingSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Salvar Configura\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 343,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Logs de Seguran\\xe7a\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoadingLogs ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-primary-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTable, {\n                        moduleColor: \"admin\",\n                        title: \"Logs de Seguran\\xe7a\",\n                        columns: [\n                            {\n                                header: 'Data/Hora',\n                                field: 'createdAt',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Usuário',\n                                field: 'user',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Ação',\n                                field: 'action',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Detalhes',\n                                field: 'details',\n                                width: '35%'\n                            },\n                            {\n                                header: 'Status',\n                                field: 'status',\n                                width: '10%'\n                            },\n                            {\n                                header: 'IP',\n                                field: 'ipAddress',\n                                width: '10%'\n                            }\n                        ],\n                        data: securityLogs,\n                        isLoading: isLoadingLogs,\n                        emptyMessage: \"Nenhum log de seguran\\xe7a encontrado\",\n                        emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 384,\n                            columnNumber: 24\n                        }, void 0),\n                        currentPage: currentPage,\n                        totalPages: totalPages,\n                        totalItems: totalLogs,\n                        onPageChange: (page)=>loadSecurityLogs(page, itemsPerPage),\n                        showPagination: true,\n                        tableId: \"security-logs-table\",\n                        enableColumnToggle: true,\n                        itemsPerPage: itemsPerPage,\n                        onItemsPerPageChange: (newItemsPerPage)=>{\n                            setItemsPerPage(newItemsPerPage);\n                            loadSecurityLogs(1, newItemsPerPage);\n                        },\n                        renderRow: (log, index, moduleColors, visibleColumns)=>{\n                            var _log_user, _log_details, _log_details1, _log_details2, _log_details3;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: moduleColors.hoverBg,\n                                children: [\n                                    visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: new Date(log.createdAt).toLocaleString('pt-BR')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('user') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-800 dark:text-neutral-100\",\n                                        children: ((_log_user = log.user) === null || _log_user === void 0 ? void 0 : _log_user.fullName) || (((_log_details = log.details) === null || _log_details === void 0 ? void 0 : _log_details.identifier) ? log.details.identifier : 'Sistema')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('action') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.action === 'LOGIN_ATTEMPT' ? 'Tentativa de Login' : log.action === 'SECURITY_SETTINGS_UPDATED' ? 'Configurações Alteradas' : log.action === 'ACCOUNT_LOCKED' ? 'Conta Bloqueada' : log.action === 'PASSWORD_EXPIRED' ? 'Senha Expirada' : log.action\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('details') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.action === 'SECURITY_SETTINGS_UPDATED' && ((_log_details1 = log.details) === null || _log_details1 === void 0 ? void 0 : _log_details1.description) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Altera\\xe7\\xf5es:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: log.details.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                log.details.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-neutral-400 dark:text-neutral-500\",\n                                                    children: [\n                                                        \"Local: \",\n                                                        log.details.source\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 421,\n                                            columnNumber: 23\n                                        }, void 0) : log.action === 'SECURITY_SETTINGS_UPDATED' && ((_log_details2 = log.details) === null || _log_details2 === void 0 ? void 0 : _log_details2.updatedFields) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Configura\\xe7\\xf5es de Seguran\\xe7a:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: log.details.updatedFields.map((field)=>{\n                                                        const fieldNames = {\n                                                            requireUppercase: 'Exigir letra maiúscula',\n                                                            requireNumber: 'Exigir número',\n                                                            requireSpecialChar: 'Exigir caractere especial',\n                                                            minPasswordLength: 'Tamanho mínimo da senha',\n                                                            passwordExpirationDays: 'Expiração de senha',\n                                                            limitLoginAttempts: 'Limitar tentativas de login',\n                                                            maxLoginAttempts: 'Máximo de tentativas',\n                                                            enforceIpTracking: 'Rastrear IPs',\n                                                            enforceSessionTimeout: 'Timeout de sessão',\n                                                            sessionTimeoutMinutes: 'Tempo de inatividade',\n                                                            accountLockoutDuration: 'Duração do bloqueio'\n                                                        };\n                                                        return fieldNames[field] || field;\n                                                    }).join(', ')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 433,\n                                            columnNumber: 23\n                                        }, void 0) : log.action === 'LOGIN_ATTEMPT' && ((_log_details3 = log.details) === null || _log_details3 === void 0 ? void 0 : _log_details3.identifier) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                \"Email: \",\n                                                log.details.identifier\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 455,\n                                            columnNumber: 23\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-neutral-400\",\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 457,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 419,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full \".concat(log.status === 'SUCCESS' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : log.status === 'FAILURE' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'),\n                                            children: log.status === 'SUCCESS' ? 'Sucesso' : log.status === 'FAILURE' ? 'Falhou' : log.status === 'WARNING' ? 'Aviso' : log.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 463,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 462,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('ipAddress') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.ipAddress || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 476,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, log.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, void 0);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecurityTab, \"aLH9YwC6IlYdjYFVBWnRi8BOsaI=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = SecurityTab;\nconst SettingsPage = ()=>{\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        siteName: \"High Tide\",\n        siteUrl: \"https://hightide.site\",\n        adminEmail: \"<EMAIL>\",\n        allowRegistration: true,\n        defaultTimeZone: \"America/Sao_Paulo\",\n        dateFormat: \"DD/MM/YYYY\",\n        timeFormat: \"24h\",\n        backupEnabled: true,\n        backupFrequency: \"daily\",\n        backupTime: \"01:00\",\n        maxFileSize: 5,\n        allowedFileTypes: \"jpg,png,pdf,doc,docx,xls,xlsx\",\n        logRetention: 90\n    });\n    // Define user role constants\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"SYSTEM_ADMIN\";\n    const isCompanyAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"COMPANY_ADMIN\";\n    // Determine which tabs are available based on user role\n    const availableTabs = {\n        general: isSystemAdmin || isCompanyAdmin,\n        companies: isSystemAdmin || isCompanyAdmin,\n        branches: isSystemAdmin || isCompanyAdmin,\n        email: isSystemAdmin || isCompanyAdmin,\n        backup: isSystemAdmin,\n        security: isSystemAdmin || isCompanyAdmin,\n        preferencias: true\n    };\n    // Load data for the active tab\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            // Auto-select the first available tab if the current active tab is not available\n            const tabKeys = Object.keys(availableTabs);\n            const firstAvailableTab = tabKeys.find({\n                \"SettingsPage.useEffect.firstAvailableTab\": (tab)=>availableTabs[tab]\n            }[\"SettingsPage.useEffect.firstAvailableTab\"]);\n            if (!availableTabs[activeTab] && firstAvailableTab) {\n                setActiveTab(firstAvailableTab);\n                return;\n            }\n            if (activeTab === \"general\") {\n                loadGeneralSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        activeTab,\n        user\n    ]);\n    // Load general settings\n    const loadGeneralSettings = async ()=>{\n        setIsLoading(true);\n        try {\n            // This would typically be an API call\n            // For now, we'll just simulate loading with a timeout\n            setTimeout(()=>{\n                setGeneralSettings({\n                    siteName: \"High Tide\",\n                    siteUrl: \"https://dentrodascasinhas.com.br\",\n                    adminEmail: \"<EMAIL>\",\n                    allowRegistration: true,\n                    defaultTimeZone: \"America/Sao_Paulo\",\n                    dateFormat: \"DD/MM/YYYY\",\n                    timeFormat: \"24h\",\n                    backupEnabled: true,\n                    backupFrequency: \"daily\",\n                    backupTime: \"01:00\",\n                    maxFileSize: 5,\n                    allowedFileTypes: \"jpg,png,pdf,doc,docx,xls,xlsx\",\n                    logRetention: 90\n                });\n                setIsLoading(false);\n            }, 500);\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n            setError(\"Falha ao carregar configurações\");\n            setIsLoading(false);\n        }\n    };\n    // Function to save general settings\n    const saveGeneralSettings = async ()=>{\n        setIsLoading(true);\n        try {\n            // This would be an API call in production\n            // For now, we just simulate with a timeout\n            setTimeout(()=>{\n                alert(\"Configurações salvas com sucesso!\");\n                setIsLoading(false);\n            }, 500);\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            setError(\"Falha ao salvar configurações\");\n            setIsLoading(false);\n        }\n    };\n    // Get tab label based on user role\n    const getTabLabel = (tabKey)=>{\n        const labels = {\n            general: \"Geral\",\n            companies: isSystemAdmin ? \"Empresas\" : \"Minha Empresa\",\n            branches: \"Unidades\",\n            email: \"Email\",\n            backup: \"Backup\",\n            security: \"Segurança\",\n            preferencias: \"Preferências\"\n        };\n        return labels[tabKey] || tabKey;\n    };\n    // Get tab icon\n    const getTabIcon = (tabKey)=>{\n        const icons = {\n            general: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 609,\n                columnNumber: 16\n            }, undefined),\n            companies: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 610,\n                columnNumber: 18\n            }, undefined),\n            branches: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 611,\n                columnNumber: 17\n            }, undefined),\n            email: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 612,\n                columnNumber: 14\n            }, undefined),\n            backup: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 613,\n                columnNumber: 15\n            }, undefined),\n            security: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 614,\n                columnNumber: 17\n            }, undefined),\n            preferencias: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 615,\n                columnNumber: 21\n            }, undefined)\n        };\n        return icons[tabKey] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n            lineNumber: 618,\n            columnNumber: 29\n        }, undefined);\n    };\n    // Preparar as tabs para o componente ModuleTabs\n    const tabsConfig = Object.keys(availableTabs).filter((tabKey)=>availableTabs[tabKey]).map((tabKey)=>({\n            id: tabKey,\n            label: getTabLabel(tabKey),\n            icon: getTabIcon(tabKey)\n        }));\n    // Import tutorial steps from tutorialMapping\n    const settingsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SettingsPage.useMemo[settingsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/settings'] || [];\n        }\n    }[\"SettingsPage.useMemo[settingsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-800 dark:text-white flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 24,\n                            className: \"mr-2 text-gray-600 dark:text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Configura\\xe7\\xf5es do Sistema\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 641,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 640,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl border border-module-admin-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTabs, {\n                    tabs: tabsConfig,\n                    activeTab: activeTab,\n                    onTabChange: setActiveTab,\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 663,\n                            columnNumber: 13\n                        }, undefined),\n                        isLoading && activeTab !== \"companies\" && activeTab !== \"branches\" && activeTab !== \"email\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                activeTab === \"general\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Configura\\xe7\\xf5es B\\xe1sicas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleFormGroup, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                                    htmlFor: \"siteName\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    children: \"Nome do Site\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                                    id: \"siteName\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    type: \"text\",\n                                                                                    value: generalSettings.siteName,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            siteName: e.target.value\n                                                                                        }),\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleFormGroup, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                                    htmlFor: \"siteUrl\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    children: \"URL do Site\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 713,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                                    id: \"siteUrl\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    type: \"url\",\n                                                                                    value: generalSettings.siteUrl,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            siteUrl: e.target.value\n                                                                                        }),\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"adminEmail\",\n                                                                                    children: \"Email do Administrador\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"adminEmail\",\n                                                                                    type: \"email\",\n                                                                                    value: generalSettings.adminEmail,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            adminEmail: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"allowRegistration\",\n                                                                                    checked: generalSettings.allowRegistration,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            allowRegistration: e.target.checked\n                                                                                        }),\n                                                                                    className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"allowRegistration\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Permitir cadastro de novos usu\\xe1rios\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Localiza\\xe7\\xe3o e Formato\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"defaultTimeZone\",\n                                                                                    children: \"Fuso Hor\\xe1rio Padr\\xe3o\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"defaultTimeZone\",\n                                                                                    value: generalSettings.defaultTimeZone,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            defaultTimeZone: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Sao_Paulo\",\n                                                                                            children: \"Am\\xe9rica/S\\xe3o Paulo\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 806,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Recife\",\n                                                                                            children: \"Am\\xe9rica/Recife\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 809,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Manaus\",\n                                                                                            children: \"Am\\xe9rica/Manaus\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 812,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Belem\",\n                                                                                            children: \"Am\\xe9rica/Bel\\xe9m\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 815,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 788,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"dateFormat\",\n                                                                                    children: \"Formato de Data\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 822,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"dateFormat\",\n                                                                                    value: generalSettings.dateFormat,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            dateFormat: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"DD/MM/YYYY\",\n                                                                                            children: \"DD/MM/YYYY\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 839,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"MM/DD/YYYY\",\n                                                                                            children: \"MM/DD/YYYY\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 840,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"YYYY-MM-DD\",\n                                                                                            children: \"YYYY-MM-DD\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 841,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"timeFormat\",\n                                                                                    children: \"Formato de Hora\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"timeFormat\",\n                                                                                    value: generalSettings.timeFormat,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            timeFormat: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"24h\",\n                                                                                            children: \"24h (ex: 14:30)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 863,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"12h\",\n                                                                                            children: \"12h (ex: 2:30 PM)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 864,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 852,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"Backup\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"backupEnabled\",\n                                                                                    checked: generalSettings.backupEnabled,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            backupEnabled: e.target.checked\n                                                                                        }),\n                                                                                    className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"backupEnabled\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Ativar backup autom\\xe1tico\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 893,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 880,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        generalSettings.backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                            htmlFor: \"backupFrequency\",\n                                                                                            children: \"Frequ\\xeancia\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 904,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                            id: \"backupFrequency\",\n                                                                                            value: generalSettings.backupFrequency,\n                                                                                            onChange: (e)=>setGeneralSettings({\n                                                                                                    ...generalSettings,\n                                                                                                    backupFrequency: e.target.value\n                                                                                                }),\n                                                                                            className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"daily\",\n                                                                                                    children: \"Di\\xe1rio\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 921,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"weekly\",\n                                                                                                    children: \"Semanal\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 922,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"monthly\",\n                                                                                                    children: \"Mensal\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 923,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 910,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 903,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                            htmlFor: \"backupTime\",\n                                                                                            children: \"Hor\\xe1rio\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 928,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            id: \"backupTime\",\n                                                                                            type: \"time\",\n                                                                                            value: generalSettings.backupTime,\n                                                                                            onChange: (e)=>setGeneralSettings({\n                                                                                                    ...generalSettings,\n                                                                                                    backupTime: e.target.value\n                                                                                                }),\n                                                                                            className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 934,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"Armazenamento e Logs\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"maxFileSize\",\n                                                                                    children: \"Tamanho m\\xe1ximo de arquivo (MB)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 960,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"maxFileSize\",\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    max: \"100\",\n                                                                                    value: generalSettings.maxFileSize,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            maxFileSize: parseInt(e.target.value)\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 959,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"allowedFileTypes\",\n                                                                                    children: \"Tipos de arquivo permitidos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 983,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"allowedFileTypes\",\n                                                                                    type: \"text\",\n                                                                                    value: generalSettings.allowedFileTypes,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            allowedFileTypes: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    placeholder: \"jpg,png,pdf,doc,...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 989,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"mt-1 text-xs text-neutral-500 dark:text-neutral-400\",\n                                                                                    children: \"Separados por v\\xedrgula, sem pontos ou espa\\xe7os\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 982,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"logRetention\",\n                                                                                    children: \"Per\\xedodo de reten\\xe7\\xe3o de logs (dias)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1008,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"logRetention\",\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    max: \"365\",\n                                                                                    value: generalSettings.logRetention,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            logRetention: parseInt(e.target.value)\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1014,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 958,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 681,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: saveGeneralSettings,\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                                                disabled: !isSystemAdmin && !isCompanyAdmin,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"hidden\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Salvar Configura\\xe7\\xf5es\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 1036,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1035,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 680,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"companies\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_CompanyManagementTab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1053,\n                                    columnNumber: 45\n                                }, undefined),\n                                activeTab === \"branches\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_BranchesTab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1056,\n                                    columnNumber: 44\n                                }, undefined),\n                                activeTab === \"email\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_EmailSettingsTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 41\n                                }, undefined),\n                                activeTab === \"backup\" && isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1065,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Configura\\xe7\\xf5es de Backup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1064,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupStorage\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Armazenamento de Backup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                                                                    id: \"backupStorage\",\n                                                                    moduleColor: \"admin\",\n                                                                    defaultValue: \"local\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"local\",\n                                                                            children: \"Servidor Local\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1083,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"s3\",\n                                                                            children: \"Amazon S3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"azure\",\n                                                                            children: \"Azure Blob Storage\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"google\",\n                                                                            children: \"Google Cloud Storage\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1086,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupDirectory\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Diret\\xf3rio Local\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                    id: \"backupDirectory\",\n                                                                    moduleColor: \"admin\",\n                                                                    type: \"text\",\n                                                                    defaultValue: \"/var/backups/dentrodascasinhas\",\n                                                                    placeholder: \"/var/backups/dentrodascasinhas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupRetention\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Reten\\xe7\\xe3o de Backups (dias)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                    id: \"backupRetention\",\n                                                                    moduleColor: \"admin\",\n                                                                    type: \"number\",\n                                                                    defaultValue: \"30\",\n                                                                    min: \"1\",\n                                                                    max: \"365\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1106,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1070,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupCompression\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Compress\\xe3o\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                                                                    id: \"backupCompression\",\n                                                                    moduleColor: \"admin\",\n                                                                    defaultValue: \"gzip\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"gzip\",\n                                                                            children: \"GZIP\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1137,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"bzip2\",\n                                                                            children: \"BZIP2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1138,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"none\",\n                                                                            children: \"Sem compress\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1132,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Criptografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            id: \"encryptBackup\",\n                                                                            className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"encryptBackup\",\n                                                                            className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                            children: \"Ativar criptografia de backup\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1147,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1143,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Notifica\\xe7\\xf5es de Backup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"notifySuccess\",\n                                                                                    className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                                                    defaultChecked: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1168,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"notifySuccess\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Notificar backups bem-sucedidos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1174,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1167,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"notifyFailure\",\n                                                                                    className: \"h-4 w-4 text-primary-600 dark:text-primary-500 focus:ring-primary-500 dark:focus:ring-primary-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                                                    defaultChecked: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1182,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"notifyFailure\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Notificar falhas de backup\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1188,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1181,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1069,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Backups Dispon\\xedveis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1202,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTable, {\n                                                    moduleColor: \"admin\",\n                                                    title: \"Hist\\xf3rico de Backups\",\n                                                    columns: [\n                                                        {\n                                                            header: 'Nome',\n                                                            field: 'name',\n                                                            width: '25%'\n                                                        },\n                                                        {\n                                                            header: 'Data',\n                                                            field: 'date',\n                                                            width: '15%'\n                                                        },\n                                                        {\n                                                            header: 'Tamanho',\n                                                            field: 'size',\n                                                            width: '10%'\n                                                        },\n                                                        {\n                                                            header: 'Status',\n                                                            field: 'status',\n                                                            width: '10%'\n                                                        },\n                                                        {\n                                                            header: 'Tipo',\n                                                            field: 'type',\n                                                            width: '15%'\n                                                        },\n                                                        {\n                                                            header: 'Ações',\n                                                            field: 'actions',\n                                                            width: '15%',\n                                                            sortable: false\n                                                        }\n                                                    ],\n                                                    data: [\n                                                        {\n                                                            id: 1,\n                                                            name: 'backup_completo_20240601',\n                                                            date: '01/06/2024 01:00',\n                                                            size: '1.2 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 2,\n                                                            name: 'backup_completo_20240531',\n                                                            date: '31/05/2024 01:00',\n                                                            size: '1.1 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 3,\n                                                            name: 'backup_completo_20240530',\n                                                            date: '30/05/2024 01:00',\n                                                            size: '1.1 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 4,\n                                                            name: 'backup_incremental_20240529',\n                                                            date: '29/05/2024 01:00',\n                                                            size: '250 MB',\n                                                            status: 'Concluído',\n                                                            type: 'Incremental'\n                                                        },\n                                                        {\n                                                            id: 5,\n                                                            name: 'backup_completo_20240528',\n                                                            date: '28/05/2024 01:00',\n                                                            size: '1.0 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        }\n                                                    ],\n                                                    isLoading: false,\n                                                    emptyMessage: \"Nenhum backup encontrado\",\n                                                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 34\n                                                    }, void 0),\n                                                    currentPage: 1,\n                                                    totalPages: 1,\n                                                    totalItems: 5,\n                                                    showPagination: false,\n                                                    tableId: \"admin-backups-table\",\n                                                    enableColumnToggle: true,\n                                                    renderRow: (backup, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: moduleColors.hoverBg,\n                                                            children: [\n                                                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1239,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100\",\n                                                                                children: backup.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1240,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1238,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1237,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('date') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('size') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.size\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\",\n                                                                        children: backup.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('type') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                                                title: \"Restaurar\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1273,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1269,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                                                title: \"Download\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1279,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1275,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                                                title: \"Excluir\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1285,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1281,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1268,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, backup.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Executar Backup Agora\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Salvar Configura\\xe7\\xf5es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1302,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1295,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1063,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecurityTab, {\n                                    isSystemAdmin: isSystemAdmin,\n                                    isCompanyAdmin: isCompanyAdmin\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1310,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"preferencias\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_PreferencesPanel__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1317,\n                                    columnNumber: 48\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 661,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SettingsPage, \"Mw45LE88dod0CQdEKxElMPn1bEg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c1 = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"SecurityTab\");\n$RefreshReg$(_c1, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/settings/SettingsPage.js\n"));

/***/ })

});