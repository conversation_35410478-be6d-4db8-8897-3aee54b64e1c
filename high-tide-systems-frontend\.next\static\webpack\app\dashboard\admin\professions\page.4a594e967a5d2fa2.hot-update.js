"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupProfessionsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ProfessionGroupProfessionsModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupProfessionsModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: 'Tem certeza que deseja excluir o grupo \"'.concat(group.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 639,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 645,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 653,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 704,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 731,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 722,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 737,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 757,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 749,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 764,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 780,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 773,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 703,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 787,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 786,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 702,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 841,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 838,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 848,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 859,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 868,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 909,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 902,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 845,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 837,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 918,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 924,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 932,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 939,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 930,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 916,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 963,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 958,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 957,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 980,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1014,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1013,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1025,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1028,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1024,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1023,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1040,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1044,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1038,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1055,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1056,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1054,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1061,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1052,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1072,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1070,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1069,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1084,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1082,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1081,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1106,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1093,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1092,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1122,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1117,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1130,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1125,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1124,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1134,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1133,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1116,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1115,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1011,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 953,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 952,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1164,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1159,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1179,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1202,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1201,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1214,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1213,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1217,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1216,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1212,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1211,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1229,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1227,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1226,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1242,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1240,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1247,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1238,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1257,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1258,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1256,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1255,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1267,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1266,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1297,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1291,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1307,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1301,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1300,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1290,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1289,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1199,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1155,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1154,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1320,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1331,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1356,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-profession\" ? \"Excluir Profissão\" : (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-group\" ? \"Excluir Grupo\" : \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1362,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 836,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"z2ueIfMvpFqwLSBIlnDUAH37acU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/ProfessionGroupProfessionsModal.js":
/*!*****************************************************************!*\
  !*** ./src/components/admin/ProfessionGroupProfessionsModal.js ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Loader2,RefreshCw,Search,Users,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ProfessionGroupProfessionsModal = (param)=>{\n    let { isOpen, onClose, groupId } = param;\n    _s();\n    const { toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [group, setGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [filteredProfessions, setFilteredProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupProfessionsModal.useEffect\": ()=>{\n            if (isOpen && groupId) {\n                loadProfessions();\n            }\n        }\n    }[\"ProfessionGroupProfessionsModal.useEffect\"], [\n        isOpen,\n        groupId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionGroupProfessionsModal.useEffect\": ()=>{\n            if (professions.length > 0) {\n                filterProfessions();\n            }\n        }\n    }[\"ProfessionGroupProfessionsModal.useEffect\"], [\n        search,\n        statusFilter,\n        professions\n    ]);\n    const loadProfessions = async ()=>{\n        setIsLoading(true);\n        try {\n            const params = {};\n            if (statusFilter) {\n                params.active = statusFilter === \"active\";\n            }\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_2__.professionsService.getGroupProfessions(groupId, params);\n            setGroup(data.group);\n            setProfessions(data.professions);\n            setFilteredProfessions(data.professions);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões do grupo:\", error);\n            toast_error(\"Erro ao carregar profissões do grupo\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const filterProfessions = ()=>{\n        let filtered = [\n            ...professions\n        ];\n        // Filtrar por termo de busca\n        if (search.trim() !== \"\") {\n            const searchLower = search.toLowerCase();\n            filtered = filtered.filter((profession)=>profession.name.toLowerCase().includes(searchLower) || profession.description && profession.description.toLowerCase().includes(searchLower));\n        }\n        // Filtrar por status\n        if (statusFilter === \"active\") {\n            filtered = filtered.filter((profession)=>profession.active);\n        } else if (statusFilter === \"inactive\") {\n            filtered = filtered.filter((profession)=>!profession.active);\n        }\n        setFilteredProfessions(filtered);\n    };\n    const handleSearch = (e)=>{\n        setSearch(e.target.value);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        loadProfessions();\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setStatusFilter(\"\");\n        loadProfessions();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[11050] flex items-center justify-center overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed left-[50%] top-[50%] z-[11050] grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-gray-300 dark:border-gray-600 bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-xl max-w-4xl max-h-[90vh] overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center pb-4 border-b-2 border-gray-400 dark:border-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Profiss\\xf5es do Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    group && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                        children: group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-gray-500 dark:text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex flex-col md:flex-row gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                                value: search,\n                                                onChange: handleSearch,\n                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 dark:focus:ring-slate-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 w-full md:w-40\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.ModuleSelect, {\n                                            moduleColor: \"admin\",\n                                            value: statusFilter,\n                                            onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                            placeholder: \"Status\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Todos os status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"active\",\n                                                    children: \"Ativos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"inactive\",\n                                                    children: \"Inativos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleResetFilters,\n                                        className: \"flex-shrink-0 flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Limpar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center py-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    size: 32,\n                                    className: \"animate-spin text-primary-500 dark:text-primary-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined) : filteredProfessions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                children: \"Nenhuma profiss\\xe3o encontrada neste grupo.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-300 dark:border-gray-600 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"w-full divide-y divide-gray-200 dark:divide-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                className: \"bg-gray-50 dark:bg-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                                        children: \"Profiss\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                                        children: \"Descri\\xe7\\xe3o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                                        children: \"Usu\\xe1rios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                        lineNumber: 172,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        className: \"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            className: \"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\",\n                                            children: filteredProfessions.map((profession)=>{\n                                                var _profession_company, _profession__count;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hover:bg-gray-50 dark:hover:bg-gray-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-shrink-0 h-10 w-10 bg-slate-100 dark:bg-slate-900/30 text-slate-600 dark:text-slate-400 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            size: 20\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 186,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-4\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100\",\n                                                                            children: profession.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 189,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 188,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs\",\n                                                                children: profession.description || \"N/A\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: ((_profession_company = profession.company) === null || _profession_company === void 0 ? void 0 : _profession_company.name) || \"N/A\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 203,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center text-sm text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        size: 14,\n                                                                        className: \"mr-1 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 208,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                        lineNumber: 209,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            className: \"px-4 py-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                                                children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            size: 12\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 222,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Ativo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Loader2_RefreshCw_Search_Users_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            size: 12\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 227,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Inativo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                            lineNumber: 228,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                            lineNumber: 212,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, profession.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 21\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\ProfessionGroupProfessionsModal.js\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionGroupProfessionsModal, \"vjHgN/Y1uC7C1ED7s5NJbrtm1J4=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = ProfessionGroupProfessionsModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionGroupProfessionsModal);\nvar _c;\n$RefreshReg$(_c, \"ProfessionGroupProfessionsModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/ProfessionGroupProfessionsModal.js\n"));

/***/ })

});