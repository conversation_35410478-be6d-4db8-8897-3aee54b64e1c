"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/ui/ModuleTabs.js":
/*!*****************************************!*\
  !*** ./src/components/ui/ModuleTabs.js ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/**\r\n * Componente de tabs para módulos com suporte a cores específicas\r\n *\r\n * @param {Object} props\r\n * @param {Array} props.tabs - Array de objetos de tab com {id, label, icon}\r\n * @param {string} props.activeTab - ID da tab ativa\r\n * @param {Function} props.onTabChange - Função chamada quando uma tab é clicada\r\n * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, etc)\r\n */ const ModuleTabs = (param)=>{\n    let { tabs, activeTab, onTabChange, moduleColor = \"admin\" } = param;\n    // Mapeamento de cores por módulo\n    const moduleColors = {\n        people: {\n            active: {\n                text: \"text-orange-800 dark:text-orange-100\",\n                border: \"border-orange-500 dark:border-orange-400\",\n                bg: \"bg-orange-50 dark:bg-orange-900/30\",\n                icon: \"text-orange-600 dark:text-orange-300\"\n            },\n            inactive: {\n                text: \"text-neutral-600 dark:text-neutral-400\",\n                hover: \"hover:text-orange-800 dark:hover:text-orange-200 hover:bg-orange-50 dark:hover:bg-orange-900/20\"\n            }\n        },\n        scheduler: {\n            active: {\n                text: \"text-purple-800 dark:text-purple-100\",\n                border: \"border-purple-500 dark:border-purple-400\",\n                bg: \"bg-purple-50 dark:bg-purple-900/30\",\n                icon: \"text-purple-600 dark:text-purple-300\"\n            },\n            inactive: {\n                text: \"text-neutral-600 dark:text-neutral-400\",\n                hover: \"hover:text-purple-800 dark:hover:text-purple-200 hover:bg-purple-50 dark:hover:bg-purple-900/20\"\n            }\n        },\n        admin: {\n            active: {\n                text: \"text-gray-800 dark:text-gray-100\",\n                border: \"border-gray-500 dark:border-gray-400\",\n                bg: \"bg-gray-50 dark:bg-gray-700/30\",\n                icon: \"text-gray-600 dark:text-gray-300\"\n            },\n            inactive: {\n                text: \"text-gray-600 dark:text-gray-400\",\n                hover: \"hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/20\"\n            }\n        },\n        financial: {\n            active: {\n                text: \"text-green-800 dark:text-green-100\",\n                border: \"border-green-500 dark:border-green-400\",\n                bg: \"bg-green-50 dark:bg-green-900/30\",\n                icon: \"text-green-600 dark:text-green-300\"\n            },\n            inactive: {\n                text: \"text-neutral-600 dark:text-neutral-400\",\n                hover: \"hover:text-green-800 dark:hover:text-green-200 hover:bg-green-50 dark:hover:bg-green-900/20\"\n            }\n        }\n    };\n    // Obter as cores do módulo atual\n    const colors = moduleColors[moduleColor] || moduleColors.admin;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mt-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex border-b border-neutral-200 dark:border-gray-700 overflow-x-auto\",\n            children: tabs.map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors relative \".concat(activeTab === tab.id ? \"border-b-2 \".concat(colors.active.border, \" \").concat(colors.active.text, \" \").concat(colors.active.bg) : \"\".concat(colors.inactive.text, \" \").concat(colors.inactive.hover)),\n                    onClick: (e)=>{\n                        // Prevenir a propagação do evento para evitar submissão de formulários\n                        e.preventDefault();\n                        e.stopPropagation();\n                        onTabChange(tab.id);\n                    },\n                    children: [\n                        tab.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: activeTab === tab.id ? colors.active.icon : \"\",\n                            children: tab.icon\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleTabs.js\",\n                            lineNumber: 94,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: tab.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleTabs.js\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, tab.id, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleTabs.js\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleTabs.js\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleTabs.js\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ModuleTabs;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModuleTabs);\nvar _c;\n$RefreshReg$(_c, \"ModuleTabs\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ModuleTabs.js\n"));

/***/ })

});