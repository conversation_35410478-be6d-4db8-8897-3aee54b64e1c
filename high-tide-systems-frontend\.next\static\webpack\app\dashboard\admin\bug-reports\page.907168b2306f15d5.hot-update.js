"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/admin/bug-reports/page.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleTable */ \"(app-pages-browser)/./src/components/ui/ModuleTable.js\");\n/* harmony import */ var _components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/bug-report/BugReportsFilters */ \"(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/bugReportService */ \"(app-pages-browser)/./src/services/bugReportService.js\");\n/* harmony import */ var _components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/BugDetailsModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugDetailsModal.js\");\n/* harmony import */ var _components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/BugEditModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugEditModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BugReportsPage = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast_error, toast_success } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [bugs, setBugs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedBug, setSelectedBug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        status: [],\n        priority: [],\n        category: [],\n        companies: []\n    });\n    // Carregar dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsPage.useEffect\": ()=>{\n            if (!user) return;\n            if (user.role !== 'SYSTEM_ADMIN') {\n                router.push('/dashboard');\n                return;\n            }\n            loadBugReports();\n            loadStats();\n        }\n    }[\"BugReportsPage.useEffect\"], [\n        user,\n        router,\n        filters,\n        pagination.page\n    ]);\n    const loadBugReports = async ()=>{\n        try {\n            setLoading(true);\n            const params = {\n                page: pagination.page,\n                limit: pagination.limit,\n                ...filters\n            };\n            // Remove filtros vazios\n            Object.keys(params).forEach((key)=>{\n                if (Array.isArray(params[key]) && params[key].length === 0) {\n                    delete params[key];\n                } else if (params[key] === '') {\n                    delete params[key];\n                }\n            });\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.listAll(params);\n            if (response.data) {\n                setBugs(response.data.bugReports);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            }\n        } catch (error) {\n            console.error('Erro ao carregar bug reports:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao carregar relatórios de bugs'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadStats = async ()=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.getStats();\n            if (response.data) {\n                setStats(response.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar estatísticas:', error);\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN') {\n        return null;\n    }\n    const handleViewBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowDetailsModal(true);\n    };\n    const handleEditBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowEditModal(true);\n    };\n    const handleSaveBug = async (updatedBug)=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.updateStatus(updatedBug.id, {\n                status: updatedBug.status,\n                priority: updatedBug.priority,\n                adminNotes: updatedBug.adminNotes\n            });\n            if (response.data) {\n                setBugs((prev)=>prev.map((bug)=>bug.id === updatedBug.id ? {\n                            ...bug,\n                            ...updatedBug\n                        } : bug));\n                setShowEditModal(false);\n                setSelectedBug(null);\n                toast_success({\n                    title: 'Sucesso',\n                    message: 'Bug atualizado com sucesso'\n                });\n            }\n        } catch (error) {\n            console.error('Erro ao atualizar bug:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao atualizar o bug'\n            });\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, undefined);\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        const colors = {\n            'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',\n            'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',\n            'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',\n            'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n        };\n        return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            'CRITICAL': 'Crítica',\n            'HIGH': 'Alta',\n            'MEDIUM': 'Média',\n            'LOW': 'Baixa'\n        };\n        return labels[priority] || 'Desconhecida';\n    };\n    const columns = [\n        {\n            header: 'Status',\n            field: 'status',\n            width: '120px'\n        },\n        {\n            header: 'Bug',\n            field: 'title',\n            width: 'auto'\n        },\n        {\n            header: 'Prioridade',\n            field: 'priority',\n            width: '120px'\n        },\n        {\n            header: 'Reportado por',\n            field: 'reportedBy',\n            width: '200px'\n        },\n        {\n            header: 'Data',\n            field: 'createdAt',\n            width: '120px'\n        },\n        {\n            header: 'Ações',\n            field: 'actions',\n            width: '120px',\n            sortable: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                size: 24,\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Relat\\xf3rios de Bugs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie todos os bugs reportados pelos usu\\xe1rios do sistema\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 211,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Filtros e Busca\",\n                description: \"Utilize os filtros abaixo para encontrar bugs espec\\xedficos.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 221,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 224,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                moduleColor: \"admin\",\n                title: \"Lista de Bugs Reportados\",\n                data: bugs,\n                columns: columns,\n                isLoading: loading,\n                emptyMessage: \"Nenhum bug reportado ainda.\",\n                emptyIcon: \"\\uD83D\\uDC1B\",\n                enableColumnToggle: true,\n                renderRow: (bug, _index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getStatusIcon(bug.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: bug.status === 'OPEN' ? 'Aberto' : bug.status === 'IN_PROGRESS' ? 'Em Progresso' : bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help\",\n                                            title: bug.description,\n                                            children: bug.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('priority') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(bug.priority)),\n                                    children: getPriorityLabel(bug.priority)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('reportedBy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.reportedBy.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: bug.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                children: new Date(bug.createdAt).toLocaleDateString('pt-BR')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, bug.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDetailsModal,\n                onClose: ()=>{\n                    setShowDetailsModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug,\n                onSave: handleSaveBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsPage, \"q81YIsVpT1m/rqoFz1uU63xXAZc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BugReportsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsPage);\nvar _c;\n$RefreshReg$(_c, \"BugReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js\n"));

/***/ })

});