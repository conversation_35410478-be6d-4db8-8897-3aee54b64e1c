"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _planData_subscription, _planData_subscription1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    console.log('[PlansPage] Inicializando página');\n    console.log('[PlansPage] Usuário atual:', user);\n    console.log('[PlansPage] Permissões:', user === null || user === void 0 ? void 0 : user.permissions);\n    console.log('[PlansPage] Módulos:', user === null || user === void 0 ? void 0 : user.modules);\n    console.log('[PlansPage] Role:', user === null || user === void 0 ? void 0 : user.role);\n    console.log('[PlansPage] CompanyId:', user === null || user === void 0 ? void 0 : user.companyId);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // Adicionar após a declaração de outros estados\n    const [showUpgradeModal, setShowUpgradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPlanId, setSelectedPlanId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const basePrice = 19.90; // Preço base por usuário\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        // Desconto adicional de 10% para pagamento anual à vista\n        const annualDiscount = 0.10;\n        const yearlyPriceWithDiscount = finalPrice * 12 * (1 - annualDiscount);\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice: finalPrice * 12,\n            yearlyPriceWithDiscount,\n            annualDiscount: annualDiscount * 100\n        };\n    };\n    const pricing = calculatePrice(userCount);\n    // Opções de usuários predefinidas\n    const userOptions = [\n        1,\n        5,\n        20,\n        50,\n        100,\n        200\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadData = {\n                \"PlansPage.useEffect.loadData\": async ()=>{\n                    try {\n                        if (isSystemAdmin) {\n                            // Carregar lista de empresas para admin do sistema\n                            const companiesData = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n                            setCompanies(companiesData);\n                            // Se houver empresas, selecionar a primeira por padrão\n                            if (companiesData.length > 0 && !selectedCompanyId) {\n                                setSelectedCompanyId(companiesData[0].id);\n                            }\n                        } else if (user === null || user === void 0 ? void 0 : user.companyId) {\n                            // Para usuários não-admin, usar a empresa atual\n                            setSelectedCompanyId(user.companyId);\n                            try {\n                                const company = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCurrentCompany();\n                                setCurrentCompany(company);\n                            } catch (error) {\n                                console.error('[PlansPage] Erro ao carregar empresa atual:', error);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('[PlansPage] Erro ao carregar dados iniciais:', error);\n                        toast_error('Erro ao carregar dados iniciais');\n                    }\n                }\n            }[\"PlansPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        user,\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadPlanData = {\n                \"PlansPage.useEffect.loadPlanData\": async ()=>{\n                    if (!selectedCompanyId) return;\n                    setIsLoading(true);\n                    try {\n                        console.log('[PlansPage] Carregando dados do plano para companyId:', selectedCompanyId);\n                        const [plansData, subscriptionData] = await Promise.all([\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans(),\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(selectedCompanyId)\n                        ]);\n                        console.log('[PlansPage] Dados dos planos carregados:', plansData);\n                        console.log('[PlansPage] Dados da assinatura carregados:', subscriptionData);\n                        setPlans(plansData);\n                    } catch (error) {\n                        var _error_response;\n                        console.error('[PlansPage] Erro ao carregar dados dos planos:', error);\n                        console.error('[PlansPage] Detalhes do erro:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                        toast_error('Erro ao carregar dados dos planos');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"PlansPage.useEffect.loadPlanData\"];\n            loadPlanData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [couponCode, setCouponCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [couponValidation, setCouponValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isValidatingCoupon, setIsValidatingCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        console.log('[DEBUG] currentUser.companyId:', user === null || user === void 0 ? void 0 : user.companyId);\n        console.log('[DEBUG] Tem permissão admin.dashboard.view?', can('admin.dashboard.view'));\n        // Para system_admin, não carregar se não tiver empresa selecionada\n        if (isSystemAdmin && !selectedCompanyId) {\n            console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');\n            setIsLoading(false);\n            return;\n        }\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : user === null || user === void 0 ? void 0 : user.companyId;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            if (!companyId) {\n                console.error('[DEBUG] Nenhum companyId disponível para carregar dados do plano');\n                throw new Error('ID da empresa não disponível');\n            }\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] availablePlans.modules:', availablePlansResponse === null || availablePlansResponse === void 0 ? void 0 : availablePlansResponse.modules);\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response_data, _error_response3;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error(\"Error status:\", (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            console.error(\"Error headers:\", (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.headers);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data = _error_response3.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o custo adicional usando a função centralizada\n    const calculateAdditionalCost = ()=>{\n        if (!planData) return {\n            additionalCost: 0,\n            costPerAdditionalUser: 19.90\n        };\n        const currentUsers = planData.subscription.userLimit;\n        const isAnnual = planData.subscription.billingCycle === 'YEARLY';\n        return (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.calculateAdditionalUsersCost)(currentUsers, additionalUsersCount, isAnnual);\n    };\n    // Função para calcular preço por usuário atual\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90;\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90;\n    };\n    // Função para validar cupom\n    const validateCoupon = async (code)=>{\n        if (!code || code.trim() === '') {\n            setCouponValidation(null);\n            return;\n        }\n        setIsValidatingCoupon(true);\n        try {\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.validateCoupon(code.trim());\n            setCouponValidation({\n                valid: true,\n                coupon: response.coupon,\n                message: \"Cupom v\\xe1lido! \".concat(response.coupon.type === 'PERCENT' ? \"\".concat(response.coupon.value, \"% de desconto\") : \"R$ \".concat(response.coupon.value.toFixed(2), \" de desconto\"))\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setCouponValidation({\n                valid: false,\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Cupom inválido'\n            });\n        } finally{\n            setIsValidatingCoupon(false);\n        }\n    };\n    // Debounce para validação de cupom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"PlansPage.useEffect.timer\": ()=>{\n                    if (couponCode.trim()) {\n                        validateCoupon(couponCode);\n                    } else {\n                        setCouponValidation(null);\n                    }\n                }\n            }[\"PlansPage.useEffect.timer\"], 500);\n            return ({\n                \"PlansPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"PlansPage.useEffect\"];\n        }\n    }[\"PlansPage.useEffect\"], [\n        couponCode\n    ]);\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        try {\n            setIsUpdating(true);\n            const newUserCount = planData.usage.currentUsers + additionalUsersCount;\n            // Validar cupom antes de prosseguir se foi informado\n            if (couponCode.trim() && (!couponValidation || !couponValidation.valid)) {\n                toast_error('Aguarde a validação do cupom ou remova-o para continuar');\n                return;\n            }\n            // Criar uma nova sessão de checkout do Stripe\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(planData.subscription.billingCycle.toLowerCase(), newUserCount, couponCode.trim() || null);\n            // Redirecionar para a página de checkout do Stripe\n            window.location.href = response.url;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao atualizar usuários:', error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao atualizar usuários');\n        } finally{\n            setIsUpdating(false);\n            handleCloseAddUsersModal();\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        console.log('[DEBUG] openAddModuleConfirmation:', {\n            moduleType,\n            availablePlans\n        });\n        console.log('[DEBUG] availablePlans.modules:', availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules);\n        if (!(availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules)) {\n            console.error('[DEBUG] availablePlans.modules não está disponível');\n            toast_error({\n                title: \"Erro\",\n                message: \"Dados dos módulos não estão disponíveis. Tente recarregar a página.\"\n            });\n            return;\n        }\n        const moduleInfo = availablePlans.modules[moduleType];\n        console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Adicionar a função handleUpgrade após a função handleCancelSubscription\n    const handleUpgrade = async ()=>{\n        try {\n            const billingCycle = isAnnual ? 'yearly' : 'monthly';\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(billingCycle, userCount);\n            if (response.url) {\n                window.location.href = response.url;\n            } else {\n                throw new Error('URL de checkout não encontrada');\n            }\n        } catch (error) {\n            console.error('Erro ao iniciar checkout:', error);\n            toast_error('Erro ao iniciar processo de upgrade. Tente novamente.');\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    // Após carregar planData (ou subscriptionData)\n    const isTrial = (planData === null || planData === void 0 ? void 0 : (_planData_subscription = planData.subscription) === null || _planData_subscription === void 0 ? void 0 : _planData_subscription.status) === 'TRIAL';\n    const trialEndDate = (planData === null || planData === void 0 ? void 0 : (_planData_subscription1 = planData.subscription) === null || _planData_subscription1 === void 0 ? void 0 : _planData_subscription1.endDate) ? new Date(planData.subscription.endDate).toLocaleDateString('pt-BR') : 'N/A';\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 640,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 638,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 651,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 663,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 665,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 656,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 655,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 648,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 692,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 707,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 698,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 697,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 717,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 689,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Gerenciamento de Planos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name) : \"Gerencie seu plano, usuários e módulos da assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 741,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 737,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 759,\n                    columnNumber: 17\n                }, void 0),\n                description: \"Selecione uma empresa para visualizar e gerenciar seu plano.\",\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 771,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 773,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 764,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 763,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 757,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 789,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 793,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 792,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 787,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 799,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 815,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 821,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 814,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Detalhamento dos Valores\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 834,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Usu\\xe1rios ativos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                children: [\n                                                                    planData.usage.currentUsers,\n                                                                    \" de \",\n                                                                    planData.usage.userLimit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Pre\\xe7o base (R$ 19,90/usu\\xe1rio):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (planData.usage.userLimit * 19.90).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    (()=>{\n                                                        const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                                        const discountAmount = planData.usage.userLimit * 19.90 * (currentDiscount / 100);\n                                                        const priceAfterDiscount = planData.usage.userLimit * 19.90 - discountAmount;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                currentDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Desconto por volume (\",\n                                                                                currentDiscount,\n                                                                                \"%):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 863,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                discountAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: \"Desconto anual (10%):\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                (priceAfterDiscount * 0.10).toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.appliedCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Cupom aplicado (\",\n                                                                                planData.subscription.appliedCoupon.code,\n                                                                                \"):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: planData.subscription.appliedCoupon.type === 'PERCENT' ? \"-\".concat(planData.subscription.appliedCoupon.value, \"%\") : \"-R$ \".concat(planData.subscription.appliedCoupon.value.toFixed(2))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 886,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-t border-gray-200 dark:border-gray-700 pt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 897,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: [\n                                                                                    \"R$ \",\n                                                                                    (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 898,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 mt-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-blue-700 dark:text-blue-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 907,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Plano Anual Ativo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 908,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                            children: [\n                                                                                \"Voc\\xea economiza R$ \",\n                                                                                (priceAfterDiscount * 12 - planData.subscription.pricePerMonth * 12).toFixed(2),\n                                                                                \" por ano\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 839,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 833,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'CANCELED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'TRIAL' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Fazer Upgrade\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 942,\n                                                columnNumber: 17\n                                            }, undefined) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const url = isSystemAdmin && selectedCompanyId ? \"/subscription/invoices?companyId=\".concat(selectedCompanyId) : '/subscription/invoices';\n                                                    router.push(url);\n                                                },\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 952,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 972,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Uso de Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOpenAddUsersModal,\n                                        disabled: isUpdating || planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED',\n                                        className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED' ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 984,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Adicionar Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 970,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Usu\\xe1rios Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.currentUsers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 991,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Limite de Usu\\xe1rios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.userLimit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 997,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 990,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2.5 rounded-full transition-all duration-500\",\n                                            style: {\n                                                width: \"\".concat(planData.usage.userLimitUsage, \"%\"),\n                                                backgroundColor: planData.usage.userLimitUsage >= 90 ? '#EF4444' // Vermelho para uso >= 90%\n                                                 : planData.usage.userLimitUsage >= 75 ? '#F59E0B' // Amarelo para uso >= 75%\n                                                 : '#3B82F6' // Azul para uso < 75%\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1006,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.currentUsers,\n                                                    \" de \",\n                                                    planData.usage.userLimit,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1021,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1024,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1020,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    planData.usage.userLimitUsage >= 90 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline-block mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Voc\\xea est\\xe1 pr\\xf3ximo do limite de usu\\xe1rios. Considere adicionar mais usu\\xe1rios ao seu plano.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1030,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 989,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 969,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1042,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1041,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1053,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1061,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1064,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1049,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1073,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                children: \"Plano B\\xe1sico Completo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1074,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1072,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 dark:text-blue-200 mb-2\",\n                                        children: \"Seu plano j\\xe1 inclui todos os m\\xf3dulos essenciais para o funcionamento completo do sistema.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Administra\\xe7\\xe3o completa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1082,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Sistema de agendamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Gerenciamento de pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1084,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Relat\\xf3rios e dashboards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1085,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Suporte t\\xe9cnico inclu\\xeddo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1086,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1046,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1040,\n                columnNumber: 7\n            }, undefined),\n            isTrial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-6 h-6 text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1095,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Voc\\xea est\\xe1 em per\\xedodo de avalia\\xe7\\xe3o (trial).\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1097,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Aproveite para testar todos os recursos! O acesso ser\\xe1 limitado ap\\xf3s o t\\xe9rmino do trial.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1099,\n                                        columnNumber: 103\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Data de t\\xe9rmino do trial:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" \",\n                                    trialEndDate,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1100,\n                                        columnNumber: 72\n                                    }, undefined),\n                                    \"Caso queira migrar para um plano completo, clique no bot\\xe3o abaixo.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1098,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUpgradeModal(true),\n                                className: \"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                children: \"Fazer Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1103,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1096,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1094,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showUpgradeModal,\n                onClose: ()=>setShowUpgradeModal(false),\n                title: \"Quantos usu\\xe1rios voc\\xea precisa?\",\n                size: \"lg\",\n                moduleColor: \"admin\",\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row items-center justify-center gap-3 px-4 py-3 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUpgradeModal(false),\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: \"Cancelar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1122,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleUpgrade,\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: [\n                                \"Confirmar Upgrade\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"ml-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1133,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1128,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1121,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4 px-2 py-2 md:px-4 md:py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 text-base md:text-lg mt-2 mb-2\",\n                                    children: \"Selecione a quantidade de usu\\xe1rios para ver o pre\\xe7o personalizado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mt-1 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1 \".concat(!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAnnual(!isAnnual),\n                                            className: \"relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 \".concat(isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition \".concat(isAnnual ? 'translate-x-9' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1150,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 \".concat(isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Anual\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-2 mb-1\",\n                                    children: userOptions.map((option)=>{\n                                        const discount = getDiscountByUserCount(option);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUserCount(option),\n                                            className: \"relative p-5 min-w-[130px] min-h-[70px] rounded-xl border-2 transition-all text-center flex flex-col items-center justify-center gap-1 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 text-lg font-semibold \".concat(userCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-white'),\n                                            children: [\n                                                discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow\",\n                                                    children: [\n                                                        \"-\",\n                                                        discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: option\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1177,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 font-normal\",\n                                                    children: option === 1 ? 'usuário' : 'usuários'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, option, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1163,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-1 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-300 mb-0.5\",\n                                            children: \"Ou digite uma quantidade personalizada:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1185,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center w-32 md:w-44\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: \"1\",\n                                                        max: \"1000\",\n                                                        value: userCount,\n                                                        onChange: (e)=>setUserCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                        className: \"pl-10 pr-2 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 w-full text-center text-base\",\n                                                        placeholder: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1187,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-3 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mt-1 border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Usu\\xe1rios:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: userCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"Pre\\xe7o por usu\\xe1rio \",\n                                                        isAnnual ? 'anual à vista' : 'mensal',\n                                                        \":\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: pricing.discount > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1214,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 font-medium text-gray-900 dark:text-white\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * (1 - pricing.discount / 100) * 12 * (1 - pricing.annualDiscount / 100) : basePrice * (1 - pricing.discount / 100)).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1215,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1209,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto por quantidade:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1223,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Valor mensal sem desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1229,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        pricing.totalWithoutDiscount.toFixed(2).replace('.', ',')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isAnnual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto anual \\xe0 vista:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.annualDiscount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-end justify-center min-w-[180px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                            children: \"Valor mensal:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl font-bold text-orange-600 dark:text-orange-400\",\n                                            children: [\n                                                \"R$ \",\n                                                isAnnual ? (pricing.yearlyPriceWithDiscount / 12).toFixed(2).replace('.', ',') : pricing.monthlyPrice.toFixed(2).replace('.', ',')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1114,\n                columnNumber: 7\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg max-w-4xl w-full border border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Gerenciar limite de usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: \"Ajuste o limite de usu\\xe1rios do seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1255,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1251,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-8\",\n                            children: [\n                                1,\n                                5,\n                                10,\n                                20\n                            ].map((option)=>{\n                                const newLimit = planData.usage.userLimit + option;\n                                const discount = getDiscountByUserCount(newLimit);\n                                const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                const discountChange = discount - currentDiscount;\n                                const isValid = true;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setAdditionalUsersCount(option),\n                                    className: \"p-4 rounded-lg border-2 transition-all text-center relative \".concat(additionalUsersCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600'),\n                                    children: [\n                                        discountChange > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\",\n                                            children: [\n                                                \"+\",\n                                                discountChange,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1279,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"+\",\n                                                option\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1283,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: option === 1 ? 'usuário' : 'usuários'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1286,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, option, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1269,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1260,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Cupom de desconto (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1296,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: couponCode,\n                                            onChange: (e)=>setCouponCode(e.target.value.toUpperCase()),\n                                            placeholder: \"Digite o c\\xf3digo do cupom\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1300,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isValidatingCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"animate-spin h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1309,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1308,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1299,\n                                    columnNumber: 15\n                                }, undefined),\n                                couponValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm \".concat(couponValidation.valid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'),\n                                    children: couponValidation.valid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1317,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1316,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1322,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1321,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1314,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1295,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Limite atual:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1334,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1333,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Altera\\xe7\\xe3o:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1338,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: [\n                                                                \"+\",\n                                                                additionalUsersCount\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo limite:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit + additionalUsersCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Cupom aplicado:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.code\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1350,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1332,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Pre\\xe7o atual por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1359,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1360,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo pre\\xe7o por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                calculateAdditionalCost().costPerAdditionalUser.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1366,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Diferen\\xe7a mensal:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(calculateAdditionalCost().additionalCost < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'),\n                                                            children: [\n                                                                calculateAdditionalCost().additionalCost > 0 ? '+' : '',\n                                                                \"R$ \",\n                                                                Math.abs(calculateAdditionalCost().additionalCost).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1370,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Desconto do cupom:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1378,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.type === 'PERCENT' ? \"\".concat(couponValidation.coupon.value, \"%\") : \"R$ \".concat(couponValidation.coupon.value.toFixed(2))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1377,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1357,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1331,\n                                    columnNumber: 15\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser < planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1392,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Voc\\xea receber\\xe1 um desconto adicional por ter mais usu\\xe1rios!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1391,\n                                    columnNumber: 17\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser > planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1399,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O pre\\xe7o por usu\\xe1rio aumentar\\xe1 devido \\xe0 redu\\xe7\\xe3o do desconto por volume.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1398,\n                                    columnNumber: 17\n                                }, undefined),\n                                planData.usage.currentUsers > planData.usage.userLimit + additionalUsersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1406,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O novo limite n\\xe3o pode ser menor que a quantidade atual de usu\\xe1rios (\",\n                                        planData.usage.currentUsers,\n                                        \").\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1405,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1330,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCloseAddUsersModal,\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1413,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddUsers,\n                                    disabled: isUpdating || additionalUsersCount <= 0 || isValidatingCoupon || couponCode.trim() && (!couponValidation || !couponValidation.valid),\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors disabled:opacity-50\",\n                                    children: [\n                                        isValidatingCoupon ? 'Validando cupom...' : 'Confirmar Alteração',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1425,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1419,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1412,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1250,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1249,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 734,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"otwcSAx1ZVpjp+PdiSfJL+hqVP8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});