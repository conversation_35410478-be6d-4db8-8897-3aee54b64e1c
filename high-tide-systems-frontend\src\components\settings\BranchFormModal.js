"use client";

import React, { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button.js";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import { ModuleSelect, ModuleTextarea, ModuleMaskedInput } from "@/components/ui";
import { 
  Building, 
  MapPin, 
  Phone, 
  Mail, 
  Star, 
  Clock, 
  AlertCircle, 
  Info,
  Settings,
  Shield,
  Loader2
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import AddressForm from "@/components/common/AddressForm";
import { useCep } from "@/hooks/useCep";
import BranchWorkingHoursForm from "@/components/workingHours/BranchWorkingHoursForm";
import { useToast } from "@/contexts/ToastContext";
import MaskedInput from "@/components/common/MaskedInput";

const BranchFormModal = ({ isOpen, onClose, branch, onSuccess }) => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const workingHoursFormRef = useRef(null);
  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();
  
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    description: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    email: "",
    isHeadquarters: false,
    companyId: "",
    defaultWorkingHours: null,
    applyToUsers: false
  });

  const [companies, setCompanies] = useState([]);
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [activeTab, setActiveTab] = useState("basic");

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Carregar empresas se for system_admin
  useEffect(() => {
    const loadCompanies = async () => {
      if (isSystemAdmin) {
        setLoadingCompanies(true);
        try {
          const response = await companyService.getCompanies({
            active: true,
            limit: 100
          });
          setCompanies(response.companies || []);
        } catch (error) {
          console.error("Erro ao carregar empresas:", error);
        } finally {
          setLoadingCompanies(false);
        }
      }
    };

    if (isOpen && isSystemAdmin) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setActiveTab("basic");
      if (branch) {
        setFormData({
          name: branch.name || "",
          code: branch.code || "",
          description: branch.description || "",
          address: branch.address || "",
          neighborhood: branch.neighborhood || "",
          city: branch.city || "",
          state: branch.state || "",
          postalCode: branch.postalCode || "",
          phone: branch.phone || "",
          email: branch.email || "",
          isHeadquarters: branch.isHeadquarters || false,
          companyId: branch.companyId || user?.companyId || "",
          defaultWorkingHours: branch.defaultWorkingHours || null,
          applyToUsers: false
        });

        // Se branch existe mas não tem horários padrão, carregá-los da API
        if (branch.id && !branch.defaultWorkingHours) {
          loadDefaultWorkingHours(branch.id);
        }
      } else {
        setFormData({
          name: "",
          code: "",
          description: "",
          address: "",
          neighborhood: "",
          city: "",
          state: "",
          postalCode: "",
          phone: "",
          email: "",
          isHeadquarters: false,
          companyId: user?.companyId || "",
          defaultWorkingHours: null,
          applyToUsers: false
        });
      }
    }
  }, [isOpen, branch, user]);

  const loadDefaultWorkingHours = async (branchId) => {
    try {
      const data = await branchService.getDefaultWorkingHours(branchId);
      setFormData(prev => ({
        ...prev,
        defaultWorkingHours: data.defaultWorkingHours
      }));
    } catch (error) {
      console.error("Erro ao carregar horários padrão:", error);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome da unidade é obrigatório";
    }
    if (!formData.address.trim()) {
      newErrors.address = "Endereço é obrigatório";
    }
    if (isSystemAdmin && !formData.companyId) {
      newErrors.companyId = "Empresa é obrigatória";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email deve ser válido";
    }

    if (formData.phone && !/^\d{10,11}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = "Telefone deve ter 10 ou 11 dígitos";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleWorkingHoursChange = (workingHours) => {
    if (workingHours) {
      setFormData(prev => ({
        ...prev,
        defaultWorkingHours: workingHours
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    // Se estiver na aba de horários de trabalho, validar os horários
    if (activeTab === "workingHours" && workingHoursFormRef.current) {
      const isWorkingHoursValid = workingHoursFormRef.current.validateAllTimeSlots();
      if (!isWorkingHoursValid) {
        toast_error({
          title: "Erro de validação",
          message: "Verifique os horários de trabalho e corrija os erros antes de salvar."
        });
        return;
      }
    }

    setIsLoading(true);

    try {
      const payload = {
        name: formData.name.trim(),
        code: formData.code.trim() || undefined,
        description: formData.description.trim() || undefined,
        address: formData.address.trim(),
        neighborhood: formData.neighborhood.trim() || undefined,
        city: formData.city.trim() || undefined,
        state: formData.state.trim() || undefined,
        postalCode: formData.postalCode.trim() || undefined,
        phone: formData.phone ? formData.phone.replace(/\D/g, '') : undefined,
        email: formData.email.trim() || undefined,
        isHeadquarters: formData.isHeadquarters,
        companyId: formData.companyId,
        defaultWorkingHours: formData.defaultWorkingHours,
        applyToUsers: formData.applyToUsers
      };

      if (branch) {
        await branchService.updateBranch(branch.id, payload);

        // Se applyToUsers é true, aplicar horários aos usuários
        if (formData.applyToUsers && formData.defaultWorkingHours) {
          try {
            await branchService.applyWorkingHoursToUsers(branch.id);
            toast_success({
              title: "Horários aplicados",
              message: "Horários de trabalho aplicados com sucesso aos usuários da unidade"
            });
          } catch (error) {
            console.error("Erro ao aplicar horários aos usuários:", error);
            toast_error({
              title: "Erro",
              message: "Erro ao aplicar horários de trabalho aos usuários"
            });
          }
        }
      } else {
        await branchService.createBranch(payload);
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error("Erro ao salvar unidade:", error);
      
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          general: error.response?.data?.message || "Erro ao salvar unidade"
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] flex flex-col">
        <DialogHeader className="pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg text-white">
              <Building className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-gray-800 dark:text-white">
                {branch ? "Editar Unidade" : "Nova Unidade"}
              </DialogTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {branch ? "Modifique as informações da unidade" : "Configure uma nova unidade/filial da empresa"}
              </p>
            </div>
          </div>
        </DialogHeader>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700 -mx-6 px-6">
          <button
            onClick={() => setActiveTab("basic")}
            className={`px-4 py-3 font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === "basic"
                ? 'text-gray-600 border-gray-500'
                : 'text-gray-500 border-transparent hover:text-gray-600 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Informações Básicas
            </div>
          </button>
          <button
            onClick={() => setActiveTab("workingHours")}
            className={`px-4 py-3 font-medium text-sm transition-colors duration-200 border-b-2 ${
              activeTab === "workingHours"
                ? 'text-gray-600 border-gray-500'
                : 'text-gray-500 border-transparent hover:text-gray-600 hover:border-gray-400'
            }`}
          >
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Horários de Trabalho
            </div>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex-1 flex flex-col min-h-0">
          <div className="flex-1 overflow-y-auto space-y-6 pt-4">
          {/* Erro geral */}
          {errors.general && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
              <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
            </div>
          )}

          {activeTab === "basic" && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Coluna Esquerda - Informações Básicas */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-4 w-4 text-slate-600" />
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Informações Básicas</h3>
                </div>

                {/* Nome da unidade */}
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-slate-500 rounded-full"></span>
                    Nome da Unidade *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, name: e.target.value }));
                      setErrors(prev => ({ ...prev, name: "" }));
                    }}
                    placeholder="Ex: Filial Centro, Matriz São Paulo..."
                    className={errors.name ? "border-red-500 focus:ring-red-500" : ""}
                  />
                  {errors.name && (
                    <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.name}
                    </p>
                  )}
                </div>

                {/* Código */}
                <div className="space-y-2">
                  <Label htmlFor="code" className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    Código
                  </Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, code: e.target.value }));
                      setErrors(prev => ({ ...prev, code: "" }));
                    }}
                    placeholder="Ex: FIL001, MTZ"
                    className={errors.code ? "border-red-500 focus:ring-red-500" : ""}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Código único para identificação interna da unidade
                  </p>
                </div>

                {/* Descrição */}
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-sm font-medium flex items-center gap-2">
                    <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                    Descrição
                  </Label>
                  <ModuleTextarea
                    id="description"
                    moduleColor="admin"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Breve descrição sobre a unidade, sua função e características..."
                    rows={3}
                    className="resize-none"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Informações adicionais que ajudem a identificar esta unidade
                  </p>
                </div>

                {/* Matriz/Sede */}
                <div className="space-y-3 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isHeadquarters"
                      checked={formData.isHeadquarters}
                      onChange={(e) => setFormData(prev => ({ ...prev, isHeadquarters: e.target.checked }))}
                      className="h-4 w-4 text-slate-600 border-gray-300 rounded focus:ring-slate-500"
                      disabled={branch && branch.isHeadquarters}
                    />
                    <Label htmlFor="isHeadquarters" className="text-sm flex items-center gap-2">
                      <Star className="h-4 w-4 text-amber-500" />
                      Definir como matriz/sede principal
                    </Label>
                  </div>
                  <p className="text-xs text-amber-700 dark:text-amber-300 ml-6">
                    Apenas uma unidade pode ser matriz por empresa. Isso alterará automaticamente o status de outras unidades.
                  </p>
                </div>
              </div>

              {/* Coluna Direita - Endereço e Contato */}
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <MapPin className="h-4 w-4 text-slate-600" />
                  <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Endereço e Contato</h3>
                </div>



                {/* CEP com busca automática */}
                <div className="space-y-2">

                  <AddressForm
                    formData={formData}
                    setFormData={setFormData}
                    errors={errors}
                    isLoading={isLoading}
                    moduleColor="admin"
                    fieldMapping={{
                      cep: 'postalCode',
                      logradouro: 'address',
                      bairro: 'neighborhood',
                      localidade: 'city',
                      uf: 'state'
                    }}
                  />
                </div>

                {/* Telefone e Email */}
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-sm font-medium flex items-center gap-2">
                      <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                      Telefone
                    </Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10" />
                      <ModuleMaskedInput
                        moduleColor="admin"
                        mask="(99) 99999-9999"
                        replacement={{ 9: /[0-9]/ }}
                        value={formData.phone}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, phone: e.target.value }));
                          setErrors(prev => ({ ...prev, phone: "" }));
                        }}
                        placeholder="(00) 00000-0000"
                        className={`pl-10 ${errors.phone ? "border-red-500 focus:ring-red-500" : ""}`}
                        error={!!errors.phone}
                      />
                    </div>
                    {errors.phone && (
                      <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.phone}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium flex items-center gap-2">
                      <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                      Email
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, email: e.target.value }));
                          setErrors(prev => ({ ...prev, email: "" }));
                        }}
                        placeholder="<EMAIL>"
                        className={`pl-10 ${errors.email ? "border-red-500 focus:ring-red-500" : ""}`}
                      />
                    </div>
                    {errors.email && (
                      <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.email}
                      </p>
                    )}
                  </div>
                </div>

                {/* Configurações de empresa (apenas para system_admin) */}
                {isSystemAdmin && (
                  <div className="space-y-4 p-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900/20 dark:to-slate-800/20 rounded-lg border border-slate-200 dark:border-slate-700">
                    <div className="flex items-center gap-2 mb-4">
                      <Shield className="h-4 w-4 text-slate-600" />
                      <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Configurações de Empresa</h3>
                      <div className="flex items-center gap-1 px-2 py-1 bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-300 rounded-full text-xs">
                        <Info className="h-3 w-3" />
                        System Admin
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="companyId" className="text-sm font-medium flex items-center gap-2">
                        <Building className="h-3 w-3 text-slate-600" />
                        Selecionar Empresa *
                      </Label>
                      <ModuleSelect
                        id="companyId"
                        moduleColor="admin"
                        value={formData.companyId}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, companyId: e.target.value }));
                          setErrors(prev => ({ ...prev, companyId: "" }));
                        }}
                        disabled={loadingCompanies}
                        className={errors.companyId ? "border-red-500 focus:ring-red-500" : ""}
                      >
                        <option value="">
                          {loadingCompanies ? "Carregando empresas..." : "Selecione uma empresa"}
                        </option>
                        {companies.map((company) => (
                          <option key={company.id} value={company.id}>
                            {company.name}
                          </option>
                        ))}
                      </ModuleSelect>
                      {errors.companyId && (
                        <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.companyId}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeTab === "workingHours" && (
            <div className="space-y-8">
              {/* Header com informações principais */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg">
                    <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      Horários de Funcionamento
                    </h3>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                      Configure os horários padrão de funcionamento desta unidade. Estes horários servirão como base para agendamentos e podem ser aplicados aos usuários.
                    </p>
                    
                    {/* Estatísticas rápidas */}
                    <div className="flex flex-wrap gap-4 text-xs">
                      <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                        <span className="text-blue-800 dark:text-blue-200">
                          {Object.keys(formData.defaultWorkingHours || {}).filter(day => 
                            formData.defaultWorkingHours[day] && formData.defaultWorkingHours[day].length > 0
                          ).length} dias configurados
                        </span>
                      </div>
                      <div className="flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                        <Clock className="h-3 w-3 text-blue-600" />
                        <span className="text-blue-800 dark:text-blue-200">
                          Horários flexíveis por dia
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid gap-8">
                {/* Coluna Principal - Configuração de Horários */}
                <div className="xl:col-span-3 space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <div className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50 px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <Settings className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                          <h4 className="font-semibold text-gray-900 dark:text-white">Configuração Semanal</h4>
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Configure os horários para cada dia da semana
                        </div>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <BranchWorkingHoursForm
                        ref={workingHoursFormRef}
                        defaultWorkingHours={formData.defaultWorkingHours}
                        onChange={handleWorkingHoursChange}
                        isLoading={isLoading}
                        onValidationChange={(isValid) => {
                          // Opcional: Usar para indicador visual
                        }}
                      />
                    </div>
                  </div>
                </div>

                {/* Coluna Lateral - Configurações e Informações */}
                <div className="xl:col-span-1 space-y-6">
                  {/* Aplicar aos usuários - apenas ao editar */}
                  {branch && (
                    <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-5 border border-green-200 dark:border-green-800">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="p-2 bg-green-100 dark:bg-green-900/40 rounded-lg">
                          <Shield className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </div>
                        <h4 className="font-semibold text-gray-900 dark:text-white">Aplicar Horários</h4>
                      </div>

                      <div className="space-y-4">
                        <div className="flex items-start space-x-3">
                          <input
                            type="checkbox"
                            id="applyToUsers"
                            checked={formData.applyToUsers}
                            onChange={(e) => setFormData(prev => ({ ...prev, applyToUsers: e.target.checked }))}
                            className="h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mt-1"
                            disabled={isLoading}
                          />
                          <div className="flex-1">
                            <Label htmlFor="applyToUsers" className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer">
                              Aplicar aos usuários existentes
                            </Label>
                            <p className="text-xs text-green-700 dark:text-green-300 mt-2 leading-relaxed">
                              Os horários configurados serão aplicados automaticamente a todos os usuários desta unidade.
                            </p>
                          </div>
                        </div>

                        {formData.applyToUsers && (
                          <div className="p-3 bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700 rounded-lg">
                            <div className="flex items-start gap-2">
                              <AlertCircle className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                              <p className="text-xs text-amber-700 dark:text-amber-300 leading-relaxed">
                                <strong>Atenção:</strong> Esta ação substituirá os horários individuais dos usuários.
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          </div>

          {/* Botões */}
          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-auto">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              animated={false}
              className="min-w-[100px] border-gray-300 text-gray-600 hover:border-gray-400 hover:text-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 focus:border-gray-300 focus:text-gray-600 active:border-gray-300 active:text-gray-600 transition-colors duration-200"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              animated={false}
              className="min-w-[120px] bg-gradient-to-r from-gray-400 to-gray-500 hover:from-gray-500 hover:to-gray-600 focus:from-gray-400 focus:to-gray-500 text-white border-0 transition-colors duration-200"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Salvando...
                </>
              ) : (
                branch ? "Atualizar Unidade" : "Criar Unidade"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default BranchFormModal;