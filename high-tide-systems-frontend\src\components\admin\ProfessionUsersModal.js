"use client";

import React, { useState, useEffect } from "react";
import { X, Loader2, Search, RefreshCw, User, CheckCircle, XCircle, Mail, Phone } from "lucide-react";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { useToast } from "@/contexts/ToastContext";
import { ModuleSelect } from "@/components/ui";

const ProfessionUsersModal = ({ isOpen, onClose, professionId }) => {
  const { toast_error } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [profession, setProfession] = useState(null);
  const [users, setUsers] = useState([]);
  const [search, setSearch] = useState("");
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");

  useEffect(() => {
    if (isOpen && professionId) {
      loadUsers();
    }
  }, [isOpen, professionId]);

  useEffect(() => {
    if (users.length > 0) {
      filterUsers();
    }
  }, [search, statusFilter, users]);

  const loadUsers = async () => {
    setIsLoading(true);
    try {
      const params = {};
      if (statusFilter) {
        params.active = statusFilter === "active";
      }

      const data = await professionsService.getProfessionUsers(professionId, params);
      setProfession(data.profession);
      setUsers(data.users);
      setFilteredUsers(data.users);
    } catch (error) {
      console.error("Erro ao carregar usuários da profissão:", error);
      toast_error("Erro ao carregar usuários da profissão");
    } finally {
      setIsLoading(false);
    }
  };

  const filterUsers = () => {
    let filtered = [...users];

    // Filtrar por termo de busca
    if (search.trim() !== "") {
      const searchLower = search.toLowerCase();
      filtered = filtered.filter(
        (user) =>
          user.fullName.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.login.toLowerCase().includes(searchLower)
      );
    }

    // Filtrar por status
    if (statusFilter === "active") {
      filtered = filtered.filter((user) => user.active);
    } else if (statusFilter === "inactive") {
      filtered = filtered.filter((user) => !user.active);
    }

    setFilteredUsers(filtered);
  };

  const handleSearch = (e) => {
    setSearch(e.target.value);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    loadUsers();
  };

  const handleResetFilters = () => {
    setSearch("");
    setStatusFilter("");
    loadUsers();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11050] flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="fixed left-[50%] top-[50%] z-[11050] grid w-full translate-x-[-50%] translate-y-[-50%] gap-4 border-2 border-gray-300 dark:border-gray-600 bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-xl max-w-4xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center pb-4 border-b-2 border-gray-400 dark:border-gray-500">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Usuários da Profissão
            </h2>
            {profession && (
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {profession.name}
              </p>
            )}
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X size={20} className="text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="">
          {/* Filtros */}
          <div className="mb-4 flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
              <input
                type="text"
                placeholder="Buscar por nome, email ou login..."
                value={search}
                onChange={handleSearch}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 dark:focus:ring-slate-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              />
            </div>

            <div className="flex-shrink-0 w-full md:w-40">
              <ModuleSelect
                moduleColor="admin"
                value={statusFilter}
                onChange={(e) => handleStatusFilterChange(e.target.value)}
                placeholder="Status"
              >
                <option value="">Todos os status</option>
                <option value="active">Ativos</option>
                <option value="inactive">Inativos</option>
              </ModuleSelect>
            </div>

            <button
              onClick={handleResetFilters}
              className="flex-shrink-0 flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
            >
              <RefreshCw size={16} />
              <span>Limpar</span>
            </button>
          </div>

          {/* Lista de usuários */}
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 size={32} className="animate-spin text-primary-500 dark:text-primary-400" />
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              Nenhum usuário encontrado com esta profissão.
            </div>
          ) : (
            <div className="border border-gray-300 dark:border-gray-600 rounded-lg">
              <table className="w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Usuário
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Contato
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Empresa
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-4 py-4">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 bg-slate-100 dark:bg-slate-900/30 text-slate-600 dark:text-slate-400 rounded-full flex items-center justify-center">
                            <User size={20} />
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {user.fullName}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {user.login}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="flex flex-col space-y-1">
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Mail size={14} className="mr-1 flex-shrink-0" />
                            <span className="truncate">{user.email}</span>
                          </div>
                          {user.phone && (
                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                              <Phone size={14} className="mr-1 flex-shrink-0" />
                              <span>{user.phone}</span>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <div className="text-sm text-gray-900 dark:text-gray-100 truncate">
                          {user.company?.name || "N/A"}
                        </div>
                      </td>
                      <td className="px-4 py-4">
                        <span
                          className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                            user.active
                              ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                              : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                          }`}
                        >
                          {user.active ? (
                            <>
                              <CheckCircle size={12} />
                              <span>Ativo</span>
                            </>
                          ) : (
                            <>
                              <XCircle size={12} />
                              <span>Inativo</span>
                            </>
                          )}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfessionUsersModal;
