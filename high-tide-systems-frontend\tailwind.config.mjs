// tailwind.config.js
/** @type {import('tailwindcss').Config} */
export default {
	darkMode: ["class"],
	content: [
		"./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			colors: {
				// Cores primária e secundária existentes
				primary: {
					'50': '#FFF7ED',
					'100': '#FFE4CC',
					'200': '#FFD5A8',
					'300': '#FFC285',
					'400': '#FFB366',
					'500': '#FF9933',
					'600': '#FF7F00',
					'700': '#CC6600',
					'800': '#994C00',
					'900': '#663300',
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					'50': '#F5F7FF',
					'100': '#ECF0FF',
					'200': '#D9E1FF',
					'300': '#B3C2FF',
					'400': '#8C9EFF',
					'500': '#667AFF',
					'600': '#4D5FCC',
					'700': '#334499',
					'800': '#1A2A66',
					'900': '#000F33',
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				// Aqui definimos cores específicas para cada módulo
				module: {
					// Módulo Admin (tons de cinza/gray)
					admin: {
						border: '#9ca3af', // gray-400
						'border-dark': '#6b7280', // gray-500 para dark mode
						bg: '#f9fafb', // gray-50
						'bg-dark': '#374151', // gray-700 para dark mode
						icon: '#6b7280', // gray-500
						'icon-dark': '#9ca3af', // gray-400 para dark mode
						text: '#6b7280', // gray-500
						'text-dark': '#d1d5db', // gray-300 para dark mode
						hover: '#f3f4f6', // gray-100
						'hover-dark': '#4b5563', // gray-600 para dark mode
					},
					// Módulo Financeiro (tons de verde/emerald)
					financial: {
						border: '#34d399', // emerald-400
						'border-dark': '#10b981', // emerald-500 para dark mode
						bg: '#ecfdf5', // emerald-50
						'bg-dark': '#064e3b', // emerald-900 para dark mode
						icon: '#059669', // emerald-600
						'icon-dark': '#6ee7b7', // emerald-300 para dark mode
						text: '#065f46', // emerald-800
						'text-dark': '#d1fae5', // emerald-100 para dark mode
						hover: '#a7f3d0', // emerald-200
						'hover-dark': '#065f46', // emerald-800 para dark mode
					},
					// Módulo RH (tons de vermelho/red)
					hr: {
						border: '#f87171', // red-400
						'border-dark': '#ef4444', // red-500 para dark mode
						bg: '#fee2e2', // red-100
						'bg-dark': '#7f1d1d', // red-900 para dark mode
						icon: '#dc2626', // red-600
						'icon-dark': '#fca5a5', // red-300 para dark mode
						text: '#991b1b', // red-800
						'text-dark': '#fee2e2', // red-100 para dark mode
						hover: '#fecaca', // red-200
						'hover-dark': '#991b1b', // red-800 para dark mode
					},
					// Módulo Pessoas (tons de laranja/orange)
					people: {
						border: '#fb923c', // orange-400
						'border-dark': '#f97316', // orange-500 para dark mode
						bg: '#ffedd5', // orange-100
						'bg-dark': '#7c2d12', // orange-900 para dark mode
						icon: '#ea580c', // orange-600
						'icon-dark': '#fdba74', // orange-300 para dark mode
						text: '#9a3412', // orange-800
						'text-dark': '#ffedd5', // orange-100 para dark mode
						hover: '#fdba74', // orange-300
						'hover-dark': '#9a3412', // orange-800 para dark mode
					},
					// Módulo Agendamento (tons de violeta/purple)
					scheduler: {
						border: '#c084fc', // violet-400
						'border-dark': '#a855f7', // violet-500 para dark mode
						bg: '#f3e8ff', // violet-100
						'bg-dark': '#581c87', // violet-900 para dark mode
						icon: '#9333ea', // violet-600
						'icon-dark': '#d8b4fe', // violet-300 para dark mode
						text: '#6b21a8', // violet-800
						'text-dark': '#f3e8ff', // violet-100 para dark mode
						hover: '#a78bfa', // violet-300
						'hover-dark': '#6b21a8', // violet-800 para dark mode
					},
					// Módulo Chat (tons de azul/blue)
					chat: {
						border: '#60a5fa', // blue-400
						'border-dark': '#3b82f6', // blue-500 para dark mode
						bg: '#eff6ff', // blue-100
						'bg-dark': '#1e3a8a', // blue-900 para dark mode
						icon: '#2563eb', // blue-600
						'icon-dark': '#93c5fd', // blue-300 para dark mode
						text: '#1e40af', // blue-800
						'text-dark': '#eff6ff', // blue-100 para dark mode
						hover: '#bfdbfe', // blue-200
						'hover-dark': '#1e40af', // blue-800 para dark mode
					},
					// Módulo ABA+ (tons de ciano/teal)
					abaplus: {
						border: '#2dd4bf', // teal-400
						'border-dark': '#14b8a6', // teal-500 para dark mode
						bg: '#f0fdfa', // teal-50
						'bg-dark': '#134e4a', // teal-900 para dark mode
						icon: '#0d9488', // teal-600
						'icon-dark': '#5eead4', // teal-300 para dark mode
						text: '#115e59', // teal-800
						'text-dark': '#ccfbf1', // teal-100 para dark mode
						hover: '#99f6e4', // teal-200
						'hover-dark': '#115e59', // teal-800 para dark mode
					},
					// Dashboard (tons de azul/blue)
					dashboard: {
						border: '#60a5fa', // blue-400
						'border-dark': '#3b82f6', // blue-500 para dark mode
						bg: '#eff6ff', // blue-50
						'bg-dark': '#1e3a8a', // blue-900 para dark mode
						icon: '#2563eb', // blue-600
						'icon-dark': '#93c5fd', // blue-300 para dark mode
						text: '#1e40af', // blue-800
						'text-dark': '#dbeafe', // blue-100 para dark mode
						hover: '#bfdbfe', // blue-200
						'hover-dark': '#1e40af', // blue-800 para dark mode
					}
				},

				appointmentStatus: {
					PENDING: {
					  border: '#FCD34D', // amber-300
					  'border-dark': '#F59E0B', // amber-500 para dark mode
					  bg: '#FFFBEB', // amber-50
					  'bg-dark': '#78350F', // amber-900 para dark mode
					  icon: '#D97706', // amber-600
					  'icon-dark': '#FCD34D', // amber-300 para dark mode
					  text: '#92400E', // amber-800
					  'text-dark': '#FEF3C7', // amber-100 para dark mode
					  hover: '#FDE68A', // amber-100
					  'hover-dark': '#92400E' // amber-800 para dark mode
					},
					CONFIRMED: {
					  border: '#6EE7B7', // emerald-300
					  'border-dark': '#10B981', // emerald-500 para dark mode
					  bg: '#ECFDF5', // emerald-50
					  'bg-dark': '#064E3B', // emerald-900 para dark mode
					  icon: '#059669', // emerald-600
					  'icon-dark': '#6EE7B7', // emerald-300 para dark mode
					  text: '#065F46', // emerald-800
					  'text-dark': '#D1FAE5', // emerald-100 para dark mode
					  hover: '#A7F3D0', // emerald-100
					  'hover-dark': '#065F46' // emerald-800 para dark mode
					},
					CANCELLED: {
					  border: '#FCA5A5', // rose-300
					  'border-dark': '#F43F5E', // rose-500 para dark mode
					  bg: '#FEF2F2', // rose-50
					  'bg-dark': '#881337', // rose-900 para dark mode
					  icon: '#E11D48', // rose-600
					  'icon-dark': '#FDA4AF', // rose-300 para dark mode
					  text: '#9F1239', // rose-800
					  'text-dark': '#FFE4E6', // rose-100 para dark mode
					  hover: '#FECDD3', // rose-100
					  'hover-dark': '#9F1239' // rose-800 para dark mode
					},
					COMPLETED: {
					  border: '#93C5FD', // blue-300
					  'border-dark': '#3B82F6', // blue-500 para dark mode
					  bg: '#EFF6FF', // blue-50
					  'bg-dark': '#1E3A8A', // blue-900 para dark mode
					  icon: '#2563EB', // blue-600
					  'icon-dark': '#93C5FD', // blue-300 para dark mode
					  text: '#1E40AF', // blue-800
					  'text-dark': '#DBEAFE', // blue-100 para dark mode
					  hover: '#BFDBFE', // blue-100
					  'hover-dark': '#1E40AF' // blue-800 para dark mode
					},
					NO_SHOW: {
					  border: '#FCD34D', // amber-300
					  'border-dark': '#F59E0B', // amber-500 para dark mode
					  bg: '#FFFBEB', // amber-50
					  'bg-dark': '#78350F', // amber-900 para dark mode
					  icon: '#D97706', // amber-600
					  'icon-dark': '#FCD34D', // amber-300 para dark mode
					  text: '#92400E', // amber-800
					  'text-dark': '#FEF3C7', // amber-100 para dark mode
					  hover: '#FDE68A', // amber-200
					  'hover-dark': '#92400E' // amber-800 para dark mode
					}
				  },
		  // Resto das cores permanece o mesmo
		  neutral: {
					'50': '#F9FAFB',
					'100': '#F3F4F6',
					'200': '#E5E7EB',
					'300': '#D1D5DB',
					'400': '#9CA3AF',
					'500': '#6B7280',
					'600': '#4B5563',
					'700': '#374151',
					'800': '#1F2937',
					'900': '#111827'
				},
				success: {
					'50': '#ECFDF5',
					'500': '#10B981',
					'700': '#047857'
				},
				warning: {
					'50': '#FFFBEB',
					'500': '#F59E0B',
					'700': '#B45309'
				},
				error: {
					'50': '#FEF2F2',
					'500': '#EF4444',
					'700': '#B91C1C'
				},
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				chart: {
					'1': 'hsl(var(--chart-1))',
					'2': 'hsl(var(--chart-2))',
					'3': 'hsl(var(--chart-3))',
					'4': 'hsl(var(--chart-4))',
					'5': 'hsl(var(--chart-5))'
				}
			},
			boxShadow: {
				soft: '0 2px 15px 0 rgba(0, 0, 0, 0.05)',
				medium: '0 4px 20px 0 rgba(0, 0, 0, 0.1)',
				hard: '0 8px 30px 0 rgba(0, 0, 0, 0.15)',
				'soft-dark': '0 2px 15px 0 rgba(0, 0, 0, 0.3)',
				'medium-dark': '0 4px 20px 0 rgba(0, 0, 0, 0.5)',
				'hard-dark': '0 8px 30px 0 rgba(0, 0, 0, 0.7)'
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			}
		}
	},
	safelist: [
		// Adicione aqui classes que precisam ser incluídas mesmo sem serem detectadas
		// Classes para modo claro
		'border-module-admin-border',
		'bg-module-admin-bg',
		'text-module-admin-icon',
		'text-module-admin-text',
		'hover:bg-module-admin-hover',

		'border-module-financial-border',
		'bg-module-financial-bg',
		'text-module-financial-icon',
		'text-module-financial-text',
		'hover:bg-module-financial-hover',

		'border-module-hr-border',
		'bg-module-hr-bg',
		'text-module-hr-icon',
		'text-module-hr-text',
		'hover:bg-module-hr-hover',

		'border-module-people-border',
		'bg-module-people-bg',
		'text-module-people-icon',
		'text-module-people-text',
		'hover:bg-module-people-hover',

		'border-module-scheduler-border',
		'bg-module-scheduler-bg',
		'text-module-scheduler-icon',
		'text-module-scheduler-text',
		'hover:bg-module-scheduler-hover',

		// Classes para modo escuro
		'dark:border-module-admin-border-dark',
		'dark:bg-module-admin-bg-dark',
		'dark:text-module-admin-icon-dark',
		'dark:text-module-admin-text-dark',
		'dark:hover:bg-module-admin-hover-dark',

		'dark:border-module-financial-border-dark',
		'dark:bg-module-financial-bg-dark',
		'dark:text-module-financial-icon-dark',
		'dark:text-module-financial-text-dark',
		'dark:hover:bg-module-financial-hover-dark',

		'dark:border-module-hr-border-dark',
		'dark:bg-module-hr-bg-dark',
		'dark:text-module-hr-icon-dark',
		'dark:text-module-hr-text-dark',
		'dark:hover:bg-module-hr-hover-dark',

		'dark:border-module-people-border-dark',
		'dark:bg-module-people-bg-dark',
		'dark:text-module-people-icon-dark',
		'dark:text-module-people-text-dark',
		'dark:hover:bg-module-people-hover-dark',

		'dark:border-module-scheduler-border-dark',
		'dark:bg-module-scheduler-bg-dark',
		'dark:text-module-scheduler-icon-dark',
		'dark:text-module-scheduler-text-dark',
		'dark:hover:bg-module-scheduler-hover-dark',

		// Módulo Chat - modo claro
		'border-module-chat-border',
		'bg-module-chat-bg',
		'text-module-chat-icon',
		'text-module-chat-text',
		'hover:bg-module-chat-hover',

		// Módulo Chat - modo escuro
		'dark:border-module-chat-border-dark',
		'dark:bg-module-chat-bg-dark',
		'dark:text-module-chat-icon-dark',
		'dark:text-module-chat-text-dark',
		'dark:hover:bg-module-chat-hover-dark',

		// Módulo ABA+ - modo claro
		'border-module-abaplus-border',
		'bg-module-abaplus-bg',
		'text-module-abaplus-icon',
		'text-module-abaplus-text',
		'hover:bg-module-abaplus-hover',

		// Módulo ABA+ - modo escuro
		'dark:border-module-abaplus-border-dark',
		'dark:bg-module-abaplus-bg-dark',
		'dark:text-module-abaplus-icon-dark',
		'dark:text-module-abaplus-text-dark',
		'dark:hover:bg-module-abaplus-hover-dark',

		// Dashboard - modo claro
		'border-module-dashboard-border',
		'bg-module-dashboard-bg',
		'text-module-dashboard-icon',
		'text-module-dashboard-text',
		'hover:bg-module-dashboard-hover',

		// Dashboard - modo escuro
		'dark:border-module-dashboard-border-dark',
		'dark:bg-module-dashboard-bg-dark',
		'dark:text-module-dashboard-icon-dark',
		'dark:text-module-dashboard-text-dark',
		'dark:hover:bg-module-dashboard-hover-dark',

		// Status de agendamento - modo claro
		'border-appointmentStatus-PENDING-border',
		'bg-appointmentStatus-PENDING-bg',
		'text-appointmentStatus-PENDING-icon',
		'text-appointmentStatus-PENDING-text',
		'hover:bg-appointmentStatus-PENDING-hover',

		'border-appointmentStatus-CONFIRMED-border',
		'bg-appointmentStatus-CONFIRMED-bg',
		'text-appointmentStatus-CONFIRMED-icon',
		'text-appointmentStatus-CONFIRMED-text',
		'hover:bg-appointmentStatus-CONFIRMED-hover',

		'border-appointmentStatus-CANCELLED-border',
		'bg-appointmentStatus-CANCELLED-bg',
		'text-appointmentStatus-CANCELLED-icon',
		'text-appointmentStatus-CANCELLED-text',
		'hover:bg-appointmentStatus-CANCELLED-hover',

		'border-appointmentStatus-COMPLETED-border',
		'bg-appointmentStatus-COMPLETED-bg',
		'text-appointmentStatus-COMPLETED-icon',
		'text-appointmentStatus-COMPLETED-text',
		'hover:bg-appointmentStatus-COMPLETED-hover',

		'border-appointmentStatus-NO_SHOW-border',
		'bg-appointmentStatus-NO_SHOW-bg',
		'text-appointmentStatus-NO_SHOW-icon',
		'text-appointmentStatus-NO_SHOW-text',
		'hover:bg-appointmentStatus-NO_SHOW-hover',

		// Status de agendamento - modo escuro
		'dark:border-appointmentStatus-PENDING-border-dark',
		'dark:bg-appointmentStatus-PENDING-bg-dark',
		'dark:text-appointmentStatus-PENDING-icon-dark',
		'dark:text-appointmentStatus-PENDING-text-dark',
		'dark:hover:bg-appointmentStatus-PENDING-hover-dark',

		'dark:border-appointmentStatus-CONFIRMED-border-dark',
		'dark:bg-appointmentStatus-CONFIRMED-bg-dark',
		'dark:text-appointmentStatus-CONFIRMED-icon-dark',
		'dark:text-appointmentStatus-CONFIRMED-text-dark',
		'dark:hover:bg-appointmentStatus-CONFIRMED-hover-dark',

		'dark:border-appointmentStatus-CANCELLED-border-dark',
		'dark:bg-appointmentStatus-CANCELLED-bg-dark',
		'dark:text-appointmentStatus-CANCELLED-icon-dark',
		'dark:text-appointmentStatus-CANCELLED-text-dark',
		'dark:hover:bg-appointmentStatus-CANCELLED-hover-dark',

		'dark:border-appointmentStatus-COMPLETED-border-dark',
		'dark:bg-appointmentStatus-COMPLETED-bg-dark',
		'dark:text-appointmentStatus-COMPLETED-icon-dark',
		'dark:text-appointmentStatus-COMPLETED-text-dark',
		'dark:hover:bg-appointmentStatus-COMPLETED-hover-dark',

		'dark:border-appointmentStatus-NO_SHOW-border-dark',
		'dark:bg-appointmentStatus-NO_SHOW-bg-dark',
		'dark:text-appointmentStatus-NO_SHOW-icon-dark',
		'dark:text-appointmentStatus-NO_SHOW-text-dark',
		'dark:hover:bg-appointmentStatus-NO_SHOW-hover-dark'
	],
	plugins: [require("tailwindcss-animate")],
};