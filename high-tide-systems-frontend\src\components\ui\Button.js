/**
 * Componente de Botão
 *
 * Este arquivo contém um componente de botão consistente para todo o sistema.
 * O componente segue o sistema de design definido.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

/**
 * Componente de Botão
 *
 * @param {Object} props
 * @param {string} props.type - Tipo do botão (button, submit, reset)
 * @param {Function} props.onClick - Função para lidar com cliques
 * @param {React.ReactNode} props.children - Conte<PERSON><PERSON> do botão
 * @param {string} props.variant - Variante do botão (primary, secondary, outline, ghost, link, danger)
 * @param {string} props.size - Tamanho do botão (sm, md, lg)
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {boolean} props.isLoading - Se o botão está em estado de carregamento
 * @param {boolean} props.disabled - Se o botão está desabilitado
 * @param {string} props.className - Classes adicionais
 * @param {React.ReactNode} props.leftIcon - Ícone à esquerda
 * @param {React.ReactNode} props.rightIcon - Ícone à direita
 * @param {boolean} props.fullWidth - Se o botão deve ocupar toda a largura disponível
 * @param {boolean} props.animated - Se o botão deve ter animações
 */
const Button = React.forwardRef(({
  type = 'button',
  onClick,
  children,
  variant = 'primary',
  size = 'md',
  moduleColor = 'default',
  isLoading = false,
  disabled = false,
  className = '',
  leftIcon,
  rightIcon,
  fullWidth = false,
  animated = true,
  ...props
}, ref) => {
  // Mapeamento de cores por módulo e variante
  const moduleVariants = {
    default: {
      primary: 'bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white',
      secondary: 'bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600',
      outline: 'bg-transparent border border-primary-500 dark:border-primary-400 text-primary-500 dark:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20',
      ghost: 'bg-transparent text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-gray-800',
      link: 'bg-transparent text-primary-500 dark:text-primary-400 hover:underline underline-offset-4',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
    },
    people: {
      primary: 'bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700',
      secondary: 'bg-white dark:bg-gray-700 border border-orange-200 dark:border-orange-800/30 text-orange-700 dark:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20',
      outline: 'bg-transparent border border-orange-500 dark:border-orange-400 text-orange-500 dark:text-orange-400 hover:bg-orange-50 dark:hover:bg-orange-900/20',
      ghost: 'bg-transparent text-orange-700 dark:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20',
      link: 'bg-transparent text-orange-500 dark:text-orange-400 hover:underline underline-offset-4',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
    },
    scheduler: {
      primary: 'bg-gradient-to-r from-violet-500 to-purple-500 dark:from-violet-600 dark:to-purple-600 text-white hover:from-violet-600 hover:to-purple-600 dark:hover:from-violet-700 dark:hover:to-purple-700',
      secondary: 'bg-white dark:bg-gray-700 border border-violet-200 dark:border-violet-800/30 text-violet-700 dark:text-violet-300 hover:bg-violet-50 dark:hover:bg-violet-900/20',
      outline: 'bg-transparent border border-violet-500 dark:border-violet-400 text-violet-500 dark:text-violet-400 hover:bg-violet-50 dark:hover:bg-violet-900/20',
      ghost: 'bg-transparent text-violet-700 dark:text-violet-300 hover:bg-violet-50 dark:hover:bg-violet-900/20',
      link: 'bg-transparent text-violet-500 dark:text-violet-400 hover:underline underline-offset-4',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
    },
    admin: {
      primary: 'bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700',
      secondary: 'bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600/30 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/20',
      outline: 'bg-transparent border border-gray-400 dark:border-gray-500 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-800/20',
      ghost: 'bg-transparent text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/20',
      link: 'bg-transparent text-gray-500 dark:text-gray-400 hover:underline underline-offset-4',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
    },
    financial: {
      primary: 'bg-gradient-to-r from-emerald-500 to-green-500 dark:from-emerald-600 dark:to-green-600 text-white hover:from-emerald-600 hover:to-green-600 dark:hover:from-emerald-700 dark:hover:to-green-700',
      secondary: 'bg-white dark:bg-gray-700 border border-emerald-200 dark:border-emerald-800/30 text-emerald-700 dark:text-emerald-300 hover:bg-emerald-50 dark:hover:bg-emerald-900/20',
      outline: 'bg-transparent border border-emerald-500 dark:border-emerald-400 text-emerald-500 dark:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20',
      ghost: 'bg-transparent text-emerald-700 dark:text-emerald-300 hover:bg-emerald-50 dark:hover:bg-emerald-900/20',
      link: 'bg-transparent text-emerald-500 dark:text-emerald-400 hover:underline underline-offset-4',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
    },
  };

  // Obter as classes de estilo para o módulo e variante
  const variantClasses = moduleVariants[moduleColor]?.[variant] || moduleVariants.default[variant];

  // Classes para tamanhos
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  // Classes base para todos os botões
  const baseClasses = "font-medium transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-900 flex items-center justify-center gap-2 rounded-lg";

  // Classes para estado desabilitado
  const disabledClasses = (disabled || isLoading) ? 'opacity-50 cursor-not-allowed' : '';

  // Classes para largura total
  const widthClasses = fullWidth ? 'w-full' : '';

  // Classes para sombra (apenas para botões primários)
  const shadowClasses = variant === 'primary' ? 'shadow-sm' : '';

  // Componente base do botão
  const ButtonComponent = animated ? motion.button : 'button';

  // Props de animação
  const animationProps = animated ? {
    whileHover: { scale: 1.02 },
    whileTap: { scale: 0.98 },
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 17
    }
  } : {};

  return (
    <ButtonComponent
      ref={ref}
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={cn(
        baseClasses,
        variantClasses,
        sizeClasses[size] || sizeClasses.md,
        disabledClasses,
        shadowClasses,
        widthClasses,
        className
      )}
      {...animationProps}
      {...props}
    >
      {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
      {!isLoading && leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
      {children}
      {!isLoading && rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
    </ButtonComponent>
  );
});

Button.displayName = 'Button';

export default Button;
