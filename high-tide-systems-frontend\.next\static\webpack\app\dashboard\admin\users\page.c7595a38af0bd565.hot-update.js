"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/users/page",{

/***/ "(app-pages-browser)/./src/components/users/UserModulesTab.js":
/*!************************************************!*\
  !*** ./src/components/users/UserModulesTab.js ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckSquare,DollarSign,Settings,UserCheck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n\n\n\nconst UserModulesTab = (param)=>{\n    let { user, savedUserId, selectedModules, setSelectedModules, isAdmin, isLoading } = param;\n    const toggleModule = (moduleId)=>{\n        if (moduleId === \"ADMIN\" && !isAdmin) return;\n        if (selectedModules.includes(moduleId)) {\n            setSelectedModules((prev)=>prev.filter((m)=>m !== moduleId));\n        } else {\n            setSelectedModules((prev)=>[\n                    ...prev,\n                    moduleId\n                ]);\n        }\n    };\n    // Função para alternar todos os módulos (igual nas permissões)\n    const toggleAllModules = ()=>{\n        const allModules = [\n            \"ADMIN\",\n            \"SCHEDULING\",\n            \"PEOPLE\"\n        ];\n        const availableModules = isAdmin ? allModules : allModules.filter((m)=>m !== \"ADMIN\");\n        // Verificar se todos os módulos disponíveis estão selecionados\n        const allSelected = availableModules.every((moduleId)=>selectedModules.includes(moduleId));\n        if (allSelected) {\n            // Desmarcar todos (manter apenas BASIC que é obrigatório)\n            setSelectedModules([\n                \"BASIC\"\n            ]);\n        } else {\n            // Selecionar todos os disponíveis (incluindo BASIC)\n            setSelectedModules([\n                \"BASIC\",\n                ...availableModules\n            ]);\n        }\n    };\n    // Verificar se todos os módulos disponíveis estão selecionados\n    const allModules = [\n        \"ADMIN\",\n        \"SCHEDULING\",\n        \"PEOPLE\"\n    ];\n    const availableModules = isAdmin ? allModules : allModules.filter((m)=>m !== \"ADMIN\");\n    const allSelected = availableModules.every((moduleId)=>selectedModules.includes(moduleId));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3\",\n                        children: (user === null || user === void 0 ? void 0 : user.fullName) || (savedUserId ? \"Novo Usuário\" : \"\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-neutral-600 dark:text-gray-300\",\n                                children: \"Selecione os m\\xf3dulos que este usu\\xe1rio ter\\xe1 acesso:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: toggleAllModules,\n                                className: \"px-3 py-1 rounded text-sm font-medium \".concat(allSelected ? \"bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700\" : \"bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700\"),\n                                disabled: isLoading,\n                                children: allSelected ? \"Desmarcar todos\" : \"Selecionar todos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border bg-white dark:bg-white/20 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: true,\n                                        onChange: ()=>{},\n                                        disabled: true,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"B\\xe1sico\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Acesso b\\xe1sico ao sistema, visualiza\\xe7\\xe3o limitada\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"M\\xf3dulo obrigat\\xf3rio para todos os usu\\xe1rios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"ADMIN\") ? \"bg-gray-200 dark:bg-gray-800/60 text-gray-800 dark:text-gray-300 border-gray-300 dark:border-gray-700/60\" : \"border-neutral-200 dark:border-gray-700\", \" \").concat(!isAdmin ? \"opacity-70\" : \"cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"ADMIN\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"ADMIN\"),\n                                        onChange: ()=>{},\n                                        disabled: !isAdmin || isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"ADMIN\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: [\n                                                        \"Administra\\xe7\\xe3o\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded\",\n                                                            children: \"Acesso Administrativo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                            lineNumber: 129,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Acesso completo ao sistema, incluindo configura\\xe7\\xf5es e gerenciamento de usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        !isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Apenas administradores podem conceder este acesso\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"SCHEDULING\") ? \"bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"SCHEDULING\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"SCHEDULING\"),\n                                        onChange: ()=>{},\n                                        disabled: isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"SCHEDULING\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"Agendamento\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Gerenciamento de compromissos, reuni\\xf5es e aloca\\xe7\\xe3o de recursos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 rounded-lg border \".concat(selectedModules.includes(\"PEOPLE\") ? \"bg-orange-300 dark:bg-orange-700/60 text-orange-900 dark:text-orange-200 border-orange-400 dark:border-orange-600/70\" : \"border-neutral-200 dark:border-gray-700\", \" cursor-pointer hover:border-slate-300 dark:hover:border-slate-700\"),\n                        onClick: ()=>toggleModule(\"PEOPLE\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-start gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 mt-0.5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: selectedModules.includes(\"PEOPLE\"),\n                                        onChange: ()=>{},\n                                        disabled: isLoading,\n                                        className: \"h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckSquare_DollarSign_Settings_UserCheck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5 \".concat(selectedModules.includes(\"PEOPLE\") ? \"\" : \"text-neutral-500 dark:text-gray-400\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                    className: \"font-medium text-neutral-800 dark:text-white\",\n                                                    children: \"Pessoas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-1 text-sm text-neutral-600 dark:text-gray-300\",\n                                            children: \"Cadastro e gerenciamento de pacientes, clientes e conv\\xeanios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                        lineNumber: 183,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\users\\\\UserModulesTab.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n_c = UserModulesTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UserModulesTab);\nvar _c;\n$RefreshReg$(_c, \"UserModulesTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/UserModulesTab.js\n"));

/***/ })

});