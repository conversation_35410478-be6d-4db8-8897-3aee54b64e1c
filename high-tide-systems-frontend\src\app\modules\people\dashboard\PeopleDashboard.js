"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContaine<PERSON>,
  <PERSON>
} from "recharts";
import {
  Users,
  UserPlus,
  CreditCard,
  Shield,
  TrendingUp,
  TrendingDown,
  User,
  Mail,
  Phone,
  Calendar,
  Clock,
  LayoutDashboard,
  Filter,
  RefreshCw,
  BarChart2,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  FileText
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { peopleDashboardService } from "../services/peopleDashboardService";
import { personsService } from "../services/personsService";
import { companyService } from "../../admin/services/companyService";
import { ModuleHeader, ModuleTable, ModuleFormGroup, ModuleSelect } from "@/components/ui";
import ExportMenu from "@/components/ui/ExportMenu";
import { useAuth } from "@/contexts/AuthContext";

// Cores para os gr<PERSON>ficos (padrão laranja)
const ORANGE_COLORS = ["#F97316", "#FB923C", "#FDBA74", "#FED7AA", "#FFF7ED"];
const INSURANCE_COLORS = ["#F97316", "#FB923C", "#FDBA74", "#FED7AA", "#FFF7ED"];
const LIMITS_COLORS = ["#DC2626", "#F97316", "#FBBF24", "#10B981", "#3B82F6"];

const PeopleDashboard = () => {
  // Estados para armazenar os dados
  const [dashboardData, setDashboardData] = useState({
    stats: {
      totalPersons: 0,
      activePersons: 0,
      totalClients: 0,
      activeClients: 0,
      totalInsurances: 0,
      totalDocuments: 0,
      totalCategories: 0
    },
    growth: {
      personsGrowth: 0,
      clientsGrowth: 0,
      insurancesGrowth: 0,
      documentsGrowth: 0
    },
    topClients: [],
    insuranceDistribution: [],
    mostUsedInsurances: [],
    insuranceLimits: [],
    highestLimitUsers: [],
    remainingLimits: [],
    recentPersons: [],
    recentDocuments: [],
    documentsByCategory: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [period, setPeriod] = useState("30dias");
  const [selectedCompany, setSelectedCompany] = useState(""); // Filtro por empresa
  const [companies, setCompanies] = useState([]);
  const [chartType, setChartType] = useState("bar"); // 'bar' ou 'pie'
  const [isExporting, setIsExporting] = useState(false);
  const { user } = useAuth();

  // Função para carregar os dados do dashboard
  const loadDashboardData = async (selectedPeriod = period, companyId = selectedCompany) => {
    setIsLoading(true);
    try {
      const params = { period: selectedPeriod };
      if (companyId) {
        params.companyId = companyId;
      }
      const data = await peopleDashboardService.getPeopleDashboardData(params);
      setDashboardData(data);
    } catch (error) {
      console.error("Erro ao carregar dados do dashboard:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para carregar as empresas para o filtro
  const loadCompanies = async () => {
    try {
      const companiesData = await companyService.getCompaniesForSelect();
      setCompanies(companiesData);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    }
  };

  // Função para alterar o período e atualizar os dados
  const handlePeriodChange = (e) => {
    const newPeriod = e.target.value;
    setPeriod(newPeriod);
    loadDashboardData(newPeriod, selectedCompany);
  };

  // Função para alterar a empresa e atualizar os dados
  const handleCompanyChange = (e) => {
    const newCompany = e.target.value;
    setSelectedCompany(newCompany);
    loadDashboardData(period, newCompany);
  };

  // Função para alternar o tipo de gráfico
  const toggleChartType = (type) => {
    setChartType(type);
  };

  // Função para exportar os dados do dashboard
  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      const params = { period };
      if (selectedCompany) {
        params.companyId = selectedCompany;
      }
      await peopleDashboardService.exportDashboardData(params, format);
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Carregar dados quando o componente montar
  useEffect(() => {
    loadCompanies();
    loadDashboardData();
  }, []);

  // Formatar data
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  // Componente personalizado para o tooltip dos gráficos
  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded shadow-md">
          <p className="font-medium text-gray-800 dark:text-gray-200">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Extrair dados do estado
  const { stats, growth, topClients, insuranceDistribution, mostUsedInsurances, insuranceLimits, highestLimitUsers, remainingLimits, recentPersons, recentDocuments, documentsByCategory } = dashboardData;

  // Log para debug
  console.log('Top Clients:', topClients);

  return (
    <div className="space-y-6">
      {/* Título e botão de exportação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <LayoutDashboard size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Dashboard de Pessoas
        </h1>

        <ExportMenu
          onExport={handleExport}
          isExporting={isExporting}
          disabled={isLoading}
          className="text-orange-600 dark:text-orange-400"
        />
      </div>

      {/* Filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-people-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Filtro por Empresa */}
          <div className="w-full md:w-64">
            <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-2">
              <Shield size={16} />
              <span className="text-sm font-medium">Empresa</span>
            </div>
            <div className="relative">
              <select
                id="company"
                name="company"
                value={selectedCompany}
                onChange={handleCompanyChange}
                className="w-full bg-gray-800 dark:bg-gray-800 text-white dark:text-white border border-gray-700 dark:border-gray-700 rounded-md py-2 px-3 appearance-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="">Todas as Empresas</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          {/* Filtro por Período */}
          <div className="w-full md:w-64">
            <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-2">
              <Calendar size={16} />
              <span className="text-sm font-medium">Período</span>
            </div>
            <div className="relative">
              <select
                id="period"
                name="period"
                value={period}
                onChange={handlePeriodChange}
                className="w-full bg-gray-800 dark:bg-gray-800 text-white dark:text-white border border-gray-700 dark:border-gray-700 rounded-md py-2 px-3 appearance-none focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              >
                <option value="7dias">Últimos 7 dias</option>
                <option value="30dias">Últimos 30 dias</option>
                <option value="3meses">Últimos 3 meses</option>
                <option value="6meses">Últimos 6 meses</option>
                <option value="1ano">Último ano</option>
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>

          {/* Chart type toggle */}
          <div className="flex items-end">
            <div className="flex border border-gray-700 dark:border-gray-700 rounded-md overflow-hidden">
              <button
                className={`p-2 ${
                  chartType === "bar"
                  ? "bg-orange-500 text-white"
                  : "bg-gray-800 dark:bg-gray-800 text-gray-400 dark:text-gray-400"
                }`}
                onClick={() => toggleChartType("bar")}
                title="Gráfico de barras"
              >
                <BarChart2 className="h-5 w-5" />
              </button>
              <button
                className={`p-2 ${
                  chartType === "pie"
                  ? "bg-orange-500 text-white"
                  : "bg-gray-800 dark:bg-gray-800 text-gray-400 dark:text-gray-400"
                }`}
                onClick={() => toggleChartType("pie")}
                title="Gráfico de pizza"
              >
                <PieChartIcon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {/* Pacientes Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Pacientes
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.totalPersons}
              </h3>
            </div>
            <div className="h-12 w-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
              <Users className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span
              className={`flex items-center ${
                growth.personsGrowth >= 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {growth.personsGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {Math.abs(growth.personsGrowth)}%
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 ml-2">
              desde o período anterior
            </span>
          </div>
        </div>

        {/* Pacientes Ativos Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Pacientes Ativos
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.activePersons}
              </h3>
            </div>
            <div className="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
              <User className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span className="text-neutral-500 dark:text-neutral-400">
              {stats.totalPersons > 0
                ? `${Math.round((stats.activePersons / stats.totalPersons) * 100)}% do total`
                : "0% do total"}
            </span>
          </div>
        </div>

        {/* Clientes Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Clientes
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.totalClients}
              </h3>
            </div>
            <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
              <UserPlus className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span
              className={`flex items-center ${
                growth.clientsGrowth >= 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {growth.clientsGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {Math.abs(growth.clientsGrowth)}%
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 ml-2">
              desde o período anterior
            </span>
          </div>
        </div>

        {/* Clientes Ativos Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Clientes Ativos
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.activeClients}
              </h3>
            </div>
            <div className="h-12 w-12 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center">
              <UserPlus className="h-6 w-6 text-teal-600 dark:text-teal-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span className="text-neutral-500 dark:text-neutral-400">
              {stats.totalClients > 0
                ? `${Math.round((stats.activeClients / stats.totalClients) * 100)}% do total`
                : "0% do total"}
            </span>
          </div>
        </div>

        {/* Convênios Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Convênios
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.totalInsurances}
              </h3>
            </div>
            <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
              <CreditCard className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span
              className={`flex items-center ${
                growth.insurancesGrowth >= 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {growth.insurancesGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {Math.abs(growth.insurancesGrowth)}%
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 ml-2">
              desde o período anterior
            </span>
          </div>
        </div>

        {/* Documentos Card */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex justify-between">
            <div>
              <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                Documentos
              </p>
              <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                {stats.totalDocuments}
              </h3>
            </div>
            <div className="h-12 w-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center">
              <FileText className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
            </div>
          </div>
          <div className="mt-4 flex items-center text-xs">
            <span
              className={`flex items-center ${
                growth.documentsGrowth >= 0
                  ? "text-green-600 dark:text-green-400"
                  : "text-red-600 dark:text-red-400"
              }`}
            >
              {growth.documentsGrowth >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {Math.abs(growth.documentsGrowth)}%
            </span>
            <span className="text-neutral-500 dark:text-neutral-400 ml-2">
              desde o período anterior
            </span>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" id="gradedashboards">
        {/* Most Used Insurances Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Convênios Mais Utilizados
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={mostUsedInsurances}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="value"
                    labelLine={true}
                    label={({ name }) => name}
                  >
                    {mostUsedInsurances.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              ) : (
                <BarChart
                  data={mostUsedInsurances}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" name="Quantidade">
                    {mostUsedInsurances.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>

          {chartType === "pie" && (
            <div className="mt-4 grid grid-cols-1 gap-2">
              {mostUsedInsurances.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: ORANGE_COLORS[index % ORANGE_COLORS.length] }}
                    ></div>
                    <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={item.name}>
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Top Clients Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <UserPlus className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Clientes com Mais Pacientes
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'bar' ? (
                <BarChart
                  data={topClients}
                  margin={{ top: 5, right: 30, left: 20, bottom: 50 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                    angle={-45}
                    textAnchor="end"
                    height={60}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="count" name="Pacientes" fill="#f97316">
                    {topClients.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              ) : (
                <PieChart>
                  <Pie
                    data={topClients}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="count"
                    nameKey="name"
                    label={({ name }) => name}
                  >
                    {topClients.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              )}
            </ResponsiveContainer>
          </div>

          {chartType === "pie" && (
            <div className="mt-4 grid grid-cols-1 gap-2">
              {topClients.map((client, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: ORANGE_COLORS[index % ORANGE_COLORS.length] }}
                    ></div>
                    <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={client.name}>
                      {client.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                    {client.count}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Insurance Limits Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <Shield className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Limites de Convênio
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={insuranceLimits}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="value"
                    labelLine={true}
                    label={({ name }) => name}
                  >
                    {insuranceLimits.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={LIMITS_COLORS[index % LIMITS_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              ) : (
                <BarChart
                  data={insuranceLimits}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" name="Quantidade">
                    {insuranceLimits.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={LIMITS_COLORS[index % LIMITS_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>

          {chartType === "pie" && (
            <div className="mt-4 grid grid-cols-1 gap-2">
              {insuranceLimits.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: LIMITS_COLORS[index % LIMITS_COLORS.length] }}
                    ></div>
                    <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={item.name}>
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Highest Limit Users Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Pacientes com Maiores Limites
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={highestLimitUsers}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="value"
                    labelLine={true}
                    label={({ name }) => name}
                  >
                    {highestLimitUsers.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              ) : (
                <BarChart
                  data={highestLimitUsers}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" name="Limite Total">
                    {highestLimitUsers.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={ORANGE_COLORS[index % ORANGE_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>

          {chartType === "pie" && (
            <div className="mt-4 grid grid-cols-1 gap-2">
              {highestLimitUsers.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: ORANGE_COLORS[index % ORANGE_COLORS.length] }}
                    ></div>
                    <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={item.name}>
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Remaining Limits Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <TrendingDown className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Limites Restantes do Mês
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={remainingLimits}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="value"
                    labelLine={true}
                    label={({ name }) => name}
                  >
                    {remainingLimits.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={LIMITS_COLORS[index % LIMITS_COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              ) : (
                <BarChart
                  data={remainingLimits}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                  <XAxis
                    dataKey="name"
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <YAxis
                    tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="value" name="Limite Restante">
                    {remainingLimits.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={LIMITS_COLORS[index % LIMITS_COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>

          {chartType === "pie" && (
            <div className="mt-4 grid grid-cols-1 gap-2">
              {remainingLimits.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div
                      className="h-3 w-3 rounded-full mr-2"
                      style={{ backgroundColor: LIMITS_COLORS[index % LIMITS_COLORS.length] }}
                    ></div>
                    <span className="text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]" title={item.name}>
                      {item.name}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Documents by Category Chart */}
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
              <FileText className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
              Documentos por Categoria
            </h3>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'pie' ? (
                <PieChart>
                  <Pie
                    data={documentsByCategory}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={90}
                    fill="#8884d8"
                    paddingAngle={5}
                    dataKey="count"
                  >
                    {documentsByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                </PieChart>
              ) : (
                <BarChart data={documentsByCategory}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar dataKey="count" fill="#6366f1" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Recent Persons Table */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-orange-500 dark:text-orange-400" />
            Pacientes Recentes
          </h3>
        </div>
        <ModuleTable
          moduleColor="people"
          columns={[
            { header: 'Nome', field: 'fullName', width: '25%' },
            { header: 'Cliente', field: 'clientName', width: '25%' },
            { header: 'Contato', field: 'contact', width: '25%' },
            { header: 'Cadastro', field: 'createdAt', width: '15%' },
            { header: 'Relação', field: 'relationship', width: '10%' }
          ]}
          data={recentPersons}
          isLoading={isLoading}
          emptyMessage="Nenhum paciente encontrado"
          emptyIcon={<User size={24} />}
          tableId="recent-persons-table"
          defaultSortField="createdAt"
          defaultSortDirection="desc"
          renderRow={(person, _index, moduleColors, visibleColumns) => (
            <tr key={person.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('fullName') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden">
                      {person.profileImageUrl ? (
                        <img
                          src={personsService.getProfileImageUrl(person.id, person.profileImageUrl)}
                          alt={person.fullName}
                          className="h-10 w-10 rounded-full object-cover"
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {person.fullName}
                      </div>
                    </div>
                  </div>
                </td>
              )}

              {visibleColumns.includes('clientName') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                    <UserPlus className="h-4 w-4 mr-2 text-gray-400 dark:text-gray-500" />
                    {person.clientName}
                  </div>
                </td>
              )}

              {visibleColumns.includes('contact') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {person.email && (
                      <div className="flex items-center mb-1">
                        <Mail className="h-4 w-4 mr-2 text-gray-400 dark:text-gray-500" />
                        {person.email}
                      </div>
                    )}
                    {person.phone && (
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-2 text-gray-400 dark:text-gray-500" />
                        {person.phone}
                      </div>
                    )}
                  </div>
                </td>
              )}

              {visibleColumns.includes('createdAt') && (
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(person.createdAt)}
                </td>
              )}

              {visibleColumns.includes('relationship') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {person.relationship}
                  </div>
                </td>
              )}
            </tr>
          )}
        />
      </div>
    </div>
  );
};

export default PeopleDashboard;
