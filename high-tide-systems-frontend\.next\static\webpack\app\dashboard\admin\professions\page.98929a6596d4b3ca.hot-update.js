"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: 'Tem certeza que deseja excluir o grupo \"'.concat(group.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 638,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 644,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 652,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 702,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 785,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 701,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 839,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 879,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 908,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 909,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 901,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 917,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 931,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 938,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 929,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 962,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 957,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 956,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 979,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1013,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1012,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1024,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1027,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1023,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1022,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1039,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1043,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1037,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1054,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1055,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1053,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1051,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1071,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1069,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1068,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1082,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1080,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1106,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1092,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1091,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1116,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1123,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1139,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1132,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1115,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1114,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1010,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 952,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 951,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1163,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1158,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1178,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1201,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1200,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1212,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1215,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1211,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1210,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1228,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1226,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1225,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1240,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1239,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1246,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1237,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1256,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1257,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1255,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1254,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1274,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1266,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1265,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1290,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1299,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1289,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1288,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1198,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1154,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1330,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1355,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-profession\" ? \"Excluir Profissão\" : (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-group\" ? \"Excluir Grupo\" : \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1361,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 835,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"z2ueIfMvpFqwLSBIlnDUAH37acU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ })

});