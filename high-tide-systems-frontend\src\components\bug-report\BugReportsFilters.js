'use client';

import React, { useState, useEffect } from 'react';
import { Filter, RefreshCw, Search, Tag, AlertTriangle, Bug, Building, CheckCircle } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import MultiSelect from '@/components/ui/multi-select';
import { companyService } from '@/app/modules/admin/services/companyService';
import { useAuth } from '@/contexts/AuthContext';

const BugReportsFilters = ({ filters, onFiltersChange, onSearch }) => {
  const { user } = useAuth();
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  const statusOptions = [
    { value: 'OPEN', label: 'Aberto' },
    { value: 'IN_PROGRESS', label: 'Em Progresso' },
    { value: 'RESOLVED', label: 'Resolvido' },
    { value: 'CLOSED', label: 'Fechado' }
  ];

  const priorityOptions = [
    { value: 'CRITICAL', label: 'Crítica' },
    { value: 'HIGH', label: 'Alta' },
    { value: 'MEDIUM', label: 'Média' },
    { value: 'LOW', label: 'Baixa' }
  ];

  const categoryOptions = [
    { value: 'GENERAL', label: 'Geral' },
    { value: 'UI_UX', label: 'Interface/Experiência' },
    { value: 'PERFORMANCE', label: 'Performance' },
    { value: 'FUNCTIONALITY', label: 'Funcionalidade' },
    { value: 'DATA', label: 'Dados' },
    { value: 'SECURITY', label: 'Segurança' },
    { value: 'INTEGRATION', label: 'Integração' }
  ];

  // Carregar empresas para system_admin
  useEffect(() => {
    const loadCompanies = async () => {
      if (!isSystemAdmin) return;

      try {
        setIsLoading(true);
        const response = await companyService.getCompanies({ page: 1, limit: 100, active: true });
        const formattedCompanies = response.companies
          .filter(company => company && company.id && company.name)
          .map(company => ({
            value: company.id,
            label: company.name,
            sortName: company.name.toLowerCase()
          }))
          .sort((a, b) => a.sortName.localeCompare(b.sortName));

        setCompanies(formattedCompanies);
      } catch (error) {
        console.error('Erro ao carregar empresas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCompanies();
  }, [isSystemAdmin]);

  const handleFilterChange = (newFilters) => {
    onFiltersChange(newFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      search: '',
      status: [],
      priority: [],
      category: [],
      companies: []
    };
    onFiltersChange(clearedFilters);
  };

  const getActiveFiltersCount = () => {
    return Object.entries(filters).filter(([key, value]) => {
      if (key === 'search') return false;
      if (Array.isArray(value)) return value.length > 0;
      return value !== null && value !== '';
    }).length;
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-3 md:items-center">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar por título, descrição, usuário ou empresa..."
              value={filters.search || ""}
              onChange={(e) => handleFilterChange({ ...filters, search: e.target.value })}
              className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <FilterButton
            type="button"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            moduleColor="admin"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-600 dark:text-gray-400" />
              <span>Filtros</span>
              <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                {getActiveFiltersCount()}
              </span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={onSearch}
            moduleColor="admin"
            variant="primary"
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>Buscar</span>
            </div>
          </FilterButton>
        </div>
      </div>

      {isFilterExpanded && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
          <div className={`grid grid-cols-1 gap-4 ${isSystemAdmin ? 'md:grid-cols-2 lg:grid-cols-4' : 'md:grid-cols-3'}`}>
            {isSystemAdmin && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                  <Building size={16} className="text-slate-600 dark:text-slate-400" />
                  Empresa
                </label>
                <MultiSelect
                  options={companies}
                  value={filters.companies || []}
                  onChange={(selected) => handleFilterChange({ ...filters, companies: selected })}
                  placeholder="Selecione as empresas"
                  className="w-full"
                  moduleOverride="admin"
                  loading={isLoading}
                />
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <CheckCircle size={16} className="text-slate-600 dark:text-slate-400" />
                Status
              </label>
              <MultiSelect
                options={statusOptions}
                value={filters.status || []}
                onChange={(selected) => handleFilterChange({ ...filters, status: selected })}
                placeholder="Selecione os status"
                className="w-full"
                moduleOverride="admin"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <AlertTriangle size={16} className="text-slate-600 dark:text-slate-400" />
                Prioridade
              </label>
              <MultiSelect
                options={priorityOptions}
                value={filters.priority || []}
                onChange={(selected) => handleFilterChange({ ...filters, priority: selected })}
                placeholder="Selecione as prioridades"
                className="w-full"
                moduleOverride="admin"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <Tag size={16} className="text-slate-600 dark:text-slate-400" />
                Categoria
              </label>
              <MultiSelect
                options={categoryOptions}
                value={filters.category || []}
                onChange={(selected) => handleFilterChange({ ...filters, category: selected })}
                placeholder="Selecione as categorias"
                className="w-full"
                moduleOverride="admin"
              />
            </div>
          </div>

          <div>
            <FilterButton
              type="button"
              onClick={handleClearFilters}
              moduleColor="admin"
              variant="secondary"
            >
              <div className="flex items-center gap-2">
                <RefreshCw size={16} />
                <span>Limpar Filtros</span>
              </div>
            </FilterButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default BugReportsFilters;
