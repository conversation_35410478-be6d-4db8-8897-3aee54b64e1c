"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _planData_subscription, _planData_subscription1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    console.log('[PlansPage] Inicializando página');\n    console.log('[PlansPage] Usuário atual:', user);\n    console.log('[PlansPage] Permissões:', user === null || user === void 0 ? void 0 : user.permissions);\n    console.log('[PlansPage] Módulos:', user === null || user === void 0 ? void 0 : user.modules);\n    console.log('[PlansPage] Role:', user === null || user === void 0 ? void 0 : user.role);\n    console.log('[PlansPage] CompanyId:', user === null || user === void 0 ? void 0 : user.companyId);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // Adicionar após a declaração de outros estados\n    const [showUpgradeModal, setShowUpgradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPlanId, setSelectedPlanId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const basePrice = 19.90; // Preço base por usuário\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        // Desconto adicional de 10% para pagamento anual à vista\n        const annualDiscount = 0.10;\n        const yearlyPriceWithDiscount = finalPrice * 12 * (1 - annualDiscount);\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice: finalPrice * 12,\n            yearlyPriceWithDiscount,\n            annualDiscount: annualDiscount * 100\n        };\n    };\n    const pricing = calculatePrice(userCount);\n    // Opções de usuários predefinidas\n    const userOptions = [\n        1,\n        5,\n        20,\n        50,\n        100,\n        200\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadData = {\n                \"PlansPage.useEffect.loadData\": async ()=>{\n                    try {\n                        if (isSystemAdmin) {\n                            // Carregar lista de empresas para admin do sistema\n                            const companiesData = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n                            setCompanies(companiesData);\n                            // Se houver empresas, selecionar a primeira por padrão\n                            if (companiesData.length > 0 && !selectedCompanyId) {\n                                setSelectedCompanyId(companiesData[0].id);\n                            }\n                        } else if (user === null || user === void 0 ? void 0 : user.companyId) {\n                            // Para usuários não-admin, usar a empresa atual\n                            setSelectedCompanyId(user.companyId);\n                            try {\n                                const company = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCurrentCompany();\n                                setCurrentCompany(company);\n                            } catch (error) {\n                                console.error('[PlansPage] Erro ao carregar empresa atual:', error);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('[PlansPage] Erro ao carregar dados iniciais:', error);\n                        toast_error('Erro ao carregar dados iniciais');\n                    }\n                }\n            }[\"PlansPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        user,\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadPlanData = {\n                \"PlansPage.useEffect.loadPlanData\": async ()=>{\n                    if (!selectedCompanyId) return;\n                    setIsLoading(true);\n                    try {\n                        console.log('[PlansPage] Carregando dados do plano para companyId:', selectedCompanyId);\n                        const [plansData, subscriptionData] = await Promise.all([\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans(),\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(selectedCompanyId)\n                        ]);\n                        console.log('[PlansPage] Dados dos planos carregados:', plansData);\n                        console.log('[PlansPage] Dados da assinatura carregados:', subscriptionData);\n                        setPlans(plansData);\n                    } catch (error) {\n                        var _error_response;\n                        console.error('[PlansPage] Erro ao carregar dados dos planos:', error);\n                        console.error('[PlansPage] Detalhes do erro:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                        toast_error('Erro ao carregar dados dos planos');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"PlansPage.useEffect.loadPlanData\"];\n            loadPlanData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [couponCode, setCouponCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [couponValidation, setCouponValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isValidatingCoupon, setIsValidatingCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        console.log('[DEBUG] currentUser.companyId:', user === null || user === void 0 ? void 0 : user.companyId);\n        console.log('[DEBUG] Tem permissão admin.dashboard.view?', can('admin.dashboard.view'));\n        // Para system_admin, não carregar se não tiver empresa selecionada\n        if (isSystemAdmin && !selectedCompanyId) {\n            console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');\n            setIsLoading(false);\n            return;\n        }\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : user === null || user === void 0 ? void 0 : user.companyId;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            if (!companyId) {\n                console.error('[DEBUG] Nenhum companyId disponível para carregar dados do plano');\n                throw new Error('ID da empresa não disponível');\n            }\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] availablePlans.modules:', availablePlansResponse === null || availablePlansResponse === void 0 ? void 0 : availablePlansResponse.modules);\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response_data, _error_response3;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error(\"Error status:\", (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            console.error(\"Error headers:\", (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.headers);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data = _error_response3.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o custo adicional usando a função centralizada\n    const calculateAdditionalCost = ()=>{\n        if (!planData) return {\n            additionalCost: 0,\n            costPerAdditionalUser: 19.90\n        };\n        const currentUsers = planData.subscription.userLimit;\n        const isAnnual = planData.subscription.billingCycle === 'YEARLY';\n        return (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.calculateAdditionalUsersCost)(currentUsers, additionalUsersCount, isAnnual);\n    };\n    // Função para calcular preço por usuário atual\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90;\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90;\n    };\n    // Função para validar cupom\n    const validateCoupon = async (code)=>{\n        if (!code || code.trim() === '') {\n            setCouponValidation(null);\n            return;\n        }\n        setIsValidatingCoupon(true);\n        try {\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.validateCoupon(code.trim());\n            setCouponValidation({\n                valid: true,\n                coupon: response.coupon,\n                message: \"Cupom v\\xe1lido! \".concat(response.coupon.type === 'PERCENT' ? \"\".concat(response.coupon.value, \"% de desconto\") : \"R$ \".concat(response.coupon.value.toFixed(2), \" de desconto\"))\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setCouponValidation({\n                valid: false,\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Cupom inválido'\n            });\n        } finally{\n            setIsValidatingCoupon(false);\n        }\n    };\n    // Debounce para validação de cupom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"PlansPage.useEffect.timer\": ()=>{\n                    if (couponCode.trim()) {\n                        validateCoupon(couponCode);\n                    } else {\n                        setCouponValidation(null);\n                    }\n                }\n            }[\"PlansPage.useEffect.timer\"], 500);\n            return ({\n                \"PlansPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"PlansPage.useEffect\"];\n        }\n    }[\"PlansPage.useEffect\"], [\n        couponCode\n    ]);\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        try {\n            setIsUpdating(true);\n            const newUserCount = planData.usage.currentUsers + additionalUsersCount;\n            // Validar cupom antes de prosseguir se foi informado\n            if (couponCode.trim() && (!couponValidation || !couponValidation.valid)) {\n                toast_error('Aguarde a validação do cupom ou remova-o para continuar');\n                return;\n            }\n            // Criar uma nova sessão de checkout do Stripe\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(planData.subscription.billingCycle.toLowerCase(), newUserCount, couponCode.trim() || null);\n            // Redirecionar para a página de checkout do Stripe\n            window.location.href = response.url;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao atualizar usuários:', error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao atualizar usuários');\n        } finally{\n            setIsUpdating(false);\n            handleCloseAddUsersModal();\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        console.log('[DEBUG] openAddModuleConfirmation:', {\n            moduleType,\n            availablePlans\n        });\n        console.log('[DEBUG] availablePlans.modules:', availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules);\n        if (!(availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules)) {\n            console.error('[DEBUG] availablePlans.modules não está disponível');\n            toast_error({\n                title: \"Erro\",\n                message: \"Dados dos módulos não estão disponíveis. Tente recarregar a página.\"\n            });\n            return;\n        }\n        const moduleInfo = availablePlans.modules[moduleType];\n        console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para abrir modal de confirmação para remover módulo\n    const openRemoveModuleConfirmation = (moduleType)=>{\n        var _planData_modules;\n        const moduleInfo = planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.find((m)=>m.moduleType === moduleType);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('remove');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Adicionar a função handleUpgrade após a função handleCancelSubscription\n    const handleUpgrade = async ()=>{\n        try {\n            const billingCycle = isAnnual ? 'yearly' : 'monthly';\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(billingCycle, userCount);\n            if (response.url) {\n                window.location.href = response.url;\n            } else {\n                throw new Error('URL de checkout não encontrada');\n            }\n        } catch (error) {\n            console.error('Erro ao iniciar checkout:', error);\n            toast_error('Erro ao iniciar processo de upgrade. Tente novamente.');\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    // Após carregar planData (ou subscriptionData)\n    const isTrial = (planData === null || planData === void 0 ? void 0 : (_planData_subscription = planData.subscription) === null || _planData_subscription === void 0 ? void 0 : _planData_subscription.status) === 'TRIAL';\n    const trialEndDate = (planData === null || planData === void 0 ? void 0 : (_planData_subscription1 = planData.subscription) === null || _planData_subscription1 === void 0 ? void 0 : _planData_subscription1.endDate) ? new Date(planData.subscription.endDate).toLocaleDateString('pt-BR') : 'N/A';\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 645,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 646,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 644,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 657,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 669,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 671,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 662,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 661,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 655,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 681,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 682,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 685,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 680,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 654,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 698,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 711,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 713,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 704,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 703,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 696,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 724,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 725,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 728,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 723,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 695,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Gerenciamento de Planos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 748,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name) : \"Gerencie seu plano, usuários e módulos da assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 751,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 747,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 743,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 765,\n                    columnNumber: 17\n                }, void 0),\n                description: \"Selecione uma empresa para visualizar e gerenciar seu plano.\",\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 777,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 779,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 770,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 769,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 763,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 795,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 794,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 799,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 798,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 793,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 814,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 812,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 805,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 821,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 827,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 820,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Detalhamento dos Valores\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Usu\\xe1rios ativos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                children: [\n                                                                    planData.usage.currentUsers,\n                                                                    \" de \",\n                                                                    planData.usage.userLimit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Pre\\xe7o base (R$ 19,90/usu\\xe1rio):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 854,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (planData.usage.userLimit * 19.90).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    (()=>{\n                                                        const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                                        const discountAmount = planData.usage.userLimit * 19.90 * (currentDiscount / 100);\n                                                        const priceAfterDiscount = planData.usage.userLimit * 19.90 - discountAmount;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                currentDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Desconto por volume (\",\n                                                                                currentDiscount,\n                                                                                \"%):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 869,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                discountAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 872,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 868,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: \"Desconto anual (10%):\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 880,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                (priceAfterDiscount * 0.10).toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 881,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.appliedCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Cupom aplicado (\",\n                                                                                planData.subscription.appliedCoupon.code,\n                                                                                \"):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: planData.subscription.appliedCoupon.type === 'PERCENT' ? \"-\".concat(planData.subscription.appliedCoupon.value, \"%\") : \"-R$ \".concat(planData.subscription.appliedCoupon.value.toFixed(2))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 892,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-t border-gray-200 dark:border-gray-700 pt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 903,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: [\n                                                                                    \"R$ \",\n                                                                                    (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 904,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 902,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 901,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 mt-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-blue-700 dark:text-blue-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 913,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Plano Anual Ativo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 914,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 912,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                            children: [\n                                                                                \"Voc\\xea economiza R$ \",\n                                                                                (priceAfterDiscount * 12 - planData.subscription.pricePerMonth * 12).toFixed(2),\n                                                                                \" por ano\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 916,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 911,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 845,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 839,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 935,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 930,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'CANCELED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 944,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 939,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'TRIAL' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 953,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Fazer Upgrade\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 948,\n                                                columnNumber: 17\n                                            }, undefined) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const url = isSystemAdmin && selectedCompanyId ? \"/subscription/invoices?companyId=\".concat(selectedCompanyId) : '/subscription/invoices';\n                                                    router.push(url);\n                                                },\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 967,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 958,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 804,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 792,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 978,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Uso de Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 977,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOpenAddUsersModal,\n                                        disabled: isUpdating || planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED',\n                                        className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED' ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 990,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Adicionar Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 981,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 976,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Usu\\xe1rios Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.currentUsers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 997,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Limite de Usu\\xe1rios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1004,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.userLimit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1005,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1003,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 996,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2.5 rounded-full transition-all duration-500\",\n                                            style: {\n                                                width: \"\".concat(planData.usage.userLimitUsage, \"%\"),\n                                                backgroundColor: planData.usage.userLimitUsage >= 90 ? '#EF4444' // Vermelho para uso >= 90%\n                                                 : planData.usage.userLimitUsage >= 75 ? '#F59E0B' // Amarelo para uso >= 75%\n                                                 : '#3B82F6' // Azul para uso < 75%\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1013,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1012,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.currentUsers,\n                                                    \" de \",\n                                                    planData.usage.userLimit,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1027,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1030,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1026,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    planData.usage.userLimitUsage >= 90 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline-block mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Voc\\xea est\\xe1 pr\\xf3ximo do limite de usu\\xe1rios. Considere adicionar mais usu\\xe1rios ao seu plano.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1036,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 995,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 975,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 790,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1048,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1047,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1058,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1063,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1056,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1070,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1055,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1079,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                children: \"Plano B\\xe1sico Completo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1080,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 dark:text-blue-200 mb-2\",\n                                        children: \"Seu plano j\\xe1 inclui todos os m\\xf3dulos essenciais para o funcionamento completo do sistema.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1084,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Administra\\xe7\\xe3o completa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1088,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Sistema de agendamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1089,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Gerenciamento de pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1090,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Relat\\xf3rios e dashboards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1091,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Suporte t\\xe9cnico inclu\\xeddo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1092,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1087,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1077,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1052,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1046,\n                columnNumber: 7\n            }, undefined),\n            isTrial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-6 h-6 text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1101,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Voc\\xea est\\xe1 em per\\xedodo de avalia\\xe7\\xe3o (trial).\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1103,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Aproveite para testar todos os recursos! O acesso ser\\xe1 limitado ap\\xf3s o t\\xe9rmino do trial.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1105,\n                                        columnNumber: 103\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Data de t\\xe9rmino do trial:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1106,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" \",\n                                    trialEndDate,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1106,\n                                        columnNumber: 72\n                                    }, undefined),\n                                    \"Caso queira migrar para um plano completo, clique no bot\\xe3o abaixo.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1104,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUpgradeModal(true),\n                                className: \"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                children: \"Fazer Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1109,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1102,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1100,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showUpgradeModal,\n                onClose: ()=>setShowUpgradeModal(false),\n                title: \"Quantos usu\\xe1rios voc\\xea precisa?\",\n                size: \"lg\",\n                moduleColor: \"admin\",\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row items-center justify-center gap-3 px-4 py-3 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUpgradeModal(false),\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: \"Cancelar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1128,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleUpgrade,\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: [\n                                \"Confirmar Upgrade\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"ml-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1139,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1134,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1127,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4 px-2 py-2 md:px-4 md:py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 text-base md:text-lg mt-2 mb-2\",\n                                    children: \"Selecione a quantidade de usu\\xe1rios para ver o pre\\xe7o personalizado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mt-1 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1 \".concat(!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAnnual(!isAnnual),\n                                            className: \"relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 \".concat(isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition \".concat(isAnnual ? 'translate-x-9' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1156,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 \".concat(isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Anual\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1150,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-2 mb-1\",\n                                    children: userOptions.map((option)=>{\n                                        const discount = getDiscountByUserCount(option);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUserCount(option),\n                                            className: \"relative p-5 min-w-[130px] min-h-[70px] rounded-xl border-2 transition-all text-center flex flex-col items-center justify-center gap-1 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 text-lg font-semibold \".concat(userCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-white'),\n                                            children: [\n                                                discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow\",\n                                                    children: [\n                                                        \"-\",\n                                                        discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1179,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: option\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1183,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 font-normal\",\n                                                    children: option === 1 ? 'usuário' : 'usuários'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1184,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, option, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1169,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1165,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-1 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-300 mb-0.5\",\n                                            children: \"Ou digite uma quantidade personalizada:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1191,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center w-32 md:w-44\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1194,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: \"1\",\n                                                        max: \"1000\",\n                                                        value: userCount,\n                                                        onChange: (e)=>setUserCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                        className: \"pl-10 pr-2 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 w-full text-center text-base\",\n                                                        placeholder: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1195,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1193,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1190,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1164,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-3 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mt-1 border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Usu\\xe1rios:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1212,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: userCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"Pre\\xe7o por usu\\xe1rio \",\n                                                        isAnnual ? 'anual à vista' : 'mensal',\n                                                        \":\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: pricing.discount > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1220,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 font-medium text-gray-900 dark:text-white\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * (1 - pricing.discount / 100) * 12 * (1 - pricing.annualDiscount / 100) : basePrice * (1 - pricing.discount / 100)).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1221,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1224,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1217,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1215,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto por quantidade:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1229,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Valor mensal sem desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        pricing.totalWithoutDiscount.toFixed(2).replace('.', ',')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1234,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isAnnual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto anual \\xe0 vista:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1240,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.annualDiscount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1241,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1239,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1210,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-end justify-center min-w-[180px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                            children: \"Valor mensal:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1246,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl font-bold text-orange-600 dark:text-orange-400\",\n                                            children: [\n                                                \"R$ \",\n                                                isAnnual ? (pricing.yearlyPriceWithDiscount / 12).toFixed(2).replace('.', ',') : pricing.monthlyPrice.toFixed(2).replace('.', ',')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1247,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1245,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1209,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1144,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1120,\n                columnNumber: 7\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg max-w-4xl w-full border border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Gerenciar limite de usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1258,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: \"Ajuste o limite de usu\\xe1rios do seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1261,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1257,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-8\",\n                            children: [\n                                1,\n                                5,\n                                10,\n                                20\n                            ].map((option)=>{\n                                const newLimit = planData.usage.userLimit + option;\n                                const discount = getDiscountByUserCount(newLimit);\n                                const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                const discountChange = discount - currentDiscount;\n                                const isValid = true;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setAdditionalUsersCount(option),\n                                    className: \"p-4 rounded-lg border-2 transition-all text-center relative \".concat(additionalUsersCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600'),\n                                    children: [\n                                        discountChange > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\",\n                                            children: [\n                                                \"+\",\n                                                discountChange,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1285,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"+\",\n                                                option\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1289,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: option === 1 ? 'usuário' : 'usuários'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1292,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, option, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1275,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1266,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Cupom de desconto (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1302,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: couponCode,\n                                            onChange: (e)=>setCouponCode(e.target.value.toUpperCase()),\n                                            placeholder: \"Digite o c\\xf3digo do cupom\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1306,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isValidatingCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"animate-spin h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1315,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1314,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1305,\n                                    columnNumber: 15\n                                }, undefined),\n                                couponValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm \".concat(couponValidation.valid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'),\n                                    children: couponValidation.valid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1323,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1322,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1328,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1327,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1320,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1301,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Limite atual:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1340,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1341,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1339,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Altera\\xe7\\xe3o:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: [\n                                                                \"+\",\n                                                                additionalUsersCount\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo limite:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1350,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit + additionalUsersCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1351,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Cupom aplicado:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1355,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.code\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1356,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1354,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1338,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Pre\\xe7o atual por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1366,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo pre\\xe7o por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                calculateAdditionalCost().costPerAdditionalUser.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1370,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Diferen\\xe7a mensal:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1377,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(calculateAdditionalCost().additionalCost < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'),\n                                                            children: [\n                                                                calculateAdditionalCost().additionalCost > 0 ? '+' : '',\n                                                                \"R$ \",\n                                                                Math.abs(calculateAdditionalCost().additionalCost).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1378,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1376,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Desconto do cupom:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1384,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.type === 'PERCENT' ? \"\".concat(couponValidation.coupon.value, \"%\") : \"R$ \".concat(couponValidation.coupon.value.toFixed(2))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1385,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1383,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1363,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1337,\n                                    columnNumber: 15\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser < planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1398,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Voc\\xea receber\\xe1 um desconto adicional por ter mais usu\\xe1rios!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1397,\n                                    columnNumber: 17\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser > planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1405,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O pre\\xe7o por usu\\xe1rio aumentar\\xe1 devido \\xe0 redu\\xe7\\xe3o do desconto por volume.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1404,\n                                    columnNumber: 17\n                                }, undefined),\n                                planData.usage.currentUsers > planData.usage.userLimit + additionalUsersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1412,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O novo limite n\\xe3o pode ser menor que a quantidade atual de usu\\xe1rios (\",\n                                        planData.usage.currentUsers,\n                                        \").\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1411,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1336,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCloseAddUsersModal,\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1419,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddUsers,\n                                    disabled: isUpdating || additionalUsersCount <= 0 || isValidatingCoupon || couponCode.trim() && (!couponValidation || !couponValidation.valid),\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors disabled:opacity-50\",\n                                    children: [\n                                        isValidatingCoupon ? 'Validando cupom...' : 'Confirmar Alteração',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1431,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1425,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1418,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1256,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1255,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 740,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"otwcSAx1ZVpjp+PdiSfJL+hqVP8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n// Função auxiliar para verificar se é módulo básico\nconst isBasicModule = (moduleType)=>{\n    return [\n        'BASIC',\n        'ADMIN',\n        'SCHEDULING'\n    ].includes(moduleType);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});