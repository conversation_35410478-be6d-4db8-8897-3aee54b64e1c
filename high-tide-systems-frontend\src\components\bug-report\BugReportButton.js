'use client';

import React, { useState } from 'react';
import { Bug } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import BugReportModal from './BugReportModal';

const BugReportButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { user } = useAuth();

  // Se o usuário não estiver logado, não mostrar o botão
  if (!user) return null;

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <button
        onClick={handleOpenModal}
        className="p-2 text-gray-600 dark:text-gray-300 hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 dark:hover:text-red-400 rounded-full relative transition-all duration-200 hover:scale-105 hover:shadow-md"
        aria-label="Reportar Bug"
        title="Reportar Bug"
      >
        <Bug size={20} aria-hidden="true" />
      </button>
      
      <BugReportModal 
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
};

export default BugReportButton;
