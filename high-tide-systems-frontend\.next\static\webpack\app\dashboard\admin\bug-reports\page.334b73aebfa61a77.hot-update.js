"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/admin/bug-reports/page.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleTable */ \"(app-pages-browser)/./src/components/ui/ModuleTable.js\");\n/* harmony import */ var _components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/bug-report/BugReportsFilters */ \"(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/bugReportService */ \"(app-pages-browser)/./src/services/bugReportService.js\");\n/* harmony import */ var _components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/BugDetailsModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugDetailsModal.js\");\n/* harmony import */ var _components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/BugEditModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugEditModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BugReportsPage = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast_error, toast_success } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [bugs, setBugs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedBug, setSelectedBug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        status: [],\n        priority: [],\n        category: [],\n        companies: []\n    });\n    // Carregar dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsPage.useEffect\": ()=>{\n            if (!user) return;\n            if (user.role !== 'SYSTEM_ADMIN') {\n                router.push('/dashboard');\n                return;\n            }\n            loadBugReports();\n            loadStats();\n        }\n    }[\"BugReportsPage.useEffect\"], [\n        user,\n        router,\n        filters,\n        pagination.page\n    ]);\n    const loadBugReports = async ()=>{\n        try {\n            setLoading(true);\n            const params = {\n                page: pagination.page,\n                limit: pagination.limit,\n                ...filters\n            };\n            // Remove filtros vazios\n            Object.keys(params).forEach((key)=>{\n                if (Array.isArray(params[key]) && params[key].length === 0) {\n                    delete params[key];\n                } else if (params[key] === '') {\n                    delete params[key];\n                }\n            });\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.listAll(params);\n            if (response.data) {\n                setBugs(response.data.bugReports);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            }\n        } catch (error) {\n            console.error('Erro ao carregar bug reports:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao carregar relatórios de bugs'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadStats = async ()=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.getStats();\n            if (response.data) {\n                setStats(response.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar estatísticas:', error);\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN') {\n        return null;\n    }\n    const handleViewBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowDetailsModal(true);\n    };\n    const handleEditBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowEditModal(true);\n    };\n    const handleSaveBug = async (updatedBug)=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.updateStatus(updatedBug.id, {\n                status: updatedBug.status,\n                priority: updatedBug.priority,\n                adminNotes: updatedBug.adminNotes\n            });\n            if (response.data) {\n                setBugs((prev)=>prev.map((bug)=>bug.id === updatedBug.id ? {\n                            ...bug,\n                            ...updatedBug\n                        } : bug));\n                setShowEditModal(false);\n                setSelectedBug(null);\n                toast_success({\n                    title: 'Sucesso',\n                    message: 'Bug atualizado com sucesso'\n                });\n            }\n        } catch (error) {\n            console.error('Erro ao atualizar bug:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao atualizar o bug'\n            });\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, undefined);\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        const colors = {\n            'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',\n            'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',\n            'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',\n            'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n        };\n        return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            'CRITICAL': 'Crítica',\n            'HIGH': 'Alta',\n            'MEDIUM': 'Média',\n            'LOW': 'Baixa'\n        };\n        return labels[priority] || 'Desconhecida';\n    };\n    const columns = [\n        {\n            header: 'Status',\n            field: 'status',\n            width: '120px'\n        },\n        {\n            header: 'Bug',\n            field: 'title',\n            width: 'auto'\n        },\n        {\n            header: 'Prioridade',\n            field: 'priority',\n            width: '120px'\n        },\n        {\n            header: 'Reportado por',\n            field: 'reportedBy',\n            width: '200px'\n        },\n        {\n            header: 'Data',\n            field: 'createdAt',\n            width: '120px'\n        },\n        {\n            header: 'Ações',\n            field: 'actions',\n            width: '120px',\n            sortable: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 28,\n                            className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Relat\\xf3rios de Bugs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie todos os bugs reportados pelos usu\\xe1rios do sistema\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Filtros e Busca\",\n                description: \"Utilize os filtros abaixo para encontrar bugs espec\\xedficos.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 219,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                customButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadBugReports(),\n                        disabled: loading,\n                        className: \"flex items-center gap-2 px-3 py-1 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors disabled:opacity-50\",\n                        title: \"Buscar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Buscar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 230,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, void 0),\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                moduleColor: \"admin\",\n                title: \"Lista de Bugs Reportados\",\n                data: bugs,\n                columns: columns,\n                isLoading: loading,\n                emptyMessage: \"Nenhum bug reportado ainda.\",\n                emptyIcon: \"\\uD83D\\uDC1B\",\n                enableColumnToggle: true,\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadBugReports(),\n                    disabled: loading,\n                    className: \"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        size: 16,\n                        className: \"text-gray-600 dark:text-gray-300 \".concat(loading ? 'animate-spin' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 259,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, void 0),\n                renderRow: (bug, _index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getStatusIcon(bug.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: bug.status === 'OPEN' ? 'Aberto' : bug.status === 'IN_PROGRESS' ? 'Em Progresso' : bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 268,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help\",\n                                            title: bug.description,\n                                            children: bug.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 281,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('priority') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(bug.priority)),\n                                    children: getPriorityLabel(bug.priority)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('reportedBy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.reportedBy.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: bug.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 303,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                children: new Date(bug.createdAt).toLocaleDateString('pt-BR')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 327,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 318,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, bug.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDetailsModal,\n                onClose: ()=>{\n                    setShowDetailsModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug,\n                onSave: handleSaveBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 352,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsPage, \"q81YIsVpT1m/rqoFz1uU63xXAZc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BugReportsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsPage);\nvar _c;\n$RefreshReg$(_c, \"BugReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js\n"));

/***/ })

});