"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/admin/bug-reports/page.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleTable */ \"(app-pages-browser)/./src/components/ui/ModuleTable.js\");\n/* harmony import */ var _components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/bug-report/BugReportsFilters */ \"(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/bugReportService */ \"(app-pages-browser)/./src/services/bugReportService.js\");\n/* harmony import */ var _components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/BugDetailsModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugDetailsModal.js\");\n/* harmony import */ var _components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/BugEditModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugEditModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BugReportsPage = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast_error, toast_success } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [bugs, setBugs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedBug, setSelectedBug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        status: [],\n        priority: [],\n        category: [],\n        companies: []\n    });\n    // Carregar dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsPage.useEffect\": ()=>{\n            if (!user) return;\n            if (user.role !== 'SYSTEM_ADMIN') {\n                router.push('/dashboard');\n                return;\n            }\n            loadBugReports();\n            loadStats();\n        }\n    }[\"BugReportsPage.useEffect\"], [\n        user,\n        router,\n        filters,\n        pagination.page\n    ]);\n    const loadBugReports = async ()=>{\n        try {\n            setLoading(true);\n            const params = {\n                page: pagination.page,\n                limit: pagination.limit,\n                ...filters\n            };\n            // Remove filtros vazios\n            Object.keys(params).forEach((key)=>{\n                if (Array.isArray(params[key]) && params[key].length === 0) {\n                    delete params[key];\n                } else if (params[key] === '') {\n                    delete params[key];\n                }\n            });\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.listAll(params);\n            if (response.data) {\n                setBugs(response.data.bugReports);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            }\n        } catch (error) {\n            console.error('Erro ao carregar bug reports:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao carregar relatórios de bugs'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadStats = async ()=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.getStats();\n            if (response.data) {\n                setStats(response.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar estatísticas:', error);\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN') {\n        return null;\n    }\n    const handleViewBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowDetailsModal(true);\n    };\n    const handleEditBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowEditModal(true);\n    };\n    const handleSaveBug = async (updatedBug)=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.updateStatus(updatedBug.id, {\n                status: updatedBug.status,\n                priority: updatedBug.priority,\n                adminNotes: updatedBug.adminNotes\n            });\n            if (response.data) {\n                setBugs((prev)=>prev.map((bug)=>bug.id === updatedBug.id ? {\n                            ...bug,\n                            ...updatedBug\n                        } : bug));\n                setShowEditModal(false);\n                setSelectedBug(null);\n                toast_success({\n                    title: 'Sucesso',\n                    message: 'Bug atualizado com sucesso'\n                });\n            }\n        } catch (error) {\n            console.error('Erro ao atualizar bug:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao atualizar o bug'\n            });\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 158,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 160,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 162,\n                    columnNumber: 16\n                }, undefined);\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 164,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 166,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        const colors = {\n            'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',\n            'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',\n            'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',\n            'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n        };\n        return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            'CRITICAL': 'Crítica',\n            'HIGH': 'Alta',\n            'MEDIUM': 'Média',\n            'LOW': 'Baixa'\n        };\n        return labels[priority] || 'Desconhecida';\n    };\n    const columns = [\n        {\n            header: 'Status',\n            field: 'status',\n            width: '120px'\n        },\n        {\n            header: 'Bug',\n            field: 'title',\n            width: 'auto'\n        },\n        {\n            header: 'Prioridade',\n            field: 'priority',\n            width: '120px'\n        },\n        {\n            header: 'Reportado por',\n            field: 'reportedBy',\n            width: '200px'\n        },\n        {\n            header: 'Data',\n            field: 'createdAt',\n            width: '120px'\n        },\n        {\n            header: 'Ações',\n            field: 'actions',\n            width: '120px',\n            sortable: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 28,\n                            className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Relat\\xf3rios de Bugs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie todos os bugs reportados pelos usu\\xe1rios do sistema\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Filtros e Busca\",\n                description: \"Utilize os filtros abaixo para encontrar bugs espec\\xedficos.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 219,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                moduleColor: \"admin\",\n                title: \"Lista de Bugs Reportados\",\n                data: bugs,\n                columns: columns,\n                isLoading: loading,\n                emptyMessage: \"Nenhum bug reportado ainda.\",\n                emptyIcon: \"\\uD83D\\uDC1B\",\n                enableColumnToggle: true,\n                renderRow: (bug, _index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getStatusIcon(bug.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: bug.status === 'OPEN' ? 'Aberto' : bug.status === 'IN_PROGRESS' ? 'Em Progresso' : bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 245,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help\",\n                                            title: bug.description,\n                                            children: bug.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 256,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('priority') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(bug.priority)),\n                                    children: getPriorityLabel(bug.priority)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('reportedBy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.reportedBy.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 279,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: bug.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 278,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                children: new Date(bug.createdAt).toLocaleDateString('pt-BR')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 299,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 293,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, bug.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 240,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDetailsModal,\n                onClose: ()=>{\n                    setShowDetailsModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug,\n                onSave: handleSaveBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 326,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsPage, \"q81YIsVpT1m/rqoFz1uU63xXAZc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BugReportsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsPage);\nvar _c;\n$RefreshReg$(_c, \"BugReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js\n"));

/***/ })

});