// src/controllers/professionGroupController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");
const errorHandler = require("../utils/errorHandler");

// Validações
const createProfessionGroupValidation = [
  body("name").notEmpty().withMessage("Nome do grupo é obrigatório"),
  body("description").optional(),
  body("companyId").optional(),
  body("defaultModules").optional().isArray().withMessage("Módulos padrão deve ser um array"),
  body("defaultPermissions").optional().isArray().withMessage("Permissões padrão deve ser um array"),
];

const updateProfessionGroupValidation = [
  body("name").optional(),
  body("description").optional(),
  body("active").optional().isBoolean().withMessage("O campo active deve ser um booleano"),
  body("defaultModules").optional().isArray().withMessage("Módulos padrão deve ser um array"),
  body("defaultPermissions").optional().isArray().withMessage("Permissões padrão deve ser um array"),
];

class ProfessionGroupController {
  /**
   * Lista todos os grupos de profissões
   */
  static async list(req, res) {
    try {
      const { search, active, companyId, groupIds, sortField = 'name', sortDirection = 'asc' } = req.query;

      // Construir filtro para o Prisma
      const where = {};

      // Filtrar por IDs específicos de grupos
      if (groupIds) {
        // Converter para array se for um único valor
        let groupIdsArray;

        if (Array.isArray(groupIds)) {
          groupIdsArray = groupIds;
        } else if (typeof groupIds === 'string' && groupIds.includes(',')) {
          // Se for uma string com vírgulas, dividir em array
          groupIdsArray = groupIds.split(',');
        } else {
          groupIdsArray = [groupIds];
        }

        // Garantir que todos os IDs são strings
        groupIdsArray = groupIdsArray.map(id => String(id));

        where.id = { in: groupIdsArray };
      }

      // Filtrar por status ativo
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Filtrar por empresa
      if (companyId) {
        where.companyId = companyId;
      } else if (req.user?.companyId) {
        // Se não foi especificado, mas o usuário tem uma empresa, filtrar por ela
        where.companyId = req.user.companyId;
      }

      // Adicionar condição para ignorar registros excluídos logicamente
      where.deletedAt = null;

      // Validar e limpar parâmetros de ordenação
      const validSortFields = ['name', 'description', 'active', 'company', 'professions', 'createdAt', 'updatedAt'];
      const cleanSortField = validSortFields.includes(sortField) ? sortField : 'name';
      const cleanSortDirection = ['asc', 'desc'].includes(sortDirection) ? sortDirection : 'asc';

      // Construir objeto de ordenação
      let orderBy;
      if (cleanSortField === 'company') {
        orderBy = {
          company: {
            name: cleanSortDirection
          }
        };
      } else if (cleanSortField === 'professions') {
        // Para ordenar por número de profissões, precisamos fazer ordenação manual após a query
        orderBy = { name: 'asc' }; // Ordenação temporária
      } else if (cleanSortField === 'active') {
        // Para o campo active, usar ordenação customizada (true primeiro em asc)
        orderBy = {
          active: cleanSortDirection === 'asc' ? 'desc' : 'asc'
        };
      } else {
        orderBy = {};
        orderBy[cleanSortField] = cleanSortDirection;
      }



      // Buscar grupos com contagem de profissões
      let groups = await prisma.professionGroup.findMany({
        where,
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              professions: true
            }
          }
        },
        orderBy
      });

      // Transformar o resultado para o formato esperado
      groups = groups.map(g => ({
        id: g.id,
        name: g.name,
        description: g.description,
        active: g.active,
        createdAt: g.createdAt,
        updatedAt: g.updatedAt,
        companyId: g.companyId,
        company: g.company,
        defaultModules: g.defaultModules || ['BASIC'],
        defaultPermissions: g.defaultPermissions || [],
        _count: g._count
      }));

      // Ordenar manualmente por número de profissões se necessário
      if (cleanSortField === 'professions') {
        groups.sort((a, b) => {
          const countA = a._count.professions;
          const countB = b._count.professions;
          return cleanSortDirection === 'asc' ? countA - countB : countB - countA;
        });
      }

      // Filtrar por termo de busca (no nome ou descrição)
      if (search) {
        const searchLower = search.toLowerCase();
        groups = groups.filter(group =>
          group.name.toLowerCase().includes(searchLower) ||
          (group.description && group.description.toLowerCase().includes(searchLower))
        );
      }

      res.json(groups);
    } catch (error) {
      console.error('Erro ao listar grupos de profissões:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista todas as profissões de um grupo específico
   */
  static async listProfessions(req, res) {
    try {
      const { id } = req.params;
      const { active } = req.query;

      // Verificar se o grupo existe
      const group = await prisma.professionGroup.findUnique({
        where: { id }
      });

      if (!group) {
        return res.status(404).json({ message: 'Grupo de profissões não encontrado' });
      }

      // Construir filtro para o Prisma
      const where = {
        groupId: id,
        deletedAt: null
      };

      // Filtrar por status ativo se especificado
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Se o usuário não for SYSTEM_ADMIN, limitar à mesma empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
        where.companyId = req.user.companyId;
      }

      // Buscar profissões do grupo especificado
      const professions = await prisma.profession.findMany({
        where,
        select: {
          id: true,
          name: true,
          description: true,
          active: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              users: true
            }
          }
        },
        orderBy: {
          name: 'asc'
        }
      });

      res.json({
        group,
        professions,
        total: professions.length
      });
    } catch (error) {
      console.error('Erro ao listar profissões do grupo:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Obtém um grupo de profissões pelo ID
   */
  static async getById(req, res) {
    try {
      const { id } = req.params;

      // Buscar o grupo pelo ID com contagem de profissões
      const group = await prisma.professionGroup.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              professions: true
            }
          }
        }
      });

      // Buscar as profissões ativas deste grupo
      const professions = await prisma.profession.findMany({
        where: {
          groupId: id,
          active: true,
          deletedAt: null
        },
        orderBy: {
          name: 'asc'
        }
      });

      // Transformar o resultado para o formato esperado
      const formattedGroup = group ? {
        id: group.id,
        name: group.name,
        description: group.description,
        active: group.active,
        createdAt: group.createdAt,
        updatedAt: group.updatedAt,
        companyId: group.companyId,
        company: group.company,
        defaultModules: group.defaultModules || ['BASIC'],
        defaultPermissions: group.defaultPermissions || [],
        professions: professions,
        _count: group._count
      } : null;

      if (!formattedGroup) {
        return res.status(404).json({ message: 'Grupo de profissões não encontrado' });
      }

      res.json(formattedGroup);
    } catch (error) {
      console.error('Erro ao buscar grupo de profissões:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Cria um novo grupo de profissões
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, description, defaultModules, defaultPermissions } = req.body;

      // Determinar a empresa
      let companyId = req.body.companyId;
      if (!companyId && req.user?.companyId) {
        companyId = req.user.companyId;
      }

      // Validar módulos padrão
      const validModules = ["ADMIN", "RH", "FINANCIAL", "SCHEDULING", "PEOPLE", "BASIC"];
      if (defaultModules && !defaultModules.every(module => validModules.includes(module))) {
        return res.status(400).json({ message: 'Módulo(s) padrão inválido(s)' });
      }

      // Verificar se já existe um grupo com o mesmo nome na empresa
      const existingGroup = await prisma.professionGroup.findFirst({
        where: {
          name,
          companyId
        }
      });

      if (existingGroup) {
        return res.status(400).json({ message: 'Já existe um grupo com este nome na empresa' });
      }

      // Criar o grupo com permissões padrão
      const newGroup = await prisma.professionGroup.create({
        data: {
          name,
          description,
          companyId,
          defaultModules: defaultModules || ["BASIC"],
          defaultPermissions: defaultPermissions || []
        },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              professions: true
            }
          }
        }
      });

      // Transformar o resultado para o formato esperado
      const formattedGroup = {
        id: newGroup.id,
        name: newGroup.name,
        description: newGroup.description,
        active: newGroup.active,
        createdAt: newGroup.createdAt,
        updatedAt: newGroup.updatedAt,
        companyId: newGroup.companyId,
        company: newGroup.company,
        defaultModules: newGroup.defaultModules || ['BASIC'],
        defaultPermissions: newGroup.defaultPermissions || [],
        _count: newGroup._count
      };

      res.status(201).json(formattedGroup);
    } catch (error) {
      console.error('Erro ao criar grupo de profissões:', error);
      const errorResponse = errorHandler.handleError(error);
      res.status(errorResponse.status).json({
        message: errorResponse.message,
        errors: errorResponse.errors
      });
    }
  }

  /**
   * Atualiza um grupo de profissões existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { name, description, active, defaultModules, defaultPermissions } = req.body;

      // Verificar se o grupo existe
      const existingGroup = await prisma.professionGroup.findUnique({
        where: { id }
      });

      if (!existingGroup) {
        return res.status(404).json({ message: 'Grupo de profissões não encontrado' });
      }

      // Verificar se o usuário tem permissão para editar este grupo
      if (req.user?.companyId && existingGroup.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para editar este grupo' });
      }

      // Verificar se já existe outro grupo com o mesmo nome na empresa
      if (name && name !== existingGroup.name) {
        const duplicateGroup = await prisma.professionGroup.findFirst({
          where: {
            name,
            companyId: existingGroup.companyId,
            id: { not: id }
          }
        });

        if (duplicateGroup) {
          return res.status(400).json({ message: 'Já existe outro grupo com este nome na empresa' });
        }
      }

      // Validar módulos padrão
      const validModules = ["ADMIN", "RH", "FINANCIAL", "SCHEDULING", "PEOPLE", "BASIC"];
      if (defaultModules && !defaultModules.every(module => validModules.includes(module))) {
        return res.status(400).json({ message: 'Módulo(s) padrão inválido(s)' });
      }

      // Preparar dados para atualização
      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (active !== undefined) updateData.active = active;
      if (defaultModules !== undefined) updateData.defaultModules = defaultModules;
      if (defaultPermissions !== undefined) updateData.defaultPermissions = defaultPermissions;

      // Atualizar o grupo
      const updatedGroup = await prisma.professionGroup.update({
        where: { id },
        data: updateData,
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              professions: true
            }
          }
        }
      });

      // Transformar o resultado para o formato esperado
      const formattedGroup = {
        id: updatedGroup.id,
        name: updatedGroup.name,
        description: updatedGroup.description,
        active: updatedGroup.active,
        createdAt: updatedGroup.createdAt,
        updatedAt: updatedGroup.updatedAt,
        companyId: updatedGroup.companyId,
        company: updatedGroup.company,
        defaultModules: updatedGroup.defaultModules || ['BASIC'],
        defaultPermissions: updatedGroup.defaultPermissions || [],
        _count: updatedGroup._count
      };

      res.json(formattedGroup);
    } catch (error) {
      console.error('Erro ao atualizar grupo de profissões:', error);
      const errorResponse = errorHandler.handleError(error);
      res.status(errorResponse.status).json({
        message: errorResponse.message,
        errors: errorResponse.errors
      });
    }
  }

  /**
   * Remove um grupo de profissões (soft delete)
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o grupo existe
      const group = await prisma.professionGroup.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              professions: true
            }
          }
        }
      });

      if (!group) {
        return res.status(404).json({ message: 'Grupo de profissões não encontrado' });
      }

      // Verificar se o usuário tem permissão para excluir este grupo
      if (req.user?.companyId && group.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para excluir este grupo' });
      }

      // Verificar se há profissões neste grupo
      if (group._count.professions > 0) {
        return res.status(400).json({
          message: 'Este grupo contém profissões e não pode ser excluído',
          professionsCount: group._count.professions
        });
      }

      // Realizar soft delete
      await prisma.professionGroup.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      res.json({ message: 'Grupo de profissões excluído com sucesso' });
    } catch (error) {
      console.error('Erro ao excluir grupo de profissões:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  ProfessionGroupController,
  createProfessionGroupValidation,
  updateProfessionGroupValidation
};
