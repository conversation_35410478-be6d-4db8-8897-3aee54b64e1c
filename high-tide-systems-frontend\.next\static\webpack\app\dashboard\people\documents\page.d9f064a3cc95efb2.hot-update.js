"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/app/modules/people/DocumentsPage/DocumentsPage.js":
/*!***************************************************************!*\
  !*** ./src/app/modules/people/DocumentsPage/DocumentsPage.js ***!
  \***************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/tutorial/TutorialManager */ \"(app-pages-browser)/./src/components/tutorial/TutorialManager.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-grid.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Edit,ExternalLink,Eye,FileText,Filter,LayoutGrid,List,Loader2,Plus,RefreshCw,Settings,Share2,Tag,Trash,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_people_DocumentFormModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/people/DocumentFormModal */ \"(app-pages-browser)/./src/components/people/DocumentFormModal.js\");\n/* harmony import */ var _components_people_CategoryFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/people/CategoryFormModal */ \"(app-pages-browser)/./src/components/people/CategoryFormModal.js\");\n/* harmony import */ var _components_people_DocumentSharingModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/people/DocumentSharingModal */ \"(app-pages-browser)/./src/components/people/DocumentSharingModal.js\");\n/* harmony import */ var _components_people_DocumentsFilters__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/people/DocumentsFilters */ \"(app-pages-browser)/./src/components/people/DocumentsFilters.js\");\n/* harmony import */ var _components_people_DocumentGridView__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/people/DocumentGridView */ \"(app-pages-browser)/./src/components/people/DocumentGridView.js\");\n/* harmony import */ var _components_people_CategoryManager__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/people/CategoryManager */ \"(app-pages-browser)/./src/components/people/CategoryManager.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/dialog.jsx */ \"(app-pages-browser)/./src/components/ui/dialog.jsx\");\n/* harmony import */ var _components_people_DocumentSharingManagementModal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/people/DocumentSharingManagementModal */ \"(app-pages-browser)/./src/components/people/DocumentSharingManagementModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Componente para visualizar imagens\nconst ImageViewer = (param)=>{\n    let { documentUrl, filename } = param;\n    _s();\n    const [imageUrl, setImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageViewer.useEffect\": ()=>{\n            const fetchImage = {\n                \"ImageViewer.useEffect.fetchImage\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const token = localStorage.getItem('token');\n                        const response = await fetch(documentUrl, {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.ok) {\n                            const blob = await response.blob();\n                            const url = window.URL.createObjectURL(blob);\n                            setImageUrl(url);\n                        } else {\n                            setError('Erro ao carregar imagem');\n                        }\n                    } catch (err) {\n                        setError('Erro ao carregar imagem');\n                        console.error('Erro ao carregar imagem:', err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ImageViewer.useEffect.fetchImage\"];\n            fetchImage();\n            // Cleanup\n            return ({\n                \"ImageViewer.useEffect\": ()=>{\n                    if (imageUrl) {\n                        window.URL.revokeObjectURL(imageUrl);\n                    }\n                }\n            })[\"ImageViewer.useEffect\"];\n        }\n    }[\"ImageViewer.useEffect\"], [\n        documentUrl\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 32,\n                        className: \"animate-spin mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Carregando imagem...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        size: 48,\n                        className: \"mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 99,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 97,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: imageUrl,\n            alt: filename,\n            className: \"max-w-full max-h-[600px] object-contain rounded-lg shadow-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageViewer, \"vE4g1dBo95zdEf/8oGjbs9A68Ng=\");\n_c = ImageViewer;\n// Componente para visualizar PDFs\nconst PdfViewer = (param)=>{\n    let { documentUrl, filename } = param;\n    _s1();\n    const [pdfUrl, setPdfUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PdfViewer.useEffect\": ()=>{\n            const fetchPdf = {\n                \"PdfViewer.useEffect.fetchPdf\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const token = localStorage.getItem('token');\n                        const response = await fetch(documentUrl, {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.ok) {\n                            const blob = await response.blob();\n                            const url = window.URL.createObjectURL(blob);\n                            setPdfUrl(url);\n                        } else {\n                            setError('Erro ao carregar PDF');\n                        }\n                    } catch (err) {\n                        setError('Erro ao carregar PDF');\n                        console.error('Erro ao carregar PDF:', err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"PdfViewer.useEffect.fetchPdf\"];\n            fetchPdf();\n            // Cleanup\n            return ({\n                \"PdfViewer.useEffect\": ()=>{\n                    if (pdfUrl) {\n                        window.URL.revokeObjectURL(pdfUrl);\n                    }\n                }\n            })[\"PdfViewer.useEffect\"];\n        }\n    }[\"PdfViewer.useEffect\"], [\n        documentUrl\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 32,\n                        className: \"animate-spin mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Carregando PDF...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 165,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        size: 48,\n                        className: \"mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 174,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n            src: pdfUrl,\n            className: \"w-full h-full border-0\",\n            title: filename\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(PdfViewer, \"2o5HnQhBjFuoVyXLiGz0GwHAdmI=\");\n_c1 = PdfViewer;\n// Componente para visualizar arquivos de texto\nconst TextFileViewer = (param)=>{\n    let { documentUrl, filename } = param;\n    _s2();\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TextFileViewer.useEffect\": ()=>{\n            const fetchContent = {\n                \"TextFileViewer.useEffect.fetchContent\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        setError(null);\n                        const token = localStorage.getItem('token');\n                        const response = await fetch(documentUrl, {\n                            headers: {\n                                'Authorization': \"Bearer \".concat(token)\n                            }\n                        });\n                        if (response.ok) {\n                            const text = await response.text();\n                            setContent(text);\n                        } else {\n                            setError('Erro ao carregar arquivo de texto');\n                        }\n                    } catch (err) {\n                        setError('Erro ao carregar arquivo de texto');\n                        console.error('Erro ao carregar arquivo de texto:', err);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"TextFileViewer.useEffect.fetchContent\"];\n            fetchContent();\n        }\n    }[\"TextFileViewer.useEffect\"], [\n        documentUrl\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 32,\n                        className: \"animate-spin mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Carregando arquivo...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        size: 48,\n                        className: \"mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 242,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-auto p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            className: \"text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono\",\n            children: content\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n            lineNumber: 253,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(TextFileViewer, \"OsaZb6vwZ4WVEqUV2x9TA9civhc=\");\n_c2 = TextFileViewer;\n// Tutorial steps para a página de documentos\nconst documentsTutorialSteps = [\n    {\n        title: \"Documentos\",\n        content: \"Esta tela permite gerenciar documentos e categorias no sistema.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Novo Documento\",\n        content: \"Clique aqui para adicionar um novo documento.\",\n        selector: \"button:has(span:contains('Novo Documento'))\",\n        position: \"left\"\n    },\n    {\n        title: \"Nova Categoria\",\n        content: \"Clique aqui para criar uma nova categoria de documentos.\",\n        selector: \"button:has(span:contains('Nova Categoria'))\",\n        position: \"left\"\n    },\n    {\n        title: \"Filtrar Documentos\",\n        content: \"Use esta barra de pesquisa para encontrar documentos específicos pelo nome.\",\n        selector: \"input[placeholder*='Buscar']\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtrar por Categoria\",\n        content: \"Filtre os documentos por categoria.\",\n        selector: \"select:first-of-type\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de documentos em diferentes formatos usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Gerenciar Documentos\",\n        content: \"Visualize, edite, compartilhe ou exclua documentos usando os botões de ação na tabela.\",\n        selector: \"table\",\n        position: \"top\"\n    }\n];\nconst DocumentsPage = ()=>{\n    var _documentToView_categoryDocument;\n    _s3();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalDocuments, setTotalDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isFilterOpen, setIsFilterOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('table'); // 'table', 'grid', 'categories'\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDocument, setSelectedDocument] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [documentFormOpen, setDocumentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [categoryFormOpen, setCategoryFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sharingModalOpen, setSharingModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedDocumentForSharing, setSelectedDocumentForSharing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [viewerModalOpen, setViewerModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentToView, setDocumentToView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sharingManagementModalOpen, setSharingManagementModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentForSharingManagement, setDocumentForSharingManagement] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Constants\n    const ITEMS_PER_PAGE = 10;\n    // Função para carregar categorias\n    const loadCategories = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentsPage.useCallback[loadCategories]\": async ()=>{\n            try {\n                console.log('[loadCategories] Iniciando carregamento de categorias');\n                const response = await fetch('/api/documents/category', {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                console.log('[loadCategories] Response status:', response.status);\n                if (response.ok) {\n                    const data = await response.json();\n                    console.log('[loadCategories] Dados recebidos:', data);\n                    setCategories(Array.isArray(data) ? data : []);\n                } else {\n                    // Tentar obter detalhes do erro\n                    let errorMessage = \"\".concat(response.status, ' \"').concat(response.statusText, '\"');\n                    try {\n                        const errorData = await response.json();\n                        errorMessage = errorData.message || errorMessage;\n                    } catch (parseError) {\n                        console.log('[loadCategories] Não foi possível fazer parse do erro');\n                    }\n                    console.error('Erro ao carregar categorias:', errorMessage);\n                    // Definir categorias como array vazio para não quebrar a interface\n                    setCategories([]);\n                // Não exibir toast para evitar spam de erros\n                // if (typeof toast_error === 'function') {\n                //   toast_error({\n                //     title: 'Erro',\n                //     message: 'Não foi possível carregar as categorias. Tente novamente mais tarde.'\n                //   });\n                // }\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar categorias:\", error);\n                // Definir categorias como array vazio para não quebrar a interface\n                setCategories([]);\n            // Não exibir toast para evitar spam de erros\n            // if (typeof toast_error === 'function') {\n            //   toast_error({\n            //     title: 'Erro',\n            //     message: 'Não foi possível carregar as categorias. Tente novamente mais tarde.'\n            //   });\n            // }\n            }\n        }\n    }[\"DocumentsPage.useCallback[loadCategories]\"], []);\n    // Função para carregar dados auxiliares (empresas e usuários)\n    const loadAuxiliaryData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"DocumentsPage.useCallback[loadAuxiliaryData]\": async ()=>{\n            try {\n                // Carregar empresas\n                const companiesResponse = await fetch('/api/companies', {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (companiesResponse.ok) {\n                    const companiesData = await companiesResponse.json();\n                    setCompanies(companiesData.companies || companiesData || []);\n                }\n                // Carregar usuários\n                const usersResponse = await fetch('/api/users', {\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (usersResponse.ok) {\n                    const usersData = await usersResponse.json();\n                    setUsers(usersData.users || usersData || []);\n                }\n            } catch (error) {\n                console.error(\"Erro ao carregar dados auxiliares:\", error);\n            }\n        }\n    }[\"DocumentsPage.useCallback[loadAuxiliaryData]\"], []);\n    // Função para carregar documentos\n    const loadDocuments = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, currentFilters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : filters, sortField = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'filename', sortDirection = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'asc';\n        setIsLoading(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentPage(pageNumber);\n            const params = new URLSearchParams({\n                page: pageNumber.toString(),\n                limit: ITEMS_PER_PAGE.toString(),\n                ...sortField && {\n                    sortField\n                },\n                ...sortDirection && {\n                    sortDirection\n                }\n            });\n            // Adicionar filtros aos parâmetros\n            Object.entries(currentFilters).forEach((param)=>{\n                let [key, value] = param;\n                if (value && value !== '') {\n                    params.append(key, value);\n                }\n            });\n            const response = await fetch(\"/api/documents?\".concat(params), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setDocuments(data.documents || data || []);\n                setTotalDocuments(data.total || data.length || 0);\n                setTotalPages(data.totalPages || Math.ceil((data.total || data.length || 0) / ITEMS_PER_PAGE));\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar documentos:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentsPage.useEffect\": ()=>{\n            loadCategories();\n            loadAuxiliaryData();\n            loadDocuments();\n        }\n    }[\"DocumentsPage.useEffect\"], [\n        loadCategories,\n        loadAuxiliaryData\n    ]);\n    // Handlers\n    const handlePageChange = (page)=>{\n        loadDocuments(page, filters);\n    };\n    const handleEditDocument = (document)=>{\n        setSelectedDocument(document);\n        setDocumentFormOpen(true);\n    };\n    const handleDeleteDocument = (document)=>{\n        setSelectedDocument(document);\n        setActionToConfirm({\n            action: 'delete',\n            message: 'Tem certeza que deseja excluir o documento \"'.concat(document.filename, '\"?')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleShareDocument = (document)=>{\n        setSelectedDocumentForSharing(document);\n        setSharingModalOpen(true);\n    };\n    const handleManageSharing = (document)=>{\n        setDocumentForSharingManagement(document);\n        setSharingManagementModalOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Implementar exportação\n            console.log(\"Exportando em formato \".concat(format));\n        } catch (error) {\n            console.error(\"Erro ao exportar:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if ((actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.action) === 'delete' && selectedDocument) {\n            try {\n                const response = await fetch(\"/api/documents/\".concat(selectedDocument.id), {\n                    method: 'DELETE',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                    }\n                });\n                if (response.ok) {\n                    loadDocuments(currentPage, filters);\n                }\n            } catch (error) {\n                console.error(\"Erro ao excluir documento:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n        setSelectedDocument(null);\n        setActionToConfirm(null);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"-\";\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_18__.format)(new Date(dateString), \"dd/MM/yyyy 'às' HH:mm\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_19__.ptBR\n            });\n        } catch (e) {\n            return dateString;\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    const handleCloseDocumentModal = ()=>{\n        setDocumentFormOpen(false);\n        setSelectedDocument(null);\n        loadDocuments(1, filters);\n    };\n    const handleCloseCategoryModal = ()=>{\n        setCategoryFormOpen(false);\n        setSelectedCategory(null);\n        loadCategories();\n    };\n    const handleCloseSharingModal = ()=>{\n        setSharingModalOpen(false);\n        setSelectedDocumentForSharing(null);\n    };\n    const handleFiltersChange = (newFilters)=>{\n        setFilters(newFilters);\n        // Aplicar filtros e recarregar documentos\n        loadDocuments(1, newFilters);\n    };\n    const handleCategoryUpdate = ()=>{\n        loadCategories();\n    };\n    const handleCategoryDelete = async (categoryId)=>{\n        try {\n            const response = await fetch(\"/api/categories/\".concat(categoryId), {\n                method: 'DELETE',\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                loadCategories();\n                loadDocuments(currentPage, filters);\n            }\n        } catch (error) {\n            console.error(\"Erro ao excluir categoria:\", error);\n        }\n    };\n    const handleViewDocument = (document)=>{\n        setDocumentToView(document);\n        setViewerModalOpen(true);\n    };\n    const handleDownloadDocument = async (document)=>{\n        try {\n            const response = await fetch(\"/api/documents/\".concat(document.id, \"/download\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                const a = document.createElement('a');\n                a.style.display = 'none';\n                a.href = url;\n                a.download = document.filename;\n                document.body.appendChild(a);\n                a.click();\n                window.URL.revokeObjectURL(url);\n            }\n        } catch (error) {\n            console.error(\"Erro ao baixar documento:\", error);\n        }\n    };\n    const handleOpenInNewTab = async (doc)=>{\n        try {\n            const token = localStorage.getItem('token');\n            const response = await fetch(\"/api/documents/\".concat(doc.id), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(token)\n                }\n            });\n            if (response.ok) {\n                const blob = await response.blob();\n                const url = window.URL.createObjectURL(blob);\n                // Abrir em nova aba\n                const newWindow = window.open(url, '_blank');\n                // Limpar a URL após um tempo para evitar vazamento de memória\n                setTimeout(()=>{\n                    window.URL.revokeObjectURL(url);\n                }, 1000);\n            } else {\n                alert('Erro ao abrir documento em nova aba');\n            }\n        } catch (error) {\n            console.error('Erro ao abrir documento em nova aba:', error);\n            alert('Erro ao abrir documento em nova aba');\n        }\n    };\n    const getDocumentViewerContent = (doc)=>{\n        if (!doc) return null;\n        const documentUrl = \"/api/documents/\".concat(doc.id);\n        // Determinar o tipo de visualização baseado no mimeType\n        const mimeType = doc.mimeType || '';\n        if (mimeType.startsWith('image/')) {\n            // Para imagens, usar componente específico\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageViewer, {\n                documentUrl: documentUrl,\n                filename: doc.filename\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 673,\n                columnNumber: 14\n            }, undefined);\n        } else if (mimeType === 'application/pdf') {\n            // Para PDFs, usar componente específico\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PdfViewer, {\n                documentUrl: documentUrl,\n                filename: doc.filename\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 676,\n                columnNumber: 14\n            }, undefined);\n        } else if (mimeType.startsWith('text/')) {\n            // Para arquivos de texto, usar componente de texto\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TextFileViewer, {\n                documentUrl: documentUrl,\n                filename: doc.filename\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 679,\n                columnNumber: 14\n            }, undefined);\n        } else {\n            // Para outros tipos, mostrar mensagem de visualização não suportada\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            size: 64,\n                            className: \"mx-auto mb-4 text-gray-400 dark:text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                            children: \"Visualiza\\xe7\\xe3o n\\xe3o suportada\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 686,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 dark:text-gray-400 mb-4\",\n                            children: \"Este tipo de arquivo n\\xe3o pode ser visualizado diretamente no navegador.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 689,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleDownloadDocument(doc),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Baixar\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 693,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleOpenInNewTab(doc),\n                                    className: \"flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Abrir em nova aba\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 700,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 692,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 684,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 683,\n                columnNumber: 9\n            }, undefined);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 22,\n                                className: \"text-module-people-icon dark:text-module-people-icon-dark\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 719,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                        children: \"Documentos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                        children: \"Gerencie documentos e categorias do sistema\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 720,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || documents.length === 0,\n                                className: \"text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setCategoryFormOpen(true),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-lg shadow transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 742,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Nova Categoria\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedDocument(null);\n                                    setDocumentFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg shadow transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 753,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Novo Documento\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 746,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-people-icon dark:text-module-people-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 762,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie documentos e categorias do sistema. Utilize os filtros abaixo para encontrar documentos espec\\xedficos.\",\n                moduleColor: \"people\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_DocumentsFilters__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: handleFiltersChange,\n                    onSearch: (searchTerm)=>{\n                        const newFilters = {\n                            ...filters,\n                            search: searchTerm\n                        };\n                        setFilters(newFilters);\n                        loadDocuments(1, newFilters);\n                    },\n                    categories: categories,\n                    companies: companies,\n                    users: users\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 766,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 760,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                children: \"Visualiza\\xe7\\xe3o:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 784,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentView('table'),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(currentView === 'table' ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Lista\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 786,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentView('grid'),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(currentView === 'grid' ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 805,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Grade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 797,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentView('categories'),\n                                        className: \"flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors \".concat(currentView === 'categories' ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm' : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 816,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Categorias\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                        lineNumber: 808,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 785,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 783,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            totalDocuments,\n                            \" documento(s) encontrado(s)\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 822,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 782,\n                columnNumber: 7\n            }, undefined),\n            currentView === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_CategoryManager__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                categories: categories,\n                onCategoryUpdate: handleCategoryUpdate,\n                onCategoryDelete: handleCategoryDelete,\n                isLoading: isLoading,\n                companies: companies,\n                users: users\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 829,\n                columnNumber: 9\n            }, undefined) : currentView === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_DocumentGridView__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                documents: documents,\n                isLoading: isLoading,\n                onView: handleViewDocument,\n                onEdit: handleEditDocument,\n                onShare: handleShareDocument,\n                onDelete: handleDeleteDocument,\n                onDownload: handleDownloadDocument,\n                onManageSharing: handleManageSharing\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 838,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.ModuleTable, {\n                moduleColor: \"people\",\n                title: \"Lista de Documentos\",\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadDocuments(),\n                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 858,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 853,\n                    columnNumber: 13\n                }, void 0),\n                columns: [\n                    {\n                        header: 'Nome',\n                        field: 'filename',\n                        width: '25%'\n                    },\n                    {\n                        header: 'Arquivo',\n                        field: 'file',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Categoria',\n                        field: 'category',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Compartilhado com',\n                        field: 'shared',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Cadastro',\n                        field: 'createdAt',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '10%',\n                        sortable: false\n                    }\n                ],\n                data: documents,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum documento encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 872,\n                    columnNumber: 22\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalDocuments,\n                onPageChange: handlePageChange,\n                showPagination: totalPages > 1,\n                tableId: \"people-documents-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"filename\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    loadDocuments(currentPage, filters, field, direction);\n                },\n                renderRow: (document, _, moduleColors, visibleColumns)=>{\n                    var _document_categoryDocument, _document_categoryDocument1, _document_DocumentPermission, _document_DocumentPermission1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('filename') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 891,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 890,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: document.filename\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-neutral-400 truncate\",\n                                                    children: formatFileSize(document.size)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 893,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 889,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 888,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('file') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 908,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: document.filename\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 909,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 907,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 906,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('category') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400\",\n                                            children: ((_document_categoryDocument = document.categoryDocument) === null || _document_categoryDocument === void 0 ? void 0 : _document_categoryDocument.name) || \"Sem categoria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 917,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        ((_document_categoryDocument1 = document.categoryDocument) === null || _document_categoryDocument1 === void 0 ? void 0 : _document_categoryDocument1.company) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: document.categoryDocument.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 921,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 916,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 915,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('shared') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleManageSharing(document),\n                                    className: \"flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors cursor-pointer\",\n                                    title: \"Gerenciar compartilhamentos\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 936,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-600 dark:text-neutral-300 text-sm\",\n                                            children: [\n                                                ((_document_DocumentPermission = document.DocumentPermission) === null || _document_DocumentPermission === void 0 ? void 0 : _document_DocumentPermission.length) || 0,\n                                                \" \",\n                                                (((_document_DocumentPermission1 = document.DocumentPermission) === null || _document_DocumentPermission1 === void 0 ? void 0 : _document_DocumentPermission1.length) || 0) === 1 ? \"compartilhamento\" : \"compartilhamentos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 937,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 931,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 930,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: formatDate(document.createdAt)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 945,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewDocument(document),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 958,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 953,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleShareDocument(document),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400 transition-colors\",\n                                            title: \"Compartilhar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 966,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 961,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditDocument(document),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 974,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 969,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDeleteDocument(document),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                            title: \"Excluir\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                lineNumber: 982,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 977,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 952,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                lineNumber: 951,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, document.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                        lineNumber: 886,\n                        columnNumber: 11\n                    }, void 0);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 849,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                moduleColor: \"people\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 993,\n                columnNumber: 7\n            }, undefined),\n            documentFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_DocumentFormModal__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: documentFormOpen,\n                onClose: handleCloseDocumentModal,\n                document: selectedDocument,\n                categories: categories,\n                onSuccess: ()=>{\n                    handleCloseDocumentModal();\n                    loadDocuments(1, filters);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1004,\n                columnNumber: 9\n            }, undefined),\n            categoryFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_CategoryFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: categoryFormOpen,\n                onClose: handleCloseCategoryModal,\n                category: selectedCategory,\n                onSuccess: ()=>{\n                    handleCloseCategoryModal();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1018,\n                columnNumber: 9\n            }, undefined),\n            sharingModalOpen && selectedDocumentForSharing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_DocumentSharingModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: sharingModalOpen,\n                onClose: handleCloseSharingModal,\n                document: selectedDocumentForSharing,\n                onSuccess: ()=>{\n                    handleCloseSharingModal();\n                    loadDocuments(1, filters);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1030,\n                columnNumber: 9\n            }, undefined),\n            viewerModalOpen && documentToView && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_14__.Dialog, {\n                open: viewerModalOpen,\n                onOpenChange: setViewerModalOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_14__.DialogContent, {\n                    className: \"max-w-6xl max-h-[90vh] overflow-hidden border-2 border-orange-400 dark:border-orange-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_14__.DialogHeader, {\n                            className: \"flex-shrink-0\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_14__.DialogTitle, {\n                                            className: \"text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-orange-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 1048,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                documentToView.filename\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 1047,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDownloadDocument(documentToView),\n                                                    className: \"p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors\",\n                                                    title: \"Baixar documento\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                        lineNumber: 1057,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 1052,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleOpenInNewTab(documentToView),\n                                                    className: \"p-2 text-gray-500 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors\",\n                                                    title: \"Abrir em nova aba\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                        lineNumber: 1064,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setViewerModalOpen(false),\n                                                    className: \"p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors\",\n                                                    title: \"Fechar\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Edit_ExternalLink_Eye_FileText_Filter_LayoutGrid_List_Loader2_Plus_RefreshCw_Settings_Share2_Tag_Trash_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                        lineNumber: 1071,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 1051,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 1046,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Tamanho: \",\n                                                formatFileSize(documentToView.size)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 1076,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Tipo: \",\n                                                documentToView.mimeType\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 1077,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                \"Categoria: \",\n                                                ((_documentToView_categoryDocument = documentToView.categoryDocument) === null || _documentToView_categoryDocument === void 0 ? void 0 : _documentToView_categoryDocument.name) || \"Sem categoria\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                            lineNumber: 1078,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                                    lineNumber: 1075,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 1045,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-hidden mt-4\",\n                            children: getDocumentViewerContent(documentToView)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                            lineNumber: 1082,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                    lineNumber: 1044,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1043,\n                columnNumber: 9\n            }, undefined),\n            sharingManagementModalOpen && documentForSharingManagement && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_DocumentSharingManagementModal__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                isOpen: sharingManagementModalOpen,\n                onClose: ()=>{\n                    setSharingManagementModalOpen(false);\n                    setDocumentForSharingManagement(null);\n                },\n                document: documentForSharingManagement,\n                onSuccess: ()=>{\n                    setSharingManagementModalOpen(false);\n                    setDocumentForSharingManagement(null);\n                    loadDocuments(1, filters);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1091,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n                lineNumber: 1107,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\DocumentsPage\\\\DocumentsPage.js\",\n        lineNumber: 715,\n        columnNumber: 5\n    }, undefined);\n};\n_s3(DocumentsPage, \"MwyCDbStM+PKex2gzNc9HierhGY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c3 = DocumentsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentsPage);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ImageViewer\");\n$RefreshReg$(_c1, \"PdfViewer\");\n$RefreshReg$(_c2, \"TextFileViewer\");\n$RefreshReg$(_c3, \"DocumentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/people/DocumentsPage/DocumentsPage.js\n"));

/***/ })

});