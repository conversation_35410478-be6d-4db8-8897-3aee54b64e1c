"use client";

import React, { useState, useEffect, useRef } from "react";
import {
  FileText,
  Upload,
  Plus,
  Loader2,
  RefreshCw,
  Grid,
  List,
  ChevronDown
} from "lucide-react";
import { api } from "@/utils/api";
import DocumentCard from "./DocumentCard";
import DocumentUploadModal from "./DocumentUploadModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

const PersonDocumentsSection = ({ personId }) => {
  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState(null);
  const [documentUploadOpen, setDocumentUploadOpen] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const [viewType, setViewType] = useState("grid"); // "grid" or "list"
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    loadDocuments();
  }, [personId]);

  useEffect(() => {
    // Close dropdown when clicking outside
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const loadDocuments = async () => {
    if (!personId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get("/documents", {
        params: {
          targetId: personId,
          targetType: "person"
        }
      });
      setDocuments(response.data || []);
    } catch (err) {
      console.error("Error fetching documents:", err);
      setError("Não foi possível carregar os documentos.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenUploadModal = (documentType) => {
    setSelectedDocumentType(documentType);
    setDocumentUploadOpen(true);
    setDropdownOpen(false);
  };

  const handleDeleteDocument = (documentId) => {
    setDocumentToDelete(documentId);
    setConfirmDialogOpen(true);
  };

  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      await api.delete(`/documents/${documentToDelete}`);
      setDocuments(documents.filter(doc => doc.id !== documentToDelete));
      setConfirmDialogOpen(false);
      setDocumentToDelete(null);
    } catch (err) {
      console.error("Error deleting document:", err);
      setError("Não foi possível excluir o documento.");
    }
  };

  const handleViewDocument = (documentId) => {
    window.open(`/api/documents/${documentId}`, '_blank');
  };

  const documentTypes = [
    { id: "RG", label: "RG" },
    { id: "CPF", label: "CPF" },
    { id: "CNH", label: "Carteira de Motorista" },
    { id: "COMP_RESIDENCIA", label: "Comprovante de Residência" },
    { id: "CERTIDAO_NASCIMENTO", label: "Certidão de Nascimento" },
    { id: "CERTIDAO_CASAMENTO", label: "Certidão de Casamento" },
    { id: "CARTAO_VACINACAO", label: "Cartão de Vacinação" },
    { id: "PASSAPORTE", label: "Passaporte" },
    { id: "TITULO_ELEITOR", label: "Título de Eleitor" },
    { id: "CARTEIRA_TRABALHO", label: "Carteira de Trabalho" },
    { id: "OUTROS", label: "Outros" }
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold dark:text-white text-neutral-800">Documentos</h3>
          {isLoading ? (
            <Loader2 size={16} className="animate-spin text-neutral-400 dark:text-gray-500" />
          ) : (
            <span className="text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
              {documents.length}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          <div className="flex border border-neutral-200 dark:border-gray-700 rounded-lg overflow-hidden">
            <button
              onClick={() => setViewType("grid")}
              className={`p-2 ${viewType === "grid" ? "bg-primary-500 dark:bg-primary-600 text-white" : "bg-white dark:bg-gray-800 text-neutral-600 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-700"}`}
              title="Visualização em grade"
            >
              <Grid size={16} />
            </button>
            <button
              onClick={() => setViewType("list")}
              className={`p-2 ${viewType === "list" ? "bg-primary-500 dark:bg-primary-600 text-white" : "bg-white dark:bg-gray-800 text-neutral-600 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-700"}`}
              title="Visualização em lista"
            >
              <List size={16} />
            </button>
          </div>

          <button
            onClick={() => loadDocuments()}
            className="p-2 text-neutral-600 dark:text-gray-300 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Atualizar"
          >
            <RefreshCw size={16} />
          </button>

          {/* Dropdown menu for document type selection */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
            >
              <Upload size={16} />
              <span>Adicionar</span>
              <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {dropdownOpen && (
              <div className="absolute right-0 mt-1 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg dark:shadow-lg dark:shadow-black/30 overflow-hidden z-10 border border-neutral-200 dark:border-gray-700">
                <div className="p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600">
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-200">Selecione o tipo de documento</h4>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {documentTypes.map(type => (
                    <button
                      key={type.id}
                      onClick={() => handleOpenUploadModal(type.id)}
                      className="w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors"
                    >
                      <FileText size={14} className="text-neutral-400 dark:text-gray-500" />
                      <span>{type.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300">
          {error}
          <button
            onClick={loadDocuments}
            className="ml-2 underline hover:no-underline"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-lg dark:shadow-black/30 p-8 flex justify-center items-center">
          <div className="flex flex-col items-center">
            <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin mb-4" />
            <p className="text-neutral-600 dark:text-gray-300">Carregando documentos...</p>
          </div>
        </div>
      ) : documents.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-lg dark:shadow-black/30 p-8 flex flex-col items-center">
          <FileText size={48} className="text-neutral-300 dark:text-gray-600 mb-4" />
          <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2">Nenhum documento</h4>
          <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center">
            Adicione documentos importantes como RG, CPF, comprovantes e outros para esta pessoa.
          </p>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 max-w-2xl">
            {documentTypes.slice(0, 8).map(type => (
              <button
                key={type.id}
                onClick={() => handleOpenUploadModal(type.id)}
                className="flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
              >
                <span className="text-xs text-neutral-600 dark:text-gray-300 text-center">{type.label}</span>
                <Plus size={16} className="text-primary-500 dark:text-primary-400 mt-1" />
              </button>
            ))}
            <button
              onClick={() => setDocumentUploadOpen(true)}
              className="flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
            >
              <span className="text-xs text-neutral-600 dark:text-gray-300">Outros</span>
              <Plus size={16} className="text-primary-500 dark:text-primary-400 mt-1" />
            </button>
          </div>
        </div>
      ) : viewType === "grid" ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {documents.map(document => (
            <DocumentCard
              key={document.id}
              document={document}
              onView={handleViewDocument}
              onDelete={handleDeleteDocument}
            />
          ))}
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-lg dark:shadow-black/30">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
            <thead>
              <tr className="bg-neutral-50 dark:bg-gray-700">
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                  Arquivo
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                  Data de Upload
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-300 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
              {documents.map(document => {
                const getDocumentTypeDisplay = (type) => {
                  const typeMap = {
                    "RG": "RG",
                    "CPF": "CPF",
                    "CNH": "Carteira de Motorista",
                    "COMP_RESIDENCIA": "Comprovante de Residência",
                    "CERTIDAO_NASCIMENTO": "Certidão de Nascimento",
                    "CERTIDAO_CASAMENTO": "Certidão de Casamento",
                    "CARTAO_VACINACAO": "Cartão de Vacinação",
                    "PASSAPORTE": "Passaporte",
                    "TITULO_ELEITOR": "Título de Eleitor",
                    "CARTEIRA_TRABALHO": "Carteira de Trabalho",
                    "OUTROS": "Outros"
                  };

                  return typeMap[type] || type;
                };

                const formatDate = (dateString) => {
                  if (!dateString) return "N/A";

                  try {
                    return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
                  } catch (error) {
                    return "Data inválida";
                  }
                };

                return (
                  <tr key={document.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-200">
                        {getDocumentTypeDisplay(document.type)}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 text-neutral-400 dark:text-gray-500 mr-2" />
                        <span className="text-neutral-700 dark:text-gray-200">{document.filename}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-neutral-500 dark:text-gray-400">
                      {formatDate(document.createdAt)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-right">
                      <div className="flex justify-end">
                        <button
                          onClick={() => handleViewDocument(document.id)}
                          className="text-xs px-2 py-1 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mx-1"
                        >
                          Visualizar
                        </button>
                        <button
                          onClick={() => window.open(`/api/documents/${document.id}?download=true`, '_blank')}
                          className="text-xs px-2 py-1 text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 mx-1"
                        >
                          Baixar
                        </button>
                        <button
                          onClick={() => handleDeleteDocument(document.id)}
                          className="text-xs px-2 py-1 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 mx-1"
                        >
                          Excluir
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* Document Upload Modal */}
      <DocumentUploadModal
        isOpen={documentUploadOpen}
        onClose={() => {
          setDocumentUploadOpen(false);
          setSelectedDocumentType(null);
        }}
        documentType={selectedDocumentType || "RG"}
        targetId={personId}
        targetType="person"
        onSuccess={loadDocuments}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => {
          setConfirmDialogOpen(false);
          setDocumentToDelete(null);
        }}
        onConfirm={confirmDeleteDocument}
        title="Excluir Documento"
        message="Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita."
        variant="danger"
        moduleColor="people"
        confirmText="Excluir"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default PersonDocumentsSection;