"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/app/modules/people/ClientsPage/ClientsPage.js":
/*!***********************************************************!*\
  !*** ./src/app/modules/people/ClientsPage/ClientsPage.js ***!
  \***********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/tutorial/TutorialManager */ \"(app-pages-browser)/./src/components/tutorial/TutorialManager.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Edit,Eye,Filter,Mail,Plus,Power,RefreshCw,Trash,UserPlus,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/people/services/clientsService */ \"(app-pages-browser)/./src/app/modules/people/services/clientsService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_people_ClientFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/people/ClientFormModal */ \"(app-pages-browser)/./src/components/people/ClientFormModal.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_common_ShareButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/common/ShareButton */ \"(app-pages-browser)/./src/components/common/ShareButton.js\");\n/* harmony import */ var _components_people_ClientsFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/people/ClientsFilters */ \"(app-pages-browser)/./src/components/people/ClientsFilters.js\");\n/* harmony import */ var _components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/SensitiveField */ \"(app-pages-browser)/./src/components/ui/SensitiveField.js\");\n/* harmony import */ var _hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/useDataPrivacy */ \"(app-pages-browser)/./src/hooks/useDataPrivacy.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Tutorial steps para a página de clientes\nconst clientsTutorialSteps = [\n    {\n        title: \"Clientes\",\n        content: \"Esta tela permite gerenciar o cadastro de clientes no sistema.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Novo Cliente\",\n        content: \"Clique aqui para adicionar um novo cliente.\",\n        selector: \"button:has(span:contains('Novo Cliente'))\",\n        position: \"left\"\n    },\n    {\n        title: \"Filtrar Clientes\",\n        content: \"Use esta barra de pesquisa para encontrar clientes específicos pelo login ou email.\",\n        selector: \"input[placeholder*='Buscar']\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtros Avançados\",\n        content: \"Clique no botão Filtros para acessar opções avançadas de filtragem.\",\n        selector: \"button:has(span:contains('Filtros'))\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de clientes em diferentes formatos usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Gerenciar Clientes\",\n        content: \"Visualize, edite, ative/desative ou exclua clientes usando os botões de ação na tabela.\",\n        selector: \"table\",\n        position: \"top\"\n    }\n];\nconst ClientsPage = ()=>{\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { applyListPrivacyMasks } = (0,_hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_14__.useDataPrivacy)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)();\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalClients, setTotalClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedClient, setSelectedClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [clientFormOpen, setClientFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(clients.map((c)=>c.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Estado para os filtros\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        clients: [],\n        dateFrom: \"\",\n        dateTo: \"\"\n    });\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const loadClients = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, filtersToUse = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : filters, sortField = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'fullName', sortDirection = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'asc', perPage = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : itemsPerPage;\n        console.log('loadClients chamado com parâmetros:', {\n            page,\n            filters: filtersToUse,\n            sortField,\n            sortDirection,\n            perPage\n        });\n        setIsLoading(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentPage(pageNumber);\n            const requestParams = {\n                page: pageNumber,\n                limit: perPage,\n                search: filtersToUse.search || undefined,\n                clientIds: filtersToUse.clients.length > 0 ? filtersToUse.clients : undefined,\n                active: filtersToUse.status === \"\" ? undefined : filtersToUse.status === \"active\",\n                companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,\n                dateFrom: filtersToUse.dateFrom || undefined,\n                dateTo: filtersToUse.dateTo || undefined,\n                sortField,\n                sortDirection,\n                include_persons: 'true'\n            };\n            console.log('ClientsPage - Parâmetros enviados para API:', requestParams);\n            const response = await _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__.clientsService.getClients(requestParams);\n            const clientsData = (response === null || response === void 0 ? void 0 : response.clients) || (response === null || response === void 0 ? void 0 : response.data) || [];\n            console.log(\"ClientsPage - Resposta da API:\", response);\n            console.log(\"ClientsPage - Dados dos clientes (primeiros 3):\", clientsData.slice(0, 3).map((c)=>({\n                    id: c.id,\n                    fullName: c.fullName,\n                    login: c.login\n                })));\n            if (!Array.isArray(clientsData)) {\n                setClients([]);\n            } else {\n                // Aplicar máscaras de privacidade aos dados dos clientes\n                const clientsWithPrivacy = applyListPrivacyMasks('client', clientsData);\n                console.log('🔒 Máscaras de privacidade aplicadas aos clientes');\n                setClients(clientsWithPrivacy);\n            }\n            setTotalClients((response === null || response === void 0 ? void 0 : response.total) || 0);\n            setTotalPages((response === null || response === void 0 ? void 0 : response.pages) || 1);\n        } catch (error) {\n            console.error(\"Erro ao carregar clientes:\", error);\n            setClients([]);\n            setTotalClients(0);\n            setTotalPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientsPage.useEffect\": ()=>{\n            console.log('ClientsPage - Carregamento inicial com ordenação por fullName asc');\n            loadClients(1, filters, 'fullName', 'asc');\n        }\n    }[\"ClientsPage.useEffect\"], []);\n    // Efeito para abrir o modal automaticamente baseado nos parâmetros da URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ClientsPage.useEffect\": ()=>{\n            const clientId = searchParams.get('clientId');\n            const openModal = searchParams.get('openModal');\n            if (clientId && openModal === 'true') {\n                const client = clients.find({\n                    \"ClientsPage.useEffect.client\": (c)=>c.id === clientId\n                }[\"ClientsPage.useEffect.client\"]);\n                if (client) {\n                    setSelectedClient(client);\n                    setClientFormOpen(true);\n                } else {\n                    _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__.clientsService.getClient(clientId).then({\n                        \"ClientsPage.useEffect\": (clientData)=>{\n                            setSelectedClient(clientData);\n                            setClientFormOpen(true);\n                        }\n                    }[\"ClientsPage.useEffect\"]).catch({\n                        \"ClientsPage.useEffect\": (error)=>{\n                            console.error(\"Erro ao buscar cliente:\", error);\n                        }\n                    }[\"ClientsPage.useEffect\"]);\n                }\n            }\n        }\n    }[\"ClientsPage.useEffect\"], [\n        searchParams,\n        clients\n    ]);\n    const handleSearch = (searchFilters)=>{\n        loadClients(1, searchFilters, 'fullName', 'asc');\n    };\n    const handlePageChange = (page)=>{\n        loadClients(page, filters, 'fullName', 'asc');\n    };\n    const handleEditClient = (client)=>{\n        setSelectedClient(client);\n        setClientFormOpen(true);\n    };\n    const handleToggleStatus = (client)=>{\n        var _client_clientPersons__person;\n        setSelectedClient(client);\n        const clientName = client.clientPersons && client.clientPersons[0] && ((_client_clientPersons__person = client.clientPersons[0].person) === null || _client_clientPersons__person === void 0 ? void 0 : _client_clientPersons__person.fullName) ? client.clientPersons[0].person.fullName : client.login;\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(client.active ? \"Desativar\" : \"Ativar\", \" o cliente \").concat(clientName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteClient = (client)=>{\n        var _client_clientPersons__person;\n        setSelectedClient(client);\n        const clientName = client.clientPersons && client.clientPersons[0] && ((_client_clientPersons__person = client.clientPersons[0].person) === null || _client_clientPersons__person === void 0 ? void 0 : _client_clientPersons__person.fullName) ? client.clientPersons[0].person.fullName : client.login;\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente o cliente \".concat(clientName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            await _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__.clientsService.exportClients({\n                search: filters.search || undefined,\n                clientIds: filters.clients.length > 0 ? filters.clients : undefined,\n                active: filters.status === \"\" ? undefined : filters.status === \"active\",\n                companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,\n                dateFrom: filters.dateFrom || undefined,\n                dateTo: filters.dateTo || undefined,\n                include_persons: 'true'\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar clientes:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if (actionToConfirm.type === \"toggle-status\") {\n            try {\n                await _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__.clientsService.toggleClientStatus(selectedClient.id);\n                loadClients(currentPage, filters, 'fullName', 'asc');\n            } catch (error) {\n                console.error(\"Erro ao alterar status do cliente:\", error);\n            }\n        } else if (actionToConfirm.type === \"delete\") {\n            try {\n                await _app_modules_people_services_clientsService__WEBPACK_IMPORTED_MODULE_6__.clientsService.deleteClient(selectedClient.id);\n                loadClients(currentPage, filters, 'fullName', 'asc');\n            } catch (error) {\n                console.error(\"Erro ao excluir cliente:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"N/A\";\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(dateString), \"dd/MM/yyyy\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_16__.ptBR\n            });\n        } catch (error) {\n            return \"Data inválida\";\n        }\n    };\n    const handleCloseClientModal = ()=>{\n        setClientFormOpen(false);\n        setSelectedClient(null);\n        const params = new URLSearchParams(window.location.search);\n        params.delete('clientId');\n        params.delete('openModal');\n        router.replace(\"/dashboard/people/clients\".concat(params.toString() ? '?' + params.toString() : ''));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-orange-600 dark:text-orange-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Clientes\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                        lineNumber: 310,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir clientes selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || clients.length === 0,\n                                className: \"text-orange-700 dark:text-orange-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedClient(null);\n                                    setClientFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                        lineNumber: 340,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Cliente\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-people-icon dark:text-module-people-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                    lineNumber: 349,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie o cadastro de clientes no sistema. Utilize os filtros abaixo para encontrar clientes espec\\xedficos.\",\n                tutorialSteps: clientsTutorialSteps,\n                tutorialName: \"clients-overview\",\n                moduleColor: \"people\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_ClientsFilters__WEBPACK_IMPORTED_MODULE_12__.ClientsFilters, {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: handleSearch\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 347,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.ModuleTable, {\n                moduleColor: \"people\",\n                title: \"Lista de Clientes\",\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadClients(),\n                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                        lineNumber: 373,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                    lineNumber: 368,\n                    columnNumber: 11\n                }, void 0),\n                columns: [\n                    {\n                        header: '',\n                        field: 'select',\n                        width: '50px',\n                        sortable: false\n                    },\n                    {\n                        header: 'Cliente',\n                        field: 'fullName',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Email',\n                        field: 'email',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Pessoas',\n                        field: 'persons',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Status',\n                        field: 'active',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Cadastro',\n                        field: 'createdAt',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '15%',\n                        sortable: false\n                    }\n                ],\n                data: clients,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum cliente encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                    lineNumber: 388,\n                    columnNumber: 20\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalClients,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                tableId: \"people-clients-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"fullName\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    console.log('ClientsPage - Ordenação solicitada:', {\n                        field,\n                        direction\n                    });\n                    loadClients(currentPage, filters, field, direction);\n                },\n                itemsPerPage: itemsPerPage,\n                onItemsPerPageChange: (newItemsPerPage)=>{\n                    setItemsPerPage(newItemsPerPage);\n                    loadClients(1, filters, 'fullName', 'asc', newItemsPerPage);\n                },\n                selectedIds: selectedIds,\n                onSelectAll: handleSelectAll,\n                renderRow: (client, _, moduleColors, visibleColumns)=>{\n                    var _client_clientPersons;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_4__.ModuleCheckbox, {\n                                    moduleColor: \"people\",\n                                    checked: selectedIds.includes(client.id),\n                                    onChange: (e)=>handleSelectOne(client.id, e.target.checked),\n                                    name: \"select-client-\".concat(client.id)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 413,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 412,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('fullName') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\",\n                                            children: (client.fullName || client.login).charAt(0).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_13__.SensitiveFullName, {\n                                                        entityType: \"client\",\n                                                        value: client.fullName || client.login,\n                                                        data: client,\n                                                        showToggle: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-neutral-500 dark:text-neutral-400 truncate\",\n                                                    children: client.login\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 423,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('email') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 447,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_13__.SensitiveEmail, {\n                                                entityType: \"client\",\n                                                value: client.email,\n                                                data: client,\n                                                showToggle: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 449,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 448,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 446,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 445,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('persons') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 463,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-600 dark:text-neutral-300 text-sm font-medium\",\n                                            children: ((_client_clientPersons = client.clientPersons) === null || _client_clientPersons === void 0 ? void 0 : _client_clientPersons.length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 464,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 462,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(client.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                    children: client.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 481,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 482,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 486,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 487,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 473,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 472,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                children: formatDate(client.createdAt)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ShareButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            itemType: \"client\",\n                                            itemId: client.id,\n                                            itemTitle: client.fullName,\n                                            size: \"xs\",\n                                            variant: \"ghost\",\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>router.push(\"/dashboard/people/clients/\".concat(client.id)),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Ver detalhes\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 517,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 512,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditClient(client),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 520,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleToggleStatus(client),\n                                            className: \"p-1 transition-colors \".concat(client.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                            title: client.active ? \"Desativar\" : \"Ativar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 536,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 528,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDeleteClient(client),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                            title: \"Excluir\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Edit_Eye_Filter_Mail_Plus_Power_RefreshCw_Trash_UserPlus_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                                lineNumber: 544,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                            lineNumber: 539,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                    lineNumber: 502,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, client.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, void 0);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 364,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                moduleColor: \"people\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 553,\n                columnNumber: 7\n            }, undefined),\n            clientFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_ClientFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: clientFormOpen,\n                onClose: handleCloseClientModal,\n                client: selectedClient,\n                onSuccess: ()=>{\n                    handleCloseClientModal();\n                    loadClients(1, filters, 'fullName', 'asc');\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 563,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\ClientsPage\\\\ClientsPage.js\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ClientsPage, \"EuB/FH578/zXHqOdn6SkFayBvEc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_14__.useDataPrivacy,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = ClientsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientsPage);\nvar _c;\n$RefreshReg$(_c, \"ClientsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/people/ClientsPage/ClientsPage.js\n"));

/***/ })

});