"use client";

import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Clock, 
  User, 
  MapPin, 
  Stethoscope, 
  MessageSquare,
  Shield,
  X,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/contexts/ToastContext';
import { appointmentRequestService } from '@/services/appointmentRequestService';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import ModuleTextarea from '@/components/ui/ModuleTextarea';
import ModuleSelect from '@/components/ui/ModuleSelect';
import Label from '@/components/ui/Label';
import ModuleLabel from '@/components/ui/ModuleLabel';
import { LoadingSpinner } from '@/components/LoadingSpinner';

const RequestAppointmentModal = ({ isOpen, onClose, onSuccess, initialData = null }) => {
  const { user } = useAuth();
  const { toast_error } = useToast();

  // Estados do formulário
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    personId: '',
    locationId: '',
    serviceTypeId: '',
    insuranceId: '',
    providerId: '',
    clientMessage: '',
    priority: 'MEDIUM'
  });

  // Estados de dados
  const [persons, setPersons] = useState([]);
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [providers, setProviders] = useState([]);
  const [insurances, setInsurances] = useState([]);

  // Estados de controle
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [errors, setErrors] = useState({});

  // Carregar dados necessários
  const loadData = async () => {
    try {
      setIsLoadingData(true);
      const [personsRes, locationsRes, serviceTypesRes, providersRes, insurancesRes] = await Promise.all([
        appointmentService.getPersons(),
        appointmentService.getLocations(),
        appointmentService.getServiceTypes(),
        appointmentService.getProviders(),
        appointmentService.getInsurances()
      ]);

      setPersons(personsRes || []);
      setLocations(locationsRes || []);
      setServiceTypes(serviceTypesRes || []);
      setProviders(providersRes || []);
      setInsurances(insurancesRes || []);

      console.log('[REQUEST-MODAL] Dados carregados:', {
        persons: personsRes?.length,
        locations: locationsRes?.length,
        serviceTypes: serviceTypesRes?.length,
        providers: providersRes?.length,
        insurances: insurancesRes?.length
      });
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      toast_error('Erro ao carregar dados necessários');
    } finally {
      setIsLoadingData(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  // Reset form quando modal fecha
  useEffect(() => {
    if (!isOpen) {
      setFormData({
        title: '',
        description: '',
        startDate: '',
        endDate: '',
        personId: '',
        locationId: '',
        serviceTypeId: '',
        insuranceId: '',
        providerId: '',
        clientMessage: '',
        priority: 'MEDIUM'
      });
      setErrors({});
    }
  }, [isOpen]);

  // Pré-preencher dados quando initialData é fornecido
  useEffect(() => {
    if (isOpen && initialData) {
      console.log('[REQUEST-MODAL] Pré-preenchendo dados:', initialData);
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [isOpen, initialData]);

  // Handler para mudanças no formulário
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Limpar erro do campo quando usuário digita
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }

    // Lógica especial para datas
    if (field === 'startDate' && value) {
      // Se não tem data de fim ou a data de fim é menor que a nova data de início
      if (!formData.endDate || new Date(formData.endDate) <= new Date(value)) {
        // Definir data de fim como 1 hora após o início
        const startDate = new Date(value);
        const endDate = new Date(startDate.getTime() + 60 * 60 * 1000); // +1 hora
        setFormData(prev => ({
          ...prev,
          [field]: value,
          endDate: endDate.toISOString().slice(0, 16)
        }));
        return;
      }
    }
  };

  // Validar formulário
  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Título é obrigatório';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Data e hora de início são obrigatórias';
    }

    if (!formData.endDate) {
      newErrors.endDate = 'Data e hora de fim são obrigatórias';
    }

    if (formData.startDate && formData.endDate) {
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      
      if (end <= start) {
        newErrors.endDate = 'Data de fim deve ser posterior à data de início';
      }

      if (start < new Date()) {
        newErrors.startDate = 'Não é possível solicitar agendamento no passado';
      }
    }

    if (!formData.personId) {
      newErrors.personId = 'Selecione um paciente';
    }

    if (!formData.locationId) {
      newErrors.locationId = 'Selecione um local';
    }

    if (!formData.serviceTypeId) {
      newErrors.serviceTypeId = 'Selecione um tipo de serviço';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handler para submissão
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      
      const requestData = {
        title: formData.title,
        description: formData.description,
        startDate: formData.startDate ? new Date(formData.startDate).toISOString() : null,
        endDate: formData.endDate ? new Date(formData.endDate).toISOString() : null,
        personId: formData.personId,
        locationId: formData.locationId,
        serviceTypeId: formData.serviceTypeId,
        insuranceId: formData.insuranceId || null,
        providerId: formData.providerId && formData.providerId.trim() !== '' ? formData.providerId : null,
        clientMessage: formData.clientMessage,
        priority: formData.priority
      };

      console.log('[REQUEST-MODAL] Enviando solicitação:', requestData);
      
      await appointmentRequestService.createRequest(requestData);
      onSuccess();
    } catch (error) {
      console.error('Erro ao criar solicitação:', error);
      toast_error(error.response?.data?.message || 'Erro ao criar solicitação');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        onClose();
      }
    }}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto border-2 border-purple-400 dark:border-purple-500">
        <DialogHeader className="pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-600 to-violet-400 rounded-lg text-white">
              <Calendar className="h-5 w-5" />
            </div>
            <div className="flex-1">
              <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white">
                Nova Solicitação de Agendamento
              </DialogTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Preencha os dados para solicitar um novo agendamento
              </p>
            </div>
          </div>
        </DialogHeader>

      {isLoadingData ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : (
        <form id="appointment-request-form" onSubmit={handleSubmit} className="pt-4 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-5">
              {/* Título */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="title"
                  icon={<Calendar size={16} />}
                  required
                >
                  Título do Agendamento
                </ModuleLabel>
                <Input
                  id="title"
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Ex: Consulta de rotina, Sessão de fisioterapia..."
                  className={errors.title ? 'border-red-300' : ''}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.title}
                  </p>
                )}
              </div>

              {/* Descrição */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="description"
                  icon={<MessageSquare size={16} />}
                >
                  Descrição
                </ModuleLabel>
                <ModuleTextarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Descreva detalhes sobre o agendamento..."
                  rows={3}
                />
              </div>

              {/* Paciente */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="personId"
                  icon={<User size={16} />}
                  required
                >
                  Paciente
                </ModuleLabel>
                <ModuleSelect
                  id="personId"
                  value={formData.personId}
                  onChange={(e) => handleInputChange('personId', e.target.value)}
                  moduleColor="scheduler"
                  className={errors.personId ? 'border-red-300' : ''}
                >
                  <option value="">Selecione um paciente</option>
                  {persons.map((person) => (
                    <option key={person.id} value={person.id}>
                      {person.name || person.fullName}
                    </option>
                  ))}
                </ModuleSelect>
                {errors.personId && (
                  <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.personId}
                  </p>
                )}
              </div>

              {/* Profissional */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="providerId"
                  icon={<Stethoscope size={16} />}
                >
                  Profissional
                </ModuleLabel>
                <ModuleSelect
                  id="providerId"
                  value={formData.providerId}
                  onChange={(e) => handleInputChange('providerId', e.target.value)}
                  moduleColor="scheduler"
                >
                  <option value="">Selecione um profissional (opcional)</option>
                  {providers.map((provider) => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name || provider.fullName}
                    </option>
                  ))}
                </ModuleSelect>
              </div>

              {/* Local */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="locationId"
                  icon={<MapPin size={16} />}
                  required
                >
                  Local
                </ModuleLabel>
                <ModuleSelect
                  id="locationId"
                  value={formData.locationId}
                  onChange={(e) => handleInputChange('locationId', e.target.value)}
                  moduleColor="scheduler"
                  className={errors.locationId ? 'border-red-300' : ''}
                >
                  <option value="">Selecione um local</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </ModuleSelect>
                {errors.locationId && (
                  <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.locationId}
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-5">
              {/* Datas */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center gap-2">
                  <Clock className="h-5 w-5 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
                  Data e Horário
                </h3>
                
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <ModuleLabel
                      moduleColor="scheduler"
                      htmlFor="startDate"
                      icon={<Calendar size={16} />}
                      required
                    >
                      Data e Hora de Início
                    </ModuleLabel>
                    <Input
                      id="startDate"
                      type="datetime-local"
                      value={formData.startDate}
                      min={new Date().toISOString().slice(0, 16)}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                      className={errors.startDate ? 'border-red-300' : ''}
                    />
                    {errors.startDate && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle size={14} />
                        {errors.startDate}
                      </p>
                    )}
                  </div>

                  <div>
                    <ModuleLabel
                      moduleColor="scheduler"
                      htmlFor="endDate"
                      icon={<Clock size={16} />}
                      required
                    >
                      Data e Hora de Fim
                    </ModuleLabel>
                    <Input
                      id="endDate"
                      type="datetime-local"
                      value={formData.endDate}
                      min={formData.startDate || new Date().toISOString().slice(0, 16)}
                      onChange={(e) => handleInputChange('endDate', e.target.value)}
                      className={errors.endDate ? 'border-red-300' : ''}
                    />
                    {errors.endDate && (
                      <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                        <AlertCircle size={14} />
                        {errors.endDate}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* Tipo de Serviço */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="serviceTypeId"
                  icon={<Stethoscope size={16} />}
                  required
                >
                  Tipo de Serviço
                </ModuleLabel>
                <ModuleSelect
                  id="serviceTypeId"
                  value={formData.serviceTypeId}
                  onChange={(e) => handleInputChange('serviceTypeId', e.target.value)}
                  moduleColor="scheduler"
                  className={errors.serviceTypeId ? 'border-red-300' : ''}
                >
                  <option value="">Selecione um tipo de serviço</option>
                  {serviceTypes.map((serviceType) => (
                    <option key={serviceType.id} value={serviceType.id}>
                      {serviceType.name}
                    </option>
                  ))}
                </ModuleSelect>
                {errors.serviceTypeId && (
                  <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.serviceTypeId}
                  </p>
                )}
              </div>

              {/* Convênio */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="insuranceId"
                  icon={<Shield size={16} />}
                >
                  Convênio
                </ModuleLabel>
                <ModuleSelect
                  id="insuranceId"
                  value={formData.insuranceId}
                  onChange={(e) => handleInputChange('insuranceId', e.target.value)}
                  moduleColor="scheduler"
                >
                  <option value="">Selecione um convênio (opcional)</option>
                  {insurances.map((insurance) => (
                    <option key={insurance.id} value={insurance.id}>
                      {insurance.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>

              {/* Prioridade */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="priority"
                  icon={<AlertCircle size={16} />}
                >
                  Prioridade
                </ModuleLabel>
                <ModuleSelect
                  id="priority"
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                  moduleColor="scheduler"
                >
                  <option value="LOW">Baixa</option>
                  <option value="MEDIUM">Média</option>
                  <option value="HIGH">Alta</option>
                  <option value="URGENT">Urgente</option>
                </ModuleSelect>
              </div>

              {/* Mensagem do Cliente */}
              <div>
                <ModuleLabel
                  moduleColor="scheduler"
                  htmlFor="clientMessage"
                  icon={<MessageSquare size={16} />}
                >
                  Mensagem Adicional
                </ModuleLabel>
                <ModuleTextarea
                  id="clientMessage"
                  value={formData.clientMessage}
                  onChange={(e) => handleInputChange('clientMessage', e.target.value)}
                  placeholder="Adicione informações extras sobre a solicitação..."
                  rows={3}
                />
              </div>
            </div>
          </div>

          {/* Aviso Informativo */}
          <div className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border border-purple-200 dark:border-purple-700 rounded-lg p-3">
            <div className="flex gap-2">
              <AlertCircle className="text-purple-600 dark:text-purple-400 flex-shrink-0 mt-0.5" size={14} />
              <div className="text-xs">
                <p className="text-purple-800 dark:text-purple-200 font-medium mb-1">
                  Como funciona a solicitação?
                </p>
                <p className="text-purple-700 dark:text-purple-300">
                  Sua solicitação será analisada e você receberá uma notificação sobre a decisão.
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              form="appointment-request-form"
              disabled={isLoading || isLoadingData}
              className="bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 text-white"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <LoadingSpinner size="sm" />
                  Enviando...
                </div>
              ) : (
                'Solicitar Agendamento'
              )}
            </Button>
          </div>
        </form>
      )}
      </DialogContent>
    </Dialog>
  );
};

export default RequestAppointmentModal;