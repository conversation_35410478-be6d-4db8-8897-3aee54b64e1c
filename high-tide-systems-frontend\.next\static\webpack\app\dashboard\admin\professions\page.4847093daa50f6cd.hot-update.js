"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupProfessionsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ProfessionGroupProfessionsModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupProfessionsModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupProfessionsModalOpen, setGroupProfessionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: 'Tem certeza que deseja excluir o grupo \"'.concat(group.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 640,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 646,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 654,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 706,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 707,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 705,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 729,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 730,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 732,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 723,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 744,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 745,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 738,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 756,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 758,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 750,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 770,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 771,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 765,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 780,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 781,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 774,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 704,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 788,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 787,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 703,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 841,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 842,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 839,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 854,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 855,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 860,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 876,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 877,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 869,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 881,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 898,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 899,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 911,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 903,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 846,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 838,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 919,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 925,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 933,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 940,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 931,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 917,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 964,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 959,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 958,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 981,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1015,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1014,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1027,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1026,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1030,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1029,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1025,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1024,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1041,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1045,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1039,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1056,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1057,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1055,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1062,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1053,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1073,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1071,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1070,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1084,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1085,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1083,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1082,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1102,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1103,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1094,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1093,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1123,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1118,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1131,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1126,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1125,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1141,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1135,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1134,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1117,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1116,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1012,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 954,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 953,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1165,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1160,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1180,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1203,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1202,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1215,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1214,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1217,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1213,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1212,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1230,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1228,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1227,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1242,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1243,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1241,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1248,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1239,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1258,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1259,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1257,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1256,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1276,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1268,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1267,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1292,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1308,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1301,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1291,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1290,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1200,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1156,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1155,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1321,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1332,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1357,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-profession\" ? \"Excluir Profissão\" : (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-group\" ? \"Excluir Grupo\" : \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1363,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 837,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"WMIwddPThsKO3kzPAYWJZAESvbk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ })

});