'use client';

import React, { useState, useEffect } from 'react';
import {
  Filter,
  AlertTriangle,
  Check<PERSON>ircle,
  Clock,
  XCircle,
  Eye,
  Edit,
  Bug,
  RefreshCw
} from 'lucide-react';
import ModuleHeader from '@/components/ui/ModuleHeader';
import ModuleTable from '@/components/ui/ModuleTable';
import BugReportsFilters from '@/components/bug-report/BugReportsFilters';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useToast } from '@/contexts/ToastContext';
import { bugReportService } from '@/services/bugReportService';
import BugDetailsModal from './components/BugDetailsModal';
import BugEditModal from './components/BugEditModal';

const BugReportsPage = () => {
  const { user } = useAuth();
  const router = useRouter();
  const { toast_error, toast_success } = useToast();
  const [bugs, setBugs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedBug, setSelectedBug] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  const [filters, setFilters] = useState({
    search: '',
    status: [],
    priority: [],
    category: [],
    companies: []
  });

  // Carregar dados
  useEffect(() => {
    if (!user) return;
    
    if (user.role !== 'SYSTEM_ADMIN') {
      router.push('/dashboard');
      return;
    }
    
    loadBugReports();
  }, [user, router, filters, pagination.page]);

  const loadBugReports = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      };

      // Remove filtros vazios
      Object.keys(params).forEach(key => {
        if (Array.isArray(params[key]) && params[key].length === 0) {
          delete params[key];
        } else if (params[key] === '') {
          delete params[key];
        }
      });

      const response = await bugReportService.listAll(params);

      if (response.data) {
        setBugs(response.data.bugReports);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total,
          totalPages: response.data.pagination.totalPages
        }));
      }
    } catch (error) {
      console.error('Erro ao carregar bug reports:', error);
      toast_error({
        title: 'Erro',
        message: 'Erro ao carregar relatórios de bugs'
      });
    } finally {
      setLoading(false);
    }
  };



  if (user?.role !== 'SYSTEM_ADMIN') {
    return null;
  }

  const handleViewBug = (bug) => {
    setSelectedBug(bug);
    setShowDetailsModal(true);
  };

  const handleEditBug = (bug) => {
    setSelectedBug(bug);
    setShowEditModal(true);
  };

  const handleSaveBug = async (updatedBug) => {
    try {
      const response = await bugReportService.updateStatus(updatedBug.id, {
        status: updatedBug.status,
        priority: updatedBug.priority,
        adminNotes: updatedBug.adminNotes
      });

      if (response.data) {
        setBugs(prev => prev.map(bug => 
          bug.id === updatedBug.id ? { ...bug, ...updatedBug } : bug
        ));
        setShowEditModal(false);
        setSelectedBug(null);
        toast_success({
          title: 'Sucesso',
          message: 'Bug atualizado com sucesso'
        });
      }
    } catch (error) {
      console.error('Erro ao atualizar bug:', error);
      toast_error({
        title: 'Erro',
        message: 'Erro ao atualizar o bug'
      });
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'OPEN':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'IN_PROGRESS':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'RESOLVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'CLOSED':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-500 rounded-full"></div>;
    }
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',
      'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',
      'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',
      'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'
    };
    return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';
  };

  const getPriorityLabel = (priority) => {
    const labels = {
      'CRITICAL': 'Crítica',
      'HIGH': 'Alta',
      'MEDIUM': 'Média',
      'LOW': 'Baixa'
    };
    return labels[priority] || 'Desconhecida';
  };

  const columns = [
    { header: 'Status', field: 'status', width: '120px' },
    { header: 'Bug', field: 'title', width: 'auto' },
    { header: 'Prioridade', field: 'priority', width: '120px' },
    { header: 'Reportado por', field: 'reportedBy', width: '200px' },
    { header: 'Data', field: 'createdAt', width: '120px' },
    { header: 'Ações', field: 'actions', width: '120px', sortable: false }
  ];

  return (
    <div className="space-y-6">
      {/* Título da página */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bug size={28} className="text-module-admin-icon dark:text-module-admin-icon-dark" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Relatórios de Bugs
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Gerencie todos os bugs reportados pelos usuários do sistema
            </p>
          </div>
        </div>
      </div>

      <ModuleHeader
        title="Filtros e Busca"
        description="Utilize os filtros abaixo para encontrar bugs específicos."
        icon={<Filter size={22} className="text-module-admin-icon dark:text-module-admin-icon-dark" />}
        moduleColor="admin"
        filters={
          <BugReportsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={loadBugReports}
          />
        }
      />

      <ModuleTable
        moduleColor="admin"
        title="Lista de Bugs Reportados"
        data={bugs}
        columns={columns}
        isLoading={loading}
        emptyMessage="Nenhum bug reportado ainda."
        emptyIcon="🐛"
        enableColumnToggle={true}
        headerContent={
          <button
            onClick={() => loadBugReports()}
            disabled={loading}
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
            title="Atualizar lista"
          >
            <RefreshCw
              size={18}
              className={`text-gray-600 dark:text-gray-300 ${loading ? 'animate-spin' : ''}`}
            />
          </button>
        }
        renderRow={(bug, _index, moduleColors, visibleColumns) => (
          <tr key={bug.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('status') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-2">
                  {getStatusIcon(bug.status)}
                  <span className="text-sm font-medium">
                    {bug.status === 'OPEN' ? 'Aberto' :
                     bug.status === 'IN_PROGRESS' ? 'Em Progresso' :
                     bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('title') && (
              <td className="px-6 py-4">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">{bug.title}</div>
                  <div
                    className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help"
                    title={bug.description}
                  >
                    {bug.description}
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('priority') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(bug.priority)}`}>
                  {getPriorityLabel(bug.priority)}
                </span>
              </td>
            )}

            {visibleColumns.includes('reportedBy') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">{bug.reportedBy.fullName}</div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">{bug.company.name}</div>
                </div>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {new Date(bug.createdAt).toLocaleDateString('pt-BR')}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right">
                <div className="flex justify-end gap-1">
                  <button
                    onClick={() => handleViewBug(bug)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Visualizar"
                  >
                    <Eye size={16} />
                  </button>
                  <button
                    onClick={() => handleEditBug(bug)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>

                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Modais */}
      <BugDetailsModal
        isOpen={showDetailsModal}
        onClose={() => {
          setShowDetailsModal(false);
          setSelectedBug(null);
        }}
        bug={selectedBug}
      />

      <BugEditModal
        isOpen={showEditModal}
        onClose={() => {
          setShowEditModal(false);
          setSelectedBug(null);
        }}
        bug={selectedBug}
        onSave={handleSaveBug}
      />
    </div>
  );
};

export default BugReportsPage;
