"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/admin/PreferencesPanel.js":
/*!**************************************************!*\
  !*** ./src/components/admin/PreferencesPanel.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PreferencesPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _preferences_UserPreferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./preferences/UserPreferences */ \"(app-pages-browser)/./src/components/admin/preferences/UserPreferences.js\");\n/* harmony import */ var _preferences_SchedulingPreferences__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./preferences/SchedulingPreferences */ \"(app-pages-browser)/./src/components/admin/preferences/SchedulingPreferences.js\");\n/* harmony import */ var _preferences_NotificationPreferences__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./preferences/NotificationPreferences */ \"(app-pages-browser)/./src/components/admin/preferences/NotificationPreferences.js\");\n/* harmony import */ var _hooks_useCompanyPreferences__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useCompanyPreferences */ \"(app-pages-browser)/./src/hooks/useCompanyPreferences.js\");\n/* harmony import */ var _contexts_CompanySelectionContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CompanySelectionContext */ \"(app-pages-browser)/./src/contexts/CompanySelectionContext.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,SlidersHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _ui_ModuleFormGroup__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../ui/ModuleFormGroup */ \"(app-pages-browser)/./src/components/ui/ModuleFormGroup.js\");\n/* harmony import */ var _ui_ModuleLabel__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../ui/ModuleLabel */ \"(app-pages-browser)/./src/components/ui/ModuleLabel.js\");\n/* harmony import */ var _ui_ModuleInput__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../ui/ModuleInput */ \"(app-pages-browser)/./src/components/ui/ModuleInput.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Componente temporário para abas não implementadas\nconst ComingSoonTab = (param)=>{\n    let { title } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-4xl mb-4\",\n                        children: \"\\uD83D\\uDEA7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-800 dark:text-gray-200 mb-2\",\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                        children: \"Esta funcionalidade est\\xe1 em desenvolvimento e estar\\xe1 dispon\\xedvel em breve.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n            lineNumber: 17,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined);\n};\n_c = ComingSoonTab;\nconst TABS = [\n    {\n        label: \"Usuário/Cliente\",\n        component: _preferences_UserPreferences__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    {\n        label: \"Agendamento\",\n        component: _preferences_SchedulingPreferences__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    },\n    {\n        label: \"Notificações\",\n        component: _preferences_NotificationPreferences__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    },\n    {\n        label: \"Documentação\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Documenta\\xe7\\xe3o\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 35,\n                columnNumber: 45\n            }, undefined)\n    },\n    {\n        label: \"Relatórios\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Relat\\xf3rios\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 36,\n                columnNumber: 43\n            }, undefined)\n    },\n    {\n        label: \"Interface\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Interface\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 37,\n                columnNumber: 42\n            }, undefined)\n    },\n    {\n        label: \"Segurança\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Seguran\\xe7a\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 38,\n                columnNumber: 42\n            }, undefined)\n    },\n    {\n        label: \"Fluxo de Trabalho\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Fluxo de Trabalho\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 39,\n                columnNumber: 50\n            }, undefined)\n    },\n    {\n        label: \"Cobrança/Faturamento\",\n        component: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ComingSoonTab, {\n                title: \"Configura\\xe7\\xf5es de Cobran\\xe7a/Faturamento\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 40,\n                columnNumber: 53\n            }, undefined)\n    }\n];\nfunction PreferencesPanel() {\n    var _TABS_activeTab;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const { isSystemAdmin } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const { preferences, isLoading, savePreferences, canSelectCompany } = (0,_hooks_useCompanyPreferences__WEBPACK_IMPORTED_MODULE_6__.useCompanyPreferences)();\n    const { selectedCompanyId, handleCompanyChange, companies, isLoading: isLoadingCompanies, selectedCompany } = (0,_contexts_CompanySelectionContext__WEBPACK_IMPORTED_MODULE_7__.useCompanySelection)();\n    const ActiveComponent = (_TABS_activeTab = TABS[activeTab]) === null || _TABS_activeTab === void 0 ? void 0 : _TABS_activeTab.component;\n    const hasSearch = search.trim().length > 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5 text-primary-500 dark:text-primary-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            \"Prefer\\xeancias da Empresa\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    selectedCompany && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: selectedCompany.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            canSelectCompany && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleFormGroup__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleLabel__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            htmlFor: \"company-select\",\n                            moduleColor: \"admin\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_SlidersHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                    lineNumber: 88,\n                                    columnNumber: 15\n                                }, this),\n                                \"Selecionar Empresa\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"company-select\",\n                            value: selectedCompanyId || '',\n                            onChange: (e)=>handleCompanyChange(e.target.value || null),\n                            disabled: isLoadingCompanies,\n                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md    focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400   bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100   disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:cursor-not-allowed\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: isLoadingCompanies ? 'Carregando empresas...' : 'Selecione uma empresa'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                        lineNumber: 105,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this),\n                        isLoadingCompanies && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 flex items-center text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this),\n                                \"Carregando empresas...\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                            lineNumber: 111,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-wrap gap-3 mb-6\",\n                children: TABS.map((tab, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-2 rounded-lg font-semibold transition-all duration-200 focus:outline-none whitespace-nowrap\\n              \".concat(activeTab === idx ? 'bg-slate-600 text-white shadow-lg scale-105' : 'bg-gray-200 dark:bg-gray-700 text-slate-700 dark:text-gray-300 hover:bg-slate-500 hover:text-white dark:hover:bg-slate-600', \"\\n            \"),\n                        onClick: ()=>setActiveTab(idx),\n                        children: tab.label\n                    }, tab.label, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ModuleInput__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    moduleColor: \"admin\",\n                    type: \"text\",\n                    placeholder: \"Buscar configura\\xe7\\xe3o...\",\n                    value: search,\n                    onChange: (e)=>setSearch(e.target.value)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6\",\n                children: isLoading || !selectedCompanyId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500 dark:border-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-3 text-gray-300\",\n                            children: !selectedCompanyId ? 'Selecione uma empresa para continuar...' : 'Carregando preferências...'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                    lineNumber: 148,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: hasSearch ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: TABS.map((tab, idx)=>{\n                            const TabComponent = tab.component;\n                            if (typeof TabComponent !== 'function') return null;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-4 mt-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-base font-semibold text-gray-400 border-b border-gray-700 flex-1 pb-1\",\n                                            children: tab.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                            lineNumber: 164,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabComponent, {\n                                        search: search,\n                                        searchMode: true,\n                                        preferences: preferences,\n                                        selectedCompanyId: selectedCompanyId,\n                                        onSave: savePreferences\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                        lineNumber: 166,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, tab.label, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                                lineNumber: 162,\n                                columnNumber: 21\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 157,\n                        columnNumber: 15\n                    }, this) : typeof ActiveComponent === 'function' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ActiveComponent, {\n                        preferences: preferences,\n                        selectedCompanyId: selectedCompanyId,\n                        onSave: savePreferences\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 179,\n                        columnNumber: 17\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"Erro ao carregar aba.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                        lineNumber: 185,\n                        columnNumber: 17\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                    lineNumber: 155,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\admin\\\\PreferencesPanel.js\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(PreferencesPanel, \"qZcU6Zns8dnakfWsfUXqlBDFRSM=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth,\n        _hooks_useCompanyPreferences__WEBPACK_IMPORTED_MODULE_6__.useCompanyPreferences,\n        _contexts_CompanySelectionContext__WEBPACK_IMPORTED_MODULE_7__.useCompanySelection\n    ];\n});\n_c1 = PreferencesPanel;\nvar _c, _c1;\n$RefreshReg$(_c, \"ComingSoonTab\");\n$RefreshReg$(_c1, \"PreferencesPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/PreferencesPanel.js\n"));

/***/ })

});