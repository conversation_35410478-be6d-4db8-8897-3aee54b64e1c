'use client';

import React, { useEffect, useState, useRef } from 'react';
import { createPortal } from 'react-dom';
import { X, AlertCircle, CheckCircle, Info } from 'lucide-react';
import CustomScrollArea from './CustomScrollArea';
import { AnimatePresence, motion } from 'framer-motion';

/**
 * Componente de modal genérico com suporte para diferentes temas de módulos
 *
 * @param {Object} props
 * @param {boolean} props.isOpen - Se o modal está aberto
 * @param {Function} props.onClose - Função para fechar o modal
 * @param {string} props.title - Título do modal
 * @param {React.ReactNode} props.icon - Ícone do título (opcional)
 * @param {React.ReactNode} props.children - Conteúdo do modal
 * @param {string} props.size - Tamanho do modal (sm, md, lg, xl, full)
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, etc)
 * @param {React.ReactNode} props.footer - Conteú<PERSON> do rodapé (opcional)
 * @param {boolean} props.preventClose - Impede o fechamento do modal ao clicar fora ou no X
 * @param {string} props.variant - Variante do modal (default, info, success, warning, danger)
 * @param {boolean} props.showCloseButton - Mostrar botão de fechar no cabeçalho
 * @param {Function} props.onInteractOutside - Função chamada quando o usuário interage fora do modal
 * @param {string} props.description - Descrição opcional do modal
 * @param {boolean} props.animateExit - Se true, anima a saída do modal (padrão: true)
 * @param {React.ReactNode} props.headerActions - Ações adicionais no cabeçalho (opcional)
 */
const ModuleModal = ({
  isOpen,
  onClose,
  title,
  icon,
  children,
  size = 'md',
  moduleColor = 'default',
  footer,
  preventClose = false,
  variant = 'default',
  showCloseButton = true,
  onInteractOutside,
  description,
  animateExit = true,
  headerActions,
}) => {
  // Estado local para controlar a visibilidade do modal
  const [isVisible, setIsVisible] = useState(isOpen);

  // Efeito para sincronizar o estado isVisible com a prop isOpen
  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    } else {
      // Forçar o fechamento do modal quando isOpen mudar para false
      setIsVisible(false);
    }
  }, [isOpen]);

  // Função para lidar com o fechamento do modal
  const handleClose = () => {
    // Sempre forçar o fechamento do modal, independente do estado atual

    // Primeiro, tornar o modal invisível imediatamente
    setIsVisible(false);

    // Depois, chamar onClose se existir
    if (onClose) {
      try {
        onClose();
      } catch (error) {
        console.error('Erro ao fechar o modal:', error);
      }
    }
  };

  // Função para lidar com o final da animação de saída
  const handleAnimationComplete = () => {
    if (!isOpen) {
      setIsVisible(false);
    }
  };

  // Refs para acessibilidade
  const modalRef = useRef(null);
  const titleId = useRef(`modal-title-${Math.random().toString(36).substring(2, 11)}`);
  const descriptionId = useRef(`modal-description-${Math.random().toString(36).substring(2, 11)}`);

  // Refs para acessibilidade
  const initialFocusRef = useRef(null);
  const [mounted, setMounted] = useState(false);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Efeito para prevenir scroll quando o modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Efeito para definir o foco inicial quando o modal abre
  useEffect(() => {
    if (!isOpen) return;

    // Definir o foco inicial apenas uma vez quando o modal abre
    // Usando setTimeout para garantir que o DOM esteja completamente renderizado
    const focusTimer = setTimeout(() => {
      // Se temos um ref específico para focar, use-o
      if (initialFocusRef.current) {
        initialFocusRef.current.focus();
        return;
      }

      // Procurar por um input para focar primeiro (melhor experiência do usuário)
      const firstInput = modalRef.current?.querySelector('input, textarea, select');
      if (firstInput) {
        firstInput.focus();
        return;
      }

      // Caso contrário, procurar elementos focáveis exceto o botão de fechar
      const focusableElements = modalRef.current?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        // Encontrar o primeiro elemento que não seja o botão de fechar
        const nonCloseButton = Array.from(focusableElements).find(el =>
          !(el.tagName === 'BUTTON' && el.getAttribute('aria-label') === 'Fechar')
        );

        if (nonCloseButton) {
          nonCloseButton.focus();
        } else {
          focusableElements[0].focus();
        }
      }
    }, 50); // Pequeno delay para garantir que o DOM esteja pronto

    return () => clearTimeout(focusTimer);
  }, [isOpen, initialFocusRef]);

  // Efeito separado para gerenciar o trap de foco e tecla ESC
  useEffect(() => {
    if (!isOpen) return;

    // Handler para tecla ESC e trap de foco
    const handleKeyDown = (e) => {
      // Fechar com ESC
      if (e.key === 'Escape' && !preventClose) {
        handleClose();
        return;
      }

      // Trap focus apenas para a tecla Tab
      if (e.key === 'Tab') {
        const focusableElements = modalRef.current?.querySelectorAll(
          'button:not([tabindex="-1"]), [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (!focusableElements || focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        // Se pressionar Shift+Tab no primeiro elemento, vá para o último
        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
        // Se pressionar Tab no último elemento, vá para o primeiro
        else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, handleClose, preventClose]);

  if (!mounted) return null;

  // Determinar a largura do modal com base no tamanho
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-xl',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    full: 'max-w-full mx-4'
  };

  // Configurações de cores por módulo
  const moduleColors = {
    default: {
      header: 'bg-white dark:bg-gray-800',
      headerBorder: 'border-neutral-200 dark:border-gray-700',
      title: 'text-neutral-800 dark:text-white',
      iconColor: 'text-primary-500 dark:text-primary-400',
    },
    people: {
      header: 'bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600',
      headerBorder: 'border-orange-400 dark:border-orange-700',
      title: 'text-white',
      iconColor: 'text-white',
    },
    scheduler: {
      header: 'bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600',
      headerBorder: 'border-purple-400 dark:border-purple-700',
      title: 'text-white',
      iconColor: 'text-white',
    },
    admin: {
      header: 'bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600',
      headerBorder: 'border-gray-300 dark:border-gray-600',
      title: 'text-white',
      iconColor: 'text-white',
    },
    financial: {
      header: 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600',
      headerBorder: 'border-green-400 dark:border-green-700',
      title: 'text-white',
      iconColor: 'text-white',
    },
    chat: {
      header: 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800',
      headerBorder: 'border-cyan-400 dark:border-cyan-700',
      title: 'text-white',
      iconColor: 'text-white',
    },
  };

  // Variantes do modal
  const variantStyles = {
    default: {},
    info: {
      icon: <Info className="h-5 w-5" />,
      iconColor: 'text-blue-500 dark:text-blue-400',
    },
    success: {
      icon: <CheckCircle className="h-5 w-5" />,
      iconColor: 'text-green-500 dark:text-green-400',
    },
    warning: {
      icon: <AlertCircle className="h-5 w-5" />,
      iconColor: 'text-amber-500 dark:text-amber-400',
    },
    danger: {
      icon: <AlertCircle className="h-5 w-5" />,
      iconColor: 'text-red-500 dark:text-red-400',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;
  const variantConfig = variantStyles[variant] || variantStyles.default;

  // Função para lidar com cliques no overlay
  const handleOverlayClick = (e) => {
    // Sempre parar a propagação para evitar eventos duplicados
    e.stopPropagation();

    if (preventClose) {
      if (onInteractOutside) {
        onInteractOutside(e);
      }
      return;
    }

    handleClose();
  };

  // Mapeamento de animações por módulo
  const moduleAnimations = {
    default: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
      }
    },
    people: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
        rotate: -2,
      }
    },
    scheduler: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
        rotate: 2,
      }
    },
    admin: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
        x: -20,
      }
    },
    financial: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
        x: 20,
      }
    },
    chat: {
      exit: {
        opacity: 0,
        scale: 0.9,
        y: -20,
        rotate: 1,
      }
    },
  };

  // Obter a animação do módulo atual
  const moduleAnimation = moduleAnimations[moduleColor] || moduleAnimations.default;

  // Variantes de animação para o modal
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.2 } },
    exit: { opacity: 0, transition: { duration: 0.3, ease: 'easeOut' } }
  };

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.95, y: 10 },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        type: 'spring',
        damping: 25,
        stiffness: 300,
        duration: 0.3
      }
    },
    exit: {
      ...moduleAnimation.exit,
      transition: {
        duration: 0.35,
        ease: [0.32, 0.72, 0, 1] // Curva de easing personalizada para uma saída mais suave
      }
    }
  };

  // Usar createPortal para renderizar o modal no nível mais alto do DOM
  const modalContent = (
    <AnimatePresence mode="wait" onExitComplete={handleAnimationComplete}>
      {isVisible && (
        <div
          className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto p-4 md:p-6"
          role="dialog"
          aria-modal="true"
          aria-labelledby={titleId.current}
          aria-describedby={description ? descriptionId.current : undefined}
        >
          {/* Overlay de fundo escuro */}
          <motion.div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            onClick={handleOverlayClick}
            initial="hidden"
            animate="visible"
            exit={animateExit ? "exit" : "hidden"}
            variants={overlayVariants}
          />

          <motion.div
            ref={modalRef}
            className={`relative bg-background rounded-xl shadow-xl dark:shadow-black/50 ${sizeClasses[size]} w-full max-h-[98vh] flex flex-col z-[11050] ${moduleColor === 'chat' ? 'border-2 border-cyan-500 dark:border-cyan-400' : ''} overflow-hidden`}
            onClick={(e) => e.stopPropagation()}
            initial="hidden"
            animate="visible"
            exit={animateExit ? "exit" : "hidden"}
            variants={modalVariants}
          >
        {/* Header */}
        <div className={`flex justify-between items-center px-6 py-4 border-b-2 ${moduleColor === 'chat' ? 'border-cyan-500 dark:border-cyan-400' : colors.headerBorder} ${colors.header} rounded-t-xl`}>
          <div className="flex items-center gap-2">
            {icon && (
              <span className={colors.iconColor}>
                {icon}
              </span>
            )}
            {variant !== 'default' && !icon && (
              <span className={variantConfig.iconColor}>
                {variantConfig.icon}
              </span>
            )}
            <h3 id={titleId.current} className={`text-xl font-semibold ${colors.title}`}>
              {title}
            </h3>
          </div>
          <div className="flex items-center gap-2">
            {headerActions && (
              <div className="flex items-center gap-2">
                {headerActions}
              </div>
            )}
            {showCloseButton && (
              <button
                onClick={(e) => {
                  // Sempre prevenir o comportamento padrão e parar a propagação
                  e.preventDefault();
                  e.stopPropagation();

                  // Sempre fechar o modal, independente de preventClose
                  handleClose();
                }}
                // Adicionar tabIndex negativo para evitar que o botão receba foco automaticamente
                tabIndex="-1"
                className={`${moduleColor === 'default' ? 'text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300' : 'text-white/80 hover:text-white'} p-2 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${moduleColor === 'scheduler' ? 'focus:ring-purple-500 dark:focus:ring-purple-400' : moduleColor === 'people' ? 'focus:ring-orange-500 dark:focus:ring-orange-400' : moduleColor === 'admin' ? 'focus:ring-slate-500 dark:focus:ring-slate-400' : moduleColor === 'financial' ? 'focus:ring-green-500 dark:focus:ring-green-400' : moduleColor === 'chat' ? 'focus:ring-cyan-500 dark:focus:ring-cyan-400' : 'focus:ring-primary-500'}`}
                disabled={preventClose}
                aria-label="Fechar"
              >
                <X size={20} />
              </button>
            )}
          </div>
        </div>

        {description && (
          <div id={descriptionId.current} className="px-6 pt-4 text-neutral-600 dark:text-neutral-300 text-sm">
            {description}
          </div>
        )}

        {/* Conteúdo */}
        <CustomScrollArea className="flex-1" moduleColor={moduleColor} isModal={true}>
          {children}
        </CustomScrollArea>

        {/* Footer (opcional) */}
        {footer && (
          <div className={`px-6 py-4 border-t-2 ${moduleColor === 'chat' ? 'border-cyan-500 dark:border-cyan-400' : 'border-neutral-200 dark:border-gray-700'} bg-background rounded-b-xl`}>
            {footer}
          </div>
        )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );


  // Renderizar o modal usando um portal para garantir que ele fique acima de tudo
  return createPortal(modalContent, document.body);
};

export default ModuleModal;
