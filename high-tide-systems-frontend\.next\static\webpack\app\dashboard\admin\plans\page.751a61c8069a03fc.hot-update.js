"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/users/UsersPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-cog.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,CheckCircle,Edit,Filter,Lock,Mail,Plus,Power,RefreshCw,Search,Shield,Trash,User,UserCog,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/users/UserFormModal */ \"(app-pages-browser)/./src/components/users/UserFormModal.js\");\n/* harmony import */ var _components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/ModulesModal */ \"(app-pages-browser)/./src/components/users/ModulesModal.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/permissions/PermissionsModal */ \"(app-pages-browser)/./src/components/permissions/PermissionsModal.js\");\n/* harmony import */ var _components_users_RoleModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/users/RoleModal */ \"(app-pages-browser)/./src/components/users/RoleModal.js\");\n/* harmony import */ var _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/modules/admin/services/userService */ \"(app-pages-browser)/./src/app/modules/admin/services/userService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_Modal__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/Modal */ \"(app-pages-browser)/./src/components/ui/Modal.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_admin_UsersFilters__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/admin/UsersFilters */ \"(app-pages-browser)/./src/components/admin/UsersFilters.js\");\n/* harmony import */ var _components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui/SensitiveField */ \"(app-pages-browser)/./src/components/ui/SensitiveField.js\");\n/* harmony import */ var _hooks_usePreferences__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/usePreferences */ \"(app-pages-browser)/./src/hooks/usePreferences.js\");\n/* harmony import */ var _hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/useDataPrivacy */ \"(app-pages-browser)/./src/hooks/useDataPrivacy.js\");\n/* harmony import */ var _utils_permissionConfig__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/utils/permissionConfig */ \"(app-pages-browser)/./src/utils/permissionConfig.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst UsersPage = ()=>{\n    var _subscriptionData_usage, _subscriptionData_usage1, _subscriptionData_usage2, _subscriptionData_usage3, _subscriptionData_usage4, _subscriptionData_usage5, _subscriptionData_usage6, _subscriptionData_usage7, _subscriptionData_usage8, _subscriptionData_usage9, _subscriptionData_usage10, _subscriptionData_usage11;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions)();\n    const { preferences } = (0,_hooks_usePreferences__WEBPACK_IMPORTED_MODULE_21__.usePreferences)();\n    const { applyListPrivacyMasks } = (0,_hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_22__.useDataPrivacy)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_18__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_18__.useSearchParams)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        module: \"\",\n        users: []\n    });\n    const [userOptions, setUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingUserOptions, setIsLoadingUserOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userFormOpen, setUserFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modulesModalOpen, setModulesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissionsModalOpen, setPermissionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [roleModalOpen, setRoleModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [subscriptionData, setSubscriptionData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingSubscription, setIsLoadingSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showUserLimitModal, setShowUserLimitModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedActiveUsers, setSelectedActiveUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [savingUserLimit, setSavingUserLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(users.map((u)=>u.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Estados para controlar ordenação\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"fullName\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // Obter módulos ativos e seus labels\n    const activeModules = (0,_utils_permissionConfig__WEBPACK_IMPORTED_MODULE_23__.getActiveModules)();\n    const MODULE_LABELS = {\n        ADMIN: \"Administração\",\n        RH: \"RH\",\n        FINANCIAL: \"Financeiro\",\n        SCHEDULING: \"Agendamento\",\n        PEOPLE: \"Pessoas\",\n        BASIC: \"Básico\"\n    };\n    // Verificar se o usuário atual é um system_admin ou company_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"COMPANY_ADMIN\";\n    // Verificar se pode adicionar usuários baseado no limite da subscription\n    const canAddUsers = (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage = subscriptionData.usage) === null || _subscriptionData_usage === void 0 ? void 0 : _subscriptionData_usage.canAddUsers) !== false;\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_12__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados da subscription\n    const loadSubscriptionData = async ()=>{\n        if (isSystemAdmin) return; // System admin não tem limite de usuários\n        setIsLoadingSubscription(true);\n        try {\n            const response = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_13__.subscriptionService.getSubscription();\n            setSubscriptionData(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar dados da subscription:\", error);\n            // Se não conseguir carregar, assume que pode adicionar usuários\n            setSubscriptionData({\n                usage: {\n                    canAddUsers: true\n                }\n            });\n        } finally{\n            setIsLoadingSubscription(false);\n        }\n    };\n    // Função para carregar opções de usuários para o multi-select\n    const loadUserOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUserOptions]\": async ()=>{\n            setIsLoadingUserOptions(true);\n            try {\n                var _response_users, _response_users_, _response_users1;\n                // ✅ CORREÇÃO: Aplicar mesmo filtro de empresa para as opções\n                const filters = {\n                    active: true,\n                    // Company admin só vê usuários da sua empresa\n                    companyId: isSystemAdmin ? undefined // System admin pode ver usuários de todas as empresas\n                     : (currentUser === null || currentUser === void 0 ? void 0 : currentUser.companyId) || undefined,\n                    excludeSystemAdmin: !isSystemAdmin\n                };\n                console.log('🔍 Filtros aplicados para loadUserOptions:', {\n                    userRole: currentUser === null || currentUser === void 0 ? void 0 : currentUser.role,\n                    isSystemAdmin,\n                    companyId: filters.companyId,\n                    filters\n                });\n                // Carregar todos os usuários para o multi-select (com limite maior)\n                const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.list(1, 100, filters);\n                console.log('📊 Opções de usuários carregadas:', {\n                    totalOptions: (_response_users = response.users) === null || _response_users === void 0 ? void 0 : _response_users.length,\n                    firstOption: (_response_users1 = response.users) === null || _response_users1 === void 0 ? void 0 : (_response_users_ = _response_users1[0]) === null || _response_users_ === void 0 ? void 0 : _response_users_.fullName\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = response.users.map({\n                    \"UsersPage.useCallback[loadUserOptions].options\": (user)=>({\n                            value: user.id,\n                            label: user.fullName\n                        })\n                }[\"UsersPage.useCallback[loadUserOptions].options\"]);\n                setUserOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de usuários:\", error);\n            } finally{\n                setIsLoadingUserOptions(false);\n            }\n        }\n    }[\"UsersPage.useCallback[loadUserOptions]\"], [\n        isSystemAdmin,\n        currentUser === null || currentUser === void 0 ? void 0 : currentUser.companyId\n    ]);\n    const loadUsers = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, filtersToUse = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : filters, sortField = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : \"fullName\", sortDirection = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : \"asc\", perPage = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            var _response_users, _response_users1, _response_users_, _response_users2, _response_users3, _response_users_1, _response_users4;\n            const apiFilters = {\n                search: filtersToUse.search || undefined,\n                active: filtersToUse.status === \"\" ? undefined : filtersToUse.status === \"active\",\n                module: filtersToUse.module || undefined,\n                excludeSystemAdmin: !isSystemAdmin,\n                userIds: filtersToUse.users.length > 0 ? filtersToUse.users : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            };\n            // Adicionar companyId apenas quando necessário\n            if (isSystemAdmin) {\n                if (filtersToUse.companies.length > 0) {\n                    apiFilters.companyId = filtersToUse.companies[0];\n                    console.log('🏢user SYSTEM_ADMIN com empresa selecionada:', filtersToUse.companies[0]);\n                } else {\n                    console.log('🌍🏢user SYSTEM_ADMIN sem empresa - buscando em todas as empresas');\n                }\n            } else if (currentUser === null || currentUser === void 0 ? void 0 : currentUser.companyId) {\n                apiFilters.companyId = currentUser.companyId;\n                console.log('🏢🏢user Usuário normal - empresa:', currentUser.companyId);\n            }\n            console.log('📋🏢user Filtros finais enviados para API:', apiFilters);\n            const response = await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.list(page, perPage, apiFilters);\n            console.log('📊🏢user Resposta da API:', {\n                total: response.total,\n                usersCount: (_response_users = response.users) === null || _response_users === void 0 ? void 0 : _response_users.length,\n                pages: response.pages\n            });\n            console.log('📊🏢user Resposta do userService.list:', {\n                totalUsers: response.total,\n                usersCount: (_response_users1 = response.users) === null || _response_users1 === void 0 ? void 0 : _response_users1.length,\n                firstUser: (_response_users2 = response.users) === null || _response_users2 === void 0 ? void 0 : (_response_users_ = _response_users2[0]) === null || _response_users_ === void 0 ? void 0 : _response_users_.fullName\n            });\n            console.log('🎯 Definindo users no estado:', (_response_users3 = response.users) === null || _response_users3 === void 0 ? void 0 : _response_users3.length, 'usuários');\n            console.log('🎯 Primeiro usuário:', (_response_users4 = response.users) === null || _response_users4 === void 0 ? void 0 : (_response_users_1 = _response_users4[0]) === null || _response_users_1 === void 0 ? void 0 : _response_users_1.fullName);\n            // Aplicar máscaras de privacidade aos dados dos usuários\n            const usersWithPrivacy = applyListPrivacyMasks('user', response.users || []);\n            console.log('🔒 Máscaras de privacidade aplicadas aos usuários');\n            setUsers(usersWithPrivacy);\n            setTotalUsers(response.total);\n            setTotalPages(response.pages);\n            setCurrentPage(page);\n        } catch (error) {\n            var _error_response, _error_response1;\n            console.error(\"Erro ao carregar usuários:\", error);\n            console.error(\"Detalhes do erro:\", {\n                message: error.message,\n                response: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data,\n                status: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers(1, filters, \"fullName\", \"asc\");\n            loadSubscriptionData();\n        }\n    }[\"UsersPage.useEffect\"], []);\n    // Efeito para abrir o modal automaticamente baseado nos parâmetros da URL\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            const userId = searchParams.get('userId');\n            const openModal = searchParams.get('openModal');\n            if (userId && openModal === 'true') {\n                // Verifica se o usuário já está carregado\n                const user = users.find({\n                    \"UsersPage.useEffect.user\": (u)=>u.id === userId\n                }[\"UsersPage.useEffect.user\"]);\n                if (user) {\n                    setSelectedUser(user);\n                    setUserFormOpen(true);\n                } else {\n                    // Se não estiver carregado, busca o usuário\n                    _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.get(userId).then({\n                        \"UsersPage.useEffect\": (userData)=>{\n                            // Aplicar máscaras de privacidade ao usuário específico\n                            const userWithPrivacy = applyListPrivacyMasks('user', [\n                                userData\n                            ])[0];\n                            setSelectedUser(userWithPrivacy);\n                            setUserFormOpen(true);\n                        }\n                    }[\"UsersPage.useEffect\"]).catch({\n                        \"UsersPage.useEffect\": (error)=>{\n                            console.error(\"Erro ao buscar usuário:\", error);\n                        }\n                    }[\"UsersPage.useEffect\"]);\n                }\n            }\n        }\n    }[\"UsersPage.useEffect\"], [\n        searchParams,\n        users\n    ]);\n    const handleSearch = (searchFilters)=>{\n        loadUsers(1, searchFilters, \"fullName\", \"asc\");\n    };\n    const handlePageChange = (page)=>{\n        // Manter a ordenação atual ao mudar de página\n        const currentSortField = sortField || \"fullName\";\n        const currentSortDirection = sortDirection || \"asc\";\n        console.log('📄 Mudando página:', {\n            page,\n            currentSortField,\n            currentSortDirection\n        });\n        loadUsers(page, filters, currentSortField, currentSortDirection, itemsPerPage);\n    };\n    const handleEditUser = (user)=>{\n        setSelectedUser(user);\n        setUserFormOpen(true);\n    };\n    const handleEditModules = (user)=>{\n        setSelectedUser(user);\n        setModulesModalOpen(true);\n    };\n    const handleManageRole = (user)=>{\n        setSelectedUser(user);\n        setRoleModalOpen(true);\n    };\n    const handleToggleStatus = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"toggle-status\",\n            message: \"\".concat(user.active ? \"Desativar\" : \"Ativar\", \" o usu\\xe1rio \").concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleDeleteUser = (user)=>{\n        setSelectedUser(user);\n        setActionToConfirm({\n            type: \"delete\",\n            message: \"Excluir permanentemente o usu\\xe1rio \".concat(user.fullName, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleManagePermissions = (user)=>{\n        setSelectedUser(user);\n        setPermissionsModalOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.exportUsers({\n                search: filters.search || undefined,\n                userIds: filters.users.length > 0 ? filters.users : undefined,\n                active: filters.status === \"\" ? undefined : filters.status === \"active\",\n                module: filters.module || undefined,\n                companyId: filters.companies.length > 0 ? filters.companies[0] : undefined\n            }, format, preferences);\n        } catch (error) {\n            console.error(\"Erro ao exportar usuários:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        if (actionToConfirm.type === \"toggle-status\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.toggleStatus(selectedUser.id, !selectedUser.active);\n                loadUsers();\n                loadSubscriptionData(); // Recarregar dados da subscription\n            } catch (error) {\n                console.error(\"Erro ao alterar status do usuário:\", error);\n            }\n        } else if (actionToConfirm.type === \"delete\") {\n            try {\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.delete(selectedUser.id);\n                loadUsers();\n                loadSubscriptionData(); // Recarregar dados da subscription\n            } catch (error) {\n                console.error(\"Erro ao excluir usuário:\", error);\n            }\n        }\n        setConfirmationDialogOpen(false);\n    };\n    // Import tutorial steps from tutorialMapping\n    const admUsersTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"UsersPage.useMemo[admUsersTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/users'] || [];\n        }\n    }[\"UsersPage.useMemo[admUsersTutorialSteps]\"], []);\n    // Função para abrir o modal e pré-selecionar os primeiros permitidos\n    const openUserLimitModal = ()=>{\n        // Filtra o usuário atual da lista de usuários disponíveis\n        const availableUsers = users.filter((u)=>u.active && u.id !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id));\n        // Seleciona os primeiros (userLimit - 1) usuários, já que o usuário atual sempre ficará ativo\n        if (availableUsers.length > 0 && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage)) {\n            const usersToSelect = Math.min(subscriptionData.usage.userLimit - 1, availableUsers.length);\n            setSelectedActiveUsers(availableUsers.slice(0, usersToSelect).map((u)=>u.id));\n        }\n        setShowUserLimitModal(true);\n    };\n    // Função para salvar seleção e desativar excedentes\n    const handleSaveUserLimit = async ()=>{\n        setSavingUserLimit(true);\n        try {\n            // Desativa todos os usuários ativos que não estão selecionados E que não são o usuário atual\n            const usersToDeactivate = users.filter((u)=>u.active && !selectedActiveUsers.includes(u.id) && u.id !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id // Garantir que o usuário atual nunca seja desativado\n                ));\n            for (const user of usersToDeactivate){\n                await _app_modules_admin_services_userService__WEBPACK_IMPORTED_MODULE_11__.userService.toggleStatus(user.id, false);\n            }\n            setShowUserLimitModal(false);\n            loadUsers();\n            loadSubscriptionData();\n        } catch (err) {\n            alert('Erro ao atualizar usuários: ' + (err.message || ''));\n        } finally{\n            setSavingUserLimit(false);\n        }\n    };\n    // Função para fechar o modal e limpar a URL\n    const handleCloseUserModal = ()=>{\n        setUserFormOpen(false);\n        setSelectedUser(null);\n        // Remove userId e openModal da URL\n        const params = new URLSearchParams(window.location.search);\n        params.delete('userId');\n        params.delete('openModal');\n        router.replace(\"/dashboard/admin/users\".concat(params.toString() ? '?' + params.toString() : ''));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage) && subscriptionData.usage.currentUsers > subscriptionData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 border-l-4 border-module-admin-primary dark:border-module-admin-primary-dark rounded-lg shadow-sm mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 20,\n                                                className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 453,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 451,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-semibold text-red-800 dark:text-red-200 mb-1\",\n                                                children: \"Limite de usu\\xe1rios excedido\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-red-700 dark:text-red-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"mb-2\",\n                                                        children: [\n                                                            \"Voc\\xea est\\xe1 usando \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold\",\n                                                                children: subscriptionData.usage.currentUsers\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 40\n                                                            }, undefined),\n                                                            \" de \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-semibold\",\n                                                                children: subscriptionData.usage.userLimit\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 120\n                                                            }, undefined),\n                                                            \" usu\\xe1rios permitidos pelo seu plano.\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs\",\n                                                        children: \"Selecione quais usu\\xe1rios permanecer\\xe3o ativos para estar em conformidade com o limite da sua assinatura.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 460,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 450,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-shrink-0 ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: openUserLimitModal,\n                                    className: \"inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-module-admin-primary to-module-admin-accent hover:from-module-admin-primary-dark hover:to-module-admin-accent-dark dark:from-module-admin-primary-dark dark:to-module-admin-accent-dark dark:hover:from-slate-700 dark:hover:to-slate-600 text-white text-sm font-medium rounded-lg shadow-sm transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-module-admin-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 476,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"whitespace-nowrap\",\n                                            children: \"Gerenciar usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 477,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 472,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 471,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 449,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 pb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-xs text-red-600 dark:text-red-400 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Uso atual:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: [\n                                            Math.round(subscriptionData.usage.currentUsers / subscriptionData.usage.userLimit * 100),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 486,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 484,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full bg-red-200 dark:bg-red-800/30 rounded-full h-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-red-500 to-orange-500 h-2 rounded-full transition-all duration-300\",\n                                    style: {\n                                        width: \"\".concat(Math.min(subscriptionData.usage.currentUsers / subscriptionData.usage.userLimit * 100, 100), \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 491,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 490,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 483,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 448,\n                columnNumber: 11\n            }, undefined),\n            (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Modal__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                isOpen: showUserLimitModal,\n                onClose: ()=>setShowUserLimitModal(false),\n                title: \"Gerenciar usu\\xe1rios ativos\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-module-admin-bg dark:bg-module-admin-bg-dark rounded-lg p-4 border border-module-admin-border dark:border-module-admin-border-dark\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-semibold text-module-admin-text dark:text-module-admin-text-dark\",\n                                                    children: \"Limite da assinatura\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-module-admin-muted dark:text-module-admin-muted-dark\",\n                                                    children: [\n                                                        \"Selecione at\\xe9 \",\n                                                        (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage1 = subscriptionData.usage) === null || _subscriptionData_usage1 === void 0 ? void 0 : _subscriptionData_usage1.userLimit) - 1,\n                                                        \" usu\\xe1rios adicionais (voc\\xea permanecer\\xe1 ativo automaticamente)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 513,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-module-admin-muted dark:text-module-admin-muted-dark\",\n                                                    children: [\n                                                        \"Selecionados: \",\n                                                        selectedActiveUsers.length + 1,\n                                                        \" de \",\n                                                        subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage2 = subscriptionData.usage) === null || _subscriptionData_usage2 === void 0 ? void 0 : _subscriptionData_usage2.userLimit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-semibold \".concat(selectedActiveUsers.length + 1 === (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage3 = subscriptionData.usage) === null || _subscriptionData_usage3 === void 0 ? void 0 : _subscriptionData_usage3.userLimit) ? 'text-green-600 dark:text-green-400' : selectedActiveUsers.length + 1 > (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage4 = subscriptionData.usage) === null || _subscriptionData_usage4 === void 0 ? void 0 : _subscriptionData_usage4.userLimit) ? 'text-red-600 dark:text-red-400' : 'text-module-admin-accent dark:text-module-admin-accent-dark'),\n                                                    children: selectedActiveUsers.length + 1 === (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage5 = subscriptionData.usage) === null || _subscriptionData_usage5 === void 0 ? void 0 : _subscriptionData_usage5.userLimit) ? '✓ Completo' : selectedActiveUsers.length + 1 > (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage6 = subscriptionData.usage) === null || _subscriptionData_usage6 === void 0 ? void 0 : _subscriptionData_usage6.userLimit) ? '⚠ Excedido' : '○ Incompleto'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-module-admin-secondary dark:bg-module-admin-secondary-dark rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-2 rounded-full transition-all duration-300 \".concat(selectedActiveUsers.length + 1 === (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage7 = subscriptionData.usage) === null || _subscriptionData_usage7 === void 0 ? void 0 : _subscriptionData_usage7.userLimit) ? 'bg-gradient-to-r from-green-500 to-emerald-500' : 'bg-gradient-to-r from-red-500 to-red-600'),\n                                                style: {\n                                                    width: \"\".concat(Math.min((selectedActiveUsers.length + 1) / (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage8 = subscriptionData.usage) === null || _subscriptionData_usage8 === void 0 ? void 0 : _subscriptionData_usage8.userLimit) * 100, 100), \"%\")\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 547,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-green-600 dark:text-green-400 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 563,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-800 dark:text-green-300 font-medium\",\n                                                children: [\n                                                    \"Voc\\xea (\",\n                                                    currentUser === null || currentUser === void 0 ? void 0 : currentUser.fullName,\n                                                    \") permanecer\\xe1 ativo automaticamente\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 562,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 561,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 512,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-module-admin-text dark:text-module-admin-text-dark px-1\",\n                                    children: [\n                                        \"Outros usu\\xe1rios ativos dispon\\xedveis (\",\n                                        users.filter((u)=>u.active && u.id !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id)).length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-h-80 overflow-y-auto border border-module-admin-border dark:border-module-admin-border-dark rounded-lg bg-module-admin-card dark:bg-module-admin-card-dark\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"divide-y divide-module-admin-border dark:divide-module-admin-border-dark\",\n                                        children: users.filter((u)=>u.active && u.id !== (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id)).map((user, index)=>{\n                                            const isSelected = selectedActiveUsers.includes(user.id);\n                                            const isDisabled = !isSelected && selectedActiveUsers.length >= subscriptionData.usage.userLimit - 1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 p-4 cursor-pointer transition-all duration-150 \".concat(isDisabled ? 'opacity-50 cursor-not-allowed bg-module-admin-secondary/20 dark:bg-module-admin-secondary-dark/20' : isSelected ? 'bg-module-admin-secondary/30 dark:bg-module-admin-secondary-dark/30' : 'bg-module-admin-card dark:bg-module-admin-card-dark hover:bg-module-admin-secondary/20 dark:hover:bg-module-admin-secondary-dark/20'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"checkbox\",\n                                                            checked: isSelected,\n                                                            onChange: (e)=>{\n                                                                if (e.target.checked) {\n                                                                    if (selectedActiveUsers.length < subscriptionData.usage.userLimit - 1) {\n                                                                        setSelectedActiveUsers([\n                                                                            ...selectedActiveUsers,\n                                                                            user.id\n                                                                        ]);\n                                                                    }\n                                                                } else {\n                                                                    setSelectedActiveUsers(selectedActiveUsers.filter((id)=>id !== user.id));\n                                                                }\n                                                            },\n                                                            disabled: isDisabled,\n                                                            className: \"w-4 h-4 text-module-admin-primary border-module-admin-border dark:border-module-admin-border-dark rounded focus:ring-module-admin-primary dark:focus:ring-module-admin-primary-dark disabled:opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 594,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3 flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveAvatar, {\n                                                                entityType: \"user\",\n                                                                src: user.profileImageFullUrl,\n                                                                alt: user.fullName,\n                                                                size: 32,\n                                                                className: \"flex-shrink-0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                lineNumber: 613,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium text-module-admin-text dark:text-module-admin-text-dark truncate\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveFullName, {\n                                                                            entityType: \"user\",\n                                                                            value: user.fullName,\n                                                                            showToggle: false\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                            lineNumber: 624,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 text-xs text-module-admin-muted dark:text-module-admin-muted-dark mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1 truncate\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                        size: 12,\n                                                                                        className: \"flex-shrink-0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                        lineNumber: 633,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"truncate\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveEmail, {\n                                                                                            entityType: \"user\",\n                                                                                            value: user.email,\n                                                                                            data: user,\n                                                                                            showToggle: true\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                            lineNumber: 635,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                        lineNumber: 634,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                lineNumber: 632,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            user.professionObj && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"•\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                        lineNumber: 645,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-1 truncate\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                size: 12,\n                                                                                                className: \"flex-shrink-0\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                                lineNumber: 647,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"truncate\",\n                                                                                                children: user.professionObj.name\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                                lineNumber: 648,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                                        lineNumber: 646,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-shrink-0\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 rounded-full\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            size: 12\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                            lineNumber: 659,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        \"Selecionado\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                    lineNumber: 658,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 584,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 578,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between pt-4 border-t border-module-admin-border dark:border-module-admin-border-dark\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-module-admin-muted dark:text-module-admin-muted-dark\",\n                                    children: selectedActiveUsers.length + 1 !== (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage9 = subscriptionData.usage) === null || _subscriptionData_usage9 === void 0 ? void 0 : _subscriptionData_usage9.userLimit) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                size: 12,\n                                                className: \"text-module-admin-accent\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 677,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Selecione at\\xe9 \",\n                                            (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage10 = subscriptionData.usage) === null || _subscriptionData_usage10 === void 0 ? void 0 : _subscriptionData_usage10.userLimit) - 1,\n                                            \" usu\\xe1rios adicionais para continuar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 676,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 674,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            onClick: ()=>setShowUserLimitModal(false),\n                                            variant: \"secondary\",\n                                            className: \"text-sm\",\n                                            children: \"Cancelar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 684,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            onClick: handleSaveUserLimit,\n                                            isLoading: savingUserLimit,\n                                            disabled: selectedActiveUsers.length + 1 > (subscriptionData === null || subscriptionData === void 0 ? void 0 : (_subscriptionData_usage11 = subscriptionData.usage) === null || _subscriptionData_usage11 === void 0 ? void 0 : _subscriptionData_usage11.userLimit),\n                                            variant: \"primary\",\n                                            className: \"text-sm\",\n                                            children: savingUserLimit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Salvando...\"\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                        size: 14,\n                                                        className: \"mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 705,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    \"Salvar sele\\xe7\\xe3o\"\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 691,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 683,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 510,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 719,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Usu\\xe1rios\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 718,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir usuários selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 731,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 726,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || users.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 736,\n                                columnNumber: 11\n                            }, undefined),\n                            can(\"admin.users.create\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            if (canAddUsers) {\n                                                setSelectedUser(null);\n                                                setUserFormOpen(true);\n                                            }\n                                        },\n                                        disabled: !canAddUsers,\n                                        className: \"flex items-center gap-2 px-4 py-2 rounded-lg shadow-md transition-all \".concat(canAddUsers ? \"bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800\" : \"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                size: 18\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Novo Usu\\xe1rio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 760,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 746,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !canAddUsers && (subscriptionData === null || subscriptionData === void 0 ? void 0 : subscriptionData.usage) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                        children: [\n                                            \"Limite de usu\\xe1rios atingido (\",\n                                            subscriptionData.usage.currentUsers,\n                                            \"/\",\n                                            subscriptionData.usage.userLimit,\n                                            \")\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 767,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                        lineNumber: 765,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 745,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 723,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 717,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 778,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie os usu\\xe1rios do sistema. Utilize os filtros abaixo para encontrar usu\\xe1rios espec\\xedficos.\",\n                moduleColor: \"admin\",\n                tutorialSteps: admUsersTutorialSteps,\n                tutorialName: \"admin-users-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_UsersFilters__WEBPACK_IMPORTED_MODULE_19__.UsersFilters, {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: handleSearch\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 784,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.ModuleTable, {\n                moduleColor: \"admin\",\n                title: \"Lista de Usu\\xe1rios\",\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadUsers(),\n                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-module-admin-primary dark:hover:text-module-admin-primary-dark transition-colors\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 802,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 797,\n                    columnNumber: 11\n                }, void 0),\n                columns: [\n                    {\n                        header: '',\n                        field: 'select',\n                        width: '50px',\n                        sortable: false\n                    },\n                    {\n                        header: 'Usuário',\n                        field: 'fullName',\n                        width: '20%'\n                    },\n                    {\n                        header: 'Email',\n                        field: 'email',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Profissão',\n                        field: 'profession',\n                        width: '10%',\n                        maxWidth: '160px'\n                    },\n                    {\n                        header: 'Grupo',\n                        field: 'group',\n                        width: '10%',\n                        maxWidth: '140px'\n                    },\n                    {\n                        header: 'Módulos',\n                        field: 'modules',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Função',\n                        field: 'role',\n                        width: '10%'\n                    },\n                    {\n                        header: 'Status',\n                        field: 'active',\n                        width: '8%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '14%',\n                        sortable: false\n                    }\n                ],\n                data: users,\n                ...console.log('📋 ModuleTable recebendo users:', users === null || users === void 0 ? void 0 : users.length, 'itens') || {},\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum usu\\xe1rio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                    lineNumber: 821,\n                    columnNumber: 20\n                }, void 0),\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalUsers,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                tableId: \"admin-users-table\",\n                enableColumnToggle: true,\n                defaultSortField: sortField,\n                defaultSortDirection: sortDirection,\n                onSort: (field, direction)=>{\n                    console.log('🔄 Ordenando:', {\n                        field,\n                        direction\n                    });\n                    setSortField(field);\n                    setSortDirection(direction);\n                    loadUsers(currentPage, filters, field, direction, itemsPerPage);\n                },\n                itemsPerPage: itemsPerPage,\n                onItemsPerPageChange: (newItemsPerPage)=>{\n                    setItemsPerPage(newItemsPerPage);\n                    loadUsers(1, filters, \"fullName\", \"asc\", newItemsPerPage);\n                },\n                selectedIds: selectedIds,\n                onSelectAll: handleSelectAll,\n                renderRow: (user, index, moduleColors, visibleColumns)=>{\n                    console.log(\"\\uD83D\\uDD04 Renderizando linha \".concat(index, \":\"), user.fullName);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_14__.ModuleCheckbox, {\n                                    moduleColor: \"admin\",\n                                    checked: selectedIds.includes(user.id),\n                                    onChange: (e)=>handleSelectOne(user.id, e.target.checked),\n                                    name: \"select-user-\".concat(user.id)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 850,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 849,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('fullName') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveAvatar, {\n                                            entityType: \"user\",\n                                            src: user.profileImageFullUrl,\n                                            alt: user.fullName,\n                                            size: 40,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 861,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3 min-w-0 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveFullName, {\n                                                        entityType: \"user\",\n                                                        value: user.fullName,\n                                                        showToggle: false\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 870,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1 text-xs text-neutral-500 dark:text-neutral-400 truncate mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            size: 12,\n                                                            className: \"flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: user.login\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                            lineNumber: 880,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 878,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 868,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 860,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 859,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('email') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1 text-sm text-neutral-600 dark:text-neutral-300 truncate\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            size: 14,\n                                            className: \"flex-shrink-0 text-neutral-400 dark:text-neutral-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 891,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_SensitiveField__WEBPACK_IMPORTED_MODULE_20__.SensitiveEmail, {\n                                                entityType: \"user\",\n                                                value: user.email,\n                                                data: user,\n                                                showToggle: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 892,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 890,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 888,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('profession') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 max-w-[160px] truncate\",\n                                children: user.professionObj ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            size: 14,\n                                            className: \"text-neutral-500 dark:text-neutral-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 908,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.professionObj.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 909,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 907,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                    children: \"Sem profiss\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 914,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 905,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300 max-w-[140px] truncate\",\n                                children: user.professionObj && user.professionObj.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            size: 14,\n                                            className: \"text-neutral-500 dark:text-neutral-400 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 923,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.professionObj.group.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 924,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 922,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                    children: \"Sem grupo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 927,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 920,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('modules') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-1\",\n                                    children: [\n                                        user.modules.filter((module)=>activeModules.includes(module)).slice(0, 2).map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                                children: MODULE_LABELS[module]\n                                            }, module, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 936,\n                                                columnNumber: 21\n                                            }, void 0)),\n                                        user.modules.filter((module)=>activeModules.includes(module)).length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300\",\n                                            children: [\n                                                \"+\",\n                                                user.modules.filter((module)=>activeModules.includes(module)).length - 2\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 944,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 934,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 933,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('role') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full inline-flex items-center gap-1 \".concat(user.role === \"SYSTEM_ADMIN\" ? \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\" : user.role === \"COMPANY_ADMIN\" ? \"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400\" : \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            size: 12,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 962,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"truncate\",\n                                            children: user.role === \"SYSTEM_ADMIN\" ? \"Admin Sistema\" : user.role === \"COMPANY_ADMIN\" ? \"Admin Empresa\" : \"Funcionário\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 963,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 954,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 953,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(user.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                    children: user.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 984,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Ativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 985,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                size: 12,\n                                                className: \"flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 989,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Inativo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 990,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 976,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 975,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right text-sm font-medium\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                id: \"edicaoUsuario\",\n                                                title: \"Editar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1001,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1000,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleEditModules(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                id: \"gerenciarModulo\",\n                                                title: \"Gerenciar m\\xf3dulos\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1018,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1012,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1011,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.permissions.manage\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManagePermissions(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors\",\n                                                id: \"gerenciarPermissoes\",\n                                                title: \"Gerenciar permiss\\xf5es\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1023,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1022,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleManageRole(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 transition-colors\",\n                                                id: \"gerenciarFuncao\",\n                                                title: \"Alterar fun\\xe7\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1042,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1036,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1035,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.users.edit\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleToggleStatus(user),\n                                                className: \"p-1 transition-colors \".concat(user.active ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\" : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"),\n                                                id: \"desativarUsuario\",\n                                                title: user.active ? \"Desativar\" : \"Ativar\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1050,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1049,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        (user.role !== \"SYSTEM_ADMIN\" || isSystemAdmin) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_5__.Protected, {\n                                            permission: \"admin.users.delete\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDeleteUser(user),\n                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                id: \"excluirUsuario\",\n                                                title: \"Excluir\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_CheckCircle_Edit_Filter_Lock_Mail_Plus_Power_RefreshCw_Search_Shield_Trash_User_UserCog_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                    size: 16\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                    lineNumber: 1073,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                                lineNumber: 1067,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                            lineNumber: 1066,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                    lineNumber: 999,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                                lineNumber: 998,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, user.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                        lineNumber: 847,\n                        columnNumber: 11\n                    }, void 0);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 793,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_UserFormModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: userFormOpen,\n                onClose: handleCloseUserModal,\n                user: selectedUser,\n                onSuccess: ()=>{\n                    handleCloseUserModal();\n                    loadUsers();\n                    loadSubscriptionData(); // Recarregar dados da subscription\n                },\n                currentUser: currentUser\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 1086,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_ModulesModal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: modulesModalOpen,\n                onClose: ()=>setModulesModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setModulesModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 1098,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 1108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_PermissionsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: permissionsModalOpen,\n                onClose: ()=>setPermissionsModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setPermissionsModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 1117,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_RoleModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: roleModalOpen,\n                onClose: ()=>setRoleModalOpen(false),\n                user: selectedUser,\n                onSuccess: ()=>{\n                    setRoleModalOpen(false);\n                    loadUsers();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n                lineNumber: 1126,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\users\\\\UsersPage.js\",\n        lineNumber: 444,\n        columnNumber: 5\n    }, undefined);\n};\n_s(UsersPage, \"2pFJoBixY7hAIB31mbL00LDwgXY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_4__.usePermissions,\n        _hooks_usePreferences__WEBPACK_IMPORTED_MODULE_21__.usePreferences,\n        _hooks_useDataPrivacy__WEBPACK_IMPORTED_MODULE_22__.useDataPrivacy,\n        next_navigation__WEBPACK_IMPORTED_MODULE_18__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_18__.useSearchParams\n    ];\n});\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\nvar _c;\n$RefreshReg$(_c, \"UsersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/users/UsersPage.js\n"));

/***/ })

});