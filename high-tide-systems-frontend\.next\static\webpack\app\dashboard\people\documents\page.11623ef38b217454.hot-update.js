"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/components/people/DocumentSharingModal.js":
/*!*******************************************************!*\
  !*** ./src/components/people/DocumentSharingModal.js ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog.jsx */ \"(app-pages-browser)/./src/components/ui/dialog.jsx\");\n/* harmony import */ var _components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button.js */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input.js */ \"(app-pages-browser)/./src/components/ui/Input.js\");\n/* harmony import */ var _components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Label.js */ \"(app-pages-browser)/./src/components/ui/Label.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge */ \"(app-pages-browser)/./src/components/ui/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Minus,Plus,Search,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst DocumentSharingModal = (param)=>{\n    let { isOpen, onClose, document, onSuccess, initialPermissions = {\n        users: [],\n        professions: [],\n        clients: []\n    } } = param;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredUsers, setFilteredUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredClients, setFilteredClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para seleções\n    const [selectedUsers, setSelectedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPermissions.users || []);\n    const [selectedProfessions, setSelectedProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPermissions.professions || []);\n    const [selectedClients, setSelectedClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialPermissions.clients || []);\n    // Estados para filtros\n    const [userSearch, setUserSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [clientSearch, setClientSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentSharingModal.useEffect\": ()=>{\n            if (isOpen) {\n                loadUsers();\n                loadProfessions();\n                loadClients();\n            }\n        }\n    }[\"DocumentSharingModal.useEffect\"], [\n        isOpen\n    ]);\n    // Filtrar usuários baseado na busca\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentSharingModal.useEffect\": ()=>{\n            const filtered = users.filter({\n                \"DocumentSharingModal.useEffect.filtered\": (user)=>user.fullName.toLowerCase().includes(userSearch.toLowerCase()) || user.email.toLowerCase().includes(userSearch.toLowerCase())\n            }[\"DocumentSharingModal.useEffect.filtered\"]);\n            setFilteredUsers(filtered);\n        }\n    }[\"DocumentSharingModal.useEffect\"], [\n        users,\n        userSearch\n    ]);\n    // Filtrar clientes baseado na busca\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentSharingModal.useEffect\": ()=>{\n            const filtered = clients.filter({\n                \"DocumentSharingModal.useEffect.filtered\": (client)=>client.login.toLowerCase().includes(clientSearch.toLowerCase()) || client.email.toLowerCase().includes(clientSearch.toLowerCase())\n            }[\"DocumentSharingModal.useEffect.filtered\"]);\n            setFilteredClients(filtered);\n        }\n    }[\"DocumentSharingModal.useEffect\"], [\n        clients,\n        clientSearch\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            const response = await fetch(\"/api/users\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUsers(data.users || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar usuários:\", error);\n        }\n    };\n    const loadProfessions = async ()=>{\n        try {\n            const response = await fetch(\"/api/professions\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setProfessions(data.professions || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n        }\n    };\n    const loadClients = async ()=>{\n        try {\n            const response = await fetch(\"/api/clients\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setClients(data.clients || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar clientes:\", error);\n        }\n    };\n    const handleAddProfession = ()=>{\n        if (selectedProfession && !selectedProfessions.includes(selectedProfession)) {\n            setSelectedProfessions((prev)=>[\n                    ...prev,\n                    selectedProfession\n                ]);\n            setSelectedProfession(\"\");\n        }\n    };\n    const handleRemoveProfession = (professionId)=>{\n        setSelectedProfessions((prev)=>prev.filter((id)=>id !== professionId));\n    };\n    const handleAddUser = (userId)=>{\n        if (!selectedUsers.includes(userId)) {\n            setSelectedUsers((prev)=>[\n                    ...prev,\n                    userId\n                ]);\n        }\n    };\n    const handleRemoveUser = (userId)=>{\n        setSelectedUsers((prev)=>prev.filter((id)=>id !== userId));\n    };\n    const handleAddClient = (clientId)=>{\n        if (!selectedClients.includes(clientId)) {\n            setSelectedClients((prev)=>[\n                    ...prev,\n                    clientId\n                ]);\n        }\n    };\n    const handleRemoveClient = (clientId)=>{\n        setSelectedClients((prev)=>prev.filter((id)=>id !== clientId));\n    };\n    const handleSubmit = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"/api/documents/\".concat(document.id, \"/permissions\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify({\n                    users: selectedUsers,\n                    professions: selectedProfessions,\n                    clients: selectedClients\n                })\n            });\n            if (response.ok) {\n                onSuccess();\n                onClose();\n            } else {\n                const error = await response.json();\n                alert(error.message || \"Erro ao salvar permissões\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar permissões:\", error);\n            alert(\"Erro ao salvar permissões\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getSelectedUserNames = ()=>{\n        return selectedUsers.map((id)=>{\n            const user = users.find((u)=>u.id === id);\n            return (user === null || user === void 0 ? void 0 : user.fullName) || id;\n        });\n    };\n    const getSelectedProfessionNames = ()=>{\n        return selectedProfessions.map((id)=>{\n            const profession = professions.find((p)=>p.id === id);\n            return (profession === null || profession === void 0 ? void 0 : profession.name) || id;\n        });\n    };\n    const getSelectedClientNames = ()=>{\n        return selectedClients.map((id)=>{\n            const client = clients.find((c)=>c.id === id);\n            return (client === null || client === void 0 ? void 0 : client.login) || id;\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-4xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        className: \"text-xl font-semibold text-slate-800 dark:text-white\",\n                        children: [\n                            \"Compartilhar Documento: \",\n                            document === null || document === void 0 ? void 0 : document.filename\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                    lineNumber: 205,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Profiss\\xf5es\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                    children: \"Selecione profiss\\xf5es para incluir automaticamente todos os usu\\xe1rios dessa profiss\\xe3o\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                            moduleColor: \"people\",\n                                            value: selectedProfession,\n                                            onChange: (e)=>setSelectedProfession(e.target.value),\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: \"Selecione uma profiss\\xe3o\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                professions.map((profession)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: profession.id,\n                                                        children: profession.name\n                                                    }, profession.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            type: \"button\",\n                                            onClick: handleAddProfession,\n                                            disabled: !selectedProfession,\n                                            className: \"bg-orange-500 hover:bg-orange-600\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedProfessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Profiss\\xf5es selecionadas:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: getSelectedProfessionNames().map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"secondary\",\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            size: 12\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        name,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: ()=>handleRemoveProfession(selectedProfessions[index]),\n                                                            className: \"ml-1 text-red-500 hover:text-red-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: 12\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, \"profession-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Usu\\xe1rios\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            placeholder: \"Buscar usu\\xe1rios...\",\n                                            value: userSearch,\n                                            onChange: (e)=>setUserSearch(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-h-40 overflow-y-auto border rounded-lg p-2\",\n                                    children: filteredUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleCheckbox, {\n                                                            moduleColor: \"people\",\n                                                            checked: selectedUsers.includes(user.id),\n                                                            onChange: ()=>{\n                                                                if (selectedUsers.includes(user.id)) {\n                                                                    handleRemoveUser(user.id);\n                                                                } else {\n                                                                    handleAddUser(user.id);\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: user.fullName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: user.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                                    lineNumber: 305,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        if (selectedUsers.includes(user.id)) {\n                                                            handleRemoveUser(user.id);\n                                                        } else {\n                                                            handleAddUser(user.id);\n                                                        }\n                                                    },\n                                                    children: selectedUsers.includes(user.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 56\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 78\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 290,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"text-lg font-medium\",\n                                            children: \"Clientes\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 331,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 329,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            placeholder: \"Buscar clientes...\",\n                                            value: clientSearch,\n                                            onChange: (e)=>setClientSearch(e.target.value),\n                                            className: \"pl-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-h-40 overflow-y-auto border rounded-lg p-2\",\n                                    children: filteredClients.map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleCheckbox, {\n                                                            moduleColor: \"people\",\n                                                            checked: selectedClients.includes(client.id),\n                                                            onChange: ()=>{\n                                                                if (selectedClients.includes(client.id)) {\n                                                                    handleRemoveClient(client.id);\n                                                                } else {\n                                                                    handleAddClient(client.id);\n                                                                }\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: client.login\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                                    lineNumber: 362,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-500\",\n                                                                    children: client.email\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                                    lineNumber: 363,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    type: \"button\",\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>{\n                                                        if (selectedClients.includes(client.id)) {\n                                                            handleRemoveClient(client.id);\n                                                        } else {\n                                                            handleAddClient(client.id);\n                                                        }\n                                                    },\n                                                    children: selectedClients.includes(client.id) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 60\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 82\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, client.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                            lineNumber: 328,\n                            columnNumber: 11\n                        }, undefined),\n                        (selectedUsers.length > 0 || selectedProfessions.length > 0 || selectedClients.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"text-lg font-medium\",\n                                    children: \"Resumo dos Compartilhamentos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedProfessions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Profiss\\xf5es (\",\n                                                selectedProfessions.length,\n                                                \"):\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 392,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: getSelectedProfessionNames().map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            size: 12,\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        name\n                                                    ]\n                                                }, \"summary-profession-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 395,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 391,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedUsers.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Usu\\xe1rios (\",\n                                                selectedUsers.length,\n                                                \"):\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: getSelectedUserNames().map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 12,\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 414,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        name\n                                                    ]\n                                                }, \"summary-user-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 411,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, undefined),\n                                selectedClients.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                            children: [\n                                                \"Clientes (\",\n                                                selectedClients.length,\n                                                \"):\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: getSelectedClientNames().map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Minus_Plus_Search_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            size: 12,\n                                                            className: \"mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        name\n                                                    ]\n                                                }, \"summary-client-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 423,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    disabled: isLoading,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    onClick: handleSubmit,\n                                    disabled: isLoading,\n                                    className: \"bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600\",\n                                    children: isLoading ? \"Salvando...\" : \"Salvar Compartilhamentos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                            lineNumber: 441,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingModal.js\",\n        lineNumber: 203,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentSharingModal, \"yhD+rsUdL3bXumRmgx+6bFxwrJA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = DocumentSharingModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentSharingModal);\nvar _c;\n$RefreshReg$(_c, \"DocumentSharingModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/people/DocumentSharingModal.js\n"));

/***/ })

});