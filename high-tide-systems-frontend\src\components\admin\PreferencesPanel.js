import { useState } from "react";
import { Card } from "../ui/Card";
import UserPreferences from "./preferences/UserPreferences";
import SchedulingPreferences from "./preferences/SchedulingPreferences";
import NotificationPreferences from "./preferences/NotificationPreferences";
import { useCompanyPreferences } from "@/hooks/useCompanyPreferences";
import { useCompanySelection } from "@/contexts/CompanySelectionContext";
import { useAuth } from "@/contexts/AuthContext";
import { SlidersHorizontal, Building2 } from "lucide-react";
import ModuleFormGroup from "../ui/ModuleFormGroup";
import ModuleLabel from "../ui/ModuleLabel";
import ModuleInput from "../ui/ModuleInput";

// Componente temporário para abas não implementadas
const ComingSoonTab = ({ title }) => (
  <div className="text-center py-12">
    <div className="max-w-md mx-auto">
      <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-lg p-8 border border-gray-200 dark:border-gray-600">
        <div className="text-4xl mb-4">🚧</div>
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200 mb-2">
          {title}
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Esta funcionalidade está em desenvolvimento e estará disponível em breve.
        </p>
      </div>
    </div>
  </div>
);

const TABS = [
  { label: "Usuário/Cliente", component: UserPreferences },
  { label: "Agendamento", component: SchedulingPreferences },
  { label: "Notificações", component: NotificationPreferences },
  { label: "Documentação", component: () => <ComingSoonTab title="Configurações de Documentação" /> },
  { label: "Relatórios", component: () => <ComingSoonTab title="Configurações de Relatórios" /> },
  { label: "Interface", component: () => <ComingSoonTab title="Configurações de Interface" /> },
  { label: "Segurança", component: () => <ComingSoonTab title="Configurações de Segurança" /> },
  { label: "Fluxo de Trabalho", component: () => <ComingSoonTab title="Configurações de Fluxo de Trabalho" /> },
  { label: "Cobrança/Faturamento", component: () => <ComingSoonTab title="Configurações de Cobrança/Faturamento" /> },
];

export default function PreferencesPanel() {
  const [activeTab, setActiveTab] = useState(0);
  const [search, setSearch] = useState("");
  const { isSystemAdmin } = useAuth();
  
  const {
    preferences,
    isLoading,
    savePreferences,
    canSelectCompany
  } = useCompanyPreferences();

  const {
    selectedCompanyId,
    handleCompanyChange,
    companies,
    isLoading: isLoadingCompanies,
    selectedCompany
  } = useCompanySelection();
  
  const ActiveComponent = TABS[activeTab]?.component;
  const hasSearch = search.trim().length > 0;

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex items-center justify-between">
        <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2">
          <SlidersHorizontal className="h-5 w-5 text-primary-500 dark:text-primary-400" />
          Preferências da Empresa
        </h3>
        
        {selectedCompany && (
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Building2 className="h-4 w-4" />
            <span>{selectedCompany.name}</span>
          </div>
        )}
      </div>

      {/* Select de Empresa (apenas para SYSTEM_ADMIN) */}
      {canSelectCompany && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <ModuleFormGroup moduleColor="admin">
            <ModuleLabel htmlFor="company-select" moduleColor="admin">
              <Building2 className="h-4 w-4 mr-2" />
              Selecionar Empresa
            </ModuleLabel>
            <select
              id="company-select"
              value={selectedCompanyId || ''}
              onChange={(e) => handleCompanyChange(e.target.value || null)}
              disabled={isLoadingCompanies}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md 
                focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400
                bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100
                disabled:bg-gray-100 dark:disabled:bg-gray-600 disabled:cursor-not-allowed"
            >
              <option value="">
                {isLoadingCompanies ? 'Carregando empresas...' : 'Selecione uma empresa'}
              </option>
              {companies.map((company) => (
                <option key={company.id} value={company.id}>
                  {company.name}
                </option>
              ))}
            </select>
            {isLoadingCompanies && (
              <div className="mt-2 flex items-center text-sm text-gray-500">
                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500 mr-2"></div>
                Carregando empresas...
              </div>
            )}
          </ModuleFormGroup>
        </div>
      )}
      {/* Botões de sub-abas */}
      <div className="flex flex-wrap gap-3 mb-6">
        {TABS.map((tab, idx) => (
          <button
            key={tab.label}
            className={`px-6 py-2 rounded-lg font-semibold transition-all duration-200 focus:outline-none whitespace-nowrap
              ${activeTab === idx
                ? 'bg-slate-600 text-white shadow-lg scale-105'
                : 'bg-gray-200 dark:bg-gray-700 text-slate-700 dark:text-gray-300 hover:bg-slate-500 hover:text-white dark:hover:bg-slate-600'}
            `}
            onClick={() => setActiveTab(idx)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      {/* Área de busca */}
      <div className="mb-6">
        <ModuleInput
          moduleColor="admin"
          type="text"
          placeholder="Buscar configuração..."
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
      </div>
      {/* Conteúdo da sub-aba */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
        {isLoading || !selectedCompanyId ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500 dark:border-gray-400"></div>
            <span className="ml-3 text-gray-300">
              {!selectedCompanyId ? 'Selecione uma empresa para continuar...' : 'Carregando preferências...'}
            </span>
          </div>
        ) : (
          <div className="w-full">
            {hasSearch ? (
              <div className="space-y-8">
                {TABS.map((tab, idx) => {
                  const TabComponent = tab.component;
                  if (typeof TabComponent !== 'function') return null;
                  return (
                    <div key={tab.label} className="w-full">
                      <div className="flex items-center gap-2 mb-4 mt-8">
                        <span className="text-base font-semibold text-gray-400 border-b border-gray-700 flex-1 pb-1">{tab.label}</span>
                      </div>
                      <TabComponent 
                        search={search} 
                        searchMode 
                        preferences={preferences}
                        selectedCompanyId={selectedCompanyId}
                        onSave={savePreferences}
                      />
                    </div>
                  );
                })}
              </div>
            ) : (
              typeof ActiveComponent === 'function' ? (
                <ActiveComponent 
                  preferences={preferences}
                  selectedCompanyId={selectedCompanyId}
                  onSave={savePreferences}
                />
              ) : (
                <div>Erro ao carregar aba.</div>
              )
            )}
          </div>
        )}
      </div>
    </div>
  );
} 