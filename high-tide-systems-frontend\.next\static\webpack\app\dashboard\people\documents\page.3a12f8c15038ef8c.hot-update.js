"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/app/modules/people/InsurancesPage/InsurancesPage.js":
/*!*****************************************************************!*\
  !*** ./src/app/modules/people/InsurancesPage/InsurancesPage.js ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/tutorial/TutorialManager */ \"(app-pages-browser)/./src/components/tutorial/TutorialManager.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building,CreditCard,Edit,Filter,Plus,RefreshCw,Trash,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/modules/people/services/insurancesService */ \"(app-pages-browser)/./src/app/modules/people/services/insurancesService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_people_InsurancesFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/people/InsurancesFormModal */ \"(app-pages-browser)/./src/components/people/InsurancesFormModal.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_common_ShareButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/common/ShareButton */ \"(app-pages-browser)/./src/components/common/ShareButton.js\");\n/* harmony import */ var _components_people_InsurancesFilters__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/people/InsurancesFilters */ \"(app-pages-browser)/./src/components/people/InsurancesFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst insurancesTutorialSteps = [\n    {\n        title: \"Convênios\",\n        content: \"Esta tela permite gerenciar os convênios disponíveis no sistema.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Novo Convênio\",\n        content: \"Clique aqui para adicionar um novo convênio.\",\n        selector: \"button:has(span:contains('Novo Convênio'))\",\n        position: \"left\"\n    },\n    {\n        title: \"Filtrar Convênios\",\n        content: \"Use esta barra de pesquisa para encontrar convênios específicos pelo nome.\",\n        selector: \"input[placeholder*='Buscar']\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtros Avançados\",\n        content: \"Clique no botão Filtros para acessar opções avançadas de filtragem.\",\n        selector: \"button:has(span:contains('Filtros'))\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de convênios em diferentes formatos usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Gerenciar Convênios\",\n        content: \"Edite ou exclua convênios existentes usando os botões de ação na tabela.\",\n        selector: \"table\",\n        position: \"top\"\n    }\n];\nconst InsurancesPage = ()=>{\n    var _currentUser_modules, _currentUser_modules1;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_modules = currentUser.modules) === null || _currentUser_modules === void 0 ? void 0 : _currentUser_modules.includes(\"ADMIN\")) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser_modules1 = currentUser.modules) === null || _currentUser_modules1 === void 0 ? void 0 : _currentUser_modules1.includes(\"RH\"));\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [insurances, setInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedInsurance, setSelectedInsurance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [insuranceFormOpen, setInsuranceFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(insurances.map((i)=>i.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        insurances: [],\n        dateFrom: \"\",\n        dateTo: \"\"\n    });\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const loadInsurances = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, filtersToUse = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : filters, sortField = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'name', sortDirection = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'asc', perPage = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            const response = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_6__.insurancesService.getInsurances({\n                search: filtersToUse.search || undefined,\n                insuranceIds: filtersToUse.insurances.length > 0 ? filtersToUse.insurances : undefined,\n                companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,\n                dateFrom: filtersToUse.dateFrom || undefined,\n                dateTo: filtersToUse.dateTo || undefined,\n                page,\n                limit: perPage,\n                sortField,\n                sortDirection\n            });\n            if (response && typeof response === 'object') {\n                const insurancesArray = response.insurances || [];\n                const total = response.total || insurancesArray.length;\n                const pages = response.pages || Math.ceil(total / perPage);\n                setInsurances(insurancesArray);\n                setTotalItems(total);\n                setTotalPages(pages);\n                setCurrentPage(page);\n            } else {\n                setInsurances([]);\n                setTotalItems(0);\n                setTotalPages(1);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar convênios:\", error);\n            setInsurances([]);\n            setTotalItems(0);\n            setTotalPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InsurancesPage.useEffect\": ()=>{\n            loadInsurances(1, filters);\n        }\n    }[\"InsurancesPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InsurancesPage.useEffect\": ()=>{\n            const insuranceId = searchParams.get('insuranceId');\n            const openModal = searchParams.get('openModal');\n            const mode = searchParams.get('mode');\n            if (insuranceId && openModal === 'true' && mode === 'edit') {\n                const loadInsuranceForEdit = {\n                    \"InsurancesPage.useEffect.loadInsuranceForEdit\": async ()=>{\n                        try {\n                            const insurance = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_6__.insurancesService.getInsurance(insuranceId);\n                            if (insurance) {\n                                setSelectedInsurance(insurance);\n                                setInsuranceFormOpen(true);\n                            }\n                        } catch (error) {\n                            console.error('Erro ao carregar convênio compartilhado:', error);\n                        }\n                    }\n                }[\"InsurancesPage.useEffect.loadInsuranceForEdit\"];\n                loadInsuranceForEdit();\n            }\n        }\n    }[\"InsurancesPage.useEffect\"], [\n        searchParams\n    ]);\n    const handleSearch = (searchFilters)=>{\n        loadInsurances(1, searchFilters);\n    };\n    const handlePageChange = (page)=>{\n        loadInsurances(page, filters);\n    };\n    const handleEditInsurance = (insurance)=>{\n        setSelectedInsurance(insurance);\n        setInsuranceFormOpen(true);\n    };\n    const handleDeleteInsurance = (insurance)=>{\n        setSelectedInsurance(insurance);\n        setConfirmationDialogOpen(true);\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_6__.insurancesService.exportInsurances({\n                search: filters.search || undefined,\n                insuranceIds: filters.insurances.length > 0 ? filters.insurances : undefined,\n                companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,\n                dateFrom: filters.dateFrom || undefined,\n                dateTo: filters.dateTo || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar convênios:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmDeleteInsurance = async ()=>{\n        try {\n            await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_6__.insurancesService.deleteInsurance(selectedInsurance.id);\n            loadInsurances(currentPage, filters);\n        } catch (error) {\n            console.error(\"Erro ao excluir convênio:\", error);\n        }\n        setConfirmationDialogOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-orange-600 dark:text-orange-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Conv\\xeanios\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir convênios selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                        lineNumber: 235,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || insurances.length === 0,\n                                className: \"text-orange-700 dark:text-orange-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedInsurance(null);\n                                    setInsuranceFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Conv\\xeanio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 221,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-people-icon dark:text-module-people-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                    lineNumber: 262,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Gerencie os conv\\xeanios dispon\\xedveis no sistema. Utilize os filtros abaixo para encontrar conv\\xeanios espec\\xedficos.\",\n                tutorialSteps: insurancesTutorialSteps,\n                tutorialName: \"insurances-overview\",\n                moduleColor: \"people\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_InsurancesFilters__WEBPACK_IMPORTED_MODULE_12__.InsurancesFilters, {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: handleSearch\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                    lineNumber: 268,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ModuleTable, {\n                moduleColor: \"people\",\n                title: \"Lista de Conv\\xeanios\",\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadInsurances(),\n                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                        lineNumber: 285,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                    lineNumber: 280,\n                    columnNumber: 11\n                }, void 0),\n                columns: [\n                    {\n                        header: '',\n                        field: 'select',\n                        width: '50px',\n                        sortable: false\n                    },\n                    {\n                        header: 'Nome',\n                        field: 'name',\n                        width: '40%'\n                    },\n                    {\n                        header: 'Empresa',\n                        field: 'company',\n                        width: '40%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '20%',\n                        sortable: false\n                    }\n                ],\n                data: insurances,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum conv\\xeanio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                    lineNumber: 297,\n                    columnNumber: 20\n                }, void 0),\n                tableId: \"people-insurances-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"name\",\n                defaultSortDirection: \"asc\",\n                onSort: (field, direction)=>{\n                    loadInsurances(currentPage, filters, field, direction);\n                },\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalItems,\n                onPageChange: handlePageChange,\n                showPagination: true,\n                selectedIds: selectedIds,\n                onSelectAll: handleSelectAll,\n                itemsPerPage: itemsPerPage,\n                onItemsPerPageChange: (newItemsPerPage)=>{\n                    setItemsPerPage(newItemsPerPage);\n                    loadInsurances(1, filters, newItemsPerPage);\n                },\n                renderRow: (insurance, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ModuleCheckbox, {\n                                    moduleColor: \"people\",\n                                    checked: selectedIds.includes(insurance.id),\n                                    onChange: (e)=>handleSelectOne(insurance.id, e.target.checked),\n                                    name: \"select-insurance-\".concat(insurance.id)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                    lineNumber: 321,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 320,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                lineNumber: 333,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                children: insurance.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                lineNumber: 336,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: insurance.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                            lineNumber: 348,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-700 dark:text-neutral-300\",\n                                            children: insurance.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                            lineNumber: 349,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                    lineNumber: 347,\n                                    columnNumber: 19\n                                }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-neutral-400 dark:text-neutral-500 text-sm\",\n                                    children: \"Conv\\xeanio geral\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                    lineNumber: 352,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 345,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ShareButton__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            itemType: \"insurance\",\n                                            itemId: insurance.id,\n                                            itemTitle: insurance.name,\n                                            size: \"xs\",\n                                            variant: \"ghost\",\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                            lineNumber: 362,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditInsurance(insurance),\n                                                    className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors\",\n                                                    title: \"Editar\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 25\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteInsurance(insurance),\n                                                    className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                    title: \"Excluir\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_CreditCard_Edit_Filter_Plus_RefreshCw_Trash_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 25\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 23\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                    lineNumber: 361,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                                lineNumber: 360,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, insurance.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                        lineNumber: 318,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmDeleteInsurance,\n                title: \"Excluir Conv\\xeanio\",\n                message: 'Tem certeza que deseja excluir o conv\\xeanio \"'.concat(selectedInsurance === null || selectedInsurance === void 0 ? void 0 : selectedInsurance.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.'),\n                variant: \"danger\",\n                moduleColor: \"people\",\n                confirmText: \"Excluir\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 395,\n                columnNumber: 7\n            }, undefined),\n            insuranceFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_InsurancesFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: insuranceFormOpen,\n                onClose: ()=>setInsuranceFormOpen(false),\n                insurance: selectedInsurance,\n                onSuccess: ()=>{\n                    setInsuranceFormOpen(false);\n                    loadInsurances(currentPage, filters);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsurancesPage\\\\InsurancesPage.js\",\n        lineNumber: 220,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InsurancesPage, \"1IaSzzX/I+HkI6dC455vYWtBcac=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = InsurancesPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InsurancesPage);\nvar _c;\n$RefreshReg$(_c, \"InsurancesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/people/InsurancesPage/InsurancesPage.js\n"));

/***/ })

});