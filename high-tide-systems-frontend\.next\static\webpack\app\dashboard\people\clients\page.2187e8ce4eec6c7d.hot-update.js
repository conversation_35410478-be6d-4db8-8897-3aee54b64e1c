"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/clients/page",{

/***/ "(app-pages-browser)/./src/components/people/CategoryFormModal.js":
/*!****************************************************!*\
  !*** ./src/components/people/CategoryFormModal.js ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog.jsx */ \"(app-pages-browser)/./src/components/ui/dialog.jsx\");\n/* harmony import */ var _components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button.js */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.js\");\n/* harmony import */ var _components_ui_Label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Label */ \"(app-pages-browser)/./src/components/ui/Label.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle2,Globe,Info,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst CategoryFormModal = (param)=>{\n    let { isOpen, onClose, category, onSuccess } = param;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        description: \"\",\n        companyId: \"\",\n        isGlobal: false\n    });\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loadingCompanies, setLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Verificar se o usuário é system_admin\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Carregar empresas se for system_admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryFormModal.useEffect\": ()=>{\n            const loadCompanies = {\n                \"CategoryFormModal.useEffect.loadCompanies\": async ()=>{\n                    if (isSystemAdmin) {\n                        setLoadingCompanies(true);\n                        try {\n                            const companiesData = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n                            setCompanies(companiesData);\n                        } catch (error) {\n                            console.error(\"Erro ao carregar empresas:\", error);\n                        } finally{\n                            setLoadingCompanies(false);\n                        }\n                    }\n                }\n            }[\"CategoryFormModal.useEffect.loadCompanies\"];\n            if (isOpen && isSystemAdmin) {\n                loadCompanies();\n            }\n        }\n    }[\"CategoryFormModal.useEffect\"], [\n        isOpen,\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CategoryFormModal.useEffect\": ()=>{\n            if (isOpen) {\n                setErrors({});\n                if (category) {\n                    const isGlobal = category.companyId === null;\n                    setFormData({\n                        name: category.name || \"\",\n                        description: category.description || \"\",\n                        companyId: isGlobal ? \"\" : category.companyId || \"\",\n                        isGlobal: isGlobal\n                    });\n                } else {\n                    setFormData({\n                        name: \"\",\n                        description: \"\",\n                        companyId: \"\",\n                        isGlobal: false\n                    });\n                }\n            }\n        }\n    }[\"CategoryFormModal.useEffect\"], [\n        isOpen,\n        category\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Nome da categoria é obrigatório\";\n        }\n        if (isSystemAdmin) {\n            if (!formData.isGlobal && !formData.companyId) {\n                newErrors.companyId = \"Selecione uma empresa ou marque como categoria global\";\n            }\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const url = category ? \"/api/documents/category/\".concat(category.id) : '/api/documents/category';\n            const method = category ? 'PUT' : 'POST';\n            // Preparar dados para envio\n            const dataToSend = {\n                name: formData.name.trim(),\n                description: formData.description.trim(),\n                companyId: formData.isGlobal ? null : formData.companyId || null\n            };\n            const response = await fetch(url, {\n                method,\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                },\n                body: JSON.stringify(dataToSend)\n            });\n            if (response.ok) {\n                onSuccess();\n                onClose();\n            } else {\n                const error = await response.json();\n                if (error.errors) {\n                    setErrors(error.errors);\n                } else {\n                    setErrors({\n                        general: error.message || 'Erro ao salvar categoria'\n                    });\n                }\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar categoria:\", error);\n            setErrors({\n                general: 'Erro ao salvar categoria'\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleGlobalToggle = (isGlobal)=>{\n        setFormData((prev)=>({\n                ...prev,\n                isGlobal,\n                companyId: isGlobal ? \"\" : prev.companyId\n            }));\n        setErrors((prev)=>({\n                ...prev,\n                companyId: \"\"\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-3xl max-h-[95vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"pb-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                        className: \"text-xl font-semibold text-slate-800 dark:text-white\",\n                                        children: category ? \"Editar Categoria\" : \"Nova Categoria\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                        children: category ? \"Modifique as informações da categoria\" : \"Crie uma nova categoria para organizar documentos\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-6 pt-4\",\n                    children: [\n                        errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 text-red-600 dark:text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-red-700 dark:text-red-300\",\n                                    children: errors.general\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                            lineNumber: 168,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            htmlFor: \"name\",\n                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Nome da Categoria *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            id: \"name\",\n                                            value: formData.name,\n                                            onChange: (e)=>{\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        name: e.target.value\n                                                    }));\n                                                setErrors((prev)=>({\n                                                        ...prev,\n                                                        name: \"\"\n                                                    }));\n                                            },\n                                            placeholder: \"Ex: Documentos Pessoais, Comprovantes, Contratos...\",\n                                            className: errors.name ? \"border-red-500 focus:ring-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 187,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                errors.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            htmlFor: \"description\",\n                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Descri\\xe7\\xe3o\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleTextarea, {\n                                            id: \"description\",\n                                            moduleColor: \"people\",\n                                            value: formData.description,\n                                            onChange: (e)=>setFormData((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"Descreva o prop\\xf3sito desta categoria e que tipos de documentos ela deve conter...\",\n                                            rows: 3,\n                                            className: \"resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: \"Uma boa descri\\xe7\\xe3o ajuda outros usu\\xe1rios a entender quando usar esta categoria\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined),\n                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Configura\\xe7\\xf5es de Empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"System Admin\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 229,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleGlobalToggle(true),\n                                                className: \"flex items-center gap-3 p-3 rounded-lg border-2 transition-all \".concat(formData.isGlobal ? 'border-green-500 bg-green-50 dark:bg-green-900/20' : 'border-gray-200 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-700'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full border-2 flex items-center justify-center \".concat(formData.isGlobal ? 'border-green-500 bg-green-500' : 'border-gray-300 dark:border-gray-600'),\n                                                        children: formData.isGlobal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-2 h-2 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: \"Categoria Global\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400 mt-1\",\n                                                                children: \"Dispon\\xedvel para todas as empresas do sistema\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                lineNumber: 241,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 240,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start gap-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>handleGlobalToggle(false),\n                                                className: \"flex items-center gap-3 p-3 rounded-lg border-2 transition-all \".concat(!formData.isGlobal ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-700'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 rounded-full border-2 flex items-center justify-center \".concat(!formData.isGlobal ? 'border-blue-500 bg-blue-500' : 'border-gray-300 dark:border-gray-600'),\n                                                        children: !formData.isGlobal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-2 h-2 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 46\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-left\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-blue-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                                        children: \"Categoria Espec\\xedfica\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-600 dark:text-gray-400 mt-1\",\n                                                                children: \"Vinculada a uma empresa espec\\xedfica\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined),\n                                !formData.isGlobal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            htmlFor: \"companyId\",\n                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-3 w-3 text-blue-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Selecionar Empresa *\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 302,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                            id: \"companyId\",\n                                            moduleColor: \"people\",\n                                            value: formData.companyId,\n                                            onChange: (e)=>{\n                                                setFormData((prev)=>({\n                                                        ...prev,\n                                                        companyId: e.target.value\n                                                    }));\n                                                setErrors((prev)=>({\n                                                        ...prev,\n                                                        companyId: \"\"\n                                                    }));\n                                            },\n                                            disabled: loadingCompanies,\n                                            className: errors.companyId ? \"border-red-500 focus:ring-red-500\" : \"\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"\",\n                                                    children: loadingCompanies ? \"Carregando empresas...\" : \"Selecione uma empresa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: company.id,\n                                                        children: company.name\n                                                    }, company.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        errors.companyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle2_Globe_Info_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                errors.companyId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 327,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-blue-600 dark:text-blue-400\",\n                                            children: \"A categoria ser\\xe1 vis\\xedvel apenas para usu\\xe1rios desta empresa\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 301,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    disabled: isLoading,\n                                    className: \"min-w-[100px]\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    className: \"min-w-[120px] bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white\",\n                                    children: isLoading ? \"Salvando...\" : category ? \"Atualizar Categoria\" : \"Criar Categoria\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                                    lineNumber: 353,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\CategoryFormModal.js\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CategoryFormModal, \"TZQlsdY98ztGdeXVbjwBhh+Pd8A=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth\n    ];\n});\n_c = CategoryFormModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryFormModal);\nvar _c;\n$RefreshReg$(_c, \"CategoryFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/people/CategoryFormModal.js\n"));

/***/ })

});