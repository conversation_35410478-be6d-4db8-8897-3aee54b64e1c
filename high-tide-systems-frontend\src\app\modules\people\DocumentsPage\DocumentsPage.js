"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleTable } from "@/components/ui";
import ExportMenu from "@/components/ui/ExportMenu";
import {
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Eye,
  Plus,
  FileText,
  Share2,
  Download,
  ExternalLink,
  Tag,
  LayoutGrid,
  List,
  Settings,
  X,
  Loader2
} from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import DocumentFormModal from "@/components/people/DocumentFormModal";
import CategoryFormModal from "@/components/people/CategoryFormModal";
import DocumentSharingModal from "@/components/people/DocumentSharingModal";
import DocumentsFilters from "@/components/people/DocumentsFilters";
import DocumentGridView from "@/components/people/DocumentGridView";
import CategoryManager from "@/components/people/CategoryManager";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import DocumentSharingManagementModal from "@/components/people/DocumentSharingManagementModal";

// Componente para visualizar imagens
const ImageViewer = ({ documentUrl, filename }) => {
  const [imageUrl, setImageUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchImage = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const token = localStorage.getItem('token');
        const response = await fetch(documentUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          setImageUrl(url);
        } else {
          setError('Erro ao carregar imagem');
        }
      } catch (err) {
        setError('Erro ao carregar imagem');
        console.error('Erro ao carregar imagem:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchImage();

    // Cleanup
    return () => {
      if (imageUrl) {
        window.URL.revokeObjectURL(imageUrl);
      }
    };
  }, [documentUrl]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Loader2 size={32} className="animate-spin mb-2" />
          <p>Carregando imagem...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <X size={48} className="mb-2" />
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg">
      <img 
        src={imageUrl} 
        alt={filename}
        className="max-w-full max-h-[600px] object-contain rounded-lg shadow-lg"
      />
    </div>
  );
};

// Componente para visualizar PDFs
const PdfViewer = ({ documentUrl, filename }) => {
  const [pdfUrl, setPdfUrl] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPdf = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const token = localStorage.getItem('token');
        const response = await fetch(documentUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          setPdfUrl(url);
        } else {
          setError('Erro ao carregar PDF');
        }
      } catch (err) {
        setError('Erro ao carregar PDF');
        console.error('Erro ao carregar PDF:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPdf();

    // Cleanup
    return () => {
      if (pdfUrl) {
        window.URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [documentUrl]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Loader2 size={32} className="animate-spin mb-2" />
          <p>Carregando PDF...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <X size={48} className="mb-2" />
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
      <iframe
        src={pdfUrl}
        className="w-full h-full border-0"
        title={filename}
      />
    </div>
  );
};

// Componente para visualizar arquivos de texto
const TextFileViewer = ({ documentUrl, filename }) => {
  const [content, setContent] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        const token = localStorage.getItem('token');
        const response = await fetch(documentUrl, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const text = await response.text();
          setContent(text);
        } else {
          setError('Erro ao carregar arquivo de texto');
        }
      } catch (err) {
        setError('Erro ao carregar arquivo de texto');
        console.error('Erro ao carregar arquivo de texto:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchContent();
  }, [documentUrl]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <Loader2 size={32} className="animate-spin mb-2" />
          <p>Carregando arquivo...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
          <X size={48} className="mb-2" />
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-[600px] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-auto p-4">
      <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono">
        {content}
      </pre>
    </div>
  );
};

// Tutorial steps para a página de documentos
const documentsTutorialSteps = [
  {
    title: "Documentos",
    content: "Esta tela permite gerenciar documentos e categorias no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Documento",
    content: "Clique aqui para adicionar um novo documento.",
    selector: "button:has(span:contains('Novo Documento'))",
    position: "left"
  },
  {
    title: "Nova Categoria",
    content: "Clique aqui para criar uma nova categoria de documentos.",
    selector: "button:has(span:contains('Nova Categoria'))",
    position: "left"
  },
  {
    title: "Filtrar Documentos",
    content: "Use esta barra de pesquisa para encontrar documentos específicos pelo nome.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Categoria",
    content: "Filtre os documentos por categoria.",
    selector: "select:first-of-type",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de documentos em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Documentos",
    content: "Visualize, edite, compartilhe ou exclua documentos usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const DocumentsPage = () => {
  const { user: currentUser } = useAuth();
  const [documents, setDocuments] = useState([]);
  const [categories, setCategories] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalDocuments, setTotalDocuments] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentView, setCurrentView] = useState('table'); // 'table', 'grid', 'categories'
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [documentFormOpen, setDocumentFormOpen] = useState(false);
  const [categoryFormOpen, setCategoryFormOpen] = useState(false);
  const [sharingModalOpen, setSharingModalOpen] = useState(false);
  const [selectedDocumentForSharing, setSelectedDocumentForSharing] = useState(null);
  const [isExporting, setIsExporting] = useState(false);
  const [viewerModalOpen, setViewerModalOpen] = useState(false);
  const [documentToView, setDocumentToView] = useState(null);
  const [sharingManagementModalOpen, setSharingManagementModalOpen] = useState(false);
  const [documentForSharingManagement, setDocumentForSharingManagement] = useState(null);

  // Constants
  const ITEMS_PER_PAGE = 10;

  // Função para carregar categorias
  const loadCategories = useCallback(async () => {
    try {
      console.log('[loadCategories] Iniciando carregamento de categorias');
      const response = await fetch('/api/documents/category', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      console.log('[loadCategories] Response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('[loadCategories] Dados recebidos:', data);
        setCategories(Array.isArray(data) ? data : []);
      } else {
        // Tentar obter detalhes do erro
        let errorMessage = `${response.status} "${response.statusText}"`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.log('[loadCategories] Não foi possível fazer parse do erro');
        }
        
        console.error('Erro ao carregar categorias:', errorMessage);
        
        // Definir categorias como array vazio para não quebrar a interface
        setCategories([]);
        
        // Não exibir toast para evitar spam de erros
        // if (typeof toast_error === 'function') {
        //   toast_error({
        //     title: 'Erro',
        //     message: 'Não foi possível carregar as categorias. Tente novamente mais tarde.'
        //   });
        // }
      }
    } catch (error) {
      console.error("Erro ao carregar categorias:", error);
      
      // Definir categorias como array vazio para não quebrar a interface
      setCategories([]);
      
      // Não exibir toast para evitar spam de erros
      // if (typeof toast_error === 'function') {
      //   toast_error({
      //     title: 'Erro',
      //     message: 'Não foi possível carregar as categorias. Tente novamente mais tarde.'
      //   });
      // }
    }
  }, []);

  // Função para carregar dados auxiliares (empresas e usuários)
  const loadAuxiliaryData = useCallback(async () => {
    try {
      // Carregar empresas
      const companiesResponse = await fetch('/api/companies', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        setCompanies(companiesData.companies || companiesData || []);
      }

      // Carregar usuários
      const usersResponse = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (usersResponse.ok) {
        const usersData = await usersResponse.json();
        setUsers(usersData.users || usersData || []);
      }
    } catch (error) {
      console.error("Erro ao carregar dados auxiliares:", error);
    }
  }, []);



  // Função para carregar documentos
  const loadDocuments = async (
    page = currentPage,
    currentFilters = filters,
    sortField = 'filename',
    sortDirection = 'asc'
  ) => {
    setIsLoading(true);
    try {
      const pageNumber = parseInt(page, 10);
      setCurrentPage(pageNumber);

      const params = new URLSearchParams({
        page: pageNumber.toString(),
        limit: ITEMS_PER_PAGE.toString(),
        ...(sortField && { sortField }),
        ...(sortDirection && { sortDirection })
      });

      // Adicionar filtros aos parâmetros
      Object.entries(currentFilters).forEach(([key, value]) => {
        if (value && value !== '') {
          params.append(key, value);
        }
      });

      const response = await fetch(`/api/documents?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setDocuments(data.documents || data || []);
        setTotalDocuments(data.total || data.length || 0);
        setTotalPages(data.totalPages || Math.ceil((data.total || data.length || 0) / ITEMS_PER_PAGE));
      }
    } catch (error) {
      console.error("Erro ao carregar documentos:", error);
    } finally {
      setIsLoading(false);
    }
  };



  // Carregar dados iniciais
  useEffect(() => {
    loadCategories();
    loadAuxiliaryData();
    loadDocuments();
  }, [loadCategories, loadAuxiliaryData]);

  // Handlers
  const handlePageChange = (page) => {
    loadDocuments(page, filters);
  };

  const handleEditDocument = (document) => {
    setSelectedDocument(document);
    setDocumentFormOpen(true);
  };

  const handleDeleteDocument = (document) => {
    setSelectedDocument(document);
    setActionToConfirm({
      action: 'delete',
      message: `Tem certeza que deseja excluir o documento "${document.filename}"?`
    });
    setConfirmationDialogOpen(true);
  };

  const handleShareDocument = (document) => {
    setSelectedDocumentForSharing(document);
    setSharingModalOpen(true);
  };

  const handleManageSharing = (document) => {
    setDocumentForSharingManagement(document);
    setSharingManagementModalOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Implementar exportação
      console.log(`Exportando em formato ${format}`);
    } catch (error) {
      console.error("Erro ao exportar:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm?.action === 'delete' && selectedDocument) {
      try {
        const response = await fetch(`/api/documents/${selectedDocument.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          loadDocuments(currentPage, filters);
        }
      } catch (error) {
        console.error("Erro ao excluir documento:", error);
      }
    }
    setConfirmationDialogOpen(false);
    setSelectedDocument(null);
    setActionToConfirm(null);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
    } catch {
      return dateString;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleCloseDocumentModal = () => {
    setDocumentFormOpen(false);
    setSelectedDocument(null);
    loadDocuments(1, filters);
  };

  const handleCloseCategoryModal = () => {
    setCategoryFormOpen(false);
    setSelectedCategory(null);
    loadCategories();
  };

  const handleCloseSharingModal = () => {
    setSharingModalOpen(false);
    setSelectedDocumentForSharing(null);
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    // Aplicar filtros e recarregar documentos
    loadDocuments(1, newFilters);
  };

  const handleCategoryUpdate = () => {
    loadCategories();
  };

  const handleCategoryDelete = async (categoryId) => {
    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        loadCategories();
        loadDocuments(currentPage, filters);
      }
    } catch (error) {
      console.error("Erro ao excluir categoria:", error);
    }
  };

  const handleViewDocument = (document) => {
    setDocumentToView(document);
    setViewerModalOpen(true);
  };

  const handleDownloadDocument = async (document) => {
    try {
      const response = await fetch(`/api/documents/${document.id}/download`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = document.filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error("Erro ao baixar documento:", error);
    }
  };





  const handleOpenInNewTab = async (doc) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/documents/${doc.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        
        // Abrir em nova aba
        const newWindow = window.open(url, '_blank');
        
        // Limpar a URL após um tempo para evitar vazamento de memória
        setTimeout(() => {
          window.URL.revokeObjectURL(url);
        }, 1000);
      } else {
        alert('Erro ao abrir documento em nova aba');
      }
    } catch (error) {
      console.error('Erro ao abrir documento em nova aba:', error);
      alert('Erro ao abrir documento em nova aba');
    }
  };

  const getDocumentViewerContent = (doc) => {
    if (!doc) return null;

    const documentUrl = `/api/documents/${doc.id}`;
    
    // Determinar o tipo de visualização baseado no mimeType
    const mimeType = doc.mimeType || '';
    
    if (mimeType.startsWith('image/')) {
      // Para imagens, usar componente específico
      return <ImageViewer documentUrl={documentUrl} filename={doc.filename} />;
    } else if (mimeType === 'application/pdf') {
      // Para PDFs, usar componente específico
      return <PdfViewer documentUrl={documentUrl} filename={doc.filename} />;
    } else if (mimeType.startsWith('text/')) {
      // Para arquivos de texto, usar componente de texto
      return <TextFileViewer documentUrl={documentUrl} filename={doc.filename} />;
    } else {
      // Para outros tipos, mostrar mensagem de visualização não suportada
      return (
        <div className="flex flex-col items-center justify-center min-h-[400px] bg-gray-100 dark:bg-gray-800 rounded-lg">
          <div className="text-center">
            <FileText size={64} className="mx-auto mb-4 text-gray-400 dark:text-gray-500" />
            <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
              Visualização não suportada
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              Este tipo de arquivo não pode ser visualizado diretamente no navegador.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => handleDownloadDocument(doc)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Download size={16} />
                Baixar
              </button>
              <button
                onClick={() => handleOpenInNewTab(doc)}
                className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                <ExternalLink size={16} />
                Abrir em nova aba
              </button>
            </div>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho da página */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <FileText size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Documentos
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Gerencie documentos e categorias do sistema
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || documents.length === 0}
            className="text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300"
          />

          <button
            onClick={() => setCategoryFormOpen(true)}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white rounded-lg shadow transition-all"
          >
            <Plus size={16} />
            Nova Categoria
          </button>

          <button
            onClick={() => {
              setSelectedDocument(null);
              setDocumentFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 text-white rounded-lg shadow transition-all"
          >
            <Plus size={16} />
            Novo Documento
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie documentos e categorias do sistema. Utilize os filtros abaixo para encontrar documentos específicos."
        moduleColor="people"
        filters={
          <DocumentsFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onSearch={(searchTerm) => {
              const newFilters = { ...filters, search: searchTerm };
              setFilters(newFilters);
              loadDocuments(1, newFilters);
            }}
            categories={categories}
            companies={companies}
            users={users}
          />
        }
      />

      {/* Opções de Visualização */}
      <div className="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Visualização:</span>
          <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setCurrentView('table')}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                currentView === 'table'
                  ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <List size={16} />
              Lista
            </button>
            <button
              onClick={() => setCurrentView('grid')}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                currentView === 'grid'
                  ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <LayoutGrid size={16} />
              Grade
            </button>
            <button
              onClick={() => setCurrentView('categories')}
              className={`flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors ${
                currentView === 'categories'
                  ? 'bg-white dark:bg-gray-600 text-orange-600 dark:text-orange-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <Settings size={16} />
              Categorias
            </button>
          </div>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          {totalDocuments} documento(s) encontrado(s)
        </div>
      </div>

      {/* Conteúdo baseado na visualização */}
      {currentView === 'categories' ? (
        <CategoryManager
          categories={categories}
          onCategoryUpdate={handleCategoryUpdate}
          onCategoryDelete={handleCategoryDelete}
          isLoading={isLoading}
          companies={companies}
          users={users}
        />
      ) : currentView === 'grid' ? (
        <DocumentGridView
          documents={documents}
          isLoading={isLoading}
          onView={handleViewDocument}
          onEdit={handleEditDocument}
          onShare={handleShareDocument}
          onDelete={handleDeleteDocument}
          onDownload={handleDownloadDocument}
          onManageSharing={handleManageSharing}
        />
      ) : (
        <ModuleTable
          moduleColor="people"
          title="Lista de Documentos"
          headerContent={
            <button
              onClick={() => loadDocuments()}
              className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors"
              title="Atualizar lista"
            >
              <RefreshCw size={18} />
            </button>
          }
          columns={[
            { header: 'Nome', field: 'filename', width: '25%' },
            { header: 'Arquivo', field: 'file', width: '20%' },
            { header: 'Categoria', field: 'category', width: '15%' },
            { header: 'Compartilhado com', field: 'shared', width: '20%' },
            { header: 'Cadastro', field: 'createdAt', width: '10%' },
            { header: 'Ações', field: 'actions', className: 'text-right', width: '10%', sortable: false }
          ]}
          data={documents}
          isLoading={isLoading}
          emptyMessage="Nenhum documento encontrado"
          emptyIcon={<FileText size={24} />}
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalDocuments}
          onPageChange={handlePageChange}
          showPagination={totalPages > 1}
          tableId="people-documents-table"
          enableColumnToggle={true}
          defaultSortField="filename"
          defaultSortDirection="asc"
          onSort={(field, direction) => {
            loadDocuments(currentPage, filters, field, direction);
          }}
        renderRow={(document, _, moduleColors, visibleColumns) => (
          <tr key={document.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('filename') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden">
                    <FileText size={16} />
                  </div>
                  <div className="ml-3 min-w-0">
                    <p className="text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      {document.filename}
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                      {formatFileSize(document.size)}
                    </p>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('file') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="truncate">{document.filename}</span>
                </div>
              </td>
            )}

            {visibleColumns.includes('category') && (
              <td className="px-4 py-4">
                <div className="flex flex-col gap-1">
                  <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-400">
                    {document.categoryDocument?.name || "Sem categoria"}
                  </span>
                  {document.categoryDocument?.company && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {document.categoryDocument.company.name}
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('shared') && (
              <td className="px-4 py-4">
                <button
                  onClick={() => handleManageSharing(document)}
                  className="flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 px-2 py-1 rounded transition-colors cursor-pointer"
                  title="Gerenciar compartilhamentos"
                >
                  <Share2 className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="text-neutral-600 dark:text-neutral-300 text-sm">
                    {document.DocumentPermission?.length || 0} {(document.DocumentPermission?.length || 0) === 1 ? "compartilhamento" : "compartilhamentos"}
                  </span>
                </button>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                {formatDate(document.createdAt)}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right text-sm font-medium">
                <div className="flex justify-end gap-1">
                  <button
                    onClick={() => handleViewDocument(document)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Visualizar"
                  >
                    <Eye size={16} />
                  </button>

                  <button
                    onClick={() => handleShareDocument(document)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400 transition-colors"
                    title="Compartilhar"
                  >
                    <Share2 size={16} />
                  </button>

                  <button
                    onClick={() => handleEditDocument(document)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>

                  <button
                    onClick={() => handleDeleteDocument(document)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
        />
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        moduleColor="people"
      />

      {/* Document Form Modal */}
      {documentFormOpen && (
        <DocumentFormModal
          isOpen={documentFormOpen}
          onClose={handleCloseDocumentModal}
          document={selectedDocument}
          categories={categories}
          onSuccess={() => {
            handleCloseDocumentModal();
            loadDocuments(1, filters);
          }}
        />
      )}

      {/* Category Form Modal */}
      {categoryFormOpen && (
        <CategoryFormModal
          isOpen={categoryFormOpen}
          onClose={handleCloseCategoryModal}
          category={selectedCategory}
          onSuccess={() => {
            handleCloseCategoryModal();
          }}
        />
      )}

      {/* Document Sharing Modal */}
      {sharingModalOpen && selectedDocumentForSharing && (
        <DocumentSharingModal
          isOpen={sharingModalOpen}
          onClose={handleCloseSharingModal}
          document={selectedDocumentForSharing}
          onSuccess={() => {
            handleCloseSharingModal();
            loadDocuments(1, filters);
          }}
        />
      )}

      {/* Document Viewer Modal */}
      {viewerModalOpen && documentToView && (
        <Dialog open={viewerModalOpen} onOpenChange={setViewerModalOpen}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden border-2 border-orange-400 dark:border-orange-500">
            <DialogHeader className="flex-shrink-0">
              <div className="flex items-center justify-between">
                <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2">
                  <FileText size={20} className="text-orange-500" />
                  {documentToView.filename}
                </DialogTitle>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleDownloadDocument(documentToView)}
                    className="p-2 text-gray-500 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    title="Baixar documento"
                  >
                    <Download size={16} />
                  </button>
                  <button
                    onClick={() => handleOpenInNewTab(documentToView)}
                    className="p-2 text-gray-500 hover:text-green-500 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-lg transition-colors"
                    title="Abrir em nova aba"
                  >
                    <ExternalLink size={16} />
                  </button>
                  <button
                    onClick={() => setViewerModalOpen(false)}
                    className="p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title="Fechar"
                  >
                    <X size={16} />
                  </button>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mt-2">
                <span>Tamanho: {formatFileSize(documentToView.size)}</span>
                <span>Tipo: {documentToView.mimeType}</span>
                <span>Categoria: {documentToView.categoryDocument?.name || "Sem categoria"}</span>
              </div>
            </DialogHeader>
            
            <div className="flex-1 overflow-hidden mt-4">
              {getDocumentViewerContent(documentToView)}
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Document Sharing Management Modal */}
      {sharingManagementModalOpen && documentForSharingManagement && (
        <DocumentSharingManagementModal
          isOpen={sharingManagementModalOpen}
          onClose={() => {
            setSharingManagementModalOpen(false);
            setDocumentForSharingManagement(null);
          }}
          document={documentForSharingManagement}
          onSuccess={() => {
            setSharingManagementModalOpen(false);
            setDocumentForSharingManagement(null);
            loadDocuments(1, filters);
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default DocumentsPage; 