"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/admin/bug-reports/page.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleTable */ \"(app-pages-browser)/./src/components/ui/ModuleTable.js\");\n/* harmony import */ var _components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/bug-report/BugReportsFilters */ \"(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/bugReportService */ \"(app-pages-browser)/./src/services/bugReportService.js\");\n/* harmony import */ var _components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/BugDetailsModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugDetailsModal.js\");\n/* harmony import */ var _components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/BugEditModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugEditModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BugReportsPage = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast_error, toast_success } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [bugs, setBugs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedBug, setSelectedBug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        status: [],\n        priority: [],\n        category: [],\n        companies: []\n    });\n    // Carregar dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsPage.useEffect\": ()=>{\n            if (!user) return;\n            if (user.role !== 'SYSTEM_ADMIN') {\n                router.push('/dashboard');\n                return;\n            }\n            loadBugReports();\n        }\n    }[\"BugReportsPage.useEffect\"], [\n        user,\n        router,\n        filters,\n        pagination.page\n    ]);\n    const loadBugReports = async ()=>{\n        try {\n            setLoading(true);\n            const params = {\n                page: pagination.page,\n                limit: pagination.limit,\n                ...filters\n            };\n            // Remove filtros vazios\n            Object.keys(params).forEach((key)=>{\n                if (Array.isArray(params[key]) && params[key].length === 0) {\n                    delete params[key];\n                } else if (params[key] === '') {\n                    delete params[key];\n                }\n            });\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.listAll(params);\n            if (response.data) {\n                setBugs(response.data.bugReports);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            }\n        } catch (error) {\n            console.error('Erro ao carregar bug reports:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao carregar relatórios de bugs'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN') {\n        return null;\n    }\n    const handleViewBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowDetailsModal(true);\n    };\n    const handleEditBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowEditModal(true);\n    };\n    const handleSaveBug = async (updatedBug)=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.updateStatus(updatedBug.id, {\n                status: updatedBug.status,\n                priority: updatedBug.priority,\n                adminNotes: updatedBug.adminNotes\n            });\n            if (response.data) {\n                setBugs((prev)=>prev.map((bug)=>bug.id === updatedBug.id ? {\n                            ...bug,\n                            ...updatedBug\n                        } : bug));\n                setShowEditModal(false);\n                setSelectedBug(null);\n                toast_success({\n                    title: 'Sucesso',\n                    message: 'Bug atualizado com sucesso'\n                });\n            }\n        } catch (error) {\n            console.error('Erro ao atualizar bug:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao atualizar o bug'\n            });\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 147,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 149,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 151,\n                    columnNumber: 16\n                }, undefined);\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 153,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 155,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        const colors = {\n            'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',\n            'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',\n            'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',\n            'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n        };\n        return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            'CRITICAL': 'Crítica',\n            'HIGH': 'Alta',\n            'MEDIUM': 'Média',\n            'LOW': 'Baixa'\n        };\n        return labels[priority] || 'Desconhecida';\n    };\n    const columns = [\n        {\n            header: 'Status',\n            field: 'status',\n            width: '120px'\n        },\n        {\n            header: 'Bug',\n            field: 'title',\n            width: 'auto'\n        },\n        {\n            header: 'Prioridade',\n            field: 'priority',\n            width: '120px'\n        },\n        {\n            header: 'Reportado por',\n            field: 'reportedBy',\n            width: '200px'\n        },\n        {\n            header: 'Data',\n            field: 'createdAt',\n            width: '120px'\n        },\n        {\n            header: 'Ações',\n            field: 'actions',\n            width: '120px',\n            sortable: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 28,\n                            className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Relat\\xf3rios de Bugs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie todos os bugs reportados pelos usu\\xe1rios do sistema\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Filtros e Busca\",\n                description: \"Utilize os filtros abaixo para encontrar bugs espec\\xedficos.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 208,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: loadBugReports\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 211,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                moduleColor: \"admin\",\n                title: \"Lista de Bugs Reportados\",\n                data: bugs,\n                columns: columns,\n                isLoading: loading,\n                emptyMessage: \"Nenhum bug reportado ainda.\",\n                emptyIcon: \"\\uD83D\\uDC1B\",\n                enableColumnToggle: true,\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadBugReports(),\n                    disabled: loading,\n                    className: \"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        size: 18,\n                        className: \"text-gray-600 dark:text-gray-300 \".concat(loading ? 'animate-spin' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 235,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, void 0),\n                renderRow: (bug, _index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getStatusIcon(bug.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: bug.status === 'OPEN' ? 'Aberto' : bug.status === 'IN_PROGRESS' ? 'Em Progresso' : bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 245,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help\",\n                                            title: bug.description,\n                                            children: bug.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('priority') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(bug.priority)),\n                                    children: getPriorityLabel(bug.priority)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('reportedBy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.reportedBy.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: bug.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                children: new Date(bug.createdAt).toLocaleDateString('pt-BR')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 308,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, bug.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 242,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDetailsModal,\n                onClose: ()=>{\n                    setShowDetailsModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug,\n                onSave: handleSaveBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n        lineNumber: 189,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsPage, \"q81YIsVpT1m/rqoFz1uU63xXAZc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BugReportsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsPage);\nvar _c;\n$RefreshReg$(_c, \"BugReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js\n"));

/***/ })

});