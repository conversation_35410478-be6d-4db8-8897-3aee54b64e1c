"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/settings/SettingsPage.js":
/*!********************************************************!*\
  !*** ./src/app/modules/admin/settings/SettingsPage.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Database,Download,Globe,Loader2,Mail,RefreshCw,Server,Settings,Shield,SlidersHorizontal,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_settings_BranchesTab__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/settings/BranchesTab */ \"(app-pages-browser)/./src/components/settings/BranchesTab.js\");\n/* harmony import */ var _components_settings_CompanyManagementTab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/settings/CompanyManagementTab */ \"(app-pages-browser)/./src/components/settings/CompanyManagementTab.js\");\n/* harmony import */ var _components_settings_EmailSettingsTab__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/settings/EmailSettingsTab */ \"(app-pages-browser)/./src/components/settings/EmailSettingsTab.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_PreferencesPanel__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/PreferencesPanel */ \"(app-pages-browser)/./src/components/admin/PreferencesPanel.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Componente SecurityTab\nconst SecurityTab = (param)=>{\n    let { isSystemAdmin, isCompanyAdmin } = param;\n    _s();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [securitySettings, setSecuritySettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        requireUppercase: true,\n        requireNumber: true,\n        requireSpecialChar: false,\n        minPasswordLength: 8,\n        passwordExpirationDays: 90,\n        limitLoginAttempts: true,\n        maxLoginAttempts: 5,\n        enforceIpTracking: true,\n        enforceSessionTimeout: true,\n        sessionTimeoutMinutes: 30\n    });\n    const [originalSettings, setOriginalSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [securityLogs, setSecurityLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingSettings, setIsLoadingSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingLogs, setIsLoadingLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalLogs, setTotalLogs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Carregar configurações de segurança\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SecurityTab.useEffect\": ()=>{\n            loadSecuritySettings();\n            loadSecurityLogs();\n        }\n    }[\"SecurityTab.useEffect\"], []);\n    const loadSecuritySettings = async ()=>{\n        try {\n            var _currentUser;\n            // Para system_admin, verificar se há empresa selecionada\n            const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');\n            if (((_currentUser = currentUser) === null || _currentUser === void 0 ? void 0 : _currentUser.role) === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {\n                // Se system_admin não tem empresa selecionada, não carregar configurações\n                setSecuritySettings({\n                    requireUppercase: true,\n                    requireNumber: true,\n                    requireSpecialChar: false,\n                    minPasswordLength: 8,\n                    passwordExpirationDays: 90,\n                    limitLoginAttempts: true,\n                    maxLoginAttempts: 5,\n                    enforceIpTracking: true,\n                    enforceSessionTimeout: true,\n                    sessionTimeoutMinutes: 30\n                });\n                setOriginalSettings({});\n                return;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get('/settings/security');\n            if (response.data.success) {\n                setSecuritySettings(response.data.data);\n                setOriginalSettings(response.data.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar configurações de segurança:', error);\n            // Em caso de erro, usar configurações padrão\n            setSecuritySettings({\n                requireUppercase: true,\n                requireNumber: true,\n                requireSpecialChar: false,\n                minPasswordLength: 8,\n                passwordExpirationDays: 90,\n                limitLoginAttempts: true,\n                maxLoginAttempts: 5,\n                enforceIpTracking: true,\n                enforceSessionTimeout: true,\n                sessionTimeoutMinutes: 30\n            });\n            setOriginalSettings({});\n        } finally{\n            setIsLoadingSettings(false);\n        }\n    };\n    const loadSecurityLogs = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.get(\"/settings/security/logs?page=\".concat(page, \"&limit=\").concat(perPage));\n            if (response.data.success) {\n                setSecurityLogs(response.data.data.logs);\n                setTotalLogs(response.data.data.pagination.total);\n                setTotalPages(response.data.data.pagination.pages);\n                setCurrentPage(response.data.data.pagination.page);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar logs de segurança:', error);\n            setSecurityLogs([]);\n        } finally{\n            setIsLoadingLogs(false);\n        }\n    };\n    const saveSecuritySettings = async ()=>{\n        try {\n            var _currentUser;\n            setIsLoadingSettings(true);\n            // Para system_admin, verificar se há empresa selecionada\n            const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');\n            if (((_currentUser = currentUser) === null || _currentUser === void 0 ? void 0 : _currentUser.role) === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {\n                toast_error('Selecione uma empresa para salvar as configurações de segurança');\n                return;\n            }\n            // Identificar apenas os campos que mudaram\n            const changedSettings = {};\n            Object.keys(securitySettings).forEach((key)=>{\n                if (securitySettings[key] !== originalSettings[key]) {\n                    changedSettings[key] = securitySettings[key];\n                }\n            });\n            // Se nada mudou, não fazer requisição\n            if (Object.keys(changedSettings).length === 0) {\n                toast_success('Nenhuma alteração detectada');\n                return;\n            }\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.api.put('/settings/security', changedSettings);\n            if (response.data.success) {\n                toast_success('Configurações de segurança salvas com sucesso!');\n                setOriginalSettings(securitySettings);\n                loadSecurityLogs(1, itemsPerPage);\n            }\n        } catch (error) {\n            console.error('Erro ao salvar configurações:', error);\n            toast_error('Erro ao salvar configurações de segurança');\n        } finally{\n            setIsLoadingSettings(false);\n        }\n    };\n    const handleSettingChange = (key, value)=>{\n        setSecuritySettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined),\n                    \"Configura\\xe7\\xf5es de Seguran\\xe7a\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        moduleColor: \"admin\",\n                                        children: \"Pol\\xedtica de Senhas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireUppercase\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireUppercase,\n                                                        onChange: (e)=>handleSettingChange('requireUppercase', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireUppercase\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos uma letra mai\\xfascula\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireNumber\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireNumber,\n                                                        onChange: (e)=>handleSettingChange('requireNumber', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireNumber\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos um n\\xfamero\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 197,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"requireSpecialChar\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.requireSpecialChar,\n                                                        onChange: (e)=>handleSettingChange('requireSpecialChar', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"requireSpecialChar\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Exigir pelo menos um caractere especial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"minPasswordLength\",\n                                        moduleColor: \"admin\",\n                                        children: \"Tamanho m\\xednimo da senha\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"minPasswordLength\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"6\",\n                                        max: \"32\",\n                                        value: securitySettings.minPasswordLength,\n                                        onChange: (e)=>handleSettingChange('minPasswordLength', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"passwordExpiration\",\n                                        moduleColor: \"admin\",\n                                        children: \"Expira\\xe7\\xe3o de senha (dias)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"passwordExpiration\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"0\",\n                                        max: \"365\",\n                                        value: securitySettings.passwordExpirationDays,\n                                        onChange: (e)=>handleSettingChange('passwordExpirationDays', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-1 text-xs text-neutral-500 dark:text-neutral-400\",\n                                        children: \"0 = Sem expira\\xe7\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        moduleColor: \"admin\",\n                                        children: \"Seguran\\xe7a de Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"limitLoginAttempts\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.limitLoginAttempts,\n                                                        onChange: (e)=>handleSettingChange('limitLoginAttempts', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"limitLoginAttempts\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Limitar tentativas de login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"enforceIpTracking\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.enforceIpTracking,\n                                                        onChange: (e)=>handleSettingChange('enforceIpTracking', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"enforceIpTracking\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Rastrear IPs de login\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 281,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"enforceSessionTimeout\",\n                                                        className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                        checked: securitySettings.enforceSessionTimeout,\n                                                        onChange: (e)=>handleSettingChange('enforceSessionTimeout', e.target.checked),\n                                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"enforceSessionTimeout\",\n                                                        className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                        children: \"Encerrar sess\\xf5es inativas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 267,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"maxLoginAttempts\",\n                                        moduleColor: \"admin\",\n                                        children: \"M\\xe1ximo de tentativas de login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"maxLoginAttempts\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"3\",\n                                        max: \"10\",\n                                        value: securitySettings.maxLoginAttempts,\n                                        onChange: (e)=>handleSettingChange('maxLoginAttempts', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                        htmlFor: \"sessionTimeout\",\n                                        moduleColor: \"admin\",\n                                        children: \"Tempo de inatividade at\\xe9 logout (minutos)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                        id: \"sessionTimeout\",\n                                        moduleColor: \"admin\",\n                                        type: \"number\",\n                                        min: \"5\",\n                                        max: \"240\",\n                                        value: securitySettings.sessionTimeoutMinutes,\n                                        onChange: (e)=>handleSettingChange('sessionTimeoutMinutes', parseInt(e.target.value)),\n                                        disabled: !isSystemAdmin && !isCompanyAdmin\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: saveSecuritySettings,\n                    className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors\",\n                    disabled: !isSystemAdmin && !isCompanyAdmin || isLoadingSettings,\n                    children: [\n                        isLoadingSettings ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Salvar Configura\\xe7\\xf5es\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 343,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Logs de Seguran\\xe7a\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    isLoadingLogs ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-6 w-6 animate-spin text-gray-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 366,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTable, {\n                        moduleColor: \"admin\",\n                        title: \"Logs de Seguran\\xe7a\",\n                        columns: [\n                            {\n                                header: 'Data/Hora',\n                                field: 'createdAt',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Usuário',\n                                field: 'user',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Ação',\n                                field: 'action',\n                                width: '15%'\n                            },\n                            {\n                                header: 'Detalhes',\n                                field: 'details',\n                                width: '35%'\n                            },\n                            {\n                                header: 'Status',\n                                field: 'status',\n                                width: '10%'\n                            },\n                            {\n                                header: 'IP',\n                                field: 'ipAddress',\n                                width: '10%'\n                            }\n                        ],\n                        data: securityLogs,\n                        isLoading: isLoadingLogs,\n                        emptyMessage: \"Nenhum log de seguran\\xe7a encontrado\",\n                        emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 384,\n                            columnNumber: 24\n                        }, void 0),\n                        currentPage: currentPage,\n                        totalPages: totalPages,\n                        totalItems: totalLogs,\n                        onPageChange: (page)=>loadSecurityLogs(page, itemsPerPage),\n                        showPagination: true,\n                        tableId: \"security-logs-table\",\n                        enableColumnToggle: true,\n                        itemsPerPage: itemsPerPage,\n                        onItemsPerPageChange: (newItemsPerPage)=>{\n                            setItemsPerPage(newItemsPerPage);\n                            loadSecurityLogs(1, newItemsPerPage);\n                        },\n                        renderRow: (log, index, moduleColors, visibleColumns)=>{\n                            var _log_user, _log_details, _log_details1, _log_details2, _log_details3;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: moduleColors.hoverBg,\n                                children: [\n                                    visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: new Date(log.createdAt).toLocaleString('pt-BR')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 400,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('user') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-800 dark:text-neutral-100\",\n                                        children: ((_log_user = log.user) === null || _log_user === void 0 ? void 0 : _log_user.fullName) || (((_log_details = log.details) === null || _log_details === void 0 ? void 0 : _log_details.identifier) ? log.details.identifier : 'Sistema')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 405,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('action') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.action === 'LOGIN_ATTEMPT' ? 'Tentativa de Login' : log.action === 'SECURITY_SETTINGS_UPDATED' ? 'Configurações Alteradas' : log.action === 'ACCOUNT_LOCKED' ? 'Conta Bloqueada' : log.action === 'PASSWORD_EXPIRED' ? 'Senha Expirada' : log.action\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('details') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.action === 'SECURITY_SETTINGS_UPDATED' && ((_log_details1 = log.details) === null || _log_details1 === void 0 ? void 0 : _log_details1.description) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Altera\\xe7\\xf5es:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: log.details.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                log.details.source && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-neutral-400 dark:text-neutral-500\",\n                                                    children: [\n                                                        \"Local: \",\n                                                        log.details.source\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 421,\n                                            columnNumber: 23\n                                        }, void 0) : log.action === 'SECURITY_SETTINGS_UPDATED' && ((_log_details2 = log.details) === null || _log_details2 === void 0 ? void 0 : _log_details2.updatedFields) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: \"Configura\\xe7\\xf5es de Seguran\\xe7a:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 25\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1\",\n                                                    children: log.details.updatedFields.map((field)=>{\n                                                        const fieldNames = {\n                                                            requireUppercase: 'Exigir letra maiúscula',\n                                                            requireNumber: 'Exigir número',\n                                                            requireSpecialChar: 'Exigir caractere especial',\n                                                            minPasswordLength: 'Tamanho mínimo da senha',\n                                                            passwordExpirationDays: 'Expiração de senha',\n                                                            limitLoginAttempts: 'Limitar tentativas de login',\n                                                            maxLoginAttempts: 'Máximo de tentativas',\n                                                            enforceIpTracking: 'Rastrear IPs',\n                                                            enforceSessionTimeout: 'Timeout de sessão',\n                                                            sessionTimeoutMinutes: 'Tempo de inatividade',\n                                                            accountLockoutDuration: 'Duração do bloqueio'\n                                                        };\n                                                        return fieldNames[field] || field;\n                                                    }).join(', ')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 433,\n                                            columnNumber: 23\n                                        }, void 0) : log.action === 'LOGIN_ATTEMPT' && ((_log_details3 = log.details) === null || _log_details3 === void 0 ? void 0 : _log_details3.identifier) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs\",\n                                            children: [\n                                                \"Email: \",\n                                                log.details.identifier\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 455,\n                                            columnNumber: 23\n                                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-neutral-400\",\n                                            children: \"-\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 457,\n                                            columnNumber: 23\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 419,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-2 py-1 text-xs rounded-full \".concat(log.status === 'SUCCESS' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' : log.status === 'FAILURE' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' : 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'),\n                                            children: log.status === 'SUCCESS' ? 'Sucesso' : log.status === 'FAILURE' ? 'Falhou' : log.status === 'WARNING' ? 'Aviso' : log.status\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 463,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 462,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    visibleColumns.includes('ipAddress') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                        className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                        children: log.ipAddress || '-'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                        lineNumber: 476,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, log.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, void 0);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                        lineNumber: 370,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SecurityTab, \"aLH9YwC6IlYdjYFVBWnRi8BOsaI=\", false, function() {\n    return [\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = SecurityTab;\nconst SettingsPage = ()=>{\n    _s1();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"general\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [generalSettings, setGeneralSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        siteName: \"High Tide\",\n        siteUrl: \"https://hightide.site\",\n        adminEmail: \"<EMAIL>\",\n        allowRegistration: true,\n        defaultTimeZone: \"America/Sao_Paulo\",\n        dateFormat: \"DD/MM/YYYY\",\n        timeFormat: \"24h\",\n        backupEnabled: true,\n        backupFrequency: \"daily\",\n        backupTime: \"01:00\",\n        maxFileSize: 5,\n        allowedFileTypes: \"jpg,png,pdf,doc,docx,xls,xlsx\",\n        logRetention: 90\n    });\n    // Define user role constants\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"SYSTEM_ADMIN\";\n    const isCompanyAdmin = (user === null || user === void 0 ? void 0 : user.role) === \"COMPANY_ADMIN\";\n    // Determine which tabs are available based on user role\n    const availableTabs = {\n        general: isSystemAdmin || isCompanyAdmin,\n        companies: isSystemAdmin || isCompanyAdmin,\n        branches: isSystemAdmin || isCompanyAdmin,\n        email: isSystemAdmin || isCompanyAdmin,\n        backup: isSystemAdmin,\n        security: isSystemAdmin || isCompanyAdmin,\n        preferencias: true\n    };\n    // Load data for the active tab\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SettingsPage.useEffect\": ()=>{\n            // Auto-select the first available tab if the current active tab is not available\n            const tabKeys = Object.keys(availableTabs);\n            const firstAvailableTab = tabKeys.find({\n                \"SettingsPage.useEffect.firstAvailableTab\": (tab)=>availableTabs[tab]\n            }[\"SettingsPage.useEffect.firstAvailableTab\"]);\n            if (!availableTabs[activeTab] && firstAvailableTab) {\n                setActiveTab(firstAvailableTab);\n                return;\n            }\n            if (activeTab === \"general\") {\n                loadGeneralSettings();\n            }\n        }\n    }[\"SettingsPage.useEffect\"], [\n        activeTab,\n        user\n    ]);\n    // Load general settings\n    const loadGeneralSettings = async ()=>{\n        setIsLoading(true);\n        try {\n            // This would typically be an API call\n            // For now, we'll just simulate loading with a timeout\n            setTimeout(()=>{\n                setGeneralSettings({\n                    siteName: \"High Tide\",\n                    siteUrl: \"https://dentrodascasinhas.com.br\",\n                    adminEmail: \"<EMAIL>\",\n                    allowRegistration: true,\n                    defaultTimeZone: \"America/Sao_Paulo\",\n                    dateFormat: \"DD/MM/YYYY\",\n                    timeFormat: \"24h\",\n                    backupEnabled: true,\n                    backupFrequency: \"daily\",\n                    backupTime: \"01:00\",\n                    maxFileSize: 5,\n                    allowedFileTypes: \"jpg,png,pdf,doc,docx,xls,xlsx\",\n                    logRetention: 90\n                });\n                setIsLoading(false);\n            }, 500);\n        } catch (error) {\n            console.error(\"Error loading settings:\", error);\n            setError(\"Falha ao carregar configurações\");\n            setIsLoading(false);\n        }\n    };\n    // Function to save general settings\n    const saveGeneralSettings = async ()=>{\n        setIsLoading(true);\n        try {\n            // This would be an API call in production\n            // For now, we just simulate with a timeout\n            setTimeout(()=>{\n                alert(\"Configurações salvas com sucesso!\");\n                setIsLoading(false);\n            }, 500);\n        } catch (error) {\n            console.error(\"Error saving settings:\", error);\n            setError(\"Falha ao salvar configurações\");\n            setIsLoading(false);\n        }\n    };\n    // Get tab label based on user role\n    const getTabLabel = (tabKey)=>{\n        const labels = {\n            general: \"Geral\",\n            companies: isSystemAdmin ? \"Empresas\" : \"Minha Empresa\",\n            branches: \"Unidades\",\n            email: \"Email\",\n            backup: \"Backup\",\n            security: \"Segurança\",\n            preferencias: \"Preferências\"\n        };\n        return labels[tabKey] || tabKey;\n    };\n    // Get tab icon\n    const getTabIcon = (tabKey)=>{\n        const icons = {\n            general: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 609,\n                columnNumber: 16\n            }, undefined),\n            companies: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 610,\n                columnNumber: 18\n            }, undefined),\n            branches: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 611,\n                columnNumber: 17\n            }, undefined),\n            email: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 612,\n                columnNumber: 14\n            }, undefined),\n            backup: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 613,\n                columnNumber: 15\n            }, undefined),\n            security: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 614,\n                columnNumber: 17\n            }, undefined),\n            preferencias: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 615,\n                columnNumber: 21\n            }, undefined)\n        };\n        return icons[tabKey] || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n            className: \"h-5 w-5\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n            lineNumber: 618,\n            columnNumber: 29\n        }, undefined);\n    };\n    // Preparar as tabs para o componente ModuleTabs\n    const tabsConfig = Object.keys(availableTabs).filter((tabKey)=>availableTabs[tabKey]).map((tabKey)=>({\n            id: tabKey,\n            label: getTabLabel(tabKey),\n            icon: getTabIcon(tabKey)\n        }));\n    // Import tutorial steps from tutorialMapping\n    const settingsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"SettingsPage.useMemo[settingsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/settings'] || [];\n        }\n    }[\"SettingsPage.useMemo[settingsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold text-gray-800 dark:text-white flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            size: 24,\n                            className: \"mr-2 text-gray-600 dark:text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, undefined),\n                        \"Configura\\xe7\\xf5es do Sistema\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 641,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 640,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl border border-module-admin-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTabs, {\n                    tabs: tabsConfig,\n                    activeTab: activeTab,\n                    onTabChange: setActiveTab,\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 648,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6\",\n                    children: [\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 664,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 665,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 663,\n                            columnNumber: 13\n                        }, undefined),\n                        isLoading && activeTab !== \"companies\" && activeTab !== \"branches\" && activeTab !== \"email\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-8 w-8 animate-spin text-gray-500 dark:text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                lineNumber: 674,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                            lineNumber: 673,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                activeTab === \"general\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 685,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Configura\\xe7\\xf5es B\\xe1sicas\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleFormGroup, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                                    htmlFor: \"siteName\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    children: \"Nome do Site\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                                    id: \"siteName\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    type: \"text\",\n                                                                                    value: generalSettings.siteName,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            siteName: e.target.value\n                                                                                        }),\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 697,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 690,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleFormGroup, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                                    htmlFor: \"siteUrl\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    children: \"URL do Site\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 713,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                                    id: \"siteUrl\",\n                                                                                    moduleColor: \"admin\",\n                                                                                    type: \"url\",\n                                                                                    value: generalSettings.siteUrl,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            siteUrl: e.target.value\n                                                                                        }),\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 719,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 712,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"adminEmail\",\n                                                                                    children: \"Email do Administrador\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 735,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"adminEmail\",\n                                                                                    type: \"email\",\n                                                                                    value: generalSettings.adminEmail,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            adminEmail: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    disabled: !isSystemAdmin\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 741,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"allowRegistration\",\n                                                                                    checked: generalSettings.allowRegistration,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            allowRegistration: e.target.checked\n                                                                                        }),\n                                                                                    className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 758,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"allowRegistration\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Permitir cadastro de novos usu\\xe1rios\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 770,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 757,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 689,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 683,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 783,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        \"Localiza\\xe7\\xe3o e Formato\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 782,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"defaultTimeZone\",\n                                                                                    children: \"Fuso Hor\\xe1rio Padr\\xe3o\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 789,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"defaultTimeZone\",\n                                                                                    value: generalSettings.defaultTimeZone,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            defaultTimeZone: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Sao_Paulo\",\n                                                                                            children: \"Am\\xe9rica/S\\xe3o Paulo\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 806,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Recife\",\n                                                                                            children: \"Am\\xe9rica/Recife\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 809,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Manaus\",\n                                                                                            children: \"Am\\xe9rica/Manaus\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 812,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"America/Belem\",\n                                                                                            children: \"Am\\xe9rica/Bel\\xe9m\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 815,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 795,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 788,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"dateFormat\",\n                                                                                    children: \"Formato de Data\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 822,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"dateFormat\",\n                                                                                    value: generalSettings.dateFormat,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            dateFormat: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"DD/MM/YYYY\",\n                                                                                            children: \"DD/MM/YYYY\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 839,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"MM/DD/YYYY\",\n                                                                                            children: \"MM/DD/YYYY\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 840,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"YYYY-MM-DD\",\n                                                                                            children: \"YYYY-MM-DD\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 841,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 828,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 821,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"timeFormat\",\n                                                                                    children: \"Formato de Hora\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 846,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                    id: \"timeFormat\",\n                                                                                    value: generalSettings.timeFormat,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            timeFormat: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"24h\",\n                                                                                            children: \"24h (ex: 14:30)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 863,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                            value: \"12h\",\n                                                                                            children: \"12h (ex: 2:30 PM)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 864,\n                                                                                            columnNumber: 31\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 852,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 845,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 682,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"Backup\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"backupEnabled\",\n                                                                                    checked: generalSettings.backupEnabled,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            backupEnabled: e.target.checked\n                                                                                        }),\n                                                                                    className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 881,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"backupEnabled\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Ativar backup autom\\xe1tico\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 893,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 880,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        generalSettings.backupEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                            htmlFor: \"backupFrequency\",\n                                                                                            children: \"Frequ\\xeancia\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 904,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                            id: \"backupFrequency\",\n                                                                                            value: generalSettings.backupFrequency,\n                                                                                            onChange: (e)=>setGeneralSettings({\n                                                                                                    ...generalSettings,\n                                                                                                    backupFrequency: e.target.value\n                                                                                                }),\n                                                                                            className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"daily\",\n                                                                                                    children: \"Di\\xe1rio\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 921,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"weekly\",\n                                                                                                    children: \"Semanal\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 922,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                    value: \"monthly\",\n                                                                                                    children: \"Mensal\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                                    lineNumber: 923,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 910,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 903,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                            htmlFor: \"backupTime\",\n                                                                                            children: \"Hor\\xe1rio\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 928,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                            id: \"backupTime\",\n                                                                                            type: \"time\",\n                                                                                            value: generalSettings.backupTime,\n                                                                                            onChange: (e)=>setGeneralSettings({\n                                                                                                    ...generalSettings,\n                                                                                                    backupTime: e.target.value\n                                                                                                }),\n                                                                                            className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                            lineNumber: 934,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 927,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 879,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 954,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        \"Armazenamento e Logs\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 953,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"maxFileSize\",\n                                                                                    children: \"Tamanho m\\xe1ximo de arquivo (MB)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 960,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"maxFileSize\",\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    max: \"100\",\n                                                                                    value: generalSettings.maxFileSize,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            maxFileSize: parseInt(e.target.value)\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 966,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 959,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"allowedFileTypes\",\n                                                                                    children: \"Tipos de arquivo permitidos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 983,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"allowedFileTypes\",\n                                                                                    type: \"text\",\n                                                                                    value: generalSettings.allowedFileTypes,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            allowedFileTypes: e.target.value\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                                                                    placeholder: \"jpg,png,pdf,doc,...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 989,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"mt-1 text-xs text-neutral-500 dark:text-neutral-400\",\n                                                                                    children: \"Separados por v\\xedrgula, sem pontos ou espa\\xe7os\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1002,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 982,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\",\n                                                                                    htmlFor: \"logRetention\",\n                                                                                    children: \"Per\\xedodo de reten\\xe7\\xe3o de logs (dias)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1008,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    id: \"logRetention\",\n                                                                                    type: \"number\",\n                                                                                    min: \"1\",\n                                                                                    max: \"365\",\n                                                                                    value: generalSettings.logRetention,\n                                                                                    onChange: (e)=>setGeneralSettings({\n                                                                                            ...generalSettings,\n                                                                                            logRetention: parseInt(e.target.value)\n                                                                                        }),\n                                                                                    className: \"block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1014,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1007,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 958,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 952,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 681,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: saveGeneralSettings,\n                                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors\",\n                                                disabled: !isSystemAdmin && !isCompanyAdmin,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"hidden\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Salvar Configura\\xe7\\xf5es\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1046,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                lineNumber: 1036,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1035,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 680,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"companies\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_CompanyManagementTab__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1053,\n                                    columnNumber: 45\n                                }, undefined),\n                                activeTab === \"branches\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_BranchesTab__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1056,\n                                    columnNumber: 44\n                                }, undefined),\n                                activeTab === \"email\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_settings_EmailSettingsTab__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1059,\n                                    columnNumber: 41\n                                }, undefined),\n                                activeTab === \"backup\" && isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1065,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Configura\\xe7\\xf5es de Backup\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1064,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupStorage\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Armazenamento de Backup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1072,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                                                                    id: \"backupStorage\",\n                                                                    moduleColor: \"admin\",\n                                                                    defaultValue: \"local\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"local\",\n                                                                            children: \"Servidor Local\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1083,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"s3\",\n                                                                            children: \"Amazon S3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1084,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"azure\",\n                                                                            children: \"Azure Blob Storage\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1085,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"google\",\n                                                                            children: \"Google Cloud Storage\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1086,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1078,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupDirectory\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Diret\\xf3rio Local\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1091,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                    id: \"backupDirectory\",\n                                                                    moduleColor: \"admin\",\n                                                                    type: \"text\",\n                                                                    defaultValue: \"/var/backups/dentrodascasinhas\",\n                                                                    placeholder: \"/var/backups/dentrodascasinhas\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1090,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupRetention\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Reten\\xe7\\xe3o de Backups (dias)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1107,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleInput, {\n                                                                    id: \"backupRetention\",\n                                                                    moduleColor: \"admin\",\n                                                                    type: \"number\",\n                                                                    defaultValue: \"30\",\n                                                                    min: \"1\",\n                                                                    max: \"365\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1113,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1106,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1070,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    htmlFor: \"backupCompression\",\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Compress\\xe3o\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1126,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleSelect, {\n                                                                    id: \"backupCompression\",\n                                                                    moduleColor: \"admin\",\n                                                                    defaultValue: \"gzip\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"gzip\",\n                                                                            children: \"GZIP\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1137,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"bzip2\",\n                                                                            children: \"BZIP2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1138,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"none\",\n                                                                            children: \"Sem compress\\xe3o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1139,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1132,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1125,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Criptografia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1144,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            id: \"encryptBackup\",\n                                                                            className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1148,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"encryptBackup\",\n                                                                            className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                            children: \"Ativar criptografia de backup\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1153,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1147,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1143,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleLabel, {\n                                                                    moduleColor: \"admin\",\n                                                                    children: \"Notifica\\xe7\\xf5es de Backup\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1163,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"notifySuccess\",\n                                                                                    className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                                                    defaultChecked: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1168,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"notifySuccess\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Notificar backups bem-sucedidos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1174,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1167,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    id: \"notifyFailure\",\n                                                                                    className: \"h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600\",\n                                                                                    defaultChecked: true\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1182,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"notifyFailure\",\n                                                                                    className: \"ml-2 block text-sm text-neutral-700 dark:text-neutral-300\",\n                                                                                    children: \"Notificar falhas de backup\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1188,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                            lineNumber: 1181,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1166,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1162,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1069,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \"Backups Dispon\\xedveis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1202,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ModuleTable, {\n                                                    moduleColor: \"admin\",\n                                                    title: \"Hist\\xf3rico de Backups\",\n                                                    columns: [\n                                                        {\n                                                            header: 'Nome',\n                                                            field: 'name',\n                                                            width: '25%'\n                                                        },\n                                                        {\n                                                            header: 'Data',\n                                                            field: 'date',\n                                                            width: '15%'\n                                                        },\n                                                        {\n                                                            header: 'Tamanho',\n                                                            field: 'size',\n                                                            width: '10%'\n                                                        },\n                                                        {\n                                                            header: 'Status',\n                                                            field: 'status',\n                                                            width: '10%'\n                                                        },\n                                                        {\n                                                            header: 'Tipo',\n                                                            field: 'type',\n                                                            width: '15%'\n                                                        },\n                                                        {\n                                                            header: 'Ações',\n                                                            field: 'actions',\n                                                            width: '15%',\n                                                            sortable: false\n                                                        }\n                                                    ],\n                                                    data: [\n                                                        {\n                                                            id: 1,\n                                                            name: 'backup_completo_20240601',\n                                                            date: '01/06/2024 01:00',\n                                                            size: '1.2 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 2,\n                                                            name: 'backup_completo_20240531',\n                                                            date: '31/05/2024 01:00',\n                                                            size: '1.1 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 3,\n                                                            name: 'backup_completo_20240530',\n                                                            date: '30/05/2024 01:00',\n                                                            size: '1.1 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        },\n                                                        {\n                                                            id: 4,\n                                                            name: 'backup_incremental_20240529',\n                                                            date: '29/05/2024 01:00',\n                                                            size: '250 MB',\n                                                            status: 'Concluído',\n                                                            type: 'Incremental'\n                                                        },\n                                                        {\n                                                            id: 5,\n                                                            name: 'backup_completo_20240528',\n                                                            date: '28/05/2024 01:00',\n                                                            size: '1.0 GB',\n                                                            status: 'Concluído',\n                                                            type: 'Completo'\n                                                        }\n                                                    ],\n                                                    isLoading: false,\n                                                    emptyMessage: \"Nenhum backup encontrado\",\n                                                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        size: 24\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                        lineNumber: 1227,\n                                                        columnNumber: 34\n                                                    }, void 0),\n                                                    currentPage: 1,\n                                                    totalPages: 1,\n                                                    totalItems: 5,\n                                                    showPagination: false,\n                                                    tableId: \"admin-backups-table\",\n                                                    enableColumnToggle: true,\n                                                    renderRow: (backup, index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: moduleColors.hoverBg,\n                                                            children: [\n                                                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-500 dark:text-gray-400 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1239,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-neutral-800 dark:text-neutral-100\",\n                                                                                children: backup.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1240,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1238,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1237,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('date') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.date\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('size') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.size\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\",\n                                                                        children: backup.status\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('type') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\",\n                                                                    children: backup.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 29\n                                                                }, void 0),\n                                                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"px-4 py-4 text-right\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-end gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-gray-500 dark:hover:text-gray-400 transition-colors\",\n                                                                                title: \"Restaurar\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1273,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1269,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                                                                title: \"Download\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1279,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1275,\n                                                                                columnNumber: 33\n                                                                            }, void 0),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                                                title: \"Excluir\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    size: 16\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                    lineNumber: 1285,\n                                                                                    columnNumber: 35\n                                                                                }, void 0)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                                lineNumber: 1281,\n                                                                                columnNumber: 33\n                                                                            }, void 0)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                        lineNumber: 1268,\n                                                                        columnNumber: 31\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                                    lineNumber: 1267,\n                                                                    columnNumber: 29\n                                                                }, void 0)\n                                                            ]\n                                                        }, backup.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1235,\n                                                            columnNumber: 25\n                                                        }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1201,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Executar Backup Agora\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Database_Download_Globe_Loader2_Mail_RefreshCw_Server_Settings_Shield_SlidersHorizontal_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Salvar Configura\\xe7\\xf5es\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                            lineNumber: 1302,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                            lineNumber: 1295,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1063,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"security\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SecurityTab, {\n                                    isSystemAdmin: isSystemAdmin,\n                                    isCompanyAdmin: isCompanyAdmin\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1310,\n                                    columnNumber: 17\n                                }, undefined),\n                                activeTab === \"preferencias\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_PreferencesPanel__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                                    lineNumber: 1317,\n                                    columnNumber: 48\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                    lineNumber: 661,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n                lineNumber: 658,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\settings\\\\SettingsPage.js\",\n        lineNumber: 638,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(SettingsPage, \"Mw45LE88dod0CQdEKxElMPn1bEg=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c1 = SettingsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SettingsPage);\nvar _c, _c1;\n$RefreshReg$(_c, \"SecurityTab\");\n$RefreshReg$(_c1, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/settings/SettingsPage.js\n"));

/***/ })

});