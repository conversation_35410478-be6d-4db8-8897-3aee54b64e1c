"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js":
/*!********************************************************!*\
  !*** ./src/components/bug-report/BugReportsFilters.js ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,Building,CheckCircle,Filter,RefreshCw,Search,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_multi_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/multi-select */ \"(app-pages-browser)/./src/components/ui/multi-select.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst BugReportsFilters = (param)=>{\n    let { filters, onFiltersChange, onSearch } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [isFilterExpanded, setIsFilterExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    const statusOptions = [\n        {\n            value: 'OPEN',\n            label: 'Aberto'\n        },\n        {\n            value: 'IN_PROGRESS',\n            label: 'Em Progresso'\n        },\n        {\n            value: 'RESOLVED',\n            label: 'Resolvido'\n        },\n        {\n            value: 'CLOSED',\n            label: 'Fechado'\n        }\n    ];\n    const priorityOptions = [\n        {\n            value: 'CRITICAL',\n            label: 'Crítica'\n        },\n        {\n            value: 'HIGH',\n            label: 'Alta'\n        },\n        {\n            value: 'MEDIUM',\n            label: 'Média'\n        },\n        {\n            value: 'LOW',\n            label: 'Baixa'\n        }\n    ];\n    const categoryOptions = [\n        {\n            value: 'GENERAL',\n            label: 'Geral'\n        },\n        {\n            value: 'UI_UX',\n            label: 'Interface/Experiência'\n        },\n        {\n            value: 'PERFORMANCE',\n            label: 'Performance'\n        },\n        {\n            value: 'FUNCTIONALITY',\n            label: 'Funcionalidade'\n        },\n        {\n            value: 'DATA',\n            label: 'Dados'\n        },\n        {\n            value: 'SECURITY',\n            label: 'Segurança'\n        },\n        {\n            value: 'INTEGRATION',\n            label: 'Integração'\n        }\n    ];\n    // Carregar empresas para system_admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsFilters.useEffect\": ()=>{\n            const loadCompanies = {\n                \"BugReportsFilters.useEffect.loadCompanies\": async ()=>{\n                    if (!isSystemAdmin) return;\n                    try {\n                        setIsLoading(true);\n                        const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_4__.companyService.getCompanies({\n                            page: 1,\n                            limit: 100,\n                            active: true\n                        });\n                        const formattedCompanies = response.companies.filter({\n                            \"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\": (company)=>company && company.id && company.name\n                        }[\"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\"]).map({\n                            \"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\": (company)=>({\n                                    value: company.id,\n                                    label: company.name,\n                                    sortName: company.name.toLowerCase()\n                                })\n                        }[\"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\"]).sort({\n                            \"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\": (a, b)=>a.sortName.localeCompare(b.sortName)\n                        }[\"BugReportsFilters.useEffect.loadCompanies.formattedCompanies\"]);\n                        setCompanies(formattedCompanies);\n                    } catch (error) {\n                        console.error('Erro ao carregar empresas:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"BugReportsFilters.useEffect.loadCompanies\"];\n            loadCompanies();\n        }\n    }[\"BugReportsFilters.useEffect\"], [\n        isSystemAdmin\n    ]);\n    const handleFilterChange = (newFilters)=>{\n        onFiltersChange(newFilters);\n    };\n    const handleClearFilters = ()=>{\n        const clearedFilters = {\n            search: '',\n            status: [],\n            priority: [],\n            category: [],\n            companies: []\n        };\n        onFiltersChange(clearedFilters);\n    };\n    const getActiveFiltersCount = ()=>{\n        return Object.entries(filters).filter((param)=>{\n            let [key, value] = param;\n            if (key === 'search') return false;\n            if (Array.isArray(value)) return value.length > 0;\n            return value !== null && value !== '';\n        }).length;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row gap-3 md:items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por t\\xedtulo, descri\\xe7\\xe3o, usu\\xe1rio ou empresa...\",\n                                    value: filters.search || \"\",\n                                    onChange: (e)=>handleFilterChange({\n                                            ...filters,\n                                            search: e.target.value\n                                        }),\n                                    className: \"pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__.FilterButton, {\n                                type: \"button\",\n                                onClick: ()=>setIsFilterExpanded(!isFilterExpanded),\n                                moduleColor: \"admin\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 16,\n                                            className: \"text-gray-600 dark:text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Filtros\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs\",\n                                            children: getActiveFiltersCount()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__.FilterButton, {\n                                type: \"button\",\n                                onClick: onSearch,\n                                moduleColor: \"admin\",\n                                variant: \"primary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Buscar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                            lineNumber: 135,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, undefined),\n            isFilterExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4 \".concat(isSystemAdmin ? 'md:grid-cols-2 lg:grid-cols-4' : 'md:grid-cols-3'),\n                        children: [\n                            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-slate-600 dark:text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                                lineNumber: 147,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Empresa\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_multi_select__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        options: companies,\n                                        value: filters.companies || [],\n                                        onChange: (selected)=>handleFilterChange({\n                                                ...filters,\n                                                companies: selected\n                                            }),\n                                        placeholder: \"Selecione as empresas\",\n                                        className: \"w-full\",\n                                        moduleOverride: \"admin\",\n                                        loading: isLoading\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-slate-600 dark:text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Status\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_multi_select__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        options: statusOptions,\n                                        value: filters.status || [],\n                                        onChange: (selected)=>handleFilterChange({\n                                                ...filters,\n                                                status: selected\n                                            }),\n                                        placeholder: \"Selecione os status\",\n                                        className: \"w-full\",\n                                        moduleOverride: \"admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-slate-600 dark:text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Prioridade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_multi_select__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        options: priorityOptions,\n                                        value: filters.priority || [],\n                                        onChange: (selected)=>handleFilterChange({\n                                                ...filters,\n                                                priority: selected\n                                            }),\n                                        placeholder: \"Selecione as prioridades\",\n                                        className: \"w-full\",\n                                        moduleOverride: \"admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-slate-600 dark:text-slate-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Categoria\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_multi_select__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        options: categoryOptions,\n                                        value: filters.category || [],\n                                        onChange: (selected)=>handleFilterChange({\n                                                ...filters,\n                                                category: selected\n                                            }),\n                                        placeholder: \"Selecione as categorias\",\n                                        className: \"w-full\",\n                                        moduleOverride: \"admin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__.FilterButton, {\n                            type: \"button\",\n                            onClick: handleClearFilters,\n                            moduleColor: \"admin\",\n                            variant: \"secondary\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_Building_CheckCircle_Filter_RefreshCw_Search_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 216,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Limpar Filtros\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                                lineNumber: 215,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                            lineNumber: 209,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\bug-report\\\\BugReportsFilters.js\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsFilters, \"nNu46OWkSDZphY1ty9NjAH9xJTw=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = BugReportsFilters;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsFilters);\nvar _c;\n$RefreshReg$(_c, \"BugReportsFilters\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\n"));

/***/ })

});