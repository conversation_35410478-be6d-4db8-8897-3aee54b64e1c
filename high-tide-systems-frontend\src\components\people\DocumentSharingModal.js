"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button.js";
import Input from "@/components/ui/Input.js";
import Label from "@/components/ui/Label.js";
import { ModuleSelect } from "@/components/ui";
import Badge from "@/components/ui/Badge";
import { ModuleCheckbox } from "@/components/ui";
import { X, Users, User, Building, Search, Plus, Minus } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const DocumentSharingModal = ({ 
  isOpen, 
  onClose, 
  document, 
  onSuccess,
  initialPermissions = {
    users: [],
    professions: [],
    clients: []
  }
}) => {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState([]);
  const [professions, setProfessions] = useState([]);
  const [clients, setClients] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [filteredClients, setFilteredClients] = useState([]);
  
  // Estados para seleções
  const [selectedUsers, setSelectedUsers] = useState(initialPermissions.users || []);
  const [selectedProfessions, setSelectedProfessions] = useState(initialPermissions.professions || []);
  const [selectedClients, setSelectedClients] = useState(initialPermissions.clients || []);
  
  // Estados para filtros
  const [userSearch, setUserSearch] = useState("");
  const [clientSearch, setClientSearch] = useState("");
  const [selectedProfession, setSelectedProfession] = useState("");
  
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados iniciais
  useEffect(() => {
    if (isOpen) {
      loadUsers();
      loadProfessions();
      loadClients();
    }
  }, [isOpen]);

  // Filtrar usuários baseado na busca
  useEffect(() => {
    const filtered = users.filter(user => 
      user.fullName.toLowerCase().includes(userSearch.toLowerCase()) ||
      user.email.toLowerCase().includes(userSearch.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [users, userSearch]);

  // Filtrar clientes baseado na busca
  useEffect(() => {
    const filtered = clients.filter(client => 
      client.login.toLowerCase().includes(clientSearch.toLowerCase()) ||
      client.email.toLowerCase().includes(clientSearch.toLowerCase())
    );
    setFilteredClients(filtered);
  }, [clients, clientSearch]);

  const loadUsers = async () => {
    try {
      const response = await fetch("/api/users", {
        headers: {
          "Authorization": `Bearer ${localStorage.getItem("token")}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
    }
  };

  const loadProfessions = async () => {
    try {
      const response = await fetch("/api/professions", {
        headers: {
          "Authorization": `Bearer ${localStorage.getItem("token")}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setProfessions(data.professions || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
    }
  };

  const loadClients = async () => {
    try {
      const response = await fetch("/api/clients", {
        headers: {
          "Authorization": `Bearer ${localStorage.getItem("token")}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setClients(data.clients || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
    }
  };

  const handleAddProfession = () => {
    if (selectedProfession && !selectedProfessions.includes(selectedProfession)) {
      setSelectedProfessions(prev => [...prev, selectedProfession]);
      setSelectedProfession("");
    }
  };

  const handleRemoveProfession = (professionId) => {
    setSelectedProfessions(prev => prev.filter(id => id !== professionId));
  };

  const handleAddUser = (userId) => {
    if (!selectedUsers.includes(userId)) {
      setSelectedUsers(prev => [...prev, userId]);
    }
  };

  const handleRemoveUser = (userId) => {
    setSelectedUsers(prev => prev.filter(id => id !== userId));
  };

  const handleAddClient = (clientId) => {
    if (!selectedClients.includes(clientId)) {
      setSelectedClients(prev => [...prev, clientId]);
    }
  };

  const handleRemoveClient = (clientId) => {
    setSelectedClients(prev => prev.filter(id => id !== clientId));
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/documents/${document.id}/permissions`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("token")}`
        },
        body: JSON.stringify({
          users: selectedUsers,
          professions: selectedProfessions,
          clients: selectedClients
        })
      });

      if (response.ok) {
        onSuccess();
        onClose();
      } else {
        const error = await response.json();
        alert(error.message || "Erro ao salvar permissões");
      }
    } catch (error) {
      console.error("Erro ao salvar permissões:", error);
      alert("Erro ao salvar permissões");
    } finally {
      setIsLoading(false);
    }
  };

  const getSelectedUserNames = () => {
    return selectedUsers.map(id => {
      const user = users.find(u => u.id === id);
      return user?.fullName || id;
    });
  };

  const getSelectedProfessionNames = () => {
    return selectedProfessions.map(id => {
      const profession = professions.find(p => p.id === id);
      return profession?.name || id;
    });
  };

  const getSelectedClientNames = () => {
    return selectedClients.map(id => {
      const client = clients.find(c => c.id === id);
      return client?.login || id;
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white">
            Compartilhar Documento: {document?.filename}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Seção de Profissões */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-600" />
              <Label className="text-lg font-medium">Profissões</Label>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Selecione profissões para incluir automaticamente todos os usuários dessa profissão
            </p>
            
            <div className="flex gap-2">
              <ModuleSelect
                moduleColor="people"
                value={selectedProfession}
                onChange={(e) => setSelectedProfession(e.target.value)}
                className="flex-1"
              >
                <option value="">Selecione uma profissão</option>
                {professions.map((profession) => (
                  <option key={profession.id} value={profession.id}>
                    {profession.name}
                  </option>
                ))}
              </ModuleSelect>
              <Button
                type="button"
                onClick={handleAddProfession}
                disabled={!selectedProfession}
                className="bg-orange-500 hover:bg-orange-600"
              >
                <Plus size={16} />
              </Button>
            </div>

            {/* Lista de profissões selecionadas */}
            {selectedProfessions.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Profissões selecionadas:</Label>
                <div className="flex flex-wrap gap-2">
                  {getSelectedProfessionNames().map((name, index) => (
                    <Badge key={`profession-${index}`} variant="secondary" className="flex items-center gap-1">
                      <Users size={12} />
                      {name}
                      <button
                        type="button"
                        onClick={() => handleRemoveProfession(selectedProfessions[index])}
                        className="ml-1 text-red-500 hover:text-red-700"
                      >
                        <X size={12} />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Seção de Usuários */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              <Label className="text-lg font-medium">Usuários</Label>
            </div>
            
            {/* Busca de usuários */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar usuários..."
                value={userSearch}
                onChange={(e) => setUserSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Lista de usuários */}
            <div className="max-h-40 overflow-y-auto border rounded-lg p-2">
              {filteredUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                  <div className="flex items-center gap-2">
                    <ModuleCheckbox
                      moduleColor="people"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => {
                        if (selectedUsers.includes(user.id)) {
                          handleRemoveUser(user.id);
                        } else {
                          handleAddUser(user.id);
                        }
                      }}
                    />
                    <div>
                      <p className="text-sm font-medium">{user.fullName}</p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      if (selectedUsers.includes(user.id)) {
                        handleRemoveUser(user.id);
                      } else {
                        handleAddUser(user.id);
                      }
                    }}
                  >
                    {selectedUsers.includes(user.id) ? <Minus size={16} /> : <Plus size={16} />}
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Seção de Clientes */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5 text-green-600" />
              <Label className="text-lg font-medium">Clientes</Label>
            </div>
            
            {/* Busca de clientes */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Buscar clientes..."
                value={clientSearch}
                onChange={(e) => setClientSearch(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Lista de clientes */}
            <div className="max-h-40 overflow-y-auto border rounded-lg p-2">
              {filteredClients.map((client) => (
                <div key={client.id} className="flex items-center justify-between p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded">
                  <div className="flex items-center gap-2">
                    <ModuleCheckbox
                      moduleColor="people"
                      checked={selectedClients.includes(client.id)}
                      onChange={() => {
                        if (selectedClients.includes(client.id)) {
                          handleRemoveClient(client.id);
                        } else {
                          handleAddClient(client.id);
                        }
                      }}
                    />
                    <div>
                      <p className="text-sm font-medium">{client.login}</p>
                      <p className="text-xs text-gray-500">{client.email}</p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      if (selectedClients.includes(client.id)) {
                        handleRemoveClient(client.id);
                      } else {
                        handleAddClient(client.id);
                      }
                    }}
                  >
                    {selectedClients.includes(client.id) ? <Minus size={16} /> : <Plus size={16} />}
                  </Button>
                </div>
              ))}
            </div>
          </div>

          {/* Resumo dos compartilhamentos */}
          {(selectedUsers.length > 0 || selectedProfessions.length > 0 || selectedClients.length > 0) && (
            <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <Label className="text-lg font-medium">Resumo dos Compartilhamentos</Label>
              
              {selectedProfessions.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Profissões ({selectedProfessions.length}):
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {getSelectedProfessionNames().map((name, index) => (
                      <Badge key={`summary-profession-${index}`} variant="secondary">
                        <Users size={12} className="mr-1" />
                        {name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedUsers.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Usuários ({selectedUsers.length}):
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {getSelectedUserNames().map((name, index) => (
                      <Badge key={`summary-user-${index}`} variant="secondary">
                        <User size={12} className="mr-1" />
                        {name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {selectedClients.length > 0 && (
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Clientes ({selectedClients.length}):
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {getSelectedClientNames().map((name, index) => (
                      <Badge key={`summary-client-${index}`} variant="secondary">
                        <Building size={12} className="mr-1" />
                        {name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Botões */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={isLoading}
              className="bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600"
            >
              {isLoading ? "Salvando..." : "Salvar Compartilhamentos"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentSharingModal; 