/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/design-system.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/**
 * Design System CSS - High Tide Systems
 *
 * Este arquivo define as variáveis CSS globais baseadas nos tokens de design.
 */

:root {
  /* Sistema de espaçamento */
  --spacing-0: 0;
  --spacing-0-5: 0.125rem; /* 2px */
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */

  /* Sistema de tipografia */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Sistema de bordas */
  --border-radius-none: 0;
  --border-radius-sm: 0.125rem;    /* 2px */
  --border-radius-default: 0.25rem; /* 4px */
  --border-radius-md: 0.375rem;     /* 6px */
  --border-radius-lg: 0.5rem;       /* 8px */
  --border-radius-xl: 0.75rem;      /* 12px */
  --border-radius-2xl: 1rem;        /* 16px */
  --border-radius-3xl: 1.5rem;      /* 24px */
  --border-radius-full: 9999px;

  /* Sistema de sombras - Modo Claro */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Sistema de transições */
  --transition-duration-75: 75ms;
  --transition-duration-100: 100ms;
  --transition-duration-150: 150ms;
  --transition-duration-200: 200ms;
  --transition-duration-300: 300ms;
  --transition-duration-500: 500ms;
  --transition-duration-700: 700ms;
  --transition-duration-1000: 1000ms;

  --transition-timing-linear: linear;
  --transition-timing-in: cubic-bezier(0.4, 0, 1, 1);
  --transition-timing-out: cubic-bezier(0, 0, 0.2, 1);
  --transition-timing-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-index */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1100;
  --z-index-fixed: 1200;
  --z-index-header: 9000;
  --z-index-modal: 11000;
  --z-index-dialog: 12000;
  --z-index-popover: 13000;
  --z-index-tooltip: 14000;

  /* Cores de módulos - Modo Claro */
  /* Pessoas */
  --color-module-people-bg: #fff7ed;
  --color-module-people-border: #fdba74;
  --color-module-people-icon: #f97316;
  --color-module-people-text: #c2410c;
  --color-module-people-hover: #fdba74;

  /* Agendamento */
  --color-module-scheduler-bg: #f5f3ff;
  --color-module-scheduler-border: #c4b5fd;
  --color-module-scheduler-icon: #8b5cf6;
  --color-module-scheduler-text: #6d28d9;
  --color-module-scheduler-hover: #a78bfa;

  /* Administração */
  --color-module-admin-bg: #f8fafc;
  --color-module-admin-border: #64748b;
  --color-module-admin-icon: #64748b;
  --color-module-admin-text: #475569;
  --color-module-admin-hover: #e2e8f0;

  /* Financeiro */
  --color-module-financial-bg: #ecfdf5;
  --color-module-financial-border: #6ee7b7;
  --color-module-financial-icon: #10b981;
  --color-module-financial-text: #047857;
  --color-module-financial-hover: #d1fae5;

  /* Dashboard */
  --color-module-dashboard-bg: #eff6ff;
  --color-module-dashboard-border: #60a5fa;
  --color-module-dashboard-icon: #2563eb;
  --color-module-dashboard-text: #1e40af;
  --color-module-dashboard-hover: #bfdbfe;
}

/* Modo Escuro */
.dark {
  /* Sistema de sombras - Modo Escuro */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4);
  --shadow-default: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.24);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.16);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.24);

  /* Cores de módulos - Modo Escuro */
  /* Pessoas */
  --color-module-people-bg-dark: #7c2d12;
  --color-module-people-border-dark: #c2410c;
  --color-module-people-icon-dark: #fb923c;
  --color-module-people-text-dark: #fdba74;
  --color-module-people-hover-dark: #9a3412;

  /* Agendamento */
  --color-module-scheduler-bg-dark: #5b21b6;
  --color-module-scheduler-border-dark: #7c3aed;
  --color-module-scheduler-icon-dark: #a78bfa;
  --color-module-scheduler-text-dark: #c4b5fd;
  --color-module-scheduler-hover-dark: #4c1d95;

  /* Administração */
  --color-module-admin-bg-dark: #334155;
  --color-module-admin-border-dark: #64748b;
  --color-module-admin-icon-dark: #94a3b8;
  --color-module-admin-text-dark: #e2e8f0;
  --color-module-admin-hover-dark: #475569;

  /* Financeiro */
  --color-module-financial-bg-dark: #065f46;
  --color-module-financial-border-dark: #059669;
  --color-module-financial-icon-dark: #34d399;
  --color-module-financial-text-dark: #6ee7b7;
  --color-module-financial-hover-dark: #047857;

  /* Dashboard */
  --color-module-dashboard-bg-dark: #1e3a8a;
  --color-module-dashboard-border-dark: #3b82f6;
  --color-module-dashboard-icon-dark: #93c5fd;
  --color-module-dashboard-text-dark: #dbeafe;
  --color-module-dashboard-hover-dark: #1e40af;
}

/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/scrollbar.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* Estilos de scrollbar personalizados para diferentes módulos */

/* Estilo base para scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5); /* Cor padrão - cinza */
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode base */
.dark::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Aplicar ao body - com maior especificidade */
html body::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

html body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 0;
}

html body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  border: 3px solid transparent;
  background-clip: content-box;
  background-color: rgba(156, 163, 175, 0.5);
}

html body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Dark mode para body - com maior especificidade */
html.dark body::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

html.dark body::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

html.dark body::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.7);
}

/* Módulo People (laranja) */
.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4); /* orange-500 com opacidade */
}

.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6);
}

.dark.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3);
}

.dark.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5);
}

/* Body - Módulo People - com maior especificidade */
html body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.4) !important;
}

html body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.6) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb {
  background-color: rgba(249, 115, 22, 0.3) !important;
}

html.dark body.module-people::-webkit-scrollbar-thumb:hover {
  background-color: rgba(249, 115, 22, 0.5) !important;
}

/* Módulo Scheduler (roxo) */
.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4); /* violet-600 com opacidade */
}

.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6);
}

.dark.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3);
}

.dark.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5);
}

/* Body - Módulo Scheduler - com maior especificidade */
html body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.4) !important;
}

html body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.6) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb {
  background-color: rgba(147, 51, 234, 0.3) !important;
}

html.dark body.module-scheduler::-webkit-scrollbar-thumb:hover {
  background-color: rgba(147, 51, 234, 0.5) !important;
}

/* Módulo Admin (cinza/slate) */
.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4); /* slate-500 com opacidade */
}

.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6);
}

.dark.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3);
}

.dark.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5);
}

/* Body - Módulo Admin - com maior especificidade */
html body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.4) !important;
}

html body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.6) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 116, 139, 0.3) !important;
}

html.dark body.module-admin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(100, 116, 139, 0.5) !important;
}

/* Módulo Financial (verde) */
.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4); /* emerald-600 com opacidade */
}

.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6);
}

.dark.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3);
}

.dark.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5);
}

/* Body - Módulo Financial - com maior especificidade */
html body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.4) !important;
}

html body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.6) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb {
  background-color: rgba(5, 150, 105, 0.3) !important;
}

html.dark body.module-financial::-webkit-scrollbar-thumb:hover {
  background-color: rgba(5, 150, 105, 0.5) !important;
}

/* Estilo para modais */
.modal-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  border-width: 1px;
}

/* Aplicar scrollbar personalizado a elementos específicos */
.custom-scrollbar {
  scrollbar-width: thin; /* Para Firefox */
  scrollbar-color: rgba(156, 163, 175, 0.5) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Aplicar scrollbar personalizado a elementos específicos por módulo */
.custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

.custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.4) rgba(0, 0, 0, 0.05); /* Para Firefox */
}

/* Dark mode para Firefox */
.dark .custom-scrollbar {
  scrollbar-color: rgba(75, 85, 99, 0.5) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-people {
  scrollbar-color: rgba(249, 115, 22, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-scheduler {
  scrollbar-color: rgba(147, 51, 234, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-admin {
  scrollbar-color: rgba(100, 116, 139, 0.3) rgba(255, 255, 255, 0.05);
}

.dark .custom-scrollbar.module-financial {
  scrollbar-color: rgba(5, 150, 105, 0.3) rgba(255, 255, 255, 0.05);
}

/*!************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/chat-modern.css ***!
  \************************************************************************************************************************************************************************************************************************************************************************/
/* Estilos personalizados para o Chat modernizado */

/* Scrollbar personalizada */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #06b6d4, #0891b2);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #0891b2, #0e7490);
}

/* Efeitos de hover suaves */
.chat-container * {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Animações de entrada */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Efeitos de brilho */
.glow-effect {
  position: relative;
  overflow: hidden;
}

.glow-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.glow-effect:hover::before {
  left: 100%;
}

/* Efeitos de sombra dinâmicos */
.shadow-dynamic {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.3s ease;
}

.shadow-dynamic:hover {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Gradientes animados */
.gradient-animated {
  background: linear-gradient(-45deg, #06b6d4, #0891b2, #0e7490, #06b6d4);
  background-size: 400% 400%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Efeitos de pulso */
.pulse-gentle {
  animation: pulseGentle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulseGentle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* Bordas com gradiente */
.border-gradient {
  position: relative;
  background: white;
  border-radius: 1rem;
}

.border-gradient::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, #06b6d4, #0891b2);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

/* Efeitos de texto */
.text-glow {
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.5);
}

/* Animações de loading personalizadas */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loadingDots 1.5s infinite;
}

@keyframes loadingDots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* Efeitos de transição suaves para mensagens */
.message-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.message-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.message-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Efeitos de hover para botões */
.button-hover-effect {
  position: relative;
  overflow: hidden;
}

.button-hover-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
  transition: left 0.5s;
}

.button-hover-effect:hover::after {
  left: 100%;
}

/* Anéis de foco personalizados */
.focus-ring {
  transition: all 0.2s ease;
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.3);
}

/* Efeitos de blur personalizados */
.backdrop-blur-custom {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Efeitos de vidro */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animações de notificação */
.notification-bounce {
  animation: notificationBounce 0.6s ease-out;
}

@keyframes notificationBounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Efeito shimmer */
.shimmer {
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .chat-container {
    border-radius: 1rem;
  }

  .message-bubble {
    border-radius: 1rem;
  }

  .chat-button {
    bottom: 1rem;
    right: 1rem;
  }
}

/* Modo escuro */
@media (prefers-color-scheme: dark) {
  .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .shadow-dynamic {
    box-shadow: 
      0 4px 6px -1px rgba(0, 0, 0, 0.3),
      0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }

  .shadow-dynamic:hover {
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.3),
      0 4px 6px -2px rgba(0, 0, 0, 0.2);
  }
} 
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/hover-improvements.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* Melhorias de Hover para o Sistema High Tide */

/* ===== HOVERS PARA NAVEGAÇÃO DE MÓDULOS ===== */

/* Hover para itens de navegação de módulos */
.custom-scrollbar.module-people .group:hover {
  background-color: rgba(249, 115, 22, 0.15) !important;
  border-color: rgba(249, 115, 22, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.2);
}

.custom-scrollbar.module-scheduler .group:hover {
  background-color: rgba(147, 51, 234, 0.15) !important;
  border-color: rgba(147, 51, 234, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.2);
}

.custom-scrollbar.module-admin .group:hover {
  background-color: rgba(100, 116, 139, 0.15) !important;
  border-color: rgba(100, 116, 139, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.2);
}

.custom-scrollbar.module-financial .group:hover {
  background-color: rgba(5, 150, 105, 0.15) !important;
  border-color: rgba(5, 150, 105, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.2);
}

.custom-scrollbar.module-abaplus .group:hover {
  background-color: rgba(13, 148, 136, 0.15) !important;
  border-color: rgba(13, 148, 136, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.2);
}

/* Dark mode para hovers de navegação */
.dark .custom-scrollbar.module-people .group:hover {
  background-color: rgba(249, 115, 22, 0.25) !important;
  border-color: rgba(249, 115, 22, 0.4) !important;
  box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
}

.dark .custom-scrollbar.module-scheduler .group:hover {
  background-color: rgba(147, 51, 234, 0.25) !important;
  border-color: rgba(147, 51, 234, 0.4) !important;
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
}

.dark .custom-scrollbar.module-admin .group:hover {
  background-color: rgba(100, 116, 139, 0.25) !important;
  border-color: rgba(100, 116, 139, 0.4) !important;
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.dark .custom-scrollbar.module-financial .group:hover {
  background-color: rgba(5, 150, 105, 0.25) !important;
  border-color: rgba(5, 150, 105, 0.4) !important;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.dark .custom-scrollbar.module-abaplus .group:hover {
  background-color: rgba(13, 148, 136, 0.25) !important;
  border-color: rgba(13, 148, 136, 0.4) !important;
  box-shadow: 0 4px 12px rgba(13, 148, 136, 0.3);
}

/* ===== HOVERS PARA ÍCONES DO HEADER ===== */

/* Chat Button */
button[aria-label="Mensagens"]:hover {
  background-color: rgba(6, 182, 212, 0.15) !important;
  color: #0891b2 !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
}

.dark button[aria-label="Mensagens"]:hover {
  background-color: rgba(6, 182, 212, 0.25) !important;
  color: #22d3ee !important;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

/* Notification Button */
button[aria-label="Notificações"]:hover {
  background-color: rgba(6, 182, 212, 0.15) !important;
  color: #0891b2 !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
}

.dark button[aria-label="Notificações"]:hover {
  background-color: rgba(6, 182, 212, 0.25) !important;
  color: #22d3ee !important;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

/* Bug Report Button */
button[aria-label="Reportar Bug"]:hover {
  background-color: rgba(239, 68, 68, 0.15) !important;
  color: #dc2626 !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.dark button[aria-label="Reportar Bug"]:hover {
  background-color: rgba(239, 68, 68, 0.25) !important;
  color: #f87171 !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Theme Toggle Button */
button[aria-label="Alternar tema"]:hover,
button[aria-label="Ativar modo escuro"]:hover,
button[aria-label="Ativar modo claro"]:hover {
  background-color: rgba(156, 163, 175, 0.25) !important;
  color: #4b5563 !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(156, 163, 175, 0.3);
}

.dark button[aria-label="Alternar tema"]:hover,
.dark button[aria-label="Ativar modo escuro"]:hover,
.dark button[aria-label="Ativar modo claro"]:hover {
  background-color: rgba(156, 163, 175, 0.35) !important;
  color: #d1d5db !important;
  box-shadow: 0 4px 12px rgba(156, 163, 175, 0.4);
}

/* ===== HOVERS PARA BOTÕES DE AÇÃO RÁPIDA ===== */

/* Botões de calendário e agendamentos no header */
button[aria-label="Calendário"]:hover,
button[aria-label="Meus Agendamentos"]:hover {
  background-color: rgba(147, 51, 234, 0.15) !important;
  color: #7c3aed !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.2);
}

.dark button[aria-label="Calendário"]:hover,
.dark button[aria-label="Meus Agendamentos"]:hover {
  background-color: rgba(147, 51, 234, 0.25) !important;
  color: #a78bfa !important;
  box-shadow: 0 4px 12px rgba(147, 51, 234, 0.3);
}

/* ===== HOVERS PARA ÍCONES DE CONFIGURAÇÕES E NOVIDADES ===== */

/* Botão de Configurações */
button[aria-label="Configurações"]:hover {
  background-color: rgba(59, 130, 246, 0.25) !important;
  color: #2563eb !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.dark button[aria-label="Configurações"]:hover {
  background-color: rgba(59, 130, 246, 0.35) !important;
  color: #3b82f6 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Botão de Novidades do Sistema */
button[aria-label="Novidades"]:hover,
button[title="Novidades do sistema"]:hover {
  background-color: rgba(245, 158, 11, 0.25) !important;
  color: #d97706 !important;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.dark button[aria-label="Novidades"]:hover,
.dark button[title="Novidades do sistema"]:hover {
  background-color: rgba(245, 158, 11, 0.35) !important;
  color: #f59e0b !important;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
}

/* ===== HOVERS PARA BOTÕES DE PESQUISA ===== */

/* Botão de pesquisa rápida */
button[aria-label="Abrir pesquisa rápida"]:hover {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.dark button[aria-label="Abrir pesquisa rápida"]:hover {
  background-color: rgba(59, 130, 246, 0.25) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* ===== HOVERS PARA BOTÕES DE AÇÃO EM TABELAS ===== */

/* Botões de ação (editar, excluir, etc.) */
button[title="Editar"]:hover,
button[title="Ver usuários com esta profissão"]:hover {
  background-color: rgba(59, 130, 246, 0.15) !important;
  color: #2563eb !important;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.dark button[title="Editar"]:hover,
.dark button[title="Ver usuários com esta profissão"]:hover {
  background-color: rgba(59, 130, 246, 0.25) !important;
  color: #3b82f6 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

button[title="Excluir"]:hover {
  background-color: rgba(239, 68, 68, 0.15) !important;
  color: #dc2626 !important;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.dark button[title="Excluir"]:hover {
  background-color: rgba(239, 68, 68, 0.25) !important;
  color: #ef4444 !important;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== HOVERS PARA BOTÕES DE FILTRO ===== */

/* Botões de filtro */
button[title="Atualizar lista"]:hover {
  background-color: rgba(34, 197, 94, 0.15) !important;
  color: #16a34a !important;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.dark button[title="Atualizar lista"]:hover {
  background-color: rgba(34, 197, 94, 0.25) !important;
  color: #22c55e !important;
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* ===== HOVERS PARA BOTÕES DE ADICIONAR ===== */

/* Botões de adicionar novo item */
button:has(span:contains("Nova Profissão")),
button:has(span:contains("Novo Grupo")) {
  transition: all 0.3s ease;
}

button:has(span:contains("Nova Profissão")):hover,
button:has(span:contains("Novo Grupo")):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.dark button:has(span:contains("Nova Profissão")):hover,
.dark button:has(span:contains("Novo Grupo")):hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

/* ===== HOVERS PARA ITENS DE NAVEGAÇÃO RÁPIDA ===== */

/* Itens do QuickNav */
.quick-nav-item:hover {
  background-color: rgba(59, 130, 246, 0.15) !important;
  border-color: rgba(59, 130, 246, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.dark .quick-nav-item:hover {
  background-color: rgba(59, 130, 246, 0.25) !important;
  border-color: rgba(59, 130, 246, 0.4) !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* ===== TRANSITIONS GLOBAIS ===== */

/* Aplicar transições suaves a todos os elementos interativos */
button, 
a, 
[role="button"],
[tabindex]:not([tabindex="-1"]) {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* ===== HOVERS ESPECÍFICOS PARA MÓDULOS ===== */

/* Módulo People - hovers específicos */
.module-people button:hover:not([disabled]) {
  border-color: rgba(249, 115, 22, 0.3) !important;
}

/* Módulo Scheduler - hovers específicos */
.module-scheduler button:hover:not([disabled]) {
  border-color: rgba(147, 51, 234, 0.3) !important;
}

/* Módulo Admin - hovers específicos */
.module-admin button:hover:not([disabled]) {
  border-color: rgba(100, 116, 139, 0.3) !important;
}

/* Módulo Financial - hovers específicos */
.module-financial button:hover:not([disabled]) {
  border-color: rgba(5, 150, 105, 0.3) !important;
}

/* ===== MELHORIAS PARA ELEMENTOS DESABILITADOS ===== */

button:disabled,
button[disabled] {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* ===== RESPONSIVIDADE ===== */

@media (max-width: 768px) {
  /* Reduzir efeitos de hover em dispositivos móveis */
  button:hover,
  a:hover,
  [role="button"]:hover {
    transform: none !important;
    box-shadow: none !important;
  }
  
  /* Manter apenas mudanças de cor em mobile */
  .custom-scrollbar .group:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/* Importar estilos de design system e scrollbar */

*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

.dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

*{
  border-color: hsl(var(--border));
}

body{
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.container{
  width: 100%;
}
@media (min-width: 640px){

  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){

  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){

  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){

  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){

  .container{
    max-width: 1536px;
  }
}
.sr-only{
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none{
  pointer-events: none;
}
.pointer-events-auto{
  pointer-events: auto;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.collapse{
  visibility: collapse;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.sticky{
  position: sticky;
}
.inset-0{
  inset: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.-bottom-1{
  bottom: -0.25rem;
}
.-bottom-2{
  bottom: -0.5rem;
}
.-bottom-20{
  bottom: -5rem;
}
.-bottom-24{
  bottom: -6rem;
}
.-bottom-4{
  bottom: -1rem;
}
.-bottom-6{
  bottom: -1.5rem;
}
.-bottom-8{
  bottom: -2rem;
}
.-left-1{
  left: -0.25rem;
}
.-left-2{
  left: -0.5rem;
}
.-left-2\.5{
  left: -0.625rem;
}
.-left-20{
  left: -5rem;
}
.-left-24{
  left: -6rem;
}
.-left-4{
  left: -1rem;
}
.-right-1{
  right: -0.25rem;
}
.-right-2{
  right: -0.5rem;
}
.-right-20{
  right: -5rem;
}
.-right-24{
  right: -6rem;
}
.-right-4{
  right: -1rem;
}
.-top-1{
  top: -0.25rem;
}
.-top-2{
  top: -0.5rem;
}
.-top-20{
  top: -5rem;
}
.-top-24{
  top: -6rem;
}
.-top-4{
  top: -1rem;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1{
  bottom: 0.25rem;
}
.bottom-16{
  bottom: 4rem;
}
.bottom-2{
  bottom: 0.5rem;
}
.bottom-24{
  bottom: 6rem;
}
.bottom-4{
  bottom: 1rem;
}
.bottom-6{
  bottom: 1.5rem;
}
.bottom-full{
  bottom: 100%;
}
.left-0{
  left: 0px;
}
.left-1\/2{
  left: 50%;
}
.left-10{
  left: 2.5rem;
}
.left-2{
  left: 0.5rem;
}
.left-20{
  left: 5rem;
}
.left-3{
  left: 0.75rem;
}
.left-4{
  left: 1rem;
}
.left-6{
  left: 1.5rem;
}
.left-7{
  left: 1.75rem;
}
.left-8{
  left: 2rem;
}
.left-\[50\%\]{
  left: 50%;
}
.right-0{
  right: 0px;
}
.right-1{
  right: 0.25rem;
}
.right-10{
  right: 2.5rem;
}
.right-2{
  right: 0.5rem;
}
.right-20{
  right: 5rem;
}
.right-3{
  right: 0.75rem;
}
.right-4{
  right: 1rem;
}
.right-5{
  right: 1.25rem;
}
.right-6{
  right: 1.5rem;
}
.top-0{
  top: 0px;
}
.top-1{
  top: 0.25rem;
}
.top-1\/2{
  top: 50%;
}
.top-16{
  top: 4rem;
}
.top-2{
  top: 0.5rem;
}
.top-2\.5{
  top: 0.625rem;
}
.top-20{
  top: 5rem;
}
.top-24{
  top: 6rem;
}
.top-3{
  top: 0.75rem;
}
.top-32{
  top: 8rem;
}
.top-36{
  top: 9rem;
}
.top-4{
  top: 1rem;
}
.top-40{
  top: 10rem;
}
.top-6{
  top: 1.5rem;
}
.top-8{
  top: 2rem;
}
.top-\[50\%\]{
  top: 50%;
}
.top-full{
  top: 100%;
}
.-z-10{
  z-index: -10;
}
.z-0{
  z-index: 0;
}
.z-10{
  z-index: 10;
}
.z-20{
  z-index: 20;
}
.z-30{
  z-index: 30;
}
.z-50{
  z-index: 50;
}
.z-\[10000\]{
  z-index: 10000;
}
.z-\[1000\]{
  z-index: 1000;
}
.z-\[10050\]{
  z-index: 10050;
}
.z-\[11000\]{
  z-index: 11000;
}
.z-\[11050\]{
  z-index: 11050;
}
.z-\[12000\]{
  z-index: 12000;
}
.z-\[12050\]{
  z-index: 12050;
}
.z-\[13000\]{
  z-index: 13000;
}
.z-\[13050\]{
  z-index: 13050;
}
.z-\[14000\]{
  z-index: 14000;
}
.z-\[15000\]{
  z-index: 15000;
}
.z-\[15050\]{
  z-index: 15050;
}
.z-\[55\]{
  z-index: 55;
}
.z-\[9000\]{
  z-index: 9000;
}
.z-\[9998\]{
  z-index: 9998;
}
.z-\[99999\]{
  z-index: 99999;
}
.z-\[9999\]{
  z-index: 9999;
}
.col-span-2{
  grid-column: span 2 / span 2;
}
.col-span-full{
  grid-column: 1 / -1;
}
.-m-4{
  margin: -1rem;
}
.m-2{
  margin: 0.5rem;
}
.-mx-6{
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-4{
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6{
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.-ml-1{
  margin-left: -0.25rem;
}
.-mt-0\.5{
  margin-top: -0.125rem;
}
.-mt-1{
  margin-top: -0.25rem;
}
.-mt-1\.5{
  margin-top: -0.375rem;
}
.mb-0\.5{
  margin-bottom: 0.125rem;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-1\.5{
  margin-bottom: 0.375rem;
}
.mb-10{
  margin-bottom: 2.5rem;
}
.mb-12{
  margin-bottom: 3rem;
}
.mb-16{
  margin-bottom: 4rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-20{
  margin-bottom: 5rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.ml-0\.5{
  margin-left: 0.125rem;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-3{
  margin-left: 0.75rem;
}
.ml-4{
  margin-left: 1rem;
}
.ml-5{
  margin-left: 1.25rem;
}
.ml-6{
  margin-left: 1.5rem;
}
.ml-7{
  margin-left: 1.75rem;
}
.ml-8{
  margin-left: 2rem;
}
.ml-auto{
  margin-left: auto;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-2\.5{
  margin-right: 0.625rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.mr-4{
  margin-right: 1rem;
}
.mr-5{
  margin-right: 1.25rem;
}
.mt-0{
  margin-top: 0px;
}
.mt-0\.5{
  margin-top: 0.125rem;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-1\.5{
  margin-top: 0.375rem;
}
.mt-10{
  margin-top: 2.5rem;
}
.mt-12{
  margin-top: 3rem;
}
.mt-16{
  margin-top: 4rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-5{
  margin-top: 1.25rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-8{
  margin-top: 2rem;
}
.mt-auto{
  margin-top: auto;
}
.line-clamp-1{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2{
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.hidden{
  display: none;
}
.aspect-\[16\/10\]{
  aspect-ratio: 16/10;
}
.aspect-square{
  aspect-ratio: 1 / 1;
}
.aspect-video{
  aspect-ratio: 16 / 9;
}
.h-0{
  height: 0px;
}
.h-0\.5{
  height: 0.125rem;
}
.h-1{
  height: 0.25rem;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-14{
  height: 3.5rem;
}
.h-16{
  height: 4rem;
}
.h-2{
  height: 0.5rem;
}
.h-2\.5{
  height: 0.625rem;
}
.h-20{
  height: 5rem;
}
.h-24{
  height: 6rem;
}
.h-3{
  height: 0.75rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-32{
  height: 8rem;
}
.h-4{
  height: 1rem;
}
.h-4\/5{
  height: 80%;
}
.h-40{
  height: 10rem;
}
.h-48{
  height: 12rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-64{
  height: 16rem;
}
.h-7{
  height: 1.75rem;
}
.h-72{
  height: 18rem;
}
.h-8{
  height: 2rem;
}
.h-80{
  height: 20rem;
}
.h-9{
  height: 2.25rem;
}
.h-96{
  height: 24rem;
}
.h-\[300px\]{
  height: 300px;
}
.h-\[36rem\]{
  height: 36rem;
}
.h-\[42px\]{
  height: 42px;
}
.h-\[500px\]{
  height: 500px;
}
.h-\[600px\]{
  height: 600px;
}
.h-\[85vh\]{
  height: 85vh;
}
.h-\[90vh\]{
  height: 90vh;
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-px{
  height: 1px;
}
.h-screen{
  height: 100vh;
}
.max-h-20{
  max-height: 5rem;
}
.max-h-32{
  max-height: 8rem;
}
.max-h-40{
  max-height: 10rem;
}
.max-h-48{
  max-height: 12rem;
}
.max-h-60{
  max-height: 15rem;
}
.max-h-64{
  max-height: 16rem;
}
.max-h-80{
  max-height: 20rem;
}
.max-h-96{
  max-height: 24rem;
}
.max-h-\[180px\]{
  max-height: 180px;
}
.max-h-\[200px\]{
  max-height: 200px;
}
.max-h-\[28rem\]{
  max-height: 28rem;
}
.max-h-\[400px\]{
  max-height: 400px;
}
.max-h-\[500px\]{
  max-height: 500px;
}
.max-h-\[600px\]{
  max-height: 600px;
}
.max-h-\[60vh\]{
  max-height: 60vh;
}
.max-h-\[700px\]{
  max-height: 700px;
}
.max-h-\[70vh\]{
  max-height: 70vh;
}
.max-h-\[80vh\]{
  max-height: 80vh;
}
.max-h-\[85vh\]{
  max-height: 85vh;
}
.max-h-\[90vh\]{
  max-height: 90vh;
}
.max-h-\[95vh\]{
  max-height: 95vh;
}
.max-h-\[98vh\]{
  max-height: 98vh;
}
.max-h-\[calc\(85vh-100px\)\]{
  max-height: calc(85vh - 100px);
}
.max-h-\[calc\(90vh-120px\)\]{
  max-height: calc(90vh - 120px);
}
.max-h-\[calc\(90vh-140px\)\]{
  max-height: calc(90vh - 140px);
}
.max-h-\[calc\(90vh-200px\)\]{
  max-height: calc(90vh - 200px);
}
.max-h-\[calc\(90vh-80px\)\]{
  max-height: calc(90vh - 80px);
}
.max-h-full{
  max-height: 100%;
}
.max-h-screen{
  max-height: 100vh;
}
.min-h-0{
  min-height: 0px;
}
.min-h-\[100px\]{
  min-height: 100px;
}
.min-h-\[120px\]{
  min-height: 120px;
}
.min-h-\[20px\]{
  min-height: 20px;
}
.min-h-\[250px\]{
  min-height: 250px;
}
.min-h-\[270px\]{
  min-height: 270px;
}
.min-h-\[300px\]{
  min-height: 300px;
}
.min-h-\[320px\]{
  min-height: 320px;
}
.min-h-\[340px\]{
  min-height: 340px;
}
.min-h-\[400px\]{
  min-height: 400px;
}
.min-h-\[40px\]{
  min-height: 40px;
}
.min-h-\[40vh\]{
  min-height: 40vh;
}
.min-h-\[48px\]{
  min-height: 48px;
}
.min-h-\[60px\]{
  min-height: 60px;
}
.min-h-\[70px\]{
  min-height: 70px;
}
.min-h-\[80px\]{
  min-height: 80px;
}
.min-h-screen{
  min-height: 100vh;
}
.w-0{
  width: 0px;
}
.w-0\.5{
  width: 0.125rem;
}
.w-1{
  width: 0.25rem;
}
.w-1\.5{
  width: 0.375rem;
}
.w-1\/2{
  width: 50%;
}
.w-1\/3{
  width: 33.333333%;
}
.w-10{
  width: 2.5rem;
}
.w-11{
  width: 2.75rem;
}
.w-12{
  width: 3rem;
}
.w-14{
  width: 3.5rem;
}
.w-16{
  width: 4rem;
}
.w-2{
  width: 0.5rem;
}
.w-2\.5{
  width: 0.625rem;
}
.w-2\/3{
  width: 66.666667%;
}
.w-2\/4{
  width: 50%;
}
.w-20{
  width: 5rem;
}
.w-24{
  width: 6rem;
}
.w-3{
  width: 0.75rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-3\/4{
  width: 75%;
}
.w-32{
  width: 8rem;
}
.w-36{
  width: 9rem;
}
.w-4{
  width: 1rem;
}
.w-40{
  width: 10rem;
}
.w-48{
  width: 12rem;
}
.w-5{
  width: 1.25rem;
}
.w-5\/6{
  width: 83.333333%;
}
.w-6{
  width: 1.5rem;
}
.w-64{
  width: 16rem;
}
.w-7{
  width: 1.75rem;
}
.w-72{
  width: 18rem;
}
.w-8{
  width: 2rem;
}
.w-80{
  width: 20rem;
}
.w-9{
  width: 2.25rem;
}
.w-96{
  width: 24rem;
}
.w-\[28rem\]{
  width: 28rem;
}
.w-\[30rem\]{
  width: 30rem;
}
.w-\[80vw\]{
  width: 80vw;
}
.w-\[90vw\]{
  width: 90vw;
}
.w-fit{
  width: fit-content;
}
.w-full{
  width: 100%;
}
.min-w-0{
  min-width: 0px;
}
.min-w-\[100px\]{
  min-width: 100px;
}
.min-w-\[120px\]{
  min-width: 120px;
}
.min-w-\[130px\]{
  min-width: 130px;
}
.min-w-\[150px\]{
  min-width: 150px;
}
.min-w-\[180px\]{
  min-width: 180px;
}
.min-w-\[200px\]{
  min-width: 200px;
}
.min-w-\[20px\]{
  min-width: 20px;
}
.min-w-\[40px\]{
  min-width: 40px;
}
.min-w-\[90px\]{
  min-width: 90px;
}
.min-w-full{
  min-width: 100%;
}
.max-w-20{
  max-width: 5rem;
}
.max-w-2xl{
  max-width: 42rem;
}
.max-w-32{
  max-width: 8rem;
}
.max-w-3xl{
  max-width: 48rem;
}
.max-w-4xl{
  max-width: 56rem;
}
.max-w-5xl{
  max-width: 64rem;
}
.max-w-6xl{
  max-width: 72rem;
}
.max-w-7xl{
  max-width: 80rem;
}
.max-w-\[1400px\]{
  max-width: 1400px;
}
.max-w-\[140px\]{
  max-width: 140px;
}
.max-w-\[150px\]{
  max-width: 150px;
}
.max-w-\[160px\]{
  max-width: 160px;
}
.max-w-\[300px\]{
  max-width: 300px;
}
.max-w-\[70\%\]{
  max-width: 70%;
}
.max-w-\[75\%\]{
  max-width: 75%;
}
.max-w-\[78vw\]{
  max-width: 78vw;
}
.max-w-\[80px\]{
  max-width: 80px;
}
.max-w-\[85\%\]{
  max-width: 85%;
}
.max-w-\[90vw\]{
  max-width: 90vw;
}
.max-w-full{
  max-width: 100%;
}
.max-w-lg{
  max-width: 32rem;
}
.max-w-md{
  max-width: 28rem;
}
.max-w-none{
  max-width: none;
}
.max-w-screen-2xl{
  max-width: 1536px;
}
.max-w-screen-lg{
  max-width: 1024px;
}
.max-w-screen-md{
  max-width: 768px;
}
.max-w-screen-sm{
  max-width: 640px;
}
.max-w-screen-xl{
  max-width: 1280px;
}
.max-w-sm{
  max-width: 24rem;
}
.max-w-xl{
  max-width: 36rem;
}
.max-w-xs{
  max-width: 20rem;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.flex-grow{
  flex-grow: 1;
}
.border-collapse{
  border-collapse: collapse;
}
.origin-top-left{
  transform-origin: top left;
}
.origin-top-right{
  transform-origin: top right;
}
.-translate-x-1\/2{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-12{
  --tw-translate-x: -3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-full{
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0{
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1\/2{
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-12{
  --tw-translate-x: 3rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-5{
  --tw-translate-x: 1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-6{
  --tw-translate-x: 1.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-9{
  --tw-translate-x: 2.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-50\%\]{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0{
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-1\/2{
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-2{
  --tw-translate-y: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-\[-50\%\]{
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-12{
  --tw-rotate: -12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-12{
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180{
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45{
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150{
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95{
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce{

  0%, 100%{
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50%{
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce{
  animation: bounce 1s infinite;
}
@keyframes ping{

  75%, 100%{
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping{
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse{

  50%{
    opacity: .5;
  }
}
.animate-pulse{
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes spin{

  to{
    transform: rotate(360deg);
  }
}
.animate-spin{
  animation: spin 1s linear infinite;
}
.cursor-help{
  cursor: help;
}
.cursor-move{
  cursor: move;
}
.cursor-not-allowed{
  cursor: not-allowed;
}
.cursor-pointer{
  cursor: pointer;
}
.cursor-zoom-in{
  cursor: zoom-in;
}
.select-none{
  user-select: none;
}
.select-all{
  user-select: all;
}
.resize-none{
  resize: none;
}
.resize{
  resize: both;
}
.list-inside{
  list-style-position: inside;
}
.list-outside{
  list-style-position: outside;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.appearance-none{
  appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2{
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3{
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4{
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-7{
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-8{
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-row-reverse{
  flex-direction: row-reverse;
}
.flex-col{
  flex-direction: column;
}
.flex-col-reverse{
  flex-direction: column-reverse;
}
.flex-wrap{
  flex-wrap: wrap;
}
.place-content-around{
  place-content: space-around;
}
.items-start{
  align-items: flex-start;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-baseline{
  align-items: baseline;
}
.justify-start{
  justify-content: flex-start;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.justify-items-center{
  justify-items: center;
}
.gap-0{
  gap: 0px;
}
.gap-0\.5{
  gap: 0.125rem;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-10{
  gap: 2.5rem;
}
.gap-12{
  gap: 3rem;
}
.gap-16{
  gap: 4rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-5{
  gap: 1.25rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-8{
  gap: 2rem;
}
.gap-x-4{
  column-gap: 1rem;
}
.gap-x-8{
  column-gap: 2rem;
}
.gap-y-1{
  row-gap: 0.25rem;
}
.gap-y-2{
  row-gap: 0.5rem;
}
.gap-y-4{
  row-gap: 1rem;
}
.space-x-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-16 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]){
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]){
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-gray-100 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-gray-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.divide-module-admin-border > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-divide-opacity, 1));
}
.divide-neutral-100 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-divide-opacity, 1));
}
.divide-neutral-200 > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.overflow-auto{
  overflow: auto;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-visible{
  overflow: visible;
}
.overflow-x-auto{
  overflow-x: auto;
}
.overflow-y-auto{
  overflow-y: auto;
}
.truncate{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-ellipsis{
  text-overflow: ellipsis;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.whitespace-pre-line{
  white-space: pre-line;
}
.whitespace-pre-wrap{
  white-space: pre-wrap;
}
.break-words{
  overflow-wrap: break-word;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-2xl{
  border-radius: 1rem;
}
.rounded-3xl{
  border-radius: 1.5rem;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: var(--radius);
}
.rounded-md{
  border-radius: calc(var(--radius) - 2px);
}
.rounded-sm{
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl{
  border-radius: 0.75rem;
}
.rounded-b-lg{
  border-bottom-right-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.rounded-b-xl{
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-r-md{
  border-top-right-radius: calc(var(--radius) - 2px);
  border-bottom-right-radius: calc(var(--radius) - 2px);
}
.rounded-t-2xl{
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-lg{
  border-top-left-radius: var(--radius);
  border-top-right-radius: var(--radius);
}
.rounded-t-xl{
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-bl-none{
  border-bottom-left-radius: 0px;
}
.rounded-br-none{
  border-bottom-right-radius: 0px;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-8{
  border-width: 8px;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-2{
  border-bottom-width: 2px;
}
.border-l{
  border-left-width: 1px;
}
.border-l-2{
  border-left-width: 2px;
}
.border-l-4{
  border-left-width: 4px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-4{
  border-right-width: 4px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-t-2{
  border-top-width: 2px;
}
.border-t-4{
  border-top-width: 4px;
}
.border-dashed{
  border-style: dashed;
}
.border-amber-200{
  --tw-border-opacity: 1;
  border-color: rgb(253 230 138 / var(--tw-border-opacity, 1));
}
.border-amber-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}
.border-amber-400{
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.border-amber-500{
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.border-appointmentStatus-CANCELLED-border{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-appointmentStatus-COMPLETED-border{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-appointmentStatus-CONFIRMED-border{
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.border-appointmentStatus-NO_SHOW-border{
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}
.border-appointmentStatus-PENDING-border{
  --tw-border-opacity: 1;
  border-color: rgb(252 211 77 / var(--tw-border-opacity, 1));
}
.border-blue-100{
  --tw-border-opacity: 1;
  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));
}
.border-blue-200{
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-300{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.border-blue-400{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-blue-500{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.border-blue-600{
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.border-current{
  border-color: currentColor;
}
.border-cyan-100{
  --tw-border-opacity: 1;
  border-color: rgb(207 250 254 / var(--tw-border-opacity, 1));
}
.border-cyan-200{
  --tw-border-opacity: 1;
  border-color: rgb(165 243 252 / var(--tw-border-opacity, 1));
}
.border-cyan-200\/50{
  border-color: rgb(165 243 252 / 0.5);
}
.border-cyan-400{
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));
}
.border-cyan-400\/20{
  border-color: rgb(34 211 238 / 0.2);
}
.border-cyan-500{
  --tw-border-opacity: 1;
  border-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}
.border-emerald-200{
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}
.border-emerald-500{
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.border-emerald-600{
  --tw-border-opacity: 1;
  border-color: rgb(5 150 105 / var(--tw-border-opacity, 1));
}
.border-error-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-gray-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-gray-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-gray-400{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-gray-700{
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-gray-900{
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-green-100{
  --tw-border-opacity: 1;
  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));
}
.border-green-200{
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));
}
.border-green-300{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.border-green-400{
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-green-500{
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.border-indigo-200{
  --tw-border-opacity: 1;
  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));
}
.border-indigo-600{
  --tw-border-opacity: 1;
  border-color: rgb(79 70 229 / var(--tw-border-opacity, 1));
}
.border-module-abaplus-border{
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.border-module-abaplus-border\/30{
  border-color: rgb(45 212 191 / 0.3);
}
.border-module-admin-border{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.border-module-admin-border\/30{
  border-color: rgb(100 116 139 / 0.3);
}
.border-module-chat-border{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-module-dashboard-border{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-module-financial-border{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.border-module-financial-border\/30{
  border-color: rgb(52 211 153 / 0.3);
}
.border-module-hr-border{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-module-people-border{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.border-module-people-border\/30{
  border-color: rgb(251 146 60 / 0.3);
}
.border-module-scheduler-border{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-module-scheduler-border\/30{
  border-color: rgb(192 132 252 / 0.3);
}
.border-module-scheduler-icon{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.border-neutral-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));
}
.border-neutral-200{
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-neutral-300{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-neutral-700{
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.border-orange-100{
  --tw-border-opacity: 1;
  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));
}
.border-orange-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));
}
.border-orange-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.border-orange-400{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.border-orange-500{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.border-orange-600{
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}
.border-pink-500{
  --tw-border-opacity: 1;
  border-color: rgb(236 72 153 / var(--tw-border-opacity, 1));
}
.border-primary-100{
  --tw-border-opacity: 1;
  border-color: rgb(255 228 204 / var(--tw-border-opacity, 1));
}
.border-primary-200{
  --tw-border-opacity: 1;
  border-color: rgb(255 213 168 / var(--tw-border-opacity, 1));
}
.border-primary-300{
  --tw-border-opacity: 1;
  border-color: rgb(255 194 133 / var(--tw-border-opacity, 1));
}
.border-primary-500{
  --tw-border-opacity: 1;
  border-color: rgb(255 153 51 / var(--tw-border-opacity, 1));
}
.border-purple-100{
  --tw-border-opacity: 1;
  border-color: rgb(243 232 255 / var(--tw-border-opacity, 1));
}
.border-purple-200{
  --tw-border-opacity: 1;
  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));
}
.border-purple-300{
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.border-purple-400{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-purple-500{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.border-purple-600{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.border-purple-700{
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}
.border-red-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));
}
.border-red-300{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.border-red-500{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.border-red-600{
  --tw-border-opacity: 1;
  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));
}
.border-rose-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 205 211 / var(--tw-border-opacity, 1));
}
.border-slate-200{
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
}
.border-slate-300{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.border-slate-400{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.border-slate-500{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.border-slate-600{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.border-teal-200{
  --tw-border-opacity: 1;
  border-color: rgb(153 246 228 / var(--tw-border-opacity, 1));
}
.border-teal-300{
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}
.border-teal-500{
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.border-teal-600{
  --tw-border-opacity: 1;
  border-color: rgb(13 148 136 / var(--tw-border-opacity, 1));
}
.border-transparent{
  border-color: transparent;
}
.border-violet-200{
  --tw-border-opacity: 1;
  border-color: rgb(221 214 254 / var(--tw-border-opacity, 1));
}
.border-violet-500{
  --tw-border-opacity: 1;
  border-color: rgb(139 92 246 / var(--tw-border-opacity, 1));
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/20{
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30{
  border-color: rgb(255 255 255 / 0.3);
}
.border-yellow-200{
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));
}
.border-yellow-300{
  --tw-border-opacity: 1;
  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));
}
.border-yellow-500{
  --tw-border-opacity: 1;
  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));
}
.border-yellow-600{
  --tw-border-opacity: 1;
  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));
}
.border-red-400{
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.border-l-blue-400{
  --tw-border-opacity: 1;
  border-left-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.border-l-gray-400{
  --tw-border-opacity: 1;
  border-left-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.border-l-green-400{
  --tw-border-opacity: 1;
  border-left-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.border-l-module-admin-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.border-l-module-financial-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.border-l-module-people-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.border-l-module-scheduler-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.border-l-yellow-400{
  --tw-border-opacity: 1;
  border-left-color: rgb(250 204 21 / var(--tw-border-opacity, 1));
}
.border-t-cyan-500{
  --tw-border-opacity: 1;
  border-top-color: rgb(6 182 212 / var(--tw-border-opacity, 1));
}
.border-t-gray-800{
  --tw-border-opacity: 1;
  border-top-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.border-t-gray-900{
  --tw-border-opacity: 1;
  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.border-t-transparent{
  border-top-color: transparent;
}
.bg-\[\#7c3aed\]{
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}
.bg-amber-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 243 199 / var(--tw-bg-opacity, 1));
}
.bg-amber-200{
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}
.bg-amber-200\/30{
  background-color: rgb(253 230 138 / 0.3);
}
.bg-amber-300{
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 77 / var(--tw-bg-opacity, 1));
}
.bg-amber-400{
  --tw-bg-opacity: 1;
  background-color: rgb(251 191 36 / var(--tw-bg-opacity, 1));
}
.bg-amber-400\/60{
  background-color: rgb(251 191 36 / 0.6);
}
.bg-amber-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-amber-500\/80{
  background-color: rgb(245 158 11 / 0.8);
}
.bg-amber-600{
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.bg-appointmentStatus-CANCELLED-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-appointmentStatus-COMPLETED-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-appointmentStatus-CONFIRMED-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-appointmentStatus-NO_SHOW-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-appointmentStatus-PENDING-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-background{
  background-color: hsl(var(--background));
}
.bg-black{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/0{
  background-color: rgb(0 0 0 / 0);
}
.bg-black\/10{
  background-color: rgb(0 0 0 / 0.1);
}
.bg-black\/30{
  background-color: rgb(0 0 0 / 0.3);
}
.bg-black\/40{
  background-color: rgb(0 0 0 / 0.4);
}
.bg-black\/50{
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60{
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/70{
  background-color: rgb(0 0 0 / 0.7);
}
.bg-black\/80{
  background-color: rgb(0 0 0 / 0.8);
}
.bg-black\/90{
  background-color: rgb(0 0 0 / 0.9);
}
.bg-blue-100{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-200\/30{
  background-color: rgb(191 219 254 / 0.3);
}
.bg-blue-300{
  --tw-bg-opacity: 1;
  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));
}
.bg-blue-400{
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/60{
  background-color: rgb(96 165 250 / 0.6);
}
.bg-blue-50{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-blue-50\/50{
  background-color: rgb(239 246 255 / 0.5);
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/80{
  background-color: rgb(59 130 246 / 0.8);
}
.bg-blue-600{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.bg-current{
  background-color: currentColor;
}
.bg-cyan-100{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.bg-cyan-200\/30{
  background-color: rgb(165 243 252 / 0.3);
}
.bg-cyan-400{
  --tw-bg-opacity: 1;
  background-color: rgb(34 211 238 / var(--tw-bg-opacity, 1));
}
.bg-cyan-400\/60{
  background-color: rgb(34 211 238 / 0.6);
}
.bg-cyan-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.bg-cyan-50\/50{
  background-color: rgb(236 254 255 / 0.5);
}
.bg-cyan-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-emerald-100{
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}
.bg-emerald-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-emerald-50\/50{
  background-color: rgb(236 253 245 / 0.5);
}
.bg-emerald-500{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-emerald-600{
  --tw-bg-opacity: 1;
  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));
}
.bg-error-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-error-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-gray-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-200\/30{
  background-color: rgb(229 231 235 / 0.3);
}
.bg-gray-300{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.bg-gray-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-gray-400\/60{
  background-color: rgb(156 163 175 / 0.6);
}
.bg-gray-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-50\/50{
  background-color: rgb(249 250 251 / 0.5);
}
.bg-gray-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/80{
  background-color: rgb(107 114 128 / 0.8);
}
.bg-gray-600{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.bg-gray-700{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-gray-800{
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.bg-gray-900{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.bg-green-100{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-200{
  --tw-bg-opacity: 1;
  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));
}
.bg-green-400{
  --tw-bg-opacity: 1;
  background-color: rgb(74 222 128 / var(--tw-bg-opacity, 1));
}
.bg-green-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.bg-green-50\/50{
  background-color: rgb(240 253 244 / 0.5);
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/20{
  background-color: rgb(34 197 94 / 0.2);
}
.bg-green-600{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.bg-green-700{
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.bg-indigo-100{
  --tw-bg-opacity: 1;
  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-400\/60{
  background-color: rgb(129 140 248 / 0.6);
}
.bg-indigo-50{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.bg-indigo-50\/50{
  background-color: rgb(238 242 255 / 0.5);
}
.bg-indigo-500{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.bg-indigo-600{
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}
.bg-module-abaplus-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-module-abaplus-bg\/10{
  background-color: rgb(240 253 250 / 0.1);
}
.bg-module-abaplus-bg\/5{
  background-color: rgb(240 253 250 / 0.05);
}
.bg-module-admin-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-module-admin-bg\/10{
  background-color: rgb(248 250 252 / 0.1);
}
.bg-module-admin-bg\/5{
  background-color: rgb(248 250 252 / 0.05);
}
.bg-module-chat-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-module-dashboard-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.bg-module-financial-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-module-financial-bg\/10{
  background-color: rgb(236 253 245 / 0.1);
}
.bg-module-financial-bg\/5{
  background-color: rgb(236 253 245 / 0.05);
}
.bg-module-hr-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-module-people-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-module-people-bg\/10{
  background-color: rgb(255 237 213 / 0.1);
}
.bg-module-people-bg\/5{
  background-color: rgb(255 237 213 / 0.05);
}
.bg-module-scheduler-bg{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-module-scheduler-bg\/10{
  background-color: rgb(243 232 255 / 0.1);
}
.bg-module-scheduler-bg\/5{
  background-color: rgb(243 232 255 / 0.05);
}
.bg-module-scheduler-border{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-module-scheduler-icon{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-neutral-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-neutral-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-neutral-400{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.bg-neutral-50{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-neutral-500{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-neutral-700{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.bg-orange-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-orange-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 215 170 / var(--tw-bg-opacity, 1));
}
.bg-orange-200\/30{
  background-color: rgb(254 215 170 / 0.3);
}
.bg-orange-300{
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}
.bg-orange-400{
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));
}
.bg-orange-400\/60{
  background-color: rgb(251 146 60 / 0.6);
}
.bg-orange-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-orange-50\/50{
  background-color: rgb(255 247 237 / 0.5);
}
.bg-orange-50\/80{
  background-color: rgb(255 247 237 / 0.8);
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.bg-orange-600{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.bg-pink-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));
}
.bg-pink-500{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));
}
.bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 204 / var(--tw-bg-opacity, 1));
}
.bg-primary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(255 213 168 / var(--tw-bg-opacity, 1));
}
.bg-primary-200\/30{
  background-color: rgb(255 213 168 / 0.3);
}
.bg-primary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(255 194 133 / var(--tw-bg-opacity, 1));
}
.bg-primary-300\/30{
  background-color: rgb(255 194 133 / 0.3);
}
.bg-primary-300\/60{
  background-color: rgb(255 194 133 / 0.6);
}
.bg-primary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(255 179 102 / var(--tw-bg-opacity, 1));
}
.bg-primary-400\/60{
  background-color: rgb(255 179 102 / 0.6);
}
.bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.bg-primary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(255 153 51 / var(--tw-bg-opacity, 1));
}
.bg-primary-500\/60{
  background-color: rgb(255 153 51 / 0.6);
}
.bg-primary-500\/80{
  background-color: rgb(255 153 51 / 0.8);
}
.bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(255 127 0 / var(--tw-bg-opacity, 1));
}
.bg-purple-100{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-200{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-200\/30{
  background-color: rgb(233 213 255 / 0.3);
}
.bg-purple-300{
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}
.bg-purple-400{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.bg-purple-400\/60{
  background-color: rgb(192 132 252 / 0.6);
}
.bg-purple-50{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.bg-purple-50\/50{
  background-color: rgb(250 245 255 / 0.5);
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.bg-purple-500\/20{
  background-color: rgb(168 85 247 / 0.2);
}
.bg-purple-500\/80{
  background-color: rgb(168 85 247 / 0.8);
}
.bg-purple-600{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.bg-red-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-200{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.bg-red-300{
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));
}
.bg-red-400{
  --tw-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));
}
.bg-red-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.bg-red-50\/50{
  background-color: rgb(254 242 242 / 0.5);
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.bg-red-700{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.bg-rose-100{
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 230 / var(--tw-bg-opacity, 1));
}
.bg-rose-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 242 / var(--tw-bg-opacity, 1));
}
.bg-rose-500{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity, 1));
}
.bg-secondary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(179 194 255 / var(--tw-bg-opacity, 1));
}
.bg-sky-400\/60{
  background-color: rgb(56 189 248 / 0.6);
}
.bg-slate-100{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.bg-slate-200{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.bg-slate-200\/30{
  background-color: rgb(226 232 240 / 0.3);
}
.bg-slate-300{
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}
.bg-slate-400{
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
.bg-slate-400\/60{
  background-color: rgb(148 163 184 / 0.6);
}
.bg-slate-50{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.bg-slate-500{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.bg-slate-500\/80{
  background-color: rgb(100 116 139 / 0.8);
}
.bg-slate-600{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.bg-success-500{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-teal-100{
  --tw-bg-opacity: 1;
  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));
}
.bg-teal-200{
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity, 1));
}
.bg-teal-300{
  --tw-bg-opacity: 1;
  background-color: rgb(94 234 212 / var(--tw-bg-opacity, 1));
}
.bg-teal-50{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-teal-50\/50{
  background-color: rgb(240 253 250 / 0.5);
}
.bg-teal-500{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.bg-teal-600{
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}
.bg-teal-700{
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}
.bg-transparent{
  background-color: transparent;
}
.bg-violet-100{
  --tw-bg-opacity: 1;
  background-color: rgb(237 233 254 / var(--tw-bg-opacity, 1));
}
.bg-violet-50{
  --tw-bg-opacity: 1;
  background-color: rgb(245 243 255 / var(--tw-bg-opacity, 1));
}
.bg-violet-500{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity, 1));
}
.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.bg-warning-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity, 1));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20{
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/50{
  background-color: rgb(255 255 255 / 0.5);
}
.bg-white\/80{
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90{
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95{
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-100{
  --tw-bg-opacity: 1;
  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400{
  --tw-bg-opacity: 1;
  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));
}
.bg-yellow-400\/60{
  background-color: rgb(250 204 21 / 0.6);
}
.bg-yellow-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.bg-yellow-50\/50{
  background-color: rgb(254 252 232 / 0.5);
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-zinc-400\/60{
  background-color: rgb(161 161 170 / 0.6);
}
.bg-opacity-20{
  --tw-bg-opacity: 0.2;
}
.bg-opacity-50{
  --tw-bg-opacity: 0.5;
}
.bg-gradient-to-b{
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-br{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t{
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.from-amber-50{
  --tw-gradient-from: #fffbeb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 251 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500{
  --tw-gradient-from: #f59e0b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50{
  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-50\/50{
  --tw-gradient-from: rgb(239 246 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/20{
  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-600{
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-700{
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-400{
  --tw-gradient-from: #22d3ee var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-400\/20{
  --tw-gradient-from: rgb(34 211 238 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 211 238 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-50{
  --tw-gradient-from: #ecfeff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 254 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-50\/50{
  --tw-gradient-from: rgb(236 254 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 254 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500{
  --tw-gradient-from: #06b6d4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500\/10{
  --tw-gradient-from: rgb(6 182 212 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500\/20{
  --tw-gradient-from: rgb(6 182 212 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-500\/5{
  --tw-gradient-from: rgb(6 182 212 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(6 182 212 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-cyan-600{
  --tw-gradient-from: #0891b2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-500{
  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-100{
  --tw-gradient-from: #f3f4f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 244 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-200\/50{
  --tw-gradient-from: rgb(229 231 235 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(229 231 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50{
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50{
  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-50\/50{
  --tw-gradient-from: rgb(240 253 244 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500{
  --tw-gradient-from: #22c55e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600{
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-700{
  --tw-gradient-from: #15803d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-module-scheduler-bg{
  --tw-gradient-from: #f3e8ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(243 232 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-neutral-50{
  --tw-gradient-from: #F9FAFB var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-200{
  --tw-gradient-from: #fed7aa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 215 170 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50{
  --tw-gradient-from: #fff7ed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-50\/50{
  --tw-gradient-from: rgb(255 247 237 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500{
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500\/20{
  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-600{
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-700{
  --tw-gradient-from: #c2410c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(194 65 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-900{
  --tw-gradient-from: #7c2d12 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500{
  --tw-gradient-from: #ec4899 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-100\/50{
  --tw-gradient-from: rgb(255 228 204 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 228 204 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-300{
  --tw-gradient-from: #FFC285 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 194 133 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary-50{
  --tw-gradient-from: #FFF7ED var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 247 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50{
  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-50\/50{
  --tw-gradient-from: rgb(250 245 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500{
  --tw-gradient-from: #a855f7 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-600{
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-700{
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-900{
  --tw-gradient-from: #581c87 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-50{
  --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500{
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-600{
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-700{
  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(185 28 28 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50{
  --tw-gradient-from: #f8fafc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50\/50{
  --tw-gradient-from: rgb(248 250 252 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-500{
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-600{
  --tw-gradient-from: #475569 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 85 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-900{
  --tw-gradient-from: #0f172a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-50{
  --tw-gradient-from: #f0fdfa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-600{
  --tw-gradient-from: #0d9488 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(13 148 136 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent{
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-violet-500{
  --tw-gradient-from: #8b5cf6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(139 92 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white{
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/10{
  --tw-gradient-from: rgb(255 255 255 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-50{
  --tw-gradient-from: #fefce8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(254 252 232 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-500{
  --tw-gradient-from: #eab308 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 179 8 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-blue-50{
  --tw-gradient-to: rgb(239 246 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eff6ff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500\/20{
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500\/5{
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-300{
  --tw-gradient-to: rgb(103 232 249 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #67e8f9 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-50{
  --tw-gradient-to: rgb(236 254 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ecfeff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-600{
  --tw-gradient-to: rgb(8 145 178 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0891b2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-100{
  --tw-gradient-to: rgb(243 244 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f3f4f6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-gray-50{
  --tw-gradient-to: rgb(249 250 251 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #f9fafb var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-50{
  --tw-gradient-to: rgb(255 247 237 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff7ed var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-100{
  --tw-gradient-to: rgb(255 228 204 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #FFE4CC var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary-500{
  --tw-gradient-to: rgb(255 153 51 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #FF9933 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-red-50{
  --tw-gradient-to: rgb(254 242 242 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fef2f2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-slate-800{
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1e293b var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent{
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/20{
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-amber-50{
  --tw-gradient-to: #fffbeb var(--tw-gradient-to-position);
}
.to-amber-50\/50{
  --tw-gradient-to: rgb(255 251 235 / 0.5) var(--tw-gradient-to-position);
}
.to-amber-500{
  --tw-gradient-to: #f59e0b var(--tw-gradient-to-position);
}
.to-amber-600{
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.to-blue-50{
  --tw-gradient-to: #eff6ff var(--tw-gradient-to-position);
}
.to-blue-500{
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.to-blue-600{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.to-current{
  --tw-gradient-to: currentColor var(--tw-gradient-to-position);
}
.to-cyan-100{
  --tw-gradient-to: #cffafe var(--tw-gradient-to-position);
}
.to-cyan-100\/50{
  --tw-gradient-to: rgb(207 250 254 / 0.5) var(--tw-gradient-to-position);
}
.to-cyan-50{
  --tw-gradient-to: #ecfeff var(--tw-gradient-to-position);
}
.to-cyan-50\/20{
  --tw-gradient-to: rgb(236 254 255 / 0.2) var(--tw-gradient-to-position);
}
.to-cyan-50\/30{
  --tw-gradient-to: rgb(236 254 255 / 0.3) var(--tw-gradient-to-position);
}
.to-cyan-600{
  --tw-gradient-to: #0891b2 var(--tw-gradient-to-position);
}
.to-cyan-600\/20{
  --tw-gradient-to: rgb(8 145 178 / 0.2) var(--tw-gradient-to-position);
}
.to-cyan-700{
  --tw-gradient-to: #0e7490 var(--tw-gradient-to-position);
}
.to-cyan-700\/10{
  --tw-gradient-to: rgb(14 116 144 / 0.1) var(--tw-gradient-to-position);
}
.to-cyan-700\/5{
  --tw-gradient-to: rgb(14 116 144 / 0.05) var(--tw-gradient-to-position);
}
.to-cyan-800{
  --tw-gradient-to: #155e75 var(--tw-gradient-to-position);
}
.to-emerald-50{
  --tw-gradient-to: #ecfdf5 var(--tw-gradient-to-position);
}
.to-emerald-50\/50{
  --tw-gradient-to: rgb(236 253 245 / 0.5) var(--tw-gradient-to-position);
}
.to-emerald-500{
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}
.to-emerald-600{
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.to-gray-100{
  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);
}
.to-gray-200{
  --tw-gradient-to: #e5e7eb var(--tw-gradient-to-position);
}
.to-gray-50{
  --tw-gradient-to: #f9fafb var(--tw-gradient-to-position);
}
.to-gray-50\/50{
  --tw-gradient-to: rgb(249 250 251 / 0.5) var(--tw-gradient-to-position);
}
.to-green-500{
  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);
}
.to-green-600{
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}
.to-indigo-100{
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}
.to-indigo-50{
  --tw-gradient-to: #eef2ff var(--tw-gradient-to-position);
}
.to-indigo-50\/50{
  --tw-gradient-to: rgb(238 242 255 / 0.5) var(--tw-gradient-to-position);
}
.to-indigo-500{
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.to-indigo-600{
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-module-scheduler-hover{
  --tw-gradient-to: #a78bfa var(--tw-gradient-to-position);
}
.to-neutral-100{
  --tw-gradient-to: #F3F4F6 var(--tw-gradient-to-position);
}
.to-orange-100{
  --tw-gradient-to: #ffedd5 var(--tw-gradient-to-position);
}
.to-orange-400{
  --tw-gradient-to: #fb923c var(--tw-gradient-to-position);
}
.to-orange-50{
  --tw-gradient-to: #fff7ed var(--tw-gradient-to-position);
}
.to-orange-50\/30{
  --tw-gradient-to: rgb(255 247 237 / 0.3) var(--tw-gradient-to-position);
}
.to-orange-500{
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}
.to-orange-500\/20{
  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);
}
.to-orange-600{
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}
.to-orange-800{
  --tw-gradient-to: #9a3412 var(--tw-gradient-to-position);
}
.to-pink-100{
  --tw-gradient-to: #fce7f3 var(--tw-gradient-to-position);
}
.to-pink-50{
  --tw-gradient-to: #fdf2f8 var(--tw-gradient-to-position);
}
.to-pink-600{
  --tw-gradient-to: #db2777 var(--tw-gradient-to-position);
}
.to-primary-100{
  --tw-gradient-to: #FFE4CC var(--tw-gradient-to-position);
}
.to-primary-200{
  --tw-gradient-to: #FFD5A8 var(--tw-gradient-to-position);
}
.to-primary-200\/50{
  --tw-gradient-to: rgb(255 213 168 / 0.5) var(--tw-gradient-to-position);
}
.to-primary-300{
  --tw-gradient-to: #FFC285 var(--tw-gradient-to-position);
}
.to-purple-400{
  --tw-gradient-to: #c084fc var(--tw-gradient-to-position);
}
.to-purple-500{
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-500\/20{
  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);
}
.to-purple-600{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-purple-800{
  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);
}
.to-red-500{
  --tw-gradient-to: #ef4444 var(--tw-gradient-to-position);
}
.to-red-600{
  --tw-gradient-to: #dc2626 var(--tw-gradient-to-position);
}
.to-rose-400{
  --tw-gradient-to: #fb7185 var(--tw-gradient-to-position);
}
.to-sky-50{
  --tw-gradient-to: #f0f9ff var(--tw-gradient-to-position);
}
.to-sky-50\/50{
  --tw-gradient-to: rgb(240 249 255 / 0.5) var(--tw-gradient-to-position);
}
.to-slate-100{
  --tw-gradient-to: #f1f5f9 var(--tw-gradient-to-position);
}
.to-slate-400{
  --tw-gradient-to: #94a3b8 var(--tw-gradient-to-position);
}
.to-slate-50{
  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);
}
.to-slate-500{
  --tw-gradient-to: #64748b var(--tw-gradient-to-position);
}
.to-slate-600{
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.to-slate-700{
  --tw-gradient-to: #334155 var(--tw-gradient-to-position);
}
.to-slate-800{
  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);
}
.to-slate-900{
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}
.to-teal-400{
  --tw-gradient-to: #2dd4bf var(--tw-gradient-to-position);
}
.to-transparent{
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-violet-400{
  --tw-gradient-to: #a78bfa var(--tw-gradient-to-position);
}
.to-violet-50{
  --tw-gradient-to: #f5f3ff var(--tw-gradient-to-position);
}
.to-violet-50\/50{
  --tw-gradient-to: rgb(245 243 255 / 0.5) var(--tw-gradient-to-position);
}
.to-violet-600{
  --tw-gradient-to: #7c3aed var(--tw-gradient-to-position);
}
.to-white\/10{
  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);
}
.to-white\/5{
  --tw-gradient-to: rgb(255 255 255 / 0.05) var(--tw-gradient-to-position);
}
.to-yellow-100{
  --tw-gradient-to: #fef9c3 var(--tw-gradient-to-position);
}
.to-yellow-50{
  --tw-gradient-to: #fefce8 var(--tw-gradient-to-position);
}
.to-zinc-50{
  --tw-gradient-to: #fafafa var(--tw-gradient-to-position);
}
.to-gray-600{
  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);
}
.fill-current{
  fill: currentColor;
}
.object-contain{
  object-fit: contain;
}
.object-cover{
  object-fit: cover;
}
.object-center{
  object-position: center;
}
.p-0{
  padding: 0px;
}
.p-0\.5{
  padding: 0.125rem;
}
.p-1{
  padding: 0.25rem;
}
.p-1\.5{
  padding: 0.375rem;
}
.p-10{
  padding: 2.5rem;
}
.p-12{
  padding: 3rem;
}
.p-2{
  padding: 0.5rem;
}
.p-2\.5{
  padding: 0.625rem;
}
.p-3{
  padding: 0.75rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-8{
  padding: 2rem;
}
.px-1{
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5{
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-7{
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-8{
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5{
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-10{
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12{
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16{
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20{
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-3\.5{
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5{
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6{
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8{
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-1{
  padding-bottom: 0.25rem;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pb-20{
  padding-bottom: 5rem;
}
.pb-3{
  padding-bottom: 0.75rem;
}
.pb-4{
  padding-bottom: 1rem;
}
.pb-6{
  padding-bottom: 1.5rem;
}
.pb-8{
  padding-bottom: 2rem;
}
.pl-10{
  padding-left: 2.5rem;
}
.pl-2{
  padding-left: 0.5rem;
}
.pl-3{
  padding-left: 0.75rem;
}
.pl-4{
  padding-left: 1rem;
}
.pl-5{
  padding-left: 1.25rem;
}
.pl-6{
  padding-left: 1.5rem;
}
.pl-8{
  padding-left: 2rem;
}
.pl-9{
  padding-left: 2.25rem;
}
.pr-10{
  padding-right: 2.5rem;
}
.pr-12{
  padding-right: 3rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-4{
  padding-right: 1rem;
}
.pr-8{
  padding-right: 2rem;
}
.pr-9{
  padding-right: 2.25rem;
}
.pt-1{
  padding-top: 0.25rem;
}
.pt-16{
  padding-top: 4rem;
}
.pt-2{
  padding-top: 0.5rem;
}
.pt-20{
  padding-top: 5rem;
}
.pt-3{
  padding-top: 0.75rem;
}
.pt-32{
  padding-top: 8rem;
}
.pt-4{
  padding-top: 1rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-8{
  padding-top: 2rem;
}
.pt-\[15vh\]{
  padding-top: 15vh;
}
.text-left{
  text-align: left;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.font-mono{
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl{
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl{
  font-size: 2.25rem;
  line-height: 2.5rem;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[8px\]{
  font-size: 8px;
}
.text-\[9px\]{
  font-size: 9px;
}
.text-base{
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold{
  font-weight: 700;
}
.font-extrabold{
  font-weight: 800;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.capitalize{
  text-transform: capitalize;
}
.italic{
  font-style: italic;
}
.leading-5{
  line-height: 1.25rem;
}
.leading-none{
  line-height: 1;
}
.leading-normal{
  line-height: 1.5;
}
.leading-relaxed{
  line-height: 1.625;
}
.leading-tight{
  line-height: 1.25;
}
.tracking-tight{
  letter-spacing: -0.025em;
}
.tracking-wide{
  letter-spacing: 0.025em;
}
.tracking-wider{
  letter-spacing: 0.05em;
}
.tracking-widest{
  letter-spacing: 0.1em;
}
.text-\[\#6b7280\]{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-\[\#a78bfa\]{
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.text-\[\#e5e7eb\]{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-amber-300{
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.text-amber-400{
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.text-amber-50{
  --tw-text-opacity: 1;
  color: rgb(255 251 235 / var(--tw-text-opacity, 1));
}
.text-amber-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-amber-600{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-amber-700{
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-amber-800{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-CANCELLED-icon{
  --tw-text-opacity: 1;
  color: rgb(225 29 72 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-CANCELLED-text{
  --tw-text-opacity: 1;
  color: rgb(159 18 57 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-COMPLETED-icon{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-COMPLETED-text{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-CONFIRMED-icon{
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-CONFIRMED-text{
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-NO_SHOW-icon{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-NO_SHOW-text{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-PENDING-icon{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.text-appointmentStatus-PENDING-text{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.text-black{
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.text-blue-100{
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.text-blue-200{
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.text-blue-300{
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-blue-900{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.text-current{
  color: currentColor;
}
.text-cyan-100{
  --tw-text-opacity: 1;
  color: rgb(207 250 254 / var(--tw-text-opacity, 1));
}
.text-cyan-100\/80{
  color: rgb(207 250 254 / 0.8);
}
.text-cyan-400{
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.text-cyan-500{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity, 1));
}
.text-cyan-600{
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.text-cyan-700{
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.text-cyan-800{
  --tw-text-opacity: 1;
  color: rgb(21 94 117 / var(--tw-text-opacity, 1));
}
.text-emerald-500{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-emerald-600{
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-emerald-700{
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}
.text-emerald-800{
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.text-error-50{
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.text-error-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-error-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-foreground{
  color: hsl(var(--foreground));
}
.text-gray-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-gray-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-gray-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-100{
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}
.text-green-300{
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.text-green-400{
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-green-900{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.text-indigo-400{
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.text-indigo-500{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.text-indigo-600{
  --tw-text-opacity: 1;
  color: rgb(79 70 229 / var(--tw-text-opacity, 1));
}
.text-indigo-800{
  --tw-text-opacity: 1;
  color: rgb(55 48 163 / var(--tw-text-opacity, 1));
}
.text-indigo-900{
  --tw-text-opacity: 1;
  color: rgb(49 46 129 / var(--tw-text-opacity, 1));
}
.text-module-abaplus-icon{
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}
.text-module-abaplus-text{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.text-module-abaplus-text\/70{
  color: rgb(17 94 89 / 0.7);
}
.text-module-admin-icon{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-module-admin-text{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-module-admin-text\/70{
  color: rgb(71 85 105 / 0.7);
}
.text-module-chat-icon{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-module-chat-text{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-module-dashboard-icon{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-module-dashboard-text{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-module-financial-icon{
  --tw-text-opacity: 1;
  color: rgb(5 150 105 / var(--tw-text-opacity, 1));
}
.text-module-financial-text{
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.text-module-financial-text\/70{
  color: rgb(6 95 70 / 0.7);
}
.text-module-hr-icon{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-module-hr-text{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-module-people-icon{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-module-people-text{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-module-people-text\/50{
  color: rgb(154 52 18 / 0.5);
}
.text-module-people-text\/70{
  color: rgb(154 52 18 / 0.7);
}
.text-module-scheduler-icon{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-module-scheduler-text{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-module-scheduler-text\/50{
  color: rgb(107 33 168 / 0.5);
}
.text-module-scheduler-text\/70{
  color: rgb(107 33 168 / 0.7);
}
.text-muted-foreground{
  color: hsl(var(--muted-foreground));
}
.text-neutral-200{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.text-neutral-300{
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.text-neutral-400{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-neutral-50{
  --tw-text-opacity: 1;
  color: rgb(249 250 251 / var(--tw-text-opacity, 1));
}
.text-neutral-500{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-neutral-600{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-neutral-700{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-neutral-800{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-neutral-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-orange-100{
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}
.text-orange-200{
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.text-orange-300{
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.text-orange-400{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-600{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.text-orange-700{
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-orange-800{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-orange-900{
  --tw-text-opacity: 1;
  color: rgb(124 45 18 / var(--tw-text-opacity, 1));
}
.text-pink-800{
  --tw-text-opacity: 1;
  color: rgb(157 23 77 / var(--tw-text-opacity, 1));
}
.text-primary{
  color: hsl(var(--primary));
}
.text-primary-400{
  --tw-text-opacity: 1;
  color: rgb(255 179 102 / var(--tw-text-opacity, 1));
}
.text-primary-500{
  --tw-text-opacity: 1;
  color: rgb(255 153 51 / var(--tw-text-opacity, 1));
}
.text-primary-600{
  --tw-text-opacity: 1;
  color: rgb(255 127 0 / var(--tw-text-opacity, 1));
}
.text-primary-700{
  --tw-text-opacity: 1;
  color: rgb(204 102 0 / var(--tw-text-opacity, 1));
}
.text-primary-800{
  --tw-text-opacity: 1;
  color: rgb(153 76 0 / var(--tw-text-opacity, 1));
}
.text-primary-900{
  --tw-text-opacity: 1;
  color: rgb(102 51 0 / var(--tw-text-opacity, 1));
}
.text-purple-100{
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.text-purple-200{
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.text-purple-300{
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.text-purple-400{
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-purple-600{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.text-purple-700{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.text-purple-800{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.text-purple-900{
  --tw-text-opacity: 1;
  color: rgb(88 28 135 / var(--tw-text-opacity, 1));
}
.text-red-100{
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.text-red-400{
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-red-900{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.text-rose-500{
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}
.text-rose-700{
  --tw-text-opacity: 1;
  color: rgb(190 18 60 / var(--tw-text-opacity, 1));
}
.text-slate-200{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.text-slate-600{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.text-slate-700{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-slate-800{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}
.text-success-50{
  --tw-text-opacity: 1;
  color: rgb(236 253 245 / var(--tw-text-opacity, 1));
}
.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.text-teal-300{
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.text-teal-400{
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}
.text-teal-500{
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}
.text-teal-600{
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}
.text-teal-700{
  --tw-text-opacity: 1;
  color: rgb(15 118 110 / var(--tw-text-opacity, 1));
}
.text-teal-800{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.text-teal-900{
  --tw-text-opacity: 1;
  color: rgb(19 78 74 / var(--tw-text-opacity, 1));
}
.text-violet-400{
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.text-violet-50{
  --tw-text-opacity: 1;
  color: rgb(245 243 255 / var(--tw-text-opacity, 1));
}
.text-violet-500{
  --tw-text-opacity: 1;
  color: rgb(139 92 246 / var(--tw-text-opacity, 1));
}
.text-violet-600{
  --tw-text-opacity: 1;
  color: rgb(124 58 237 / var(--tw-text-opacity, 1));
}
.text-violet-700{
  --tw-text-opacity: 1;
  color: rgb(109 40 217 / var(--tw-text-opacity, 1));
}
.text-warning-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/60{
  color: rgb(255 255 255 / 0.6);
}
.text-white\/70{
  color: rgb(255 255 255 / 0.7);
}
.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}
.text-yellow-100{
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}
.text-yellow-400{
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600{
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-yellow-700{
  --tw-text-opacity: 1;
  color: rgb(161 98 7 / var(--tw-text-opacity, 1));
}
.text-yellow-800{
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity, 1));
}
.text-yellow-900{
  --tw-text-opacity: 1;
  color: rgb(113 63 18 / var(--tw-text-opacity, 1));
}
.text-opacity-90{
  --tw-text-opacity: 0.9;
}
.underline{
  text-decoration-line: underline;
}
.line-through{
  text-decoration-line: line-through;
}
.decoration-neutral-400{
  text-decoration-color: #9CA3AF;
}
.underline-offset-4{
  text-underline-offset: 4px;
}
.placeholder-gray-500::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-module-people-text\/50::placeholder{
  color: rgb(154 52 18 / 0.5);
}
.placeholder-module-scheduler-text\/50::placeholder{
  color: rgb(107 33 168 / 0.5);
}
.placeholder-neutral-400::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.placeholder-white\/60::placeholder{
  color: rgb(255 255 255 / 0.6);
}
.accent-orange-600{
  accent-color: #ea580c;
}
.accent-teal-600{
  accent-color: #0d9488;
}
.opacity-0{
  opacity: 0;
}
.opacity-10{
  opacity: 0.1;
}
.opacity-100{
  opacity: 1;
}
.opacity-15{
  opacity: 0.15;
}
.opacity-20{
  opacity: 0.2;
}
.opacity-25{
  opacity: 0.25;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-60{
  opacity: 0.6;
}
.opacity-70{
  opacity: 0.7;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-80{
  opacity: 0.8;
}
.opacity-90{
  opacity: 0.9;
}
.mix-blend-multiply{
  mix-blend-mode: multiply;
}
.shadow{
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-inner{
  --tw-shadow: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: inset 0 2px 4px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-soft{
  --tw-shadow: 0 2px 15px 0 rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 2px 15px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-amber-100{
  --tw-shadow-color: #fef3c7;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-100{
  --tw-shadow-color: #dbeafe;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/25{
  --tw-shadow-color: rgb(59 130 246 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-cyan-100{
  --tw-shadow-color: #cffafe;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-cyan-500\/20{
  --tw-shadow-color: rgb(6 182 212 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-emerald-100{
  --tw-shadow-color: #d1fae5;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/25{
  --tw-shadow-color: rgb(34 197 94 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/30{
  --tw-shadow-color: rgb(34 197 94 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-100{
  --tw-shadow-color: #e0e7ff;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-orange-100{
  --tw-shadow-color: #ffedd5;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-100{
  --tw-shadow-color: #f3e8ff;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-100{
  --tw-shadow-color: #fee2e2;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-rose-100{
  --tw-shadow-color: #ffe4e6;
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-violet-100{
  --tw-shadow-color: #ede9fe;
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.ring{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-0{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-inset{
  --tw-ring-inset: inset;
}
.ring-black{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));
}
.ring-blue-200{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(191 219 254 / var(--tw-ring-opacity, 1));
}
.ring-cyan-200{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 243 252 / var(--tw-ring-opacity, 1));
}
.ring-cyan-200\/50{
  --tw-ring-color: rgb(165 243 252 / 0.5);
}
.ring-cyan-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 211 238 / var(--tw-ring-opacity, 1));
}
.ring-cyan-500\/50{
  --tw-ring-color: rgb(6 182 212 / 0.5);
}
.ring-gray-900\/5{
  --tw-ring-color: rgb(17 24 39 / 0.05);
}
.ring-orange-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(253 186 116 / var(--tw-ring-opacity, 1));
}
.ring-primary-200{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 213 168 / var(--tw-ring-opacity, 1));
}
.ring-purple-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(216 180 254 / var(--tw-ring-opacity, 1));
}
.ring-purple-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.ring-red-200{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(254 202 202 / var(--tw-ring-opacity, 1));
}
.ring-red-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.ring-slate-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity, 1));
}
.ring-white{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity, 1));
}
.ring-yellow-400{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(250 204 21 / var(--tw-ring-opacity, 1));
}
.ring-opacity-5{
  --tw-ring-opacity: 0.05;
}
.ring-opacity-75{
  --tw-ring-opacity: 0.75;
}
.ring-offset-background{
  --tw-ring-offset-color: hsl(var(--background));
}
.ring-offset-white{
  --tw-ring-offset-color: #fff;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl{
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg{
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl{
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow{
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur-md{
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl{
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors{
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity{
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow{
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform{
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.delay-300{
  transition-delay: 300ms;
}
.delay-500{
  transition-delay: 500ms;
}
.duration-150{
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-300{
  transition-duration: 300ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
@keyframes enter{

  from{
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit{

  to{
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in-0{
  --tw-enter-opacity: 0;
}
.zoom-in-95{
  --tw-enter-scale: .95;
}
.duration-150{
  animation-duration: 150ms;
}
.duration-200{
  animation-duration: 200ms;
}
.duration-300{
  animation-duration: 300ms;
}
.duration-500{
  animation-duration: 500ms;
}
.delay-300{
  animation-delay: 300ms;
}
.delay-500{
  animation-delay: 500ms;
}
.ease-in-out{
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out{
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

/* Garantir que a navbar tenha prioridade sobre outros elementos, mas não sobre modais */
header.sticky {
  z-index: 9000 !important;
}

body {
  font-family: 'Inter', Arial, Helvetica, sans-serif;
}

/* styles/calendar.css */
.fc {
  --fc-button-bg-color: #9333ea;
  --fc-button-border-color: #9333ea;
  --fc-button-hover-bg-color: #7e22ce;
  --fc-button-hover-border-color: #7e22ce;
  --fc-button-active-bg-color: #7c3aed;
  --fc-button-active-border-color: #7c3aed;
  --fc-today-bg-color: #faf5ff;
  --fc-border-color: #E5E7EB;
  --fc-event-border-color: transparent;
  --fc-event-text-color: #fff;
  --fc-page-bg-color: #fff;
}

.dark .fc {
  --fc-button-bg-color: #7e22ce;
  --fc-button-border-color: #7e22ce;
  --fc-button-hover-bg-color: #6b21a8;
  --fc-button-hover-border-color: #6b21a8;
  --fc-button-active-bg-color: #6d28d9;
  --fc-button-active-border-color: #6d28d9;
  --fc-today-bg-color: rgba(147, 51, 234, 0.1);
  --fc-border-color: #374151;
  --fc-event-border-color: transparent;
  --fc-event-text-color: #fff;
  --fc-page-bg-color: #1f2937;
}

/* Estilização dos botões */
.fc .fc-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.fc .fc-button:focus {
  box-shadow: 0 0 0 0px #FFE4CC;
  outline: none;
}

/* Estilização do título */
.fc .fc-toolbar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1F2937;
}

.dark .fc .fc-toolbar-title {
  color: #f3f4f6;
}

/* Espaçamento do toolbar */
.fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 1.5rem;
  padding: 1rem;
}

/* Estilização da grade */
.fc td, .fc th {
  border-color: #E5E7EB;
}

/* Estilização dos eventos */
.fc-event {
  border-radius: 0.375rem;
  margin: 1px 0;
  cursor: pointer;
}

/* Limitar a largura dos eventos para deixar espaço à direita na coluna */
.fc-timegrid-col-events {
  width: 90% !important; /* Limitar a largura para 90% da coluna */
  max-width: 90% !important; /* Garantir que não ultrapasse 90% */
}

.fc-timegrid-event {
  width: 100% !important; /* Ocupar 100% da largura disponível na coluna de eventos */
  right: auto !important;
  left: 0 !important;
  margin-right: 0 !important;
  z-index: 1 !important;
}

/* Garantir que eventos sobrepostos sejam visíveis */
.fc-timegrid-event:hover {
  z-index: 3 !important;
}

/* Estilização do dropdown do select */
.select-dropdown {
  z-index: 50 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
.file\:border-0::file-selector-button{
  border-width: 0px;
}
.file\:bg-transparent::file-selector-button{
  background-color: transparent;
}
.file\:text-sm::file-selector-button{
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:font-medium::file-selector-button{
  font-weight: 500;
}
.placeholder\:text-gray-500::placeholder{
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:left-\[2px\]::after{
  content: var(--tw-content);
  left: 2px;
}
.after\:top-\[2px\]::after{
  content: var(--tw-content);
  top: 2px;
}
.after\:h-5::after{
  content: var(--tw-content);
  height: 1.25rem;
}
.after\:w-5::after{
  content: var(--tw-content);
  width: 1.25rem;
}
.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:border::after{
  content: var(--tw-content);
  border-width: 1px;
}
.after\:border-gray-300::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.after\:bg-white::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.after\:transition-all::after{
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after{
  --tw-content: '';
  content: var(--tw-content);
}
.first\:pt-0:first-child{
  padding-top: 0px;
}
.last\:border-b-0:last-child{
  border-bottom-width: 0px;
}
.last\:pb-0:last-child{
  padding-bottom: 0px;
}
.checked\:border-blue-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.checked\:border-emerald-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.checked\:border-indigo-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.checked\:border-orange-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.checked\:border-slate-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.checked\:border-teal-400:checked{
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.checked\:bg-gradient-to-br:checked{
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.checked\:from-blue-500:checked{
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:from-emerald-500:checked{
  --tw-gradient-from: #10b981 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(16 185 129 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:from-indigo-500:checked{
  --tw-gradient-from: #6366f1 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:from-orange-500:checked{
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:from-slate-500:checked{
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:from-teal-500:checked{
  --tw-gradient-from: #14b8a6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 184 166 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.checked\:to-blue-600:checked{
  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);
}
.checked\:to-emerald-600:checked{
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.checked\:to-indigo-600:checked{
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.checked\:to-orange-600:checked{
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}
.checked\:to-slate-600:checked{
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.checked\:to-teal-600:checked{
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.hover\:-translate-y-0\.5:hover{
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-1:hover{
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:-translate-y-2:hover{
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-105:hover{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-110:hover{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-125:hover{
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:scale-\[1\.02\]:hover{
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:border-amber-600:hover{
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.hover\:border-blue-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.hover\:border-emerald-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.hover\:border-error-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.hover\:border-gray-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\:border-green-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));
}
.hover\:border-indigo-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(129 140 248 / var(--tw-border-opacity, 1));
}
.hover\:border-module-admin-border:hover{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.hover\:border-module-financial-border:hover{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.hover\:border-module-people-border:hover{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.hover\:border-module-scheduler-border:hover{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.hover\:border-neutral-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));
}
.hover\:border-orange-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.hover\:border-orange-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.hover\:border-orange-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(255 194 133 / var(--tw-border-opacity, 1));
}
.hover\:border-primary-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(255 153 51 / var(--tw-border-opacity, 1));
}
.hover\:border-purple-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.hover\:border-purple-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.hover\:border-red-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.hover\:border-slate-500:hover{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.hover\:border-teal-300:hover{
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}
.hover\:border-teal-400:hover{
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.hover\:border-white\/30:hover{
  border-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-\[\#a78bfa\]:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity, 1));
}
.hover\:bg-amber-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 251 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-amber-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.hover\:bg-amber-600\/90:hover{
  background-color: rgb(217 119 6 / 0.9);
}
.hover\:bg-appointmentStatus-CANCELLED-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 205 211 / var(--tw-bg-opacity, 1));
}
.hover\:bg-appointmentStatus-COMPLETED-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-appointmentStatus-CONFIRMED-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}
.hover\:bg-appointmentStatus-NO_SHOW-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}
.hover\:bg-appointmentStatus-PENDING-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 230 138 / var(--tw-bg-opacity, 1));
}
.hover\:bg-black:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.hover\:bg-black\/10:hover{
  background-color: rgb(0 0 0 / 0.1);
}
.hover\:bg-black\/5:hover{
  background-color: rgb(0 0 0 / 0.05);
}
.hover\:bg-black\/80:hover{
  background-color: rgb(0 0 0 / 0.8);
}
.hover\:bg-blue-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-blue-600\/90:hover{
  background-color: rgb(37 99 235 / 0.9);
}
.hover\:bg-blue-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\:bg-cyan-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(207 250 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-cyan-100\/50:hover{
  background-color: rgb(207 250 254 / 0.5);
}
.hover\:bg-cyan-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(165 243 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-cyan-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(236 254 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-emerald-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(236 253 245 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-100\/50:hover{
  background-color: rgb(243 244 246 / 0.5);
}
.hover\:bg-gray-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-400:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gray-600\/90:hover{
  background-color: rgb(75 85 99 / 0.9);
}
.hover\:bg-gray-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.hover\:bg-green-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(199 210 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-indigo-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 56 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-abaplus-bg\/10:hover{
  background-color: rgb(240 253 250 / 0.1);
}
.hover\:bg-module-abaplus-bg\/5:hover{
  background-color: rgb(240 253 250 / 0.05);
}
.hover\:bg-module-abaplus-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(153 246 228 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-admin-bg\/10:hover{
  background-color: rgb(248 250 252 / 0.1);
}
.hover\:bg-module-admin-bg\/5:hover{
  background-color: rgb(248 250 252 / 0.05);
}
.hover\:bg-module-admin-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-chat-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-dashboard-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-financial-bg\/10:hover{
  background-color: rgb(236 253 245 / 0.1);
}
.hover\:bg-module-financial-bg\/5:hover{
  background-color: rgb(236 253 245 / 0.05);
}
.hover\:bg-module-financial-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-hr-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-people-bg\/10:hover{
  background-color: rgb(255 237 213 / 0.1);
}
.hover\:bg-module-people-bg\/5:hover{
  background-color: rgb(255 237 213 / 0.05);
}
.hover\:bg-module-people-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(253 186 116 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-scheduler-bg:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-module-scheduler-bg\/10:hover{
  background-color: rgb(243 232 255 / 0.1);
}
.hover\:bg-module-scheduler-bg\/5:hover{
  background-color: rgb(243 232 255 / 0.05);
}
.hover\:bg-module-scheduler-hover:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.hover\:bg-neutral-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.hover\:bg-orange-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}
.hover\:bg-pink-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(219 39 119 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 228 204 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 213 168 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 194 133 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-400:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 179 102 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 153 51 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 127 0 / var(--tw-bg-opacity, 1));
}
.hover\:bg-primary-600\/90:hover{
  background-color: rgb(255 127 0 / 0.9);
}
.hover\:bg-primary-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(204 102 0 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.hover\:bg-purple-600\/90:hover{
  background-color: rgb(147 51 234 / 0.9);
}
.hover\:bg-purple-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:bg-red-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.hover\:bg-rose-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 242 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(226 232 240 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(203 213 225 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.hover\:bg-slate-600\/90:hover{
  background-color: rgb(71 85 105 / 0.9);
}
.hover\:bg-teal-300:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(94 234 212 / var(--tw-bg-opacity, 1));
}
.hover\:bg-teal-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.hover\:bg-teal-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.hover\:bg-teal-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}
.hover\:bg-teal-700:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}
.hover\:bg-transparent:hover{
  background-color: transparent;
}
.hover\:bg-violet-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(245 243 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-violet-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.hover\:bg-white\/10:hover{
  background-color: rgb(255 255 255 / 0.1);
}
.hover\:bg-white\/20:hover{
  background-color: rgb(255 255 255 / 0.2);
}
.hover\:bg-white\/30:hover{
  background-color: rgb(255 255 255 / 0.3);
}
.hover\:bg-white\/50:hover{
  background-color: rgb(255 255 255 / 0.5);
}
.hover\:bg-white\/80:hover{
  background-color: rgb(255 255 255 / 0.8);
}
.hover\:bg-yellow-50:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));
}
.hover\:bg-yellow-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}
.hover\:bg-gradient-to-r:hover{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.hover\:from-amber-600:hover{
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-100:hover{
  --tw-gradient-from: #dbeafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(219 234 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-600:hover{
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-blue-700:hover{
  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-cyan-100:hover{
  --tw-gradient-from: #cffafe var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(207 250 254 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-cyan-50:hover{
  --tw-gradient-from: #ecfeff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 254 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-cyan-600:hover{
  --tw-gradient-from: #0891b2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-cyan-700:hover{
  --tw-gradient-from: #0e7490 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 116 144 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-emerald-600:hover{
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-green-600:hover{
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-orange-600:hover{
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-orange-700:hover{
  --tw-gradient-from: #c2410c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(194 65 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-purple-700:hover{
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-red-600:hover{
  --tw-gradient-from: #dc2626 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-red-700:hover{
  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(185 28 28 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-slate-500:hover{
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-slate-600:hover{
  --tw-gradient-from: #475569 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 85 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-slate-700:hover{
  --tw-gradient-from: #334155 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(51 65 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:from-violet-600:hover{
  --tw-gradient-from: #7c3aed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 58 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.hover\:via-cyan-700:hover{
  --tw-gradient-to: rgb(14 116 144 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0e7490 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.hover\:to-amber-600:hover{
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.hover\:to-amber-700:hover{
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}
.hover\:to-blue-100:hover{
  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);
}
.hover\:to-blue-700:hover{
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.hover\:to-cyan-100:hover{
  --tw-gradient-to: #cffafe var(--tw-gradient-to-position);
}
.hover\:to-cyan-800:hover{
  --tw-gradient-to: #155e75 var(--tw-gradient-to-position);
}
.hover\:to-cyan-900:hover{
  --tw-gradient-to: #164e63 var(--tw-gradient-to-position);
}
.hover\:to-emerald-600:hover{
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.hover\:to-green-600:hover{
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}
.hover\:to-indigo-100:hover{
  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);
}
.hover\:to-indigo-600:hover{
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.hover\:to-indigo-700:hover{
  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);
}
.hover\:to-orange-700:hover{
  --tw-gradient-to: #c2410c var(--tw-gradient-to-position);
}
.hover\:to-purple-600:hover{
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.hover\:to-red-700:hover{
  --tw-gradient-to: #b91c1c var(--tw-gradient-to-position);
}
.hover\:to-rose-500:hover{
  --tw-gradient-to: #f43f5e var(--tw-gradient-to-position);
}
.hover\:to-slate-600:hover{
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.hover\:to-slate-700:hover{
  --tw-gradient-to: #334155 var(--tw-gradient-to-position);
}
.hover\:to-slate-800:hover{
  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);
}
.hover\:to-violet-500:hover{
  --tw-gradient-to: #8b5cf6 var(--tw-gradient-to-position);
}
.hover\:to-violet-700:hover{
  --tw-gradient-to: #6d28d9 var(--tw-gradient-to-position);
}
.hover\:to-gray-700:hover{
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}
.hover\:text-amber-500:hover{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.hover\:text-amber-600:hover{
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.hover\:text-amber-800:hover{
  --tw-text-opacity: 1;
  color: rgb(146 64 14 / var(--tw-text-opacity, 1));
}
.hover\:text-amber-900:hover{
  --tw-text-opacity: 1;
  color: rgb(120 53 15 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-500:hover{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-600:hover{
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-700:hover{
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.hover\:text-blue-900:hover{
  --tw-text-opacity: 1;
  color: rgb(30 58 138 / var(--tw-text-opacity, 1));
}
.hover\:text-cyan-600:hover{
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.hover\:text-cyan-700:hover{
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-200:hover{
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-800:hover{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.hover\:text-gray-900:hover{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.hover\:text-green-500:hover{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.hover\:text-green-600:hover{
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.hover\:text-green-700:hover{
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.hover\:text-green-800:hover{
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.hover\:text-green-900:hover{
  --tw-text-opacity: 1;
  color: rgb(20 83 45 / var(--tw-text-opacity, 1));
}
.hover\:text-indigo-500:hover{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.hover\:text-module-abaplus-text:hover{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.hover\:text-module-admin-text:hover{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.hover\:text-module-financial-text:hover{
  --tw-text-opacity: 1;
  color: rgb(6 95 70 / var(--tw-text-opacity, 1));
}
.hover\:text-module-people-text:hover{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.hover\:text-module-scheduler-hover:hover{
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.hover\:text-module-scheduler-text:hover{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.hover\:text-neutral-600:hover{
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.hover\:text-neutral-700:hover{
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.hover\:text-neutral-800:hover{
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.hover\:text-orange-200:hover{
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.hover\:text-orange-500:hover{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.hover\:text-orange-600:hover{
  --tw-text-opacity: 1;
  color: rgb(234 88 12 / var(--tw-text-opacity, 1));
}
.hover\:text-orange-700:hover{
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.hover\:text-orange-800:hover{
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-300:hover{
  --tw-text-opacity: 1;
  color: rgb(255 194 133 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-500:hover{
  --tw-text-opacity: 1;
  color: rgb(255 153 51 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-600:hover{
  --tw-text-opacity: 1;
  color: rgb(255 127 0 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-700:hover{
  --tw-text-opacity: 1;
  color: rgb(204 102 0 / var(--tw-text-opacity, 1));
}
.hover\:text-primary-800:hover{
  --tw-text-opacity: 1;
  color: rgb(153 76 0 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-200:hover{
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-500:hover{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-600:hover{
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-700:hover{
  --tw-text-opacity: 1;
  color: rgb(126 34 206 / var(--tw-text-opacity, 1));
}
.hover\:text-purple-800:hover{
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.hover\:text-red-500:hover{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.hover\:text-red-600:hover{
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.hover\:text-red-700:hover{
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.hover\:text-red-800:hover{
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.hover\:text-red-900:hover{
  --tw-text-opacity: 1;
  color: rgb(127 29 29 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-200:hover{
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-500:hover{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-600:hover{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-700:hover{
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.hover\:text-slate-800:hover{
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity, 1));
}
.hover\:text-teal-800:hover{
  --tw-text-opacity: 1;
  color: rgb(17 94 89 / var(--tw-text-opacity, 1));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.hover\:text-yellow-500:hover{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.hover\:underline:hover{
  text-decoration-line: underline;
}
.hover\:no-underline:hover{
  text-decoration-line: none;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:opacity-75:hover{
  opacity: 0.75;
}
.hover\:opacity-80:hover{
  opacity: 0.8;
}
.hover\:opacity-90:hover{
  opacity: 0.9;
}
.hover\:shadow-lg:hover{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-md:hover{
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-sm:hover{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover{
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:border-amber-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.focus\:border-blue-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\:border-module-abaplus-border:focus{
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.focus\:border-module-admin-border:focus{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.focus\:border-module-financial-border:focus{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.focus\:border-module-people-border:focus{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.focus\:border-module-scheduler-border:focus{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.focus\:border-orange-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.focus\:border-orange-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}
.focus\:border-primary-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(255 194 133 / var(--tw-border-opacity, 1));
}
.focus\:border-primary-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(255 153 51 / var(--tw-border-opacity, 1));
}
.focus\:border-purple-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.focus\:border-purple-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.focus\:border-red-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.focus\:border-slate-300:focus{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.focus\:border-slate-400:focus{
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.focus\:border-slate-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.focus\:border-slate-600:focus{
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.focus\:border-teal-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.focus\:border-transparent:focus{
  border-color: transparent;
}
.focus\:border-white\/40:focus{
  border-color: rgb(255 255 255 / 0.4);
}
.focus\:bg-slate-500:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.focus\:from-slate-500:focus{
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.focus\:to-slate-600:focus{
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.focus\:text-slate-600:focus{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.focus\:shadow-lg:focus{
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-amber-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(245 158 11 / var(--tw-ring-opacity, 1));
}
.focus\:ring-blue-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}
.focus\:ring-blue-500\/50:focus{
  --tw-ring-color: rgb(59 130 246 / 0.5);
}
.focus\:ring-cyan-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));
}
.focus\:ring-emerald-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));
}
.focus\:ring-emerald-500\/50:focus{
  --tw-ring-color: rgb(16 185 129 / 0.5);
}
.focus\:ring-green-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));
}
.focus\:ring-green-500\/50:focus{
  --tw-ring-color: rgb(34 197 94 / 0.5);
}
.focus\:ring-indigo-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));
}
.focus\:ring-indigo-500\/50:focus{
  --tw-ring-color: rgb(99 102 241 / 0.5);
}
.focus\:ring-module-abaplus-border:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(45 212 191 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-admin-border:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-financial-border:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-people-border:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-scheduler-bg:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(243 232 255 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-scheduler-border:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.focus\:ring-module-scheduler-icon:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));
}
.focus\:ring-orange-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}
.focus\:ring-orange-500\/20:focus{
  --tw-ring-color: rgb(249 115 22 / 0.2);
}
.focus\:ring-orange-500\/50:focus{
  --tw-ring-color: rgb(249 115 22 / 0.5);
}
.focus\:ring-primary:focus{
  --tw-ring-color: hsl(var(--primary));
}
.focus\:ring-primary-200:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 213 168 / var(--tw-ring-opacity, 1));
}
.focus\:ring-primary-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 194 133 / var(--tw-ring-opacity, 1));
}
.focus\:ring-primary-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 153 51 / var(--tw-ring-opacity, 1));
}
.focus\:ring-purple-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.focus\:ring-red-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus\:ring-ring:focus{
  --tw-ring-color: hsl(var(--ring));
}
.focus\:ring-slate-300:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(203 213 225 / var(--tw-ring-opacity, 1));
}
.focus\:ring-slate-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.focus\:ring-slate-500\/50:focus{
  --tw-ring-color: rgb(100 116 139 / 0.5);
}
.focus\:ring-teal-500:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}
.focus\:ring-teal-500\/50:focus{
  --tw-ring-color: rgb(20 184 166 / 0.5);
}
.focus\:ring-white\/30:focus{
  --tw-ring-color: rgb(255 255 255 / 0.3);
}
.focus\:ring-opacity-50:focus{
  --tw-ring-opacity: 0.5;
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus-visible\:\!border-module-abaplus-border:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-module-admin-border:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-module-financial-border:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-module-people-border:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-module-scheduler-border:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-primary-500:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 153 51 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:\!border-red-500:focus-visible{
  --tw-border-opacity: 1 !important;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1)) !important;
}
.focus-visible\:border-module-abaplus-border:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-module-admin-border:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-module-financial-border:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-module-people-border:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-module-scheduler-border:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-primary-500:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(255 153 51 / var(--tw-border-opacity, 1));
}
.focus-visible\:border-red-500:focus-visible{
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.focus-visible\:outline-none:focus-visible{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:\!ring-1:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color) !important;
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color) !important;
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000) !important;
}
.focus-visible\:ring-2:focus-visible{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:\!ring-module-abaplus-border:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(45 212 191 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-module-admin-border:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-module-financial-border:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-module-people-border:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-module-scheduler-border:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-primary-500:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(255 153 51 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:\!ring-red-500:focus-visible{
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1)) !important;
}
.focus-visible\:ring-gray-950:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(3 7 18 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-module-abaplus-border:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(45 212 191 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-module-admin-border:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-module-financial-border:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-module-people-border:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-module-scheduler-border:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-neutral-400:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-primary-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 153 51 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-red-500:focus-visible{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));
}
.focus-visible\:ring-offset-2:focus-visible{
  --tw-ring-offset-width: 2px;
}
.active\:scale-\[0\.99\]:active{
  --tw-scale-x: 0.99;
  --tw-scale-y: 0.99;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.active\:border-slate-300:active{
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.active\:bg-slate-500:active{
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.active\:from-slate-500:active{
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.active\:to-slate-600:active{
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.active\:text-slate-600:active{
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.active\:shadow-sm:active{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:cursor-not-allowed:disabled{
  cursor: not-allowed;
}
.disabled\:bg-gray-100:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-gray-300:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-gray-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.disabled\:bg-purple-400:disabled{
  --tw-bg-opacity: 1;
  background-color: rgb(192 132 252 / var(--tw-bg-opacity, 1));
}
.disabled\:text-gray-400:disabled{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group:hover .group-hover\:visible{
  visibility: visible;
}
.group:hover .group-hover\:translate-x-0\.5{
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:translate-x-1{
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-105{
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:scale-110{
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.group:hover .group-hover\:bg-black\/10{
  background-color: rgb(0 0 0 / 0.1);
}
.group:hover .group-hover\:bg-black\/20{
  background-color: rgb(0 0 0 / 0.2);
}
.group:hover .group-hover\:text-blue-400{
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-cyan-600{
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-cyan-700{
  --tw-text-opacity: 1;
  color: rgb(14 116 144 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-emerald-400{
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-indigo-400{
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-orange-400{
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-slate-400{
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:text-teal-400{
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.group:hover .group-hover\:opacity-30{
  opacity: 0.3;
}
.group:hover .group-hover\:opacity-70{
  opacity: 0.7;
}
.group:hover .group-hover\:ring-cyan-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(103 232 249 / var(--tw-ring-opacity, 1));
}
.group:hover .group-hover\:ring-cyan-500{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(6 182 212 / var(--tw-ring-opacity, 1));
}
.peer:checked ~ .peer-checked\:bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(255 127 0 / var(--tw-bg-opacity, 1));
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after{
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.peer:checked ~ .peer-checked\:after\:border-white::after{
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.peer:focus ~ .peer-focus\:outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.peer:focus ~ .peer-focus\:ring-4{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.peer:focus ~ .peer-focus\:ring-primary-300{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 194 133 / var(--tw-ring-opacity, 1));
}
.peer:disabled ~ .peer-disabled\:cursor-not-allowed{
  cursor: not-allowed;
}
.peer:disabled ~ .peer-disabled\:opacity-70{
  opacity: 0.7;
}
.data-\[state\=active\]\:bg-white[data-state="active"]{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.data-\[state\=open\]\:bg-accent[data-state="open"]{
  background-color: hsl(var(--accent));
}
.data-\[state\=active\]\:text-neutral-900[data-state="active"]{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.data-\[state\=open\]\:text-muted-foreground[data-state="open"]{
  color: hsl(var(--muted-foreground));
}
.data-\[state\=active\]\:shadow-sm[data-state="active"]{
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.data-\[state\=open\]\:animate-in[data-state="open"]{
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.data-\[state\=closed\]\:animate-out[data-state="closed"]{
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}
.data-\[state\=closed\]\:fade-out-0[data-state="closed"]{
  --tw-exit-opacity: 0;
}
.data-\[state\=open\]\:fade-in-0[data-state="open"]{
  --tw-enter-opacity: 0;
}
.data-\[state\=closed\]\:zoom-out-95[data-state="closed"]{
  --tw-exit-scale: .95;
}
.data-\[state\=open\]\:zoom-in-95[data-state="open"]{
  --tw-enter-scale: .95;
}
.data-\[state\=closed\]\:slide-out-to-left-1\/2[data-state="closed"]{
  --tw-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:slide-out-to-top-\[48\%\][data-state="closed"]{
  --tw-exit-translate-y: -48%;
}
.data-\[state\=open\]\:slide-in-from-left-1\/2[data-state="open"]{
  --tw-enter-translate-x: -50%;
}
.data-\[state\=open\]\:slide-in-from-top-\[48\%\][data-state="open"]{
  --tw-enter-translate-y: -48%;
}
.dark\:divide-gray-700:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-divide-opacity, 1));
}
.dark\:divide-gray-800:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-divide-opacity, 1));
}
.dark\:divide-module-admin-border-dark:is(.dark *) > :not([hidden]) ~ :not([hidden]){
  --tw-divide-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-divide-opacity, 1));
}
.dark\:border-\[\#374151\]:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:border-amber-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.dark\:border-amber-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));
}
.dark\:border-amber-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(180 83 9 / var(--tw-border-opacity, 1));
}
.dark\:border-amber-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(146 64 14 / var(--tw-border-opacity, 1));
}
.dark\:border-amber-800\/30:is(.dark *){
  border-color: rgb(146 64 14 / 0.3);
}
.dark\:border-amber-800\/50:is(.dark *){
  border-color: rgb(146 64 14 / 0.5);
}
.dark\:border-appointmentStatus-CANCELLED-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}
.dark\:border-appointmentStatus-COMPLETED-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.dark\:border-appointmentStatus-CONFIRMED-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:border-appointmentStatus-NO_SHOW-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.dark\:border-appointmentStatus-PENDING-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(245 158 11 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 64 175 / var(--tw-border-opacity, 1));
}
.dark\:border-blue-800\/30:is(.dark *){
  border-color: rgb(30 64 175 / 0.3);
}
.dark\:border-blue-800\/50:is(.dark *){
  border-color: rgb(30 64 175 / 0.5);
}
.dark\:border-cyan-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(34 211 238 / var(--tw-border-opacity, 1));
}
.dark\:border-cyan-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(14 116 144 / var(--tw-border-opacity, 1));
}
.dark\:border-cyan-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 94 117 / var(--tw-border-opacity, 1));
}
.dark\:border-cyan-800\/30:is(.dark *){
  border-color: rgb(21 94 117 / 0.3);
}
.dark\:border-cyan-800\/50:is(.dark *){
  border-color: rgb(21 94 117 / 0.5);
}
.dark\:border-emerald-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.dark\:border-emerald-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:border-emerald-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(4 120 87 / var(--tw-border-opacity, 1));
}
.dark\:border-emerald-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(6 95 70 / var(--tw-border-opacity, 1));
}
.dark\:border-emerald-800\/30:is(.dark *){
  border-color: rgb(6 95 70 / 0.3);
}
.dark\:border-error-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.dark\:border-error-700\/50:is(.dark *){
  border-color: rgb(185 28 28 / 0.5);
}
.dark\:border-gray-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-700\/20:is(.dark *){
  border-color: rgb(55 65 81 / 0.2);
}
.dark\:border-gray-700\/30:is(.dark *){
  border-color: rgb(55 65 81 / 0.3);
}
.dark\:border-gray-700\/60:is(.dark *){
  border-color: rgb(55 65 81 / 0.6);
}
.dark\:border-gray-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}
.dark\:border-gray-800\/50:is(.dark *){
  border-color: rgb(31 41 55 / 0.5);
}
.dark\:border-gray-900:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));
}
.dark\:border-green-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));
}
.dark\:border-green-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));
}
.dark\:border-green-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.dark\:border-green-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));
}
.dark\:border-green-800\/30:is(.dark *){
  border-color: rgb(22 101 52 / 0.3);
}
.dark\:border-green-800\/50:is(.dark *){
  border-color: rgb(22 101 52 / 0.5);
}
.dark\:border-indigo-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(99 102 241 / var(--tw-border-opacity, 1));
}
.dark\:border-indigo-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 48 163 / var(--tw-border-opacity, 1));
}
.dark\:border-module-abaplus-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.dark\:border-module-abaplus-border-dark\/30:is(.dark *){
  border-color: rgb(20 184 166 / 0.3);
}
.dark\:border-module-admin-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:border-module-admin-border-dark\/30:is(.dark *){
  border-color: rgb(100 116 139 / 0.3);
}
.dark\:border-module-chat-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.dark\:border-module-dashboard-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.dark\:border-module-financial-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:border-module-financial-border-dark\/30:is(.dark *){
  border-color: rgb(16 185 129 / 0.3);
}
.dark\:border-module-hr-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.dark\:border-module-people-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:border-module-people-border-dark\/30:is(.dark *){
  border-color: rgb(249 115 22 / 0.3);
}
.dark\:border-module-scheduler-border-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:border-module-scheduler-border-dark\/30:is(.dark *){
  border-color: rgb(168 85 247 / 0.3);
}
.dark\:border-module-scheduler-icon-dark:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.dark\:border-neutral-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.dark\:border-neutral-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-600\/70:is(.dark *){
  border-color: rgb(234 88 12 / 0.7);
}
.dark\:border-orange-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(154 52 18 / var(--tw-border-opacity, 1));
}
.dark\:border-orange-800\/30:is(.dark *){
  border-color: rgb(154 52 18 / 0.3);
}
.dark\:border-orange-800\/50:is(.dark *){
  border-color: rgb(154 52 18 / 0.5);
}
.dark\:border-primary-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 179 102 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 127 0 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(204 102 0 / var(--tw-border-opacity, 1));
}
.dark\:border-primary-700\/50:is(.dark *){
  border-color: rgb(204 102 0 / 0.5);
}
.dark\:border-primary-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(153 76 0 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-300:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(126 34 206 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 33 168 / var(--tw-border-opacity, 1));
}
.dark\:border-purple-800\/30:is(.dark *){
  border-color: rgb(107 33 168 / 0.3);
}
.dark\:border-purple-800\/50:is(.dark *){
  border-color: rgb(107 33 168 / 0.5);
}
.dark\:border-red-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.dark\:border-red-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.dark\:border-red-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.dark\:border-red-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(153 27 27 / var(--tw-border-opacity, 1));
}
.dark\:border-red-800\/30:is(.dark *){
  border-color: rgb(153 27 27 / 0.3);
}
.dark\:border-red-800\/50:is(.dark *){
  border-color: rgb(153 27 27 / 0.5);
}
.dark\:border-rose-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(159 18 57 / var(--tw-border-opacity, 1));
}
.dark\:border-rose-800\/50:is(.dark *){
  border-color: rgb(159 18 57 / 0.5);
}
.dark\:border-slate-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(71 85 105 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.dark\:border-slate-700\/30:is(.dark *){
  border-color: rgb(51 65 85 / 0.3);
}
.dark\:border-slate-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(30 41 59 / var(--tw-border-opacity, 1));
}
.dark\:border-success-700\/50:is(.dark *){
  border-color: rgb(4 120 87 / 0.5);
}
.dark\:border-teal-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}
.dark\:border-teal-500:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.dark\:border-teal-600:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(13 148 136 / var(--tw-border-opacity, 1));
}
.dark\:border-teal-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(15 118 110 / var(--tw-border-opacity, 1));
}
.dark\:border-teal-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(17 94 89 / var(--tw-border-opacity, 1));
}
.dark\:border-violet-400:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(167 139 250 / var(--tw-border-opacity, 1));
}
.dark\:border-violet-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(91 33 182 / var(--tw-border-opacity, 1));
}
.dark\:border-violet-800\/30:is(.dark *){
  border-color: rgb(91 33 182 / 0.3);
}
.dark\:border-violet-800\/50:is(.dark *){
  border-color: rgb(91 33 182 / 0.5);
}
.dark\:border-warning-700\/50:is(.dark *){
  border-color: rgb(180 83 9 / 0.5);
}
.dark\:border-yellow-700:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));
}
.dark\:border-yellow-800:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(133 77 14 / var(--tw-border-opacity, 1));
}
.dark\:border-yellow-800\/50:is(.dark *){
  border-color: rgb(133 77 14 / 0.5);
}
.dark\:border-l-module-admin-border:is(.dark *){
  --tw-border-opacity: 1;
  border-left-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:border-l-module-financial-border:is(.dark *){
  --tw-border-opacity: 1;
  border-left-color: rgb(52 211 153 / var(--tw-border-opacity, 1));
}
.dark\:border-l-module-people-border:is(.dark *){
  --tw-border-opacity: 1;
  border-left-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.dark\:border-l-module-scheduler-border:is(.dark *){
  --tw-border-opacity: 1;
  border-left-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:bg-\[\#1a1d2a\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(26 29 42 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#23272f\]\/90:is(.dark *){
  background-color: rgb(35 39 47 / 0.9);
}
.dark\:bg-\[\#23273a\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(35 39 58 / var(--tw-bg-opacity, 1));
}
.dark\:bg-\[\#7c3aed\]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}
.dark\:bg-amber-400\/40:is(.dark *){
  background-color: rgb(251 191 36 / 0.4);
}
.dark\:bg-amber-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(217 119 6 / var(--tw-bg-opacity, 1));
}
.dark\:bg-amber-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}
.dark\:bg-amber-800\/30:is(.dark *){
  background-color: rgb(146 64 14 / 0.3);
}
.dark\:bg-amber-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity, 1));
}
.dark\:bg-amber-900\/20:is(.dark *){
  background-color: rgb(120 53 15 / 0.2);
}
.dark\:bg-amber-900\/30:is(.dark *){
  background-color: rgb(120 53 15 / 0.3);
}
.dark\:bg-amber-900\/50:is(.dark *){
  background-color: rgb(120 53 15 / 0.5);
}
.dark\:bg-appointmentStatus-CANCELLED-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(136 19 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-appointmentStatus-COMPLETED-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.dark\:bg-appointmentStatus-CONFIRMED-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));
}
.dark\:bg-appointmentStatus-NO_SHOW-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity, 1));
}
.dark\:bg-appointmentStatus-PENDING-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(120 53 15 / var(--tw-bg-opacity, 1));
}
.dark\:bg-background:is(.dark *){
  background-color: hsl(var(--background));
}
.dark\:bg-black\/10:is(.dark *){
  background-color: rgb(0 0 0 / 0.1);
}
.dark\:bg-black\/70:is(.dark *){
  background-color: rgb(0 0 0 / 0.7);
}
.dark\:bg-blue-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-400\/40:is(.dark *){
  background-color: rgb(96 165 250 / 0.4);
}
.dark\:bg-blue-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-800\/30:is(.dark *){
  background-color: rgb(30 64 175 / 0.3);
}
.dark\:bg-blue-800\/50:is(.dark *){
  background-color: rgb(30 64 175 / 0.5);
}
.dark\:bg-blue-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.dark\:bg-blue-900\/10:is(.dark *){
  background-color: rgb(30 58 138 / 0.1);
}
.dark\:bg-blue-900\/20:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}
.dark\:bg-blue-900\/30:is(.dark *){
  background-color: rgb(30 58 138 / 0.3);
}
.dark\:bg-blue-900\/40:is(.dark *){
  background-color: rgb(30 58 138 / 0.4);
}
.dark\:bg-blue-900\/50:is(.dark *){
  background-color: rgb(30 58 138 / 0.5);
}
.dark\:bg-cyan-400\/40:is(.dark *){
  background-color: rgb(34 211 238 / 0.4);
}
.dark\:bg-cyan-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(8 145 178 / var(--tw-bg-opacity, 1));
}
.dark\:bg-cyan-800\/30:is(.dark *){
  background-color: rgb(21 94 117 / 0.3);
}
.dark\:bg-cyan-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 78 99 / var(--tw-bg-opacity, 1));
}
.dark\:bg-cyan-900\/10:is(.dark *){
  background-color: rgb(22 78 99 / 0.1);
}
.dark\:bg-cyan-900\/20:is(.dark *){
  background-color: rgb(22 78 99 / 0.2);
}
.dark\:bg-cyan-900\/30:is(.dark *){
  background-color: rgb(22 78 99 / 0.3);
}
.dark\:bg-cyan-900\/50:is(.dark *){
  background-color: rgb(22 78 99 / 0.5);
}
.dark\:bg-emerald-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.dark\:bg-emerald-800\/30:is(.dark *){
  background-color: rgb(6 95 70 / 0.3);
}
.dark\:bg-emerald-900\/10:is(.dark *){
  background-color: rgb(6 78 59 / 0.1);
}
.dark\:bg-emerald-900\/20:is(.dark *){
  background-color: rgb(6 78 59 / 0.2);
}
.dark\:bg-emerald-900\/30:is(.dark *){
  background-color: rgb(6 78 59 / 0.3);
}
.dark\:bg-error-700\/30:is(.dark *){
  background-color: rgb(185 28 28 / 0.3);
}
.dark\:bg-gray-400\/40:is(.dark *){
  background-color: rgb(156 163 175 / 0.4);
}
.dark\:bg-gray-50:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-700\/40:is(.dark *){
  background-color: rgb(55 65 81 / 0.4);
}
.dark\:bg-gray-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-800\/10:is(.dark *){
  background-color: rgb(31 41 55 / 0.1);
}
.dark\:bg-gray-800\/30:is(.dark *){
  background-color: rgb(31 41 55 / 0.3);
}
.dark\:bg-gray-800\/40:is(.dark *){
  background-color: rgb(31 41 55 / 0.4);
}
.dark\:bg-gray-800\/50:is(.dark *){
  background-color: rgb(31 41 55 / 0.5);
}
.dark\:bg-gray-800\/60:is(.dark *){
  background-color: rgb(31 41 55 / 0.6);
}
.dark\:bg-gray-800\/80:is(.dark *){
  background-color: rgb(31 41 55 / 0.8);
}
.dark\:bg-gray-800\/90:is(.dark *){
  background-color: rgb(31 41 55 / 0.9);
}
.dark\:bg-gray-800\/95:is(.dark *){
  background-color: rgb(31 41 55 / 0.95);
}
.dark\:bg-gray-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));
}
.dark\:bg-gray-900\/20:is(.dark *){
  background-color: rgb(17 24 39 / 0.2);
}
.dark\:bg-gray-900\/30:is(.dark *){
  background-color: rgb(17 24 39 / 0.3);
}
.dark\:bg-gray-900\/50:is(.dark *){
  background-color: rgb(17 24 39 / 0.5);
}
.dark\:bg-gray-900\/80:is(.dark *){
  background-color: rgb(17 24 39 / 0.8);
}
.dark\:bg-gray-900\/90:is(.dark *){
  background-color: rgb(17 24 39 / 0.9);
}
.dark\:bg-gray-900\/95:is(.dark *){
  background-color: rgb(17 24 39 / 0.95);
}
.dark\:bg-gray-950:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 101 52 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-800\/30:is(.dark *){
  background-color: rgb(22 101 52 / 0.3);
}
.dark\:bg-green-800\/50:is(.dark *){
  background-color: rgb(22 101 52 / 0.5);
}
.dark\:bg-green-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(20 83 45 / var(--tw-bg-opacity, 1));
}
.dark\:bg-green-900\/10:is(.dark *){
  background-color: rgb(20 83 45 / 0.1);
}
.dark\:bg-green-900\/20:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}
.dark\:bg-green-900\/30:is(.dark *){
  background-color: rgb(20 83 45 / 0.3);
}
.dark\:bg-green-900\/40:is(.dark *){
  background-color: rgb(20 83 45 / 0.4);
}
.dark\:bg-indigo-400\/40:is(.dark *){
  background-color: rgb(129 140 248 / 0.4);
}
.dark\:bg-indigo-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));
}
.dark\:bg-indigo-800\/30:is(.dark *){
  background-color: rgb(55 48 163 / 0.3);
}
.dark\:bg-indigo-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(49 46 129 / var(--tw-bg-opacity, 1));
}
.dark\:bg-indigo-900\/10:is(.dark *){
  background-color: rgb(49 46 129 / 0.1);
}
.dark\:bg-indigo-900\/20:is(.dark *){
  background-color: rgb(49 46 129 / 0.2);
}
.dark\:bg-indigo-900\/30:is(.dark *){
  background-color: rgb(49 46 129 / 0.3);
}
.dark\:bg-module-abaplus-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 78 74 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-abaplus-bg-dark\/10:is(.dark *){
  background-color: rgb(19 78 74 / 0.1);
}
.dark\:bg-module-abaplus-bg-dark\/5:is(.dark *){
  background-color: rgb(19 78 74 / 0.05);
}
.dark\:bg-module-admin-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-admin-bg-dark\/10:is(.dark *){
  background-color: rgb(51 65 85 / 0.1);
}
.dark\:bg-module-admin-bg-dark\/5:is(.dark *){
  background-color: rgb(51 65 85 / 0.05);
}
.dark\:bg-module-chat-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-dashboard-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-financial-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(6 78 59 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-financial-bg-dark\/10:is(.dark *){
  background-color: rgb(6 78 59 / 0.1);
}
.dark\:bg-module-financial-bg-dark\/5:is(.dark *){
  background-color: rgb(6 78 59 / 0.05);
}
.dark\:bg-module-hr-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-people-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-people-bg-dark\/10:is(.dark *){
  background-color: rgb(124 45 18 / 0.1);
}
.dark\:bg-module-people-bg-dark\/5:is(.dark *){
  background-color: rgb(124 45 18 / 0.05);
}
.dark\:bg-module-scheduler-bg-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-scheduler-bg-dark\/10:is(.dark *){
  background-color: rgb(88 28 135 / 0.1);
}
.dark\:bg-module-scheduler-bg-dark\/5:is(.dark *){
  background-color: rgb(88 28 135 / 0.05);
}
.dark\:bg-module-scheduler-border-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}
.dark\:bg-module-scheduler-icon-dark:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));
}
.dark\:bg-neutral-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.dark\:bg-neutral-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:bg-neutral-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(251 146 60 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-400\/40:is(.dark *){
  background-color: rgb(251 146 60 / 0.4);
}
.dark\:bg-orange-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-700\/60:is(.dark *){
  background-color: rgb(194 65 12 / 0.6);
}
.dark\:bg-orange-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-800\/30:is(.dark *){
  background-color: rgb(154 52 18 / 0.3);
}
.dark\:bg-orange-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(124 45 18 / var(--tw-bg-opacity, 1));
}
.dark\:bg-orange-900\/10:is(.dark *){
  background-color: rgb(124 45 18 / 0.1);
}
.dark\:bg-orange-900\/20:is(.dark *){
  background-color: rgb(124 45 18 / 0.2);
}
.dark\:bg-orange-900\/30:is(.dark *){
  background-color: rgb(124 45 18 / 0.3);
}
.dark\:bg-orange-900\/40:is(.dark *){
  background-color: rgb(124 45 18 / 0.4);
}
.dark\:bg-pink-900\/30:is(.dark *){
  background-color: rgb(131 24 67 / 0.3);
}
.dark\:bg-primary-300\/40:is(.dark *){
  background-color: rgb(255 194 133 / 0.4);
}
.dark\:bg-primary-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 179 102 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-400\/40:is(.dark *){
  background-color: rgb(255 179 102 / 0.4);
}
.dark\:bg-primary-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 153 51 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-500\/40:is(.dark *){
  background-color: rgb(255 153 51 / 0.4);
}
.dark\:bg-primary-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 127 0 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(204 102 0 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-700\/30:is(.dark *){
  background-color: rgb(204 102 0 / 0.3);
}
.dark\:bg-primary-800\/30:is(.dark *){
  background-color: rgb(153 76 0 / 0.3);
}
.dark\:bg-primary-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(102 51 0 / var(--tw-bg-opacity, 1));
}
.dark\:bg-primary-900\/20:is(.dark *){
  background-color: rgb(102 51 0 / 0.2);
}
.dark\:bg-primary-900\/30:is(.dark *){
  background-color: rgb(102 51 0 / 0.3);
}
.dark\:bg-primary-900\/40:is(.dark *){
  background-color: rgb(102 51 0 / 0.4);
}
.dark\:bg-purple-400\/40:is(.dark *){
  background-color: rgb(192 132 252 / 0.4);
}
.dark\:bg-purple-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.dark\:bg-purple-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(126 34 206 / var(--tw-bg-opacity, 1));
}
.dark\:bg-purple-800\/30:is(.dark *){
  background-color: rgb(107 33 168 / 0.3);
}
.dark\:bg-purple-800\/40:is(.dark *){
  background-color: rgb(107 33 168 / 0.4);
}
.dark\:bg-purple-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.dark\:bg-purple-900\/10:is(.dark *){
  background-color: rgb(88 28 135 / 0.1);
}
.dark\:bg-purple-900\/20:is(.dark *){
  background-color: rgb(88 28 135 / 0.2);
}
.dark\:bg-purple-900\/30:is(.dark *){
  background-color: rgb(88 28 135 / 0.3);
}
.dark\:bg-red-500\/70:is(.dark *){
  background-color: rgb(239 68 68 / 0.7);
}
.dark\:bg-red-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-600\/60:is(.dark *){
  background-color: rgb(220 38 38 / 0.6);
}
.dark\:bg-red-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-700\/50:is(.dark *){
  background-color: rgb(185 28 28 / 0.5);
}
.dark\:bg-red-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-800\/30:is(.dark *){
  background-color: rgb(153 27 27 / 0.3);
}
.dark\:bg-red-800\/40:is(.dark *){
  background-color: rgb(153 27 27 / 0.4);
}
.dark\:bg-red-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(127 29 29 / var(--tw-bg-opacity, 1));
}
.dark\:bg-red-900\/10:is(.dark *){
  background-color: rgb(127 29 29 / 0.1);
}
.dark\:bg-red-900\/20:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}
.dark\:bg-red-900\/30:is(.dark *){
  background-color: rgb(127 29 29 / 0.3);
}
.dark\:bg-red-900\/50:is(.dark *){
  background-color: rgb(127 29 29 / 0.5);
}
.dark\:bg-rose-900\/20:is(.dark *){
  background-color: rgb(136 19 55 / 0.2);
}
.dark\:bg-rose-900\/30:is(.dark *){
  background-color: rgb(136 19 55 / 0.3);
}
.dark\:bg-sky-400\/40:is(.dark *){
  background-color: rgb(56 189 248 / 0.4);
}
.dark\:bg-slate-400:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(148 163 184 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-400\/40:is(.dark *){
  background-color: rgb(148 163 184 / 0.4);
}
.dark\:bg-slate-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-800\/30:is(.dark *){
  background-color: rgb(30 41 59 / 0.3);
}
.dark\:bg-slate-800\/50:is(.dark *){
  background-color: rgb(30 41 59 / 0.5);
}
.dark\:bg-slate-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));
}
.dark\:bg-slate-900\/20:is(.dark *){
  background-color: rgb(15 23 42 / 0.2);
}
.dark\:bg-slate-900\/30:is(.dark *){
  background-color: rgb(15 23 42 / 0.3);
}
.dark\:bg-success-700\/30:is(.dark *){
  background-color: rgb(4 120 87 / 0.3);
}
.dark\:bg-teal-500:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.dark\:bg-teal-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}
.dark\:bg-teal-700:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(15 118 110 / var(--tw-bg-opacity, 1));
}
.dark\:bg-teal-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 94 89 / var(--tw-bg-opacity, 1));
}
.dark\:bg-teal-800\/30:is(.dark *){
  background-color: rgb(17 94 89 / 0.3);
}
.dark\:bg-teal-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(19 78 74 / var(--tw-bg-opacity, 1));
}
.dark\:bg-teal-900\/10:is(.dark *){
  background-color: rgb(19 78 74 / 0.1);
}
.dark\:bg-teal-900\/20:is(.dark *){
  background-color: rgb(19 78 74 / 0.2);
}
.dark\:bg-teal-900\/30:is(.dark *){
  background-color: rgb(19 78 74 / 0.3);
}
.dark\:bg-transparent:is(.dark *){
  background-color: transparent;
}
.dark\:bg-violet-600:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(124 58 237 / var(--tw-bg-opacity, 1));
}
.dark\:bg-violet-900\/30:is(.dark *){
  background-color: rgb(76 29 149 / 0.3);
}
.dark\:bg-warning-700\/30:is(.dark *){
  background-color: rgb(180 83 9 / 0.3);
}
.dark\:bg-white\/20:is(.dark *){
  background-color: rgb(255 255 255 / 0.2);
}
.dark\:bg-yellow-400\/40:is(.dark *){
  background-color: rgb(250 204 21 / 0.4);
}
.dark\:bg-yellow-800:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));
}
.dark\:bg-yellow-900:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(113 63 18 / var(--tw-bg-opacity, 1));
}
.dark\:bg-yellow-900\/10:is(.dark *){
  background-color: rgb(113 63 18 / 0.1);
}
.dark\:bg-yellow-900\/20:is(.dark *){
  background-color: rgb(113 63 18 / 0.2);
}
.dark\:bg-yellow-900\/30:is(.dark *){
  background-color: rgb(113 63 18 / 0.3);
}
.dark\:bg-zinc-400\/40:is(.dark *){
  background-color: rgb(161 161 170 / 0.4);
}
.dark\:bg-opacity-50:is(.dark *){
  --tw-bg-opacity: 0.5;
}
.dark\:bg-opacity-80:is(.dark *){
  --tw-bg-opacity: 0.8;
}
.dark\:bg-opacity-90:is(.dark *){
  --tw-bg-opacity: 0.9;
}
.dark\:from-amber-600:is(.dark *){
  --tw-gradient-from: #d97706 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(217 119 6 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-amber-700:is(.dark *){
  --tw-gradient-from: #b45309 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(180 83 9 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-amber-900\/20:is(.dark *){
  --tw-gradient-from: rgb(120 53 15 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(120 53 15 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-background:is(.dark *){
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-blue-600:is(.dark *){
  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-blue-900\/10:is(.dark *){
  --tw-gradient-from: rgb(30 58 138 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-blue-900\/20:is(.dark *){
  --tw-gradient-from: rgb(30 58 138 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-blue-900\/30:is(.dark *){
  --tw-gradient-from: rgb(30 58 138 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-cyan-600:is(.dark *){
  --tw-gradient-from: #0891b2 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(8 145 178 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-cyan-700:is(.dark *){
  --tw-gradient-from: #0e7490 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 116 144 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-cyan-900\/10:is(.dark *){
  --tw-gradient-from: rgb(22 78 99 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-cyan-900\/30:is(.dark *){
  --tw-gradient-from: rgb(22 78 99 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-emerald-600:is(.dark *){
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-700:is(.dark *){
  --tw-gradient-from: #374151 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(55 65 81 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-800:is(.dark *){
  --tw-gradient-from: #1f2937 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-800\/50:is(.dark *){
  --tw-gradient-from: rgb(31 41 55 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(31 41 55 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-900:is(.dark *){
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-gray-900\/20:is(.dark *){
  --tw-gradient-from: rgb(17 24 39 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-green-600:is(.dark *){
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-green-900\/10:is(.dark *){
  --tw-gradient-from: rgb(20 83 45 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-green-900\/20:is(.dark *){
  --tw-gradient-from: rgb(20 83 45 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(20 83 45 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-module-scheduler-bg-dark:is(.dark *){
  --tw-gradient-from: #581c87 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-orange-600:is(.dark *){
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-orange-700:is(.dark *){
  --tw-gradient-from: #c2410c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(194 65 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-orange-900\/10:is(.dark *){
  --tw-gradient-from: rgb(124 45 18 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-orange-900\/20:is(.dark *){
  --tw-gradient-from: rgb(124 45 18 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 45 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-800\/30:is(.dark *){
  --tw-gradient-from: rgb(153 76 0 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(153 76 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-primary-900\/20:is(.dark *){
  --tw-gradient-from: rgb(102 51 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(102 51 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-purple-400:is(.dark *){
  --tw-gradient-from: #c084fc var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(192 132 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-purple-700:is(.dark *){
  --tw-gradient-from: #7e22ce var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(126 34 206 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-purple-900\/10:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-purple-900\/20:is(.dark *){
  --tw-gradient-from: rgb(88 28 135 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(88 28 135 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-red-700:is(.dark *){
  --tw-gradient-from: #b91c1c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(185 28 28 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-red-900\/20:is(.dark *){
  --tw-gradient-from: rgb(127 29 29 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(127 29 29 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-400:is(.dark *){
  --tw-gradient-from: #94a3b8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(148 163 184 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-500:is(.dark *){
  --tw-gradient-from: #64748b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(100 116 139 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-600:is(.dark *){
  --tw-gradient-from: #475569 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 85 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-700:is(.dark *){
  --tw-gradient-from: #334155 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(51 65 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-900\/20:is(.dark *){
  --tw-gradient-from: rgb(15 23 42 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-slate-900\/50:is(.dark *){
  --tw-gradient-from: rgb(15 23 42 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 23 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-teal-700:is(.dark *){
  --tw-gradient-from: #0f766e var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(15 118 110 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-teal-900\/20:is(.dark *){
  --tw-gradient-from: rgb(19 78 74 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(19 78 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-violet-600:is(.dark *){
  --tw-gradient-from: #7c3aed var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(124 58 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-violet-700:is(.dark *){
  --tw-gradient-from: #6d28d9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(109 40 217 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:from-yellow-900\/20:is(.dark *){
  --tw-gradient-from: rgb(113 63 18 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(113 63 18 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:via-blue-900\/20:is(.dark *){
  --tw-gradient-to: rgb(30 58 138 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 58 138 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-cyan-600:is(.dark *){
  --tw-gradient-to: rgb(8 145 178 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #0891b2 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-cyan-900\/20:is(.dark *){
  --tw-gradient-to: rgb(22 78 99 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(22 78 99 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-gray-800:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #1f2937 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-gray-800\/20:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(31 41 55 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-gray-900:is(.dark *){
  --tw-gradient-to: rgb(17 24 39 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #111827 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-gray-900\/20:is(.dark *){
  --tw-gradient-to: rgb(17 24 39 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(17 24 39 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-orange-900\/20:is(.dark *){
  --tw-gradient-to: rgb(124 45 18 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(124 45 18 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-primary-800\/20:is(.dark *){
  --tw-gradient-to: rgb(153 76 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(153 76 0 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:via-red-900\/20:is(.dark *){
  --tw-gradient-to: rgb(127 29 29 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(127 29 29 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.dark\:to-amber-600:is(.dark *){
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.dark\:to-amber-700:is(.dark *){
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}
.dark\:to-amber-800:is(.dark *){
  --tw-gradient-to: #92400e var(--tw-gradient-to-position);
}
.dark\:to-amber-900\/10:is(.dark *){
  --tw-gradient-to: rgb(120 53 15 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-amber-900\/20:is(.dark *){
  --tw-gradient-to: rgb(120 53 15 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-background:is(.dark *){
  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);
}
.dark\:to-blue-700:is(.dark *){
  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);
}
.dark\:to-blue-900\/30:is(.dark *){
  --tw-gradient-to: rgb(30 58 138 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-cyan-800:is(.dark *){
  --tw-gradient-to: #155e75 var(--tw-gradient-to-position);
}
.dark\:to-cyan-900:is(.dark *){
  --tw-gradient-to: #164e63 var(--tw-gradient-to-position);
}
.dark\:to-cyan-900\/20:is(.dark *){
  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-emerald-600:is(.dark *){
  --tw-gradient-to: #059669 var(--tw-gradient-to-position);
}
.dark\:to-emerald-700:is(.dark *){
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}
.dark\:to-emerald-900\/10:is(.dark *){
  --tw-gradient-to: rgb(6 78 59 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-emerald-900\/20:is(.dark *){
  --tw-gradient-to: rgb(6 78 59 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-gray-600:is(.dark *){
  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);
}
.dark\:to-gray-700:is(.dark *){
  --tw-gradient-to: #374151 var(--tw-gradient-to-position);
}
.dark\:to-gray-700\/20:is(.dark *){
  --tw-gradient-to: rgb(55 65 81 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-gray-800:is(.dark *){
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}
.dark\:to-gray-800\/20:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-gray-800\/30:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-gray-800\/50:is(.dark *){
  --tw-gradient-to: rgb(31 41 55 / 0.5) var(--tw-gradient-to-position);
}
.dark\:to-gray-900:is(.dark *){
  --tw-gradient-to: #111827 var(--tw-gradient-to-position);
}
.dark\:to-gray-900\/20:is(.dark *){
  --tw-gradient-to: rgb(17 24 39 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-gray-900\/50:is(.dark *){
  --tw-gradient-to: rgb(17 24 39 / 0.5) var(--tw-gradient-to-position);
}
.dark\:to-green-600:is(.dark *){
  --tw-gradient-to: #16a34a var(--tw-gradient-to-position);
}
.dark\:to-indigo-500:is(.dark *){
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.dark\:to-indigo-900\/10:is(.dark *){
  --tw-gradient-to: rgb(49 46 129 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-indigo-900\/20:is(.dark *){
  --tw-gradient-to: rgb(49 46 129 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-indigo-900\/30:is(.dark *){
  --tw-gradient-to: rgb(49 46 129 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-module-scheduler-hover-dark:is(.dark *){
  --tw-gradient-to: #6b21a8 var(--tw-gradient-to-position);
}
.dark\:to-orange-600:is(.dark *){
  --tw-gradient-to: #ea580c var(--tw-gradient-to-position);
}
.dark\:to-orange-800\/20:is(.dark *){
  --tw-gradient-to: rgb(154 52 18 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-orange-900:is(.dark *){
  --tw-gradient-to: #7c2d12 var(--tw-gradient-to-position);
}
.dark\:to-orange-900\/20:is(.dark *){
  --tw-gradient-to: rgb(124 45 18 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-pink-900\/20:is(.dark *){
  --tw-gradient-to: rgb(131 24 67 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-primary-700\/20:is(.dark *){
  --tw-gradient-to: rgb(204 102 0 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-primary-800\/20:is(.dark *){
  --tw-gradient-to: rgb(153 76 0 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-primary-900\/30:is(.dark *){
  --tw-gradient-to: rgb(102 51 0 / 0.3) var(--tw-gradient-to-position);
}
.dark\:to-purple-600:is(.dark *){
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.dark\:to-rose-600:is(.dark *){
  --tw-gradient-to: #e11d48 var(--tw-gradient-to-position);
}
.dark\:to-sky-900\/10:is(.dark *){
  --tw-gradient-to: rgb(12 74 110 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-sky-900\/20:is(.dark *){
  --tw-gradient-to: rgb(12 74 110 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-slate-500:is(.dark *){
  --tw-gradient-to: #64748b var(--tw-gradient-to-position);
}
.dark\:to-slate-600:is(.dark *){
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.dark\:to-slate-700:is(.dark *){
  --tw-gradient-to: #334155 var(--tw-gradient-to-position);
}
.dark\:to-slate-800\/20:is(.dark *){
  --tw-gradient-to: rgb(30 41 59 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-slate-900:is(.dark *){
  --tw-gradient-to: #0f172a var(--tw-gradient-to-position);
}
.dark\:to-slate-900\/20:is(.dark *){
  --tw-gradient-to: rgb(15 23 42 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-teal-600:is(.dark *){
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.dark\:to-violet-600:is(.dark *){
  --tw-gradient-to: #7c3aed var(--tw-gradient-to-position);
}
.dark\:to-violet-700:is(.dark *){
  --tw-gradient-to: #6d28d9 var(--tw-gradient-to-position);
}
.dark\:to-violet-800:is(.dark *){
  --tw-gradient-to: #5b21b6 var(--tw-gradient-to-position);
}
.dark\:to-violet-900\/10:is(.dark *){
  --tw-gradient-to: rgb(76 29 149 / 0.1) var(--tw-gradient-to-position);
}
.dark\:to-violet-900\/20:is(.dark *){
  --tw-gradient-to: rgb(76 29 149 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-white\/5:is(.dark *){
  --tw-gradient-to: rgb(255 255 255 / 0.05) var(--tw-gradient-to-position);
}
.dark\:to-yellow-800\/20:is(.dark *){
  --tw-gradient-to: rgb(133 77 14 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-yellow-900\/20:is(.dark *){
  --tw-gradient-to: rgb(113 63 18 / 0.2) var(--tw-gradient-to-position);
}
.dark\:to-zinc-900\/20:is(.dark *){
  --tw-gradient-to: rgb(24 24 27 / 0.2) var(--tw-gradient-to-position);
}
.dark\:fill-gray-400:is(.dark *){
  fill: #9ca3af;
}
.dark\:stroke-gray-400:is(.dark *){
  stroke: #9ca3af;
}
.dark\:stroke-gray-600:is(.dark *){
  stroke: #4b5563;
}
.dark\:stroke-gray-700:is(.dark *){
  stroke: #374151;
}
.dark\:text-\[\#6b7280\]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.dark\:text-\[\#a78bfa\]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.dark\:text-amber-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.dark\:text-amber-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.dark\:text-amber-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.dark\:text-amber-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity, 1));
}
.dark\:text-amber-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(217 119 6 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-CANCELLED-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 164 175 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-CANCELLED-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 228 230 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-COMPLETED-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-COMPLETED-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-CONFIRMED-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-CONFIRMED-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 250 229 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-NO_SHOW-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-NO_SHOW-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-PENDING-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.dark\:text-appointmentStatus-PENDING-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 243 199 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.dark\:text-blue-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.dark\:text-cyan-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(165 243 252 / var(--tw-text-opacity, 1));
}
.dark\:text-cyan-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}
.dark\:text-cyan-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.dark\:text-cyan-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(8 145 178 / var(--tw-text-opacity, 1));
}
.dark\:text-emerald-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(167 243 208 / var(--tw-text-opacity, 1));
}
.dark\:text-emerald-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}
.dark\:text-emerald-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(52 211 153 / var(--tw-text-opacity, 1));
}
.dark\:text-emerald-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity, 1));
}
.dark\:text-error-50:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 242 242 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.dark\:text-gray-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.dark\:text-green-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(220 252 231 / var(--tw-text-opacity, 1));
}
.dark\:text-green-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.dark\:text-green-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.dark\:text-green-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(224 231 255 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(199 210 254 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.dark\:text-indigo-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity, 1));
}
.dark\:text-module-abaplus-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.dark\:text-module-abaplus-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(204 251 241 / var(--tw-text-opacity, 1));
}
.dark\:text-module-abaplus-text-dark\/70:is(.dark *){
  color: rgb(204 251 241 / 0.7);
}
.dark\:text-module-admin-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.dark\:text-module-admin-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:text-module-admin-text-dark\/70:is(.dark *){
  color: rgb(226 232 240 / 0.7);
}
.dark\:text-module-chat-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:text-module-chat-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(239 246 255 / var(--tw-text-opacity, 1));
}
.dark\:text-module-dashboard-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:text-module-dashboard-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(219 234 254 / var(--tw-text-opacity, 1));
}
.dark\:text-module-financial-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}
.dark\:text-module-financial-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 250 229 / var(--tw-text-opacity, 1));
}
.dark\:text-module-financial-text-dark\/70:is(.dark *){
  color: rgb(209 250 229 / 0.7);
}
.dark\:text-module-hr-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:text-module-hr-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.dark\:text-module-people-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.dark\:text-module-people-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}
.dark\:text-module-people-text-dark\/50:is(.dark *){
  color: rgb(255 237 213 / 0.5);
}
.dark\:text-module-people-text-dark\/70:is(.dark *){
  color: rgb(255 237 213 / 0.7);
}
.dark\:text-module-scheduler-icon-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.dark\:text-module-scheduler-text-dark:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.dark\:text-module-scheduler-text-dark\/50:is(.dark *){
  color: rgb(243 232 255 / 0.5);
}
.dark\:text-module-scheduler-text-dark\/70:is(.dark *){
  color: rgb(243 232 255 / 0.7);
}
.dark\:text-neutral-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:text-neutral-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:text-neutral-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:text-neutral-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:text-neutral-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.dark\:text-orange-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}
.dark\:text-orange-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.dark\:text-orange-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.dark\:text-orange-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.dark\:text-orange-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.dark\:text-pink-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(249 168 212 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 228 204 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 213 168 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 194 133 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 179 102 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-50:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 247 237 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 153 51 / var(--tw-text-opacity, 1));
}
.dark\:text-primary-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 127 0 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(216 180 254 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:text-purple-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 51 234 / var(--tw-text-opacity, 1));
}
.dark\:text-red-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 226 226 / var(--tw-text-opacity, 1));
}
.dark\:text-red-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.dark\:text-red-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:text-red-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.dark\:text-rose-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 164 175 / var(--tw-text-opacity, 1));
}
.dark\:text-rose-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 113 133 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity, 1));
}
.dark\:text-slate-600:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity, 1));
}
.dark\:text-success-50:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(236 253 245 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(204 251 241 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(153 246 228 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}
.dark\:text-teal-500:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}
.dark\:text-violet-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(196 181 253 / var(--tw-text-opacity, 1));
}
.dark\:text-violet-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(167 139 250 / var(--tw-text-opacity, 1));
}
.dark\:text-warning-50:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 251 235 / var(--tw-text-opacity, 1));
}
.dark\:text-white:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-100:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 249 195 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-200:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 240 138 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 224 71 / var(--tw-text-opacity, 1));
}
.dark\:text-yellow-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity, 1));
}
.dark\:decoration-neutral-600:is(.dark *){
  text-decoration-color: #4B5563;
}
.dark\:placeholder-gray-400:is(.dark *)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}
.dark\:placeholder-module-people-text-dark\/50:is(.dark *)::placeholder{
  color: rgb(255 237 213 / 0.5);
}
.dark\:placeholder-module-scheduler-text-dark\/50:is(.dark *)::placeholder{
  color: rgb(243 232 255 / 0.5);
}
.dark\:placeholder-neutral-500:is(.dark *)::placeholder{
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.dark\:shadow-hard-dark:is(.dark *){
  --tw-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.7);
  --tw-shadow-colored: 0 8px 30px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-lg:is(.dark *){
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-md:is(.dark *){
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-xl:is(.dark *){
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-amber-900\/20:is(.dark *){
  --tw-shadow-color: rgb(120 53 15 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-black\/10:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-black\/20:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-black\/30:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-black\/50:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-blue-900\/20:is(.dark *){
  --tw-shadow-color: rgb(30 58 138 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-cyan-900\/20:is(.dark *){
  --tw-shadow-color: rgb(22 78 99 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-emerald-900\/20:is(.dark *){
  --tw-shadow-color: rgb(6 78 59 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-gray-900:is(.dark *){
  --tw-shadow-color: #111827;
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-gray-900\/30:is(.dark *){
  --tw-shadow-color: rgb(17 24 39 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-indigo-900\/20:is(.dark *){
  --tw-shadow-color: rgb(49 46 129 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-orange-900\/20:is(.dark *){
  --tw-shadow-color: rgb(124 45 18 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-purple-900\/20:is(.dark *){
  --tw-shadow-color: rgb(88 28 135 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-red-900\/20:is(.dark *){
  --tw-shadow-color: rgb(127 29 29 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-rose-900\/20:is(.dark *){
  --tw-shadow-color: rgb(136 19 55 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:shadow-violet-900\/20:is(.dark *){
  --tw-shadow-color: rgb(76 29 149 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:ring-\[\#a78bfa\]:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(167 139 250 / var(--tw-ring-opacity, 1));
}
.dark\:ring-blue-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));
}
.dark\:ring-cyan-300:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(103 232 249 / var(--tw-ring-opacity, 1));
}
.dark\:ring-cyan-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(21 94 117 / var(--tw-ring-opacity, 1));
}
.dark\:ring-cyan-800\/50:is(.dark *){
  --tw-ring-color: rgb(21 94 117 / 0.5);
}
.dark\:ring-gray-900:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(17 24 39 / var(--tw-ring-opacity, 1));
}
.dark\:ring-orange-600:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(234 88 12 / var(--tw-ring-opacity, 1));
}
.dark\:ring-purple-600:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(147 51 234 / var(--tw-ring-opacity, 1));
}
.dark\:ring-red-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(153 27 27 / var(--tw-ring-opacity, 1));
}
.dark\:ring-slate-600:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity, 1));
}
.dark\:ring-white\/10:is(.dark *){
  --tw-ring-color: rgb(255 255 255 / 0.1);
}
.dark\:ring-offset-gray-950:is(.dark *){
  --tw-ring-offset-color: #030712;
}
.dark\:invert:is(.dark *){
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.dark\:placeholder\:text-gray-400:is(.dark *)::placeholder{
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:checked\:border-blue-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.dark\:checked\:border-emerald-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.dark\:checked\:border-indigo-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}
.dark\:checked\:border-orange-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.dark\:checked\:border-slate-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.dark\:checked\:border-teal-300:checked:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}
.dark\:checked\:bg-blue-600:checked:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.dark\:checked\:bg-primary-500:checked:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 153 51 / var(--tw-bg-opacity, 1));
}
.dark\:checked\:bg-slate-500:checked:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.dark\:checked\:from-blue-400:checked:is(.dark *){
  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:from-emerald-400:checked:is(.dark *){
  --tw-gradient-from: #34d399 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(52 211 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:from-indigo-400:checked:is(.dark *){
  --tw-gradient-from: #818cf8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(129 140 248 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:from-orange-400:checked:is(.dark *){
  --tw-gradient-from: #fb923c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(251 146 60 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:from-slate-400:checked:is(.dark *){
  --tw-gradient-from: #94a3b8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(148 163 184 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:from-teal-400:checked:is(.dark *){
  --tw-gradient-from: #2dd4bf var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(45 212 191 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:checked\:to-blue-500:checked:is(.dark *){
  --tw-gradient-to: #3b82f6 var(--tw-gradient-to-position);
}
.dark\:checked\:to-emerald-500:checked:is(.dark *){
  --tw-gradient-to: #10b981 var(--tw-gradient-to-position);
}
.dark\:checked\:to-indigo-500:checked:is(.dark *){
  --tw-gradient-to: #6366f1 var(--tw-gradient-to-position);
}
.dark\:checked\:to-orange-500:checked:is(.dark *){
  --tw-gradient-to: #f97316 var(--tw-gradient-to-position);
}
.dark\:checked\:to-slate-500:checked:is(.dark *){
  --tw-gradient-to: #64748b var(--tw-gradient-to-position);
}
.dark\:checked\:to-teal-500:checked:is(.dark *){
  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);
}
.dark\:hover\:border-blue-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-blue-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-emerald-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-gray-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-gray-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-gray-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-green-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-indigo-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(165 180 252 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-module-admin-border-dark:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-module-financial-border-dark:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-module-people-border-dark:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-module-scheduler-border-dark:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-neutral-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(107 114 128 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-orange-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-orange-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-orange-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(234 88 12 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-orange-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(194 65 12 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-primary-400:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 179 102 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-purple-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-purple-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(147 51 234 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-slate-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(203 213 225 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-slate-500:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-slate-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-teal-300:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(94 234 212 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-teal-600:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(13 148 136 / var(--tw-border-opacity, 1));
}
.dark\:hover\:border-teal-700:hover:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(15 118 110 / var(--tw-border-opacity, 1));
}
.dark\:hover\:bg-\[\#a78bfa\]:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(167 139 250 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-amber-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(180 83 9 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-amber-900\/20:hover:is(.dark *){
  background-color: rgb(120 53 15 / 0.2);
}
.dark\:hover\:bg-amber-900\/40:hover:is(.dark *){
  background-color: rgb(120 53 15 / 0.4);
}
.dark\:hover\:bg-appointmentStatus-CANCELLED-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(159 18 57 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-appointmentStatus-COMPLETED-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-appointmentStatus-CONFIRMED-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(6 95 70 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-appointmentStatus-NO_SHOW-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-appointmentStatus-PENDING-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(146 64 14 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-blue-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-blue-800\/40:hover:is(.dark *){
  background-color: rgb(30 64 175 / 0.4);
}
.dark\:hover\:bg-blue-900\/20:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.2);
}
.dark\:hover\:bg-blue-900\/30:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.3);
}
.dark\:hover\:bg-blue-900\/40:hover:is(.dark *){
  background-color: rgb(30 58 138 / 0.4);
}
.dark\:hover\:bg-cyan-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(21 94 117 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-cyan-900\/20:hover:is(.dark *){
  background-color: rgb(22 78 99 / 0.2);
}
.dark\:hover\:bg-cyan-900\/30:hover:is(.dark *){
  background-color: rgb(22 78 99 / 0.3);
}
.dark\:hover\:bg-cyan-900\/40:hover:is(.dark *){
  background-color: rgb(22 78 99 / 0.4);
}
.dark\:hover\:bg-emerald-900\/20:hover:is(.dark *){
  background-color: rgb(6 78 59 / 0.2);
}
.dark\:hover\:bg-emerald-900\/40:hover:is(.dark *){
  background-color: rgb(6 78 59 / 0.4);
}
.dark\:hover\:bg-error-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-400:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-500:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-700\/50:hover:is(.dark *){
  background-color: rgb(55 65 81 / 0.5);
}
.dark\:hover\:bg-gray-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-gray-800\/50:hover:is(.dark *){
  background-color: rgb(31 41 55 / 0.5);
}
.dark\:hover\:bg-green-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-green-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-green-900\/20:hover:is(.dark *){
  background-color: rgb(20 83 45 / 0.2);
}
.dark\:hover\:bg-green-900\/30:hover:is(.dark *){
  background-color: rgb(20 83 45 / 0.3);
}
.dark\:hover\:bg-indigo-900\/20:hover:is(.dark *){
  background-color: rgb(49 46 129 / 0.2);
}
.dark\:hover\:bg-indigo-900\/40:hover:is(.dark *){
  background-color: rgb(49 46 129 / 0.4);
}
.dark\:hover\:bg-module-abaplus-bg-dark\/20:hover:is(.dark *){
  background-color: rgb(19 78 74 / 0.2);
}
.dark\:hover\:bg-module-abaplus-bg-dark\/5:hover:is(.dark *){
  background-color: rgb(19 78 74 / 0.05);
}
.dark\:hover\:bg-module-abaplus-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(17 94 89 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-admin-bg-dark\/20:hover:is(.dark *){
  background-color: rgb(51 65 85 / 0.2);
}
.dark\:hover\:bg-module-admin-bg-dark\/5:hover:is(.dark *){
  background-color: rgb(51 65 85 / 0.05);
}
.dark\:hover\:bg-module-admin-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-chat-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-dashboard-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-financial-bg-dark\/20:hover:is(.dark *){
  background-color: rgb(6 78 59 / 0.2);
}
.dark\:hover\:bg-module-financial-bg-dark\/5:hover:is(.dark *){
  background-color: rgb(6 78 59 / 0.05);
}
.dark\:hover\:bg-module-financial-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(6 95 70 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-hr-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-people-bg-dark\/20:hover:is(.dark *){
  background-color: rgb(124 45 18 / 0.2);
}
.dark\:hover\:bg-module-people-bg-dark\/5:hover:is(.dark *){
  background-color: rgb(124 45 18 / 0.05);
}
.dark\:hover\:bg-module-people-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(154 52 18 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-scheduler-bg-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(88 28 135 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-module-scheduler-bg-dark\/20:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.2);
}
.dark\:hover\:bg-module-scheduler-bg-dark\/5:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.05);
}
.dark\:hover\:bg-module-scheduler-hover-dark:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-orange-500:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-orange-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(234 88 12 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-orange-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(194 65 12 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-orange-900\/20:hover:is(.dark *){
  background-color: rgb(124 45 18 / 0.2);
}
.dark\:hover\:bg-orange-900\/40:hover:is(.dark *){
  background-color: rgb(124 45 18 / 0.4);
}
.dark\:hover\:bg-primary-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(255 127 0 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-primary-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(204 102 0 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-primary-800\/40:hover:is(.dark *){
  background-color: rgb(153 76 0 / 0.4);
}
.dark\:hover\:bg-primary-800\/60:hover:is(.dark *){
  background-color: rgb(153 76 0 / 0.6);
}
.dark\:hover\:bg-primary-900\/20:hover:is(.dark *){
  background-color: rgb(102 51 0 / 0.2);
}
.dark\:hover\:bg-primary-900\/50:hover:is(.dark *){
  background-color: rgb(102 51 0 / 0.5);
}
.dark\:hover\:bg-purple-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-purple-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(107 33 168 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-purple-900\/20:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.2);
}
.dark\:hover\:bg-purple-900\/30:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.3);
}
.dark\:hover\:bg-purple-900\/40:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.4);
}
.dark\:hover\:bg-purple-900\/50:hover:is(.dark *){
  background-color: rgb(88 28 135 / 0.5);
}
.dark\:hover\:bg-red-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-red-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-red-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(153 27 27 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-red-800\/40:hover:is(.dark *){
  background-color: rgb(153 27 27 / 0.4);
}
.dark\:hover\:bg-red-800\/50:hover:is(.dark *){
  background-color: rgb(153 27 27 / 0.5);
}
.dark\:hover\:bg-red-900\/20:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.2);
}
.dark\:hover\:bg-red-900\/30:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.3);
}
.dark\:hover\:bg-red-900\/40:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.4);
}
.dark\:hover\:bg-red-900\/50:hover:is(.dark *){
  background-color: rgb(127 29 29 / 0.5);
}
.dark\:hover\:bg-rose-900\/40:hover:is(.dark *){
  background-color: rgb(136 19 55 / 0.4);
}
.dark\:hover\:bg-slate-500:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-700\/50:hover:is(.dark *){
  background-color: rgb(51 65 85 / 0.5);
}
.dark\:hover\:bg-slate-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-slate-800\/20:hover:is(.dark *){
  background-color: rgb(30 41 59 / 0.2);
}
.dark\:hover\:bg-slate-900\/20:hover:is(.dark *){
  background-color: rgb(15 23 42 / 0.2);
}
.dark\:hover\:bg-teal-600:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(13 148 136 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-teal-900\/20:hover:is(.dark *){
  background-color: rgb(19 78 74 / 0.2);
}
.dark\:hover\:bg-violet-700:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(109 40 217 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:bg-violet-900\/20:hover:is(.dark *){
  background-color: rgb(76 29 149 / 0.2);
}
.dark\:hover\:bg-violet-900\/40:hover:is(.dark *){
  background-color: rgb(76 29 149 / 0.4);
}
.dark\:hover\:bg-white\/5:hover:is(.dark *){
  background-color: rgb(255 255 255 / 0.05);
}
.dark\:hover\:bg-yellow-800:hover:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(133 77 14 / var(--tw-bg-opacity, 1));
}
.dark\:hover\:from-blue-900\/50:hover:is(.dark *){
  --tw-gradient-from: rgb(30 58 138 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 58 138 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-cyan-700:hover:is(.dark *){
  --tw-gradient-from: #0e7490 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(14 116 144 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-cyan-800:hover:is(.dark *){
  --tw-gradient-from: #155e75 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 94 117 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-cyan-800\/40:hover:is(.dark *){
  --tw-gradient-from: rgb(21 94 117 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 94 117 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-cyan-900\/20:hover:is(.dark *){
  --tw-gradient-from: rgb(22 78 99 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 78 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-emerald-700:hover:is(.dark *){
  --tw-gradient-from: #047857 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(4 120 87 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-green-700:hover:is(.dark *){
  --tw-gradient-from: #15803d var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(21 128 61 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-orange-600:hover:is(.dark *){
  --tw-gradient-from: #ea580c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(234 88 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-orange-700:hover:is(.dark *){
  --tw-gradient-from: #c2410c var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(194 65 12 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-purple-600:hover:is(.dark *){
  --tw-gradient-from: #9333ea var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(147 51 234 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-purple-800:hover:is(.dark *){
  --tw-gradient-from: #6b21a8 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(107 33 168 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-red-800:hover:is(.dark *){
  --tw-gradient-from: #991b1b var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(153 27 27 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-slate-600:hover:is(.dark *){
  --tw-gradient-from: #475569 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(71 85 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-slate-700:hover:is(.dark *){
  --tw-gradient-from: #334155 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(51 65 85 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:from-violet-700:hover:is(.dark *){
  --tw-gradient-from: #6d28d9 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(109 40 217 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.dark\:hover\:to-amber-600:hover:is(.dark *){
  --tw-gradient-to: #d97706 var(--tw-gradient-to-position);
}
.dark\:hover\:to-amber-700:hover:is(.dark *){
  --tw-gradient-to: #b45309 var(--tw-gradient-to-position);
}
.dark\:hover\:to-blue-800\/40:hover:is(.dark *){
  --tw-gradient-to: rgb(30 64 175 / 0.4) var(--tw-gradient-to-position);
}
.dark\:hover\:to-cyan-800\/20:hover:is(.dark *){
  --tw-gradient-to: rgb(21 94 117 / 0.2) var(--tw-gradient-to-position);
}
.dark\:hover\:to-cyan-900:hover:is(.dark *){
  --tw-gradient-to: #164e63 var(--tw-gradient-to-position);
}
.dark\:hover\:to-cyan-900\/20:hover:is(.dark *){
  --tw-gradient-to: rgb(22 78 99 / 0.2) var(--tw-gradient-to-position);
}
.dark\:hover\:to-cyan-950:hover:is(.dark *){
  --tw-gradient-to: #083344 var(--tw-gradient-to-position);
}
.dark\:hover\:to-emerald-700:hover:is(.dark *){
  --tw-gradient-to: #047857 var(--tw-gradient-to-position);
}
.dark\:hover\:to-green-700:hover:is(.dark *){
  --tw-gradient-to: #15803d var(--tw-gradient-to-position);
}
.dark\:hover\:to-indigo-900\/50:hover:is(.dark *){
  --tw-gradient-to: rgb(49 46 129 / 0.5) var(--tw-gradient-to-position);
}
.dark\:hover\:to-purple-700:hover:is(.dark *){
  --tw-gradient-to: #7e22ce var(--tw-gradient-to-position);
}
.dark\:hover\:to-rose-700:hover:is(.dark *){
  --tw-gradient-to: #be123c var(--tw-gradient-to-position);
}
.dark\:hover\:to-slate-600:hover:is(.dark *){
  --tw-gradient-to: #475569 var(--tw-gradient-to-position);
}
.dark\:hover\:to-slate-700:hover:is(.dark *){
  --tw-gradient-to: #334155 var(--tw-gradient-to-position);
}
.dark\:hover\:to-slate-800:hover:is(.dark *){
  --tw-gradient-to: #1e293b var(--tw-gradient-to-position);
}
.dark\:hover\:to-violet-600:hover:is(.dark *){
  --tw-gradient-to: #7c3aed var(--tw-gradient-to-position);
}
.dark\:hover\:to-violet-700:hover:is(.dark *){
  --tw-gradient-to: #6d28d9 var(--tw-gradient-to-position);
}
.dark\:hover\:to-gray-600:hover:is(.dark *){
  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);
}
.dark\:hover\:text-amber-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 230 138 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-amber-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-amber-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 191 36 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-blue-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(191 219 254 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-blue-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-blue-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-cyan-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-cyan-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-gray-100:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-gray-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-gray-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-gray-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-green-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(187 247 208 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-green-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-green-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-indigo-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(129 140 248 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-abaplus-text-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(204 251 241 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-admin-text-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-financial-text-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(209 250 229 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-people-text-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 237 213 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-scheduler-hover-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(107 33 168 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-module-scheduler-text-dark:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 232 255 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-neutral-100:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-neutral-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-orange-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 215 170 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-orange-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-orange-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-primary-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 213 168 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-primary-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 194 133 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-primary-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 179 102 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-purple-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(233 213 255 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-purple-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(192 132 252 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-red-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(254 202 202 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-red-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-red-400:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-slate-200:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-teal-300:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.dark\:hover\:text-white:hover:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.dark\:hover\:shadow-lg:hover:is(.dark *){
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:hover\:shadow-black\/30:hover:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:hover\:shadow-black\/50:hover:is(.dark *){
  --tw-shadow-color: rgb(0 0 0 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:hover\:shadow-gray-900\/50:hover:is(.dark *){
  --tw-shadow-color: rgb(17 24 39 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:focus\:border-amber-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(251 191 36 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-blue-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-module-abaplus-border-dark:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-module-admin-border-dark:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-module-financial-border-dark:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-module-people-border-dark:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-module-scheduler-border-dark:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-orange-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(251 146 60 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-primary-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 179 102 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-primary-600:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 127 0 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-purple-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(192 132 252 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-red-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-slate-400:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(148 163 184 / var(--tw-border-opacity, 1));
}
.dark\:focus\:border-slate-500:focus:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:focus\:ring-blue-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(96 165 250 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-blue-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(96 165 250 / 0.5);
}
.dark\:focus\:ring-cyan-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(34 211 238 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-cyan-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(8 145 178 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-emerald-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(52 211 153 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-emerald-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(52 211 153 / 0.5);
}
.dark\:focus\:ring-green-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(74 222 128 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-indigo-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(129 140 248 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-indigo-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(129 140 248 / 0.5);
}
.dark\:focus\:ring-module-abaplus-border-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-admin-border-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-financial-border-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-people-border-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-scheduler-bg-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(88 28 135 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-scheduler-border-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-module-scheduler-icon-dark:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(216 180 254 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-orange-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(251 146 60 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-orange-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(251 146 60 / 0.5);
}
.dark\:focus\:ring-primary-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 179 102 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-primary-500:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 153 51 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-primary-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 127 0 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-purple-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(192 132 252 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-red-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-slate-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(148 163 184 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-slate-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(148 163 184 / 0.5);
}
.dark\:focus\:ring-slate-600:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(71 85 105 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-teal-400:focus:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(45 212 191 / var(--tw-ring-opacity, 1));
}
.dark\:focus\:ring-teal-400\/50:focus:is(.dark *){
  --tw-ring-color: rgb(45 212 191 / 0.5);
}
.dark\:focus\:ring-offset-gray-800:focus:is(.dark *){
  --tw-ring-offset-color: #1f2937;
}
.dark\:focus\:ring-offset-gray-900:focus:is(.dark *){
  --tw-ring-offset-color: #111827;
}
.dark\:focus-visible\:\!border-module-abaplus-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-module-admin-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-module-financial-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-module-people-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-module-scheduler-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-primary-400:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 179 102 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:\!border-red-400:focus-visible:is(.dark *){
  --tw-border-opacity: 1 !important;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1)) !important;
}
.dark\:focus-visible\:border-module-abaplus-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-module-admin-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(100 116 139 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-module-financial-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(16 185 129 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-module-people-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(249 115 22 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-module-scheduler-border-dark:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(168 85 247 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-primary-400:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(255 179 102 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:border-red-400:focus-visible:is(.dark *){
  --tw-border-opacity: 1;
  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));
}
.dark\:focus-visible\:\!ring-module-abaplus-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-module-admin-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-module-financial-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-module-people-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-module-scheduler-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-primary-400:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(255 179 102 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:\!ring-red-400:focus-visible:is(.dark *){
  --tw-ring-opacity: 1 !important;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1)) !important;
}
.dark\:focus-visible\:ring-gray-300:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(209 213 219 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-gray-800:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(31 41 55 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-module-abaplus-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(20 184 166 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-module-admin-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(100 116 139 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-module-financial-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(16 185 129 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-module-people-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(249 115 22 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-module-scheduler-border-dark:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-primary-400:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 179 102 / var(--tw-ring-opacity, 1));
}
.dark\:focus-visible\:ring-red-400:focus-visible:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));
}
.dark\:disabled\:bg-gray-600:disabled:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}
.group:hover .dark\:group-hover\:text-blue-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-cyan-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(103 232 249 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-cyan-400:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(34 211 238 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-emerald-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-indigo-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(165 180 252 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-orange-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-slate-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:text-teal-300:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(94 234 212 / var(--tw-text-opacity, 1));
}
.group:hover .dark\:group-hover\:ring-cyan-200:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(165 243 252 / var(--tw-ring-opacity, 1));
}
.group:hover .dark\:group-hover\:ring-cyan-700:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(14 116 144 / var(--tw-ring-opacity, 1));
}
.peer:focus ~ .dark\:peer-focus\:ring-primary-800:is(.dark *){
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(153 76 0 / var(--tw-ring-opacity, 1));
}
.dark\:data-\[state\=active\]\:bg-gray-700[data-state="active"]:is(.dark *){
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}
.dark\:data-\[state\=active\]\:text-gray-100[data-state="active"]:is(.dark *){
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}
@media (min-width: 640px){

  .sm\:inline{
    display: inline;
  }

  .sm\:flex{
    display: flex;
  }

  .sm\:hidden{
    display: none;
  }

  .sm\:w-40{
    width: 10rem;
  }

  .sm\:w-48{
    width: 12rem;
  }

  .sm\:w-64{
    width: 16rem;
  }

  .sm\:max-w-80{
    max-width: 20rem;
  }

  .sm\:max-w-\[28rem\]{
    max-width: 28rem;
  }

  .sm\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row{
    flex-direction: row;
  }

  .sm\:items-start{
    align-items: flex-start;
  }

  .sm\:items-center{
    align-items: center;
  }

  .sm\:justify-end{
    justify-content: flex-end;
  }

  .sm\:justify-between{
    justify-content: space-between;
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]){
    --tw-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--tw-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:rounded-lg{
    border-radius: var(--radius);
  }

  .sm\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-left{
    text-align: left;
  }
}
@media (min-width: 768px){

  .md\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .md\:mb-0{
    margin-bottom: 0px;
  }

  .md\:mt-0{
    margin-top: 0px;
  }

  .md\:block{
    display: block;
  }

  .md\:flex{
    display: flex;
  }

  .md\:hidden{
    display: none;
  }

  .md\:h-56{
    height: 14rem;
  }

  .md\:h-80{
    height: 20rem;
  }

  .md\:h-\[32rem\]{
    height: 32rem;
  }

  .md\:w-1\/2{
    width: 50%;
  }

  .md\:w-1\/3{
    width: 33.333333%;
  }

  .md\:w-1\/4{
    width: 25%;
  }

  .md\:w-40{
    width: 10rem;
  }

  .md\:w-44{
    width: 11rem;
  }

  .md\:w-64{
    width: 16rem;
  }

  .md\:w-96{
    width: 24rem;
  }

  .md\:w-auto{
    width: auto;
  }

  .md\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .md\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row{
    flex-direction: row;
  }

  .md\:items-start{
    align-items: flex-start;
  }

  .md\:items-center{
    align-items: center;
  }

  .md\:justify-end{
    justify-content: flex-end;
  }

  .md\:justify-between{
    justify-content: space-between;
  }

  .md\:gap-6{
    gap: 1.5rem;
  }

  .md\:p-10{
    padding: 2.5rem;
  }

  .md\:p-6{
    padding: 1.5rem;
  }

  .md\:px-4{
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:py-2{
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:pb-28{
    padding-bottom: 7rem;
  }

  .md\:pt-40{
    padding-top: 10rem;
  }

  .md\:text-left{
    text-align: left;
  }

  .md\:text-right{
    text-align: right;
  }

  .md\:text-2xl{
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl{
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl{
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl{
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-base{
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .md\:text-lg{
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-xl{
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
@media (min-width: 1024px){

  .lg\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .lg\:col-span-2{
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .lg\:mx-0{
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:grid{
    display: grid;
  }

  .lg\:hidden{
    display: none;
  }

  .lg\:h-full{
    height: 100%;
  }

  .lg\:w-64{
    width: 16rem;
  }

  .lg\:w-auto{
    width: auto;
  }

  .lg\:flex-shrink-0{
    flex-shrink: 0;
  }

  .lg\:translate-x-0{
    --tw-translate-x: 0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-2{
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5{
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-row{
    flex-direction: row;
  }

  .lg\:flex-row-reverse{
    flex-direction: row-reverse;
  }

  .lg\:items-start{
    align-items: flex-start;
  }

  .lg\:items-center{
    align-items: center;
  }

  .lg\:justify-start{
    justify-content: flex-start;
  }

  .lg\:justify-between{
    justify-content: space-between;
  }

  .lg\:px-8{
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:text-left{
    text-align: left;
  }

  .lg\:text-6xl{
    font-size: 3.75rem;
    line-height: 1;
  }
}
@media (min-width: 1280px){

  .xl\:col-span-1{
    grid-column: span 1 / span 1;
  }

  .xl\:col-span-3{
    grid-column: span 3 / span 3;
  }

  .xl\:grid-cols-3{
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4{
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:grid-cols-6{
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }
}


