// utils/colorSchemes.js
/**
 * Esquemas de cores para cada tipo de módulo
 */
export const moduleColors = {
  admin: {
    border: 'border-gray-400',
    shadow: 'gray-300',
    shadowRGB: 'color-gray-rgb',
    iconBg: 'bg-gray-100',
    iconColor: 'text-gray-600',
    textColor: 'text-gray-900',
    descColor: 'text-gray-600',
    actionColor: 'text-gray-600'
  },
  financial: {
    border: 'border-emerald-400',
    shadow: 'emerald-300',
    shadowRGB: 'color-emerald-rgb',
    iconBg: 'bg-emerald-100',
    iconColor: 'text-emerald-600',
    textColor: 'text-emerald-900',
    descColor: 'text-emerald-700',
    actionColor: 'text-emerald-600'
  },
  hr: {
    border: 'border-red-400',
    shadow: 'red-300',
    shadowRGB: 'color-red-rgb',
    iconBg: 'bg-red-100',
    iconColor: 'text-red-600',
    textColor: 'text-red-900',
    descColor: 'text-red-700',
    actionColor: 'text-red-600'
  },
  people: {
    border: 'border-orange-400',
    shadow: 'orange-300',
    shadowRGB: 'color-orange-rgb',
    iconBg: 'bg-orange-100',
    iconColor: 'text-orange-600',
    textColor: 'text-orange-900',
    descColor: 'text-orange-700',
    actionColor: 'text-orange-600'
  },
  scheduler: {
    border: 'border-violet-400',
    shadow: 'violet-300',
    shadowRGB: 'color-violet-rgb',
    iconBg: 'bg-violet-100',
    iconColor: 'text-violet-600',
    textColor: 'text-violet-900',
    descColor: 'text-violet-700',
    actionColor: 'text-violet-600'
  }
};

/**
 * Retorna esquema de cores para um módulo específico
 * @param {string} moduleId - ID do módulo
 * @returns {Object} - Objeto com esquema de cores
 */
export const getModuleColor = (moduleId) => {
  // Mapear os IDs do módulo para os nomes de esquema de cor
  const moduleColorMap = {
    admin: 'admin',
    financial: 'financial',
    hr: 'hr',
    people: 'people',
    scheduler: 'scheduler'
  };
  
  // Obter o nome do esquema de cor a partir do ID do módulo
  const colorSchemeName = moduleColorMap[moduleId] || 'admin';
  
  return moduleColors[colorSchemeName] || {
    border: 'border-gray-200',
    shadow: 'gray-100',
    iconBg: 'bg-gray-100',
    iconColor: 'text-gray-600',
    textColor: 'text-gray-900',
    descColor: 'text-gray-700',
    actionColor: 'text-gray-600'
  };
};

/**
 * Esquemas de cores para ações rápidas
 */
export const actionColors = [
  {
    border: 'border-violet-400',
    iconBg: 'bg-violet-100',
    iconColor: 'text-violet-600',
  },
  {
    border: 'border-orange-400',
    iconBg: 'bg-orange-100',
    iconColor: 'text-orange-600',
  },
  {
    border: 'border-violet-400',
    iconBg: 'bg-violet-100',
    iconColor: 'text-violet-600',
  }
];