"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleTable, ModuleCheckbox } from "@/components/ui";
import {
    Plus,
    Search,
    Filter,
    RefreshCw,
    Edit,
    Trash,
    Tag,
    DollarSign,
    Share2,
} from "lucide-react";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ServiceTypeFormModal from "@/components/people/ServiceTypeFormModal";
import ServiceTypeViewModal from "@/components/people/ServiceTypeViewModal";
import ExportMenu from "@/components/ui/ExportMenu";
import ShareButton from "@/components/common/ShareButton";
import { ServiceTypesFilters } from "@/components/scheduler/ServiceTypesFilters";


// Tutorial steps para a página de tipos de serviço
const serviceTypeTutorialSteps = [
    {
        title: "Tipos de Serviço",
        content: "Esta tela permite gerenciar os tipos de serviço disponíveis para agendamentos no sistema.",
        selector: "h1",
        position: "bottom"
    },
    {
        title: "Adicionar Novo Serviço",
        content: "Clique aqui para adicionar um novo tipo de serviço.",
        selector: "button:has(span:contains('Novo Serviço'))",
        position: "left"
    },
    {
        title: "Filtrar Tipos de Serviço",
        content: "Use esta barra de pesquisa para encontrar tipos de serviço específicos pelo nome.",
        selector: "input[placeholder*='Buscar']",
        position: "bottom"
    },
    {
        title: "Exportar Dados",
        content: "Exporte a lista de tipos de serviço em diferentes formatos usando este botão.",
        selector: ".export-button",
        position: "left"
    },
    {
        title: "Gerenciar Tipos de Serviço",
        content: "Edite ou exclua tipos de serviço existentes usando os botões de ação na tabela.",
        selector: "table",
        position: "top"
    }
];

const ServiceTypePage = () => {
    const { user } = useAuth();
    const searchParams = useSearchParams();
    const [serviceTypes, setServiceTypes] = useState([]);
    const [companies, setCompanies] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [filters, setFilters] = useState({
        search: "",
        serviceTypes: [],
        companies: [],
        minValue: null,
        maxValue: null
    });
    const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
    const [selectedServiceType, setSelectedServiceType] = useState(null);
    const [serviceTypeFormOpen, setServiceTypeFormOpen] = useState(false);
    const [serviceTypeViewOpen, setServiceTypeViewOpen] = useState(false);
    const [sharedServiceTypeId, setSharedServiceTypeId] = useState(null);
    const [isExporting, setIsExporting] = useState(false);
    const [selectedIds, setSelectedIds] = useState([]);

    const handleSelectAll = (checked) => {
        if (checked) {
            setSelectedIds(serviceTypes.map(s => s.id));
        } else {
            setSelectedIds([]);
        }
    };

    const handleSelectOne = (id, checked) => {
        setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
    };
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalItems, setTotalItems] = useState(0);
    const [sortField, setSortField] = useState('name');
    const [sortDirection, setSortDirection] = useState('asc');
    const [itemsPerPage, setItemsPerPage] = useState(10);

    const ITEMS_PER_PAGE = 10;
    const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

    // Função para aplicar filtros locais (incluindo filtro de valor)
    const applyLocalFilters = (data, currentFilters) => {
        let filtered = [...data];

        // Filtro por valor mínimo
        if (currentFilters.minValue !== null && currentFilters.minValue !== '') {
            filtered = filtered.filter(serviceType => 
                serviceType.value >= parseFloat(currentFilters.minValue)
            );
        }

        // Filtro por valor máximo
        if (currentFilters.maxValue !== null && currentFilters.maxValue !== '') {
            filtered = filtered.filter(serviceType => 
                serviceType.value <= parseFloat(currentFilters.maxValue)
            );
        }

        return filtered;
    };

    const loadServiceTypes = async (
        currentFilters = filters,
        page = currentPage,
        sortF = sortField,
        sortD = sortDirection,
        perPage = itemsPerPage
    ) => {
        setIsLoading(true);
        try {
            const params = {
                search: currentFilters.search || undefined,
                companyId: !isSystemAdmin ? user?.companyId : undefined,
                serviceTypeIds: currentFilters.serviceTypes?.length > 0 ? currentFilters.serviceTypes : undefined,
                companies: currentFilters.companies?.length > 0 ? currentFilters.companies : undefined,
                page,
                limit: perPage,
                sortField: sortF,
                sortDirection: sortD
            };
            const response = await serviceTypeService.getServiceTypes(params);
            
            // Aplicar filtros locais (como filtro de valor)
            const filteredData = applyLocalFilters(response.serviceTypes || [], currentFilters);
            
            setServiceTypes(filteredData);
            setTotalItems(filteredData.length);
            setTotalPages(Math.ceil(filteredData.length / perPage));
            setCurrentPage(response.currentPage || 1);
            setSortField(response.sortField || sortF);
            setSortDirection(response.sortDirection || sortD);
        } catch (error) {
            console.error("Erro ao carregar tipos de serviço:", error);
            setServiceTypes([]);
            setTotalItems(0);
            setTotalPages(1);
        } finally {
            setIsLoading(false);
        }
    };



    const loadCompanies = async () => {
        try {
            const response = await companyService.getCompanies({ limit: 100 });
            setCompanies(response.companies || []);
        } catch (error) {
            console.error("Erro ao carregar empresas:", error);
        }
    };

    useEffect(() => {
        loadServiceTypes();
        if (isSystemAdmin) {
            loadCompanies();
        }
    }, []);

    // Efeito para abrir modal quando há serviceTypeId na URL (vindo do chat)
    useEffect(() => {
        const serviceTypeId = searchParams.get('serviceTypeId');
        const openModal = searchParams.get('openModal');
        const mode = searchParams.get('mode');

        if (serviceTypeId && openModal === 'true') {
            if (mode === 'edit') {
                // Para itens compartilhados do chat, abrir modal de edição diretamente
                const loadServiceTypeForEdit = async () => {
                    try {
                        const serviceType = await serviceTypeService.getServiceType(serviceTypeId);
                        if (serviceType) {
                            setSelectedServiceType(serviceType);
                            setServiceTypeFormOpen(true);
                        }
                    } catch (error) {
                        console.error('Erro ao carregar tipo de serviço para edição:', error);
                    }
                };

                loadServiceTypeForEdit();
            } else {
                // Para outros casos, abrir modal de visualização
                setSharedServiceTypeId(serviceTypeId);
                setServiceTypeViewOpen(true);
            }
        }
    }, [searchParams]);

    const handleFiltersChange = (newFilters) => {
        setFilters(newFilters);
    };

    const handleSearch = (currentFilters) => {
        setCurrentPage(1);
        loadServiceTypes(currentFilters, 1, sortField, sortDirection);
    };

    const handlePageChange = (page) => {
        setCurrentPage(page);
        loadServiceTypes(filters, page, sortField, sortDirection);
    };

    const handleSort = (field, direction) => {
        setSortField(field);
        setSortDirection(direction);
        setCurrentPage(1);
        loadServiceTypes(filters, 1, field, direction);
    };

    const handleEditServiceType = (serviceType) => {
        setSelectedServiceType(serviceType);
        setServiceTypeFormOpen(true);
    };

    const handleDeleteServiceType = (serviceType) => {
        setSelectedServiceType(serviceType);
        setConfirmationDialogOpen(true);
    };

    const handleEditFromView = (serviceType) => {
        setServiceTypeViewOpen(false);
        setSelectedServiceType(serviceType);
        setServiceTypeFormOpen(true);
    };

    const handleExport = async (format) => {
        setIsExporting(true);
        try {
            // Exportar usando os mesmos filtros da tabela atual
            await serviceTypeService.exportServiceTypes({
                search: filters.search || undefined,
                companyId: !isSystemAdmin ? user?.companyId : undefined,
                serviceTypeIds: filters.serviceTypes?.length > 0 ? filters.serviceTypes : undefined,
                companies: filters.companies?.length > 0 ? filters.companies : undefined
            }, format);
        } catch (error) {
            console.error("Erro ao exportar tipos de serviço:", error);
            // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
        } finally {
            setIsExporting(false);
        }
    };

    const confirmDeleteServiceType = async () => {
        try {
            await serviceTypeService.deleteServiceType(selectedServiceType.id);
            loadServiceTypes();
            setConfirmationDialogOpen(false);
        } catch (error) {
            console.error("Erro ao excluir tipo de serviço:", error);
        }
    };

    // Função para formatar valores monetários
    const formatCurrency = (value) => {
        return new Intl.NumberFormat("pt-BR", {
            style: "currency",
            currency: "BRL",
        }).format(value);
    };

    return (
        <div className="space-y-6">
            {/* Título e botões de ação */}
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
                    <Tag size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
                    Tipos de Serviço
                </h1>

                <div className="flex items-center gap-2">
                    {selectedIds.length > 0 && (
                        <button
                            onClick={() => console.log('Excluir tipos de serviço selecionados:', selectedIds)}
                            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
                            title="Excluir selecionados"
                        >
                            <Trash size={18} />
                            <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
                        </button>
                    )}
                    <ExportMenu
                        onExport={handleExport}
                        isExporting={isExporting}
                        disabled={isLoading || serviceTypes.length === 0}
                        className="text-purple-600 dark:text-purple-400"
                    />

                    <button
                        onClick={() => {
                            setSelectedServiceType(null);
                            setServiceTypeFormOpen(true);
                        }}
                        className="add-button flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 shadow-md transition-all"
                        title="Novo Tipo de Serviço"
                    >
                        <Plus size={18} />
                        <span className="font-medium">Novo Serviço</span>
                    </button>
                </div>
            </div>

            {/* Cabeçalho e filtros da página */}
            <ModuleHeader
                title="Filtros e Busca"
                icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
                description="Gerencie os tipos de serviço disponíveis para agendamentos no sistema."
                tutorialSteps={serviceTypeTutorialSteps}
                tutorialName="service-type-overview"
                moduleColor="scheduler"
                filters={
                    <ServiceTypesFilters
                        filters={filters}
                        onFiltersChange={handleFiltersChange}
                        onSearch={handleSearch}
                        isLoading={isLoading}
                    />
                }
            />

            {/* Tabela de Tipos de Serviço */}
            <ModuleTable
                moduleColor="scheduler"
                title="Lista de Tipos de Serviço"
                headerContent={
                    <button
                        onClick={() => loadServiceTypes()}
                        className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-scheduler-primary dark:hover:text-module-scheduler-primary-dark transition-colors"
                        title="Atualizar lista"
                    >
                        <RefreshCw size={18} />
                    </button>
                }
                columns={[
                    { header: '', field: 'select', width: '50px', sortable: false },
                    { header: 'Nome do Serviço', field: 'name', width: '40%' },
                    { header: 'Valor', field: 'value', width: '20%', className: 'text-center', dataType: 'number' },
                    ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '25%' }] : []),
                    { header: 'Ações', field: 'actions', className: 'text-right', width: '20%', sortable: false }
                ]}
                data={serviceTypes}
                isLoading={isLoading}
                emptyMessage="Nenhum tipo de serviço encontrado"
                emptyIcon={<Tag size={24} />}
                tableId="scheduler-service-types-table"
                enableColumnToggle={true}
                defaultSortField="name"
                defaultSortDirection="asc"
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                onPageChange={handlePageChange}
                showPagination={true}
                onSort={handleSort}
                sortField={sortField}
                sortDirection={sortDirection}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={(newItemsPerPage) => {
                    setItemsPerPage(newItemsPerPage);
                    const newParams = {
                        ...filters,
                        page: 1,
                        limit: newItemsPerPage
                    };
                    loadServiceTypes(filters, 1, sortField, sortDirection, newItemsPerPage);
                }}
                selectedIds={selectedIds}
                onSelectAll={handleSelectAll}
                renderRow={(serviceType, index, moduleColors, visibleColumns) => (
                    <tr key={serviceType.id} className={moduleColors.hoverBg}>
                        {visibleColumns.includes('select') && (
                            <td className="px-6 py-4 text-center">
                                <ModuleCheckbox
                                    moduleColor="scheduler"
                                    checked={selectedIds.includes(serviceType.id)}
                                    onChange={(e) => handleSelectOne(serviceType.id, e.target.checked)}
                                    name={`select-service-type-${serviceType.id}`}
                                />
                            </td>
                        )}
                        {visibleColumns.includes('name') && (
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                    <div className="flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center">
                                        <Tag size={20} />
                                    </div>
                                    <div className="ml-4">
                                        <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                                            {serviceType.name}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        )}

                        {visibleColumns.includes('value') && (
                            <td className="px-6 py-4 whitespace-nowrap text-center">
                                <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                                    {formatCurrency(serviceType.value)}
                                </div>
                            </td>
                        )}

                        {isSystemAdmin && visibleColumns.includes('company') && (
                            <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                                    {serviceType.company?.name || "N/A"}
                                </div>
                            </td>
                        )}

                        {visibleColumns.includes('actions') && (
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div className="flex justify-end gap-2">
                                    <ShareButton
                                        itemType="serviceType"
                                        itemId={serviceType.id}
                                        itemTitle={serviceType.name}
                                        size="xs"
                                        variant="ghost"
                                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-purple-500 dark:hover:text-purple-400 transition-colors"
                                    />
                                    <button
                                        onClick={() => handleEditServiceType(serviceType)}
                                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                                        title="Editar"
                                    >
                                        <Edit size={18} />
                                    </button>
                                    <button
                                        onClick={() => handleDeleteServiceType(serviceType)}
                                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                                        title="Excluir"
                                    >
                                        <Trash size={18} />
                                    </button>
                                </div>
                            </td>
                        )}
                    </tr>
                )}
            />

            {/* Confirmation Dialog */}
            <ConfirmationDialog
                isOpen={confirmationDialogOpen}
                onClose={() => setConfirmationDialogOpen(false)}
                onConfirm={confirmDeleteServiceType}
                title="Excluir Tipo de Serviço"
                message={`Tem certeza que deseja excluir o tipo de serviço "${selectedServiceType?.name}"? Esta ação não pode ser desfeita.`}
                variant="danger"
                moduleColor="scheduler"
                confirmText="Excluir"
                cancelText="Cancelar"
            />

            {/* Service Type Form Modal */}
            {serviceTypeFormOpen && (
                <ServiceTypeFormModal
                    isOpen={serviceTypeFormOpen}
                    onClose={() => setServiceTypeFormOpen(false)}
                    serviceType={selectedServiceType}
                    onSuccess={() => {
                        setServiceTypeFormOpen(false);
                        loadServiceTypes();
                    }}
                    companies={companies}
                />
            )}

            {/* Service Type View Modal (para itens compartilhados) */}
            {serviceTypeViewOpen && (
                <ServiceTypeViewModal
                    isOpen={serviceTypeViewOpen}
                    onClose={() => {
                        setServiceTypeViewOpen(false);
                        setSharedServiceTypeId(null);
                    }}
                    serviceTypeId={sharedServiceTypeId}
                    onEdit={handleEditFromView}
                />
            )}

            {/* Gerenciador de tutorial */}
            <TutorialManager />
        </div>
    );
};

export default ServiceTypePage;