"use client";

import React, { useState, useEffect } from "react";
import { useSearchParams } from 'next/navigation';
import TutorialManager from "@/components/tutorial/TutorialManager";
import ModuleHeader from "@/components/ui/ModuleHeader";
import { ModuleTable, ModuleCheckbox } from "@/components/ui";
import {
  Plus,
  Filter,
  Edit,
  Trash,
  Users,
  Building,
  RefreshCw,
  CreditCard,
} from "lucide-react";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import InsuranceFormModal from "@/components/people/InsurancesFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import ShareButton from "@/components/common/ShareButton";
import { InsurancesFilters } from "@/components/people/InsurancesFilters";

const insurancesTutorialSteps = [
  {
    title: "Convênios",
    content: "Esta tela permite gerenciar os convênios disponíveis no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Convênio",
    content: "Clique aqui para adicionar um novo convênio.",
    selector: "button:has(span:contains('Novo Convênio'))",
    position: "left"
  },
  {
    title: "Filtrar Convênios",
    content: "Use esta barra de pesquisa para encontrar convênios específicos pelo nome.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtros Avançados",
    content: "Clique no botão Filtros para acessar opções avançadas de filtragem.",
    selector: "button:has(span:contains('Filtros'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de convênios em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Convênios",
    content: "Edite ou exclua convênios existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const InsurancesPage = () => {
  const { user: currentUser } = useAuth();
  const isAdmin = currentUser?.modules?.includes("ADMIN") || currentUser?.modules?.includes("RH");
  const searchParams = useSearchParams();

  const [insurances, setInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedInsurance, setSelectedInsurance] = useState(null);
  const [insuranceFormOpen, setInsuranceFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(insurances.map(i => i.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  const [filters, setFilters] = useState({
    search: "",
    companies: [],
    insurances: [],
    dateFrom: "",
    dateTo: ""
  });

  const [itemsPerPage, setItemsPerPage] = useState(10);

  const loadInsurances = async (
    page = currentPage,
    filtersToUse = filters,
    sortField = 'name',
    sortDirection = 'asc',
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      const response = await insurancesService.getInsurances({
        search: filtersToUse.search || undefined,
        insuranceIds: filtersToUse.insurances.length > 0 ? filtersToUse.insurances : undefined,
        companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,
        dateFrom: filtersToUse.dateFrom || undefined,
        dateTo: filtersToUse.dateTo || undefined,
        page,
        limit: perPage,
        sortField,
        sortDirection
      });

      if (response && typeof response === 'object') {
        const insurancesArray = response.insurances || [];
        const total = response.total || insurancesArray.length;
        const pages = response.pages || Math.ceil(total / perPage);

        setInsurances(insurancesArray);
        setTotalItems(total);
        setTotalPages(pages);
        setCurrentPage(page);
      } else {
        setInsurances([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Erro ao carregar convênios:", error);
      setInsurances([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInsurances(1, filters);
  }, []);

  useEffect(() => {
    const insuranceId = searchParams.get('insuranceId');
    const openModal = searchParams.get('openModal');
    const mode = searchParams.get('mode');

    if (insuranceId && openModal === 'true' && mode === 'edit') {
      const loadInsuranceForEdit = async () => {
        try {
          const insurance = await insurancesService.getInsurance(insuranceId);
          if (insurance) {
            setSelectedInsurance(insurance);
            setInsuranceFormOpen(true);
          }
        } catch (error) {
          console.error('Erro ao carregar convênio compartilhado:', error);
        }
      };
      
      loadInsuranceForEdit();
    }
  }, [searchParams]);

  const handleSearch = (searchFilters) => {
    loadInsurances(1, searchFilters);
  };

  const handlePageChange = (page) => {
    loadInsurances(page, filters);
  };

  const handleEditInsurance = (insurance) => {
    setSelectedInsurance(insurance);
    setInsuranceFormOpen(true);
  };

  const handleDeleteInsurance = (insurance) => {
    setSelectedInsurance(insurance);
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      await insurancesService.exportInsurances({
        search: filters.search || undefined,
        insuranceIds: filters.insurances.length > 0 ? filters.insurances : undefined,
        companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,
        dateFrom: filters.dateFrom || undefined,
        dateTo: filters.dateTo || undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar convênios:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmDeleteInsurance = async () => {
    try {
      await insurancesService.deleteInsurance(selectedInsurance.id);
      loadInsurances(currentPage, filters);
    } catch (error) {
      console.error("Erro ao excluir convênio:", error);
    }
    setConfirmationDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <CreditCard size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Convênios
        </h1>

        <div className="flex items-center gap-2">
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir convênios selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || insurances.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          {isAdmin && (
            <button
              onClick={() => {
                setSelectedInsurance(null);
                setInsuranceFormOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
            >
              <Plus size={18} />
              <span className="font-medium">Novo Convênio</span>
            </button>
          )}
        </div>
      </div>

      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie os convênios disponíveis no sistema. Utilize os filtros abaixo para encontrar convênios específicos."
        tutorialSteps={insurancesTutorialSteps}
        tutorialName="insurances-overview"
        moduleColor="people"
        filters={
          <InsurancesFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
          />
        }
      />

      <ModuleTable
        moduleColor="people"
        title="Lista de Convênios"
        headerContent={
          <button
            onClick={() => loadInsurances()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          { header: 'Nome', field: 'name', width: '40%' },
          { header: 'Empresa', field: 'company', width: '40%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '20%', sortable: false }
        ]}
        data={insurances}
        isLoading={isLoading}
        emptyMessage="Nenhum convênio encontrado"
        emptyIcon={<Building size={24} />}
        tableId="people-insurances-table"
        enableColumnToggle={true}
        defaultSortField="name"
        defaultSortDirection="asc"
        onSort={(field, direction) => {
          loadInsurances(currentPage, filters, field, direction);
        }}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        showPagination={true}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadInsurances(1, filters, newItemsPerPage);
        }}
        renderRow={(insurance, index, moduleColors, visibleColumns) => (
          <tr key={insurance.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="people"
                  checked={selectedIds.includes(insurance.id)}
                  onChange={(e) => handleSelectOne(insurance.id, e.target.checked)}
                  name={`select-insurance-${insurance.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('name') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center">
                    <Building size={20} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {insurance.name}
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('company') && (
              <td className="px-4 py-4">
                {insurance.company ? (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                    <span className="text-neutral-700 dark:text-neutral-300">{insurance.company.name}</span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">
                    Convênio geral
                  </span>
                )}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right">
                <div className="flex justify-end gap-2">
                  <ShareButton
                    itemType="insurance"
                    itemId={insurance.id}
                    itemTitle={insurance.name}
                    size="xs"
                    variant="ghost"
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                  />
                  {isAdmin && (
                    <>
                      <button
                        onClick={() => handleEditInsurance(insurance)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors"
                        title="Editar"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteInsurance(insurance)}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                        title="Excluir"
                      >
                        <Trash size={16} />
                      </button>
                    </>
                  )}
                </div>
              </td>
            )}
          </tr>
        )}
      />

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmDeleteInsurance}
        title="Excluir Convênio"
        message={`Tem certeza que deseja excluir o convênio "${selectedInsurance?.name}"? Esta ação não pode ser desfeita.`}
        variant="danger"
        moduleColor="people"
        confirmText="Excluir"
        cancelText="Cancelar"
      />

      {insuranceFormOpen && (
        <InsuranceFormModal
          isOpen={insuranceFormOpen}
          onClose={() => setInsuranceFormOpen(false)}
          insurance={selectedInsurance}
          onSuccess={() => {
            setInsuranceFormOpen(false);
            loadInsurances(currentPage, filters);
          }}
        />
      )}

      <TutorialManager />
    </div>
  );
};

export default InsurancesPage;