// src/app/modules/people/services/peopleDashboardService.js
import { api } from "@/utils/api";
import { personsService } from "./personsService";
import { clientsService } from "./clientsService";
import { insurancesService } from "./insurancesService";
import { exportService } from "@/app/services/exportService";

export const peopleDashboardService = {
  /**
   * Get all dashboard data for people module
   * @param {Object} params - Parameters for filtering dashboard data
   * @returns {Promise<Object>} Dashboard data
   */
  getPeopleDashboardData: async (params = {}) => {
    try {
      // Diretamente usar o método de geração de dados, já que o endpoint não existe
      return peopleDashboardService.generateDashboardDataFromServices(params);
    } catch (error) {
      console.error("Error generating people dashboard data:", error);

      // Retornar dados vazios em caso de erro
      return {
        stats: {
          totalPersons: 0,
          activePersons: 0,
          totalClients: 0,
          activeClients: 0,
          totalInsurances: 0
        },
        growth: {
          personsGrowth: 0,
          clientsGrowth: 0,
          insurancesGrowth: 0
        },
        genderDistribution: [],
        topClients: [],
        insuranceDistribution: [],
        recentPersons: []
      };
    }
  },

  /**
   * Exporta os dados do dashboard de pessoas
   * @param {Object} params - Parâmetros para filtrar os dados do dashboard
   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportDashboardData: async (params = {}, exportFormat = "xlsx") => {
    try {
      // Obter os dados do dashboard
      const dashboardData = await peopleDashboardService.getPeopleDashboardData(params);

      // Timestamp atual para o subtítulo
      const timestamp = new Date().toLocaleString('pt-BR');

      // Período para o subtítulo
      let periodText = 'Todos os períodos';
      if (params.period) {
        switch (params.period) {
          case '7dias':
            periodText = 'Últimos 7 dias';
            break;
          case '30dias':
            periodText = 'Últimos 30 dias';
            break;
          case '3meses':
            periodText = 'Últimos 3 meses';
            break;
          case '6meses':
            periodText = 'Últimos 6 meses';
            break;
          case '1ano':
            periodText = 'Último ano';
            break;
          default:
            periodText = params.period;
        }
      }

      // Subtítulo com timestamp e período
      const subtitle = `Exportado em: ${timestamp} | Período: ${periodText}`;

      // Definição das colunas para cada tabela
      const statsColumns = [
        { key: "metric", header: "Métrica" },
        { key: "value", header: "Valor", align: "right" }
      ];

      const genderColumns = [
        { key: "name", header: "Gênero" },
        { key: "value", header: "Quantidade", align: "right" }
      ];

      const clientColumns = [
        { key: "name", header: "Cliente" },
        { key: "count", header: "Pacientes", align: "right" }
      ];

      const insuranceColumns = [
        { key: "name", header: "Status" },
        { key: "value", header: "Quantidade", align: "right" }
      ];

      const recentPersonsColumns = [
        { key: "fullName", header: "Nome" },
        { key: "clientName", header: "Cliente" },
        { key: "relationship", header: "Relação" },
        { key: "createdAt", header: "Data de Cadastro", type: "date" }
      ];

      // Preparar os dados de estatísticas para exportação
      const statsData = [
        { metric: "Total de Pacientes", value: dashboardData.stats.totalPersons },
        { metric: "Pacientes Ativos", value: dashboardData.stats.activePersons },
        { metric: "Total de Clientes", value: dashboardData.stats.totalClients },
        { metric: "Clientes Ativos", value: dashboardData.stats.activeClients },
        { metric: "Total de Convênios", value: dashboardData.stats.totalInsurances }
      ];

      // Exportar os dados em múltiplas tabelas
      return await exportService.exportData(
        {
          estatisticas: statsData,
          genero: dashboardData.genderDistribution,
          clientes: dashboardData.topClients,
          convenios: dashboardData.insuranceDistribution,
          recentes: dashboardData.recentPersons
        },
        {
          format: exportFormat,
          filename: "dashboard-pessoas",
          title: "Dashboard de Pessoas",
          subtitle,
          multiTable: true,
          tables: [
            { name: "estatisticas", title: "Estatísticas Gerais", columns: statsColumns },
            { name: "genero", title: "Distribuição por Gênero", columns: genderColumns },
            { name: "clientes", title: "Clientes com Mais Pacientes", columns: clientColumns },
            { name: "convenios", title: "Distribuição por Convênio", columns: insuranceColumns },
            { name: "recentes", title: "Pacientes Recentes", columns: recentPersonsColumns }
          ]
        }
      );
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard de pessoas:", error);
      return false;
    }
  },

  /**
   * Generate dashboard data from existing services
   * @param {Object} params - Parameters for filtering dashboard data
   * @returns {Promise<Object>} Dashboard data
   */
  generateDashboardDataFromServices: async (params = {}) => {
    try {
      // Prepare filters for services
      const serviceFilters = { limit: 1000 };
      if (params.companyId) {
        serviceFilters.companyId = params.companyId;
      }

      // Fetch data from existing services
      const [personsResponse, clientsResponse, insurancesResponse, documentsResponse] = await Promise.all([
        personsService.getPersons(serviceFilters),
        clientsService.getClients(serviceFilters),
        insurancesService.getInsurances(params.companyId ? { companyId: params.companyId } : {}),
        // Buscar documentos
        fetch('/api/documents', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }).then(res => res.ok ? res.json() : { documents: [] }).catch(() => ({ documents: [] }))
      ]);

      // Extract data
      const persons = personsResponse.persons || [];
      const clients = clientsResponse.clients || [];

      // Extrair convênios corretamente da resposta
      console.log("Resposta do serviço de convênios:", insurancesResponse);

      let insurances = [];

      // Se a resposta for um array, usar diretamente
      if (Array.isArray(insurancesResponse)) {
        insurances = insurancesResponse;
      }
      // Se for um objeto, procurar por propriedades que possam conter os convênios
      else if (insurancesResponse && typeof insurancesResponse === 'object') {
        // Verificar propriedades comuns
        if (Array.isArray(insurancesResponse.insurances)) {
          insurances = insurancesResponse.insurances;
        } else if (Array.isArray(insurancesResponse.data)) {
          insurances = insurancesResponse.data;
        } else {
          // Procurar por qualquer propriedade que seja um array
          for (const key in insurancesResponse) {
            if (Array.isArray(insurancesResponse[key])) {
              insurances = insurancesResponse[key];
              break;
            }
          }
        }
      }

      console.log("Convênios extraídos:", insurances);

      // Se ainda não tiver convênios, usar dados mockados para demonstração
      if (!insurances || insurances.length === 0) {
        console.log("Usando dados mockados para convênios");
        insurances = [
          { id: '1', name: 'Unimed' },
          { id: '2', name: 'Amil' },
          { id: '3', name: 'SulAmérica' },
          { id: '4', name: 'Bradesco Saúde' },
          { id: '5', name: 'NotreDame Intermédica' }
        ];
      }

      // Extrair documentos
      const documents = documentsResponse.documents || documentsResponse || [];
      console.log("Documentos extraídos:", documents);

      // Calculate stats
      const stats = {
        totalPersons: persons.length,
        activePersons: persons.filter(person => person.active).length,
        totalClients: clients.length,
        activeClients: clients.filter(client => client.active).length,
        totalInsurances: insurances.length,
        totalDocuments: documents.length,
        totalCategories: [...new Set(documents.map(doc => doc.categoryDocument?.id).filter(Boolean))].length
      };

      // Calculate growth (mock data for now)
      const growth = {
        personsGrowth: 5,
        clientsGrowth: 3,
        insurancesGrowth: 2,
        documentsGrowth: 8
      };

      // Calculate top clients by persons count
      const topClients = calculateTopClients(clients, persons);

      // Calculate insurance distribution
      const insuranceDistribution = calculateInsuranceDistribution(persons);

      // Calculate most used insurances
      const mostUsedInsurances = calculateMostUsedInsurances(persons, insurances);

      // Calculate insurance limits
      const insuranceLimits = calculateInsuranceLimits(persons);

      // Calculate highest limit users
      const highestLimitUsers = calculateHighestLimitUsers(persons);

      // Calculate remaining limits
      const remainingLimits = calculateRemainingLimits(persons);

      // Calculate recent persons
      const recentPersons = getRecentPersons(persons, clients);

      // Calculate recent documents
      const recentDocuments = documents
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 5);

      // Calculate documents by category
      const documentsByCategory = calculateDocumentsByCategory(documents);

      return {
        stats,
        growth,
        topClients,
        insuranceDistribution,
        mostUsedInsurances,
        insuranceLimits,
        highestLimitUsers,
        remainingLimits,
        recentPersons,
        recentDocuments,
        documentsByCategory
      };
    } catch (error) {
      console.error("Error generating dashboard data:", error);
      return {
        stats: {
          totalPersons: 0,
          activePersons: 0,
          totalClients: 0,
          activeClients: 0,
          totalInsurances: 0
        },
        growth: {
          personsGrowth: 0,
          clientsGrowth: 0,
          insurancesGrowth: 0
        },
        topClients: [],
        insuranceDistribution: [],
        mostUsedInsurances: [],
        insuranceLimits: [],
        highestLimitUsers: [],
        remainingLimits: [],
        recentPersons: []
      };
    }
  }
};

/**
 * Calculate most used insurances
 * @param {Array} persons - List of persons
 * @param {Array} insurances - List of insurances
 * @returns {Array} Most used insurances data
 */
function calculateMostUsedInsurances(persons, insurances) {
  const insuranceCounts = {};

  // Count insurance usage from personInsurances
  persons.forEach(person => {
    if (person.personInsurances && Array.isArray(person.personInsurances)) {
      person.personInsurances.forEach(personInsurance => {
        const insuranceId = personInsurance.insuranceId;
        if (insuranceCounts[insuranceId]) {
          insuranceCounts[insuranceId].count++;
        } else {
          const insurance = insurances.find(ins => ins.id === insuranceId);
          insuranceCounts[insuranceId] = {
            name: insurance ? insurance.name : 'Desconhecido',
            count: 1
          };
        }
      });
    }
  });

  // Convert to array and sort by count
  return Object.values(insuranceCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)
    .map(item => ({ name: item.name, value: item.count }));
}

/**
 * Calculate insurance limits distribution (services with highest limits among patients)
 * @param {Array} persons - List of persons
 * @returns {Array} Insurance limits data
 */
function calculateInsuranceLimits(persons) {
  const serviceLimits = {};

  // Group by serviceType and find highest limits
  persons.forEach(person => {
    if (person.insuranceServiceLimits && Array.isArray(person.insuranceServiceLimits)) {
      person.insuranceServiceLimits.forEach(serviceLimit => {
        const serviceTypeId = serviceLimit.serviceTypeId;
        const monthlyLimit = serviceLimit.monthlyLimit || 0;
        
        if (!serviceLimits[serviceTypeId]) {
          serviceLimits[serviceTypeId] = {
            serviceTypeName: serviceLimit.ServiceType?.name || 'Serviço Desconhecido',
            maxLimit: monthlyLimit,
            totalPatients: 1
          };
        } else {
          serviceLimits[serviceTypeId].maxLimit = Math.max(serviceLimits[serviceTypeId].maxLimit, monthlyLimit);
          serviceLimits[serviceTypeId].totalPatients++;
        }
      });
    }
  });

  // Convert to array and sort by maxLimit
  return Object.values(serviceLimits)
    .sort((a, b) => b.maxLimit - a.maxLimit)
    .slice(0, 6)
    .map(item => ({ 
      name: item.serviceTypeName, 
      value: item.maxLimit,
      patients: item.totalPatients 
    }));
}

/**
 * Calculate highest limit users (patients with highest total limits)
 * @param {Array} persons - List of persons
 * @returns {Array} Highest limit users data
 */
function calculateHighestLimitUsers(persons) {
  const userLimits = {};

  persons.forEach(person => {
    if (person.insuranceServiceLimits && Array.isArray(person.insuranceServiceLimits)) {
      let totalLimit = 0;
      person.insuranceServiceLimits.forEach(serviceLimit => {
        totalLimit += serviceLimit.monthlyLimit || 0;
      });
      
      if (totalLimit > 0) {
        userLimits[person.id] = {
          name: person.fullName || 'Paciente Desconhecido',
          totalLimit: totalLimit
        };
      }
    }
  });

  // Convert to array and sort by totalLimit
  return Object.values(userLimits)
    .sort((a, b) => b.totalLimit - a.totalLimit)
    .slice(0, 10)
    .map(item => ({ name: item.name, value: item.totalLimit }));
}

/**
 * Calculate remaining limits for the month
 * @param {Array} persons - List of persons
 * @returns {Array} Remaining limits data
 */
function calculateRemainingLimits(persons) {
  const remainingCategories = {
    'Limite Quase Esgotado (< 20%)': 0,
    'Limite Baixo (20-50%)': 0,
    'Limite Médio (50-80%)': 0,
    'Limite Alto (> 80%)': 0
  };

  persons.forEach(person => {
    if (person.insuranceServiceLimits && Array.isArray(person.insuranceServiceLimits)) {
      person.insuranceServiceLimits.forEach(serviceLimit => {
        const monthlyLimit = serviceLimit.monthlyLimit || 0;
        // Mock calculation for usage - in real scenario, this would come from actual usage data
        const usedAmount = Math.floor(monthlyLimit * Math.random() * 0.9); // Random usage up to 90%
        const remainingPercentage = ((monthlyLimit - usedAmount) / monthlyLimit) * 100;
        
        if (remainingPercentage < 20) {
          remainingCategories['Limite Quase Esgotado (< 20%)']++;
        } else if (remainingPercentage < 50) {
          remainingCategories['Limite Baixo (20-50%)']++;
        } else if (remainingPercentage < 80) {
          remainingCategories['Limite Médio (50-80%)']++;
        } else {
          remainingCategories['Limite Alto (> 80%)']++;
        }
      });
    }
  });

  return Object.entries(remainingCategories)
    .filter(([_, count]) => count > 0)
    .map(([category, count]) => ({ name: category, value: count }));
}

/**
 * Calculate top clients by persons count
 * @param {Array} clients - List of clients
 * @param {Array} persons - List of persons
 * @returns {Array} Top clients data
 */
function calculateTopClients(clients, persons) {
  // Count persons per client
  const clientPersonsCount = {};

  persons.forEach(person => {
    if (person.clientPersons && person.clientPersons.length > 0) {
      person.clientPersons.forEach(clientPerson => {
        if (!clientPersonsCount[clientPerson.clientId]) {
          clientPersonsCount[clientPerson.clientId] = 0;
        }
        clientPersonsCount[clientPerson.clientId]++;
      });
    }
  });

  // Map client IDs to client objects
  const clientMap = {};
  clients.forEach(client => {
    clientMap[client.id] = client;
  });

  // Create array of clients with person count
  const clientsWithCount = Object.keys(clientPersonsCount).map(clientId => {
    const client = clientMap[clientId];
    // Usar nome completo se disponível, caso contrário usar login ou "Cliente Desconhecido"
    const clientName = client ? (client.fullName || client.login) : 'Cliente Desconhecido';

    console.log('Client data:', client ? { id: client.id, fullName: client.fullName, login: client.login } : 'No client data');

    return {
      id: clientId,
      name: clientName,
      count: clientPersonsCount[clientId]
    };
  });

  // Sort by count and take top 5
  return clientsWithCount
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

/**
 * Calculate insurance distribution from persons data
 * @param {Array} persons - List of persons
 * @returns {Array} Insurance distribution data
 */
function calculateInsuranceDistribution(persons) {
  // Count persons with insurance
  let withInsurance = 0;
  let withoutInsurance = 0;

  persons.forEach(person => {
    if (person.personInsurances && person.personInsurances.length > 0) {
      withInsurance++;
    } else {
      withoutInsurance++;
    }
  });

  return [
    { name: 'Com Convênio', value: withInsurance },
    { name: 'Sem Convênio', value: withoutInsurance }
  ];
}

/**
 * Get recent persons
 * @param {Array} persons - List of persons
 * @returns {Array} Recent persons
 */
function getRecentPersons(persons, clients) {
  // Map client IDs to client objects for quick lookup
  const clientMap = {};
  clients.forEach(client => {
    clientMap[client.id] = client;
  });

  return persons
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 5)
    .map(person => {
      // Get client info if available
      const client = person.clientPersons && person.clientPersons.length > 0
        ? clientMap[person.clientPersons[0].clientId]
        : null;

      return {
        id: person.id,
        fullName: person.fullName,
        email: person.email,
        phone: person.phone,
        createdAt: person.createdAt,
        profileImageUrl: person.profileImageUrl,
        clientId: person.clientPersons && person.clientPersons.length > 0 ? person.clientPersons[0].clientId : null,
        clientName: client ? (client.fullName || client.login) : 'Cliente Desconhecido',
        relationship: person.relationship || 'Titular'
      };
    });
}

/**
 * Calculate documents distribution by category
 * @param {Array} documents - Array of documents
 * @returns {Array} Array of category distribution data
 */
function calculateDocumentsByCategory(documents) {
  const categoryCount = {};

  documents.forEach(doc => {
    const categoryName = doc.categoryDocument?.name || 'Sem categoria';
    categoryCount[categoryName] = (categoryCount[categoryName] || 0) + 1;
  });

  return Object.entries(categoryCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count);
}

export default peopleDashboardService;
