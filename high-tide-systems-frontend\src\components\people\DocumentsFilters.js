"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  RefreshCw,
  Filter,
  Calendar,
  FileType,
  Building,
  User,
  Tag
} from "lucide-react";
import { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleSelect } from "@/components/ui";

const DocumentsFilters = ({
  filters = {},
  onFiltersChange,
  onSearch,
  categories = [],
  companies = [],
  users = [],
  className = ""
}) => {
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // File types baseados nos mimeTypes mais comuns
  const fileTypes = [
    { value: "", label: "Todos os tipos" },
    { value: "image", label: "Imagens" },
    { value: "pdf", label: "PDF" },
    { value: "word", label: "Word" },
    { value: "excel", label: "Excel" },
    { value: "video", label: "Vídeos" },
    { value: "audio", label: "Áudios" },
    { value: "text", label: "Texto" }
  ];

  const handleFilterChange = (newFilters) => {
    onFiltersChange(newFilters);
    setHasChanges(true);
  };

  const handleSearch = () => {
    if (onSearch) {
      onSearch(filters.search || "");
    }
    setHasChanges(false);
  };

  const handleClearFilters = () => {
    const clearedFilters = { search: "" };
    onFiltersChange(clearedFilters);
    setHasChanges(false);
  };

  return (
    <div className={className}>
      <div className="flex flex-col md:flex-row gap-3 md:items-center">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar documentos por nome..."
              value={filters.search || ""}
              onChange={(e) => handleFilterChange({ ...filters, search: e.target.value })}
              className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <FilterButton
            type="button"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            moduleColor="people"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <Filter size={16} className="text-gray-600 dark:text-gray-400" />
              <span>Filtros</span>
              <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                {Object.entries(filters).filter(([key, value]) => {
                  if (key === 'search') return false;
                  if (Array.isArray(value)) return value.length > 0;
                  return value !== null && value !== '';
                }).length}
              </span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={handleSearch}
            moduleColor="people"
            variant="primary"
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>Buscar</span>
            </div>
          </FilterButton>
        </div>
      </div>

      {/* Filtros avançados (expansíveis) */}
      {isFilterExpanded && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <Tag size={16} className="text-orange-600 dark:text-orange-400" />
                Categoria
              </label>
              <ModuleSelect
                moduleColor="people"
                value={filters.category || ""}
                onChange={(e) => handleFilterChange({ ...filters, category: e.target.value })}
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {/* File Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <FileType size={16} className="text-orange-600 dark:text-orange-400" />
                Tipo de Arquivo
              </label>
              <ModuleSelect
                moduleColor="people"
                value={filters.fileType || ""}
                onChange={(e) => handleFilterChange({ ...filters, fileType: e.target.value })}
              >
                {fileTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {/* Company Filter - Only for system admins */}
            {companies.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                  <Building size={16} className="text-orange-600 dark:text-orange-400" />
                  Empresa
                </label>
                <ModuleSelect
                  moduleColor="people"
                  value={filters.company || ""}
                  onChange={(e) => handleFilterChange({ ...filters, company: e.target.value })}
                >
                  <option value="">Todas as empresas</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )}

            {/* User Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-2">
                <User size={16} className="text-orange-600 dark:text-orange-400" />
                Usuário
              </label>
              <ModuleSelect
                moduleColor="people"
                value={filters.user || ""}
                onChange={(e) => handleFilterChange({ ...filters, user: e.target.value })}
              >
                <option value="">Todos os usuários</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.fullName || user.login}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          </div>

          <div>
            <FilterButton
              type="button"
              onClick={handleClearFilters}
              moduleColor="people"
              variant="secondary"
            >
              <div className="flex items-center gap-2">
                <RefreshCw size={16} />
                <span>Limpar Filtros</span>
              </div>
            </FilterButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentsFilters;
