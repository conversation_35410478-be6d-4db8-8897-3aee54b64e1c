import React from 'react';
import { Settings, Users, DollarSign, Calendar, CheckSquare, AlertCircle, UserCheck } from 'lucide-react';

const UserModulesTab = ({
  user,
  savedUserId,
  selectedModules,
  setSelectedModules,
  isAdmin,
  isLoading
}) => {
  const toggleModule = (moduleId) => {
    if (moduleId === "ADMIN" && !isAdmin) return;

    if (selectedModules.includes(moduleId)) {
      setSelectedModules(prev => prev.filter(m => m !== moduleId));
    } else {
      setSelectedModules(prev => [...prev, moduleId]);
    }
  };

  // Função para alternar todos os módulos (igual nas permissões)
  const toggleAllModules = () => {
    const allModules = ["ADMIN", "SCHEDULING", "PEOPLE"];
    const availableModules = isAdmin ? allModules : allModules.filter(m => m !== "ADMIN");

    // Verificar se todos os módulos disponíveis estão selecionados
    const allSelected = availableModules.every(moduleId => selectedModules.includes(moduleId));

    if (allSelected) {
      // Desmarcar todos (manter apenas BASIC que é obrigatório)
      setSelectedModules(["BASIC"]);
    } else {
      // Selecionar todos os disponíveis (incluindo BASIC)
      setSelectedModules(["BASIC", ...availableModules]);
    }
  };

  // Verificar se todos os módulos disponíveis estão selecionados
  const allModules = ["ADMIN", "SCHEDULING", "PEOPLE"];
  const availableModules = isAdmin ? allModules : allModules.filter(m => m !== "ADMIN");
  const allSelected = availableModules.every(moduleId => selectedModules.includes(moduleId));

  return (
    <div>
      <div className="mb-6">
        {/* Nome do usuário em destaque sem fundo colorido */}
        <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3">
          {user?.fullName || (savedUserId ? "Novo Usuário" : "")}
        </h4>

        <div className="flex justify-between items-center">
          <p className="text-sm text-neutral-600 dark:text-gray-300">
            Selecione os módulos que este usuário terá acesso:
          </p>
          <button
            onClick={toggleAllModules}
            className={`px-3 py-1 rounded text-sm font-medium ${
              allSelected
                ? "bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
                : "bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700"
            }`}
            disabled={isLoading}
          >
            {allSelected ? "Desmarcar todos" : "Selecionar todos"}
          </button>
        </div>
      </div>

      <div className="space-y-4">
        {/* Módulo BASIC */}
        <div className="p-4 rounded-lg border bg-white dark:bg-white/20 text-neutral-700 dark:text-gray-300 border-neutral-200 dark:border-gray-600 opacity-70">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <input
                type="checkbox"
                checked={true}
                onChange={() => {}}
                disabled={true}
                className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <CheckSquare className="h-5 w-5" />
                <h5 className="font-medium text-neutral-800 dark:text-white">Básico</h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Acesso básico ao sistema, visualização limitada
              </p>
              <div className="mt-2 text-xs text-neutral-500 dark:text-gray-400 flex items-center gap-1">
                <AlertCircle size={12} />
                <span>Módulo obrigatório para todos os usuários</span>
              </div>
            </div>
          </div>
        </div>

        {/* Módulo ADMIN */}
        <div
          className={`p-4 rounded-lg border ${
            selectedModules.includes("ADMIN") 
              ? "bg-gray-200 dark:bg-gray-800/60 text-gray-800 dark:text-gray-300 border-gray-300 dark:border-gray-700/60" 
              : "border-neutral-200 dark:border-gray-700"
          } ${
            !isAdmin 
              ? "opacity-70" 
              : "cursor-pointer hover:border-slate-300 dark:hover:border-slate-700"
          }`}
          onClick={() => toggleModule("ADMIN")}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <input
                type="checkbox"
                checked={selectedModules.includes("ADMIN")}
                onChange={() => {}}
                disabled={!isAdmin || isLoading}
                className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Settings className={`h-5 w-5 ${selectedModules.includes("ADMIN") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                <h5 className="font-medium text-neutral-800 dark:text-white">
                  Administração
                  <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                    Acesso Administrativo
                  </span>
                </h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Acesso completo ao sistema, incluindo configurações e gerenciamento de usuários
              </p>

              {!isAdmin && (
                <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                  <AlertCircle size={12} />
                  <span>Apenas administradores podem conceder este acesso</span>
                </div>
              )}
            </div>
          </div>
        </div>



        {/* Módulo SCHEDULING */}
        <div
          className={`p-4 rounded-lg border ${
            selectedModules.includes("SCHEDULING")
              ? "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50"
              : "border-neutral-200 dark:border-gray-700"
          } cursor-pointer hover:border-slate-300 dark:hover:border-slate-700`}
          onClick={() => toggleModule("SCHEDULING")}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <input
                type="checkbox"
                checked={selectedModules.includes("SCHEDULING")}
                onChange={() => {}}
                disabled={isLoading}
                className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <Calendar className={`h-5 w-5 ${selectedModules.includes("SCHEDULING") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                <h5 className="font-medium text-neutral-800 dark:text-white">Agendamento</h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Gerenciamento de compromissos, reuniões e alocação de recursos
              </p>
            </div>
          </div>
        </div>

        {/* Módulo PEOPLE */}
        <div
          className={`p-4 rounded-lg border ${
            selectedModules.includes("PEOPLE")
              ? "bg-orange-300 dark:bg-orange-700/60 text-orange-900 dark:text-orange-200 border-orange-400 dark:border-orange-600/70"
              : "border-neutral-200 dark:border-gray-700"
          } cursor-pointer hover:border-slate-300 dark:hover:border-slate-700`}
          onClick={() => toggleModule("PEOPLE")}
        >
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              <input
                type="checkbox"
                checked={selectedModules.includes("PEOPLE")}
                onChange={() => {}}
                disabled={isLoading}
                className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-slate-500 dark:text-slate-400 focus:ring-slate-500 dark:focus:ring-slate-400"
              />
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-2">
                <UserCheck className={`h-5 w-5 ${selectedModules.includes("PEOPLE") ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                <h5 className="font-medium text-neutral-800 dark:text-white">Pessoas</h5>
              </div>
              <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                Cadastro e gerenciamento de pacientes, clientes e convênios
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserModulesTab;