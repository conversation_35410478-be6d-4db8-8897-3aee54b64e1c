{"timestamp":"2025-08-12 04:08:11","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Tue Aug 12 2025 04:08:11 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":147755008,"heapTotal":47730688,"heapUsed":45707032,"external":3391355,"arrayBuffers":117242}},"os":{"loadavg":[2.37,2.2,2.15],"uptime":35660.43},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-20 15:32:25","level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/professionRoutes.js:20:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:58:26)","error":{},"stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/professionRoutes.js:20:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:58:26)","exception":true,"date":"Wed Aug 20 2025 15:32:25 GMT+0000 (Coordinated Universal Time)","process":{"pid":246,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161415168,"heapTotal":105615360,"heapUsed":77858664,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[2.83,2.49,2.26],"uptime":6917.61},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"/usr/src/app/src/routes/professionRoutes.js","function":null,"line":20,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":26,"file":"/usr/src/app/src/server.js","function":null,"line":58,"method":null,"native":false}]}
{"timestamp":"2025-08-20 15:44:39","level":"error","message":"uncaughtException: write after end\nError: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","error":{},"stack":"Error: write after end\n    at writeAfterEnd (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:264:12)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:300:21)\n    at File.log (/usr/src/app/node_modules/winston/lib/winston/transports/file.js:222:34)\n    at RejectionStream._write (/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js:46:29)\n    at doWrite (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:390:139)\n    at writeOrBuffer (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:381:5)\n    at Writable.write (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js:302:11)\n    at DerivedLogger.ondata (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:629:20)\n    at DerivedLogger.emit (node:events:531:35)\n    at addChunk (/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js:279:12)","exception":true,"date":"Wed Aug 20 2025 15:44:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":313,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":150818816,"heapTotal":52973568,"heapUsed":47932376,"external":3460151,"arrayBuffers":186038}},"os":{"loadavg":[2.36,2.39,2.4],"uptime":7651.75},"trace":[{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeAfterEnd","line":264,"method":null,"native":false},{"column":21,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":300,"method":"write","native":false},{"column":34,"file":"/usr/src/app/node_modules/winston/lib/winston/transports/file.js","function":"File.log","line":222,"method":"log","native":false},{"column":29,"file":"/usr/src/app/node_modules/winston/lib/winston/rejection-stream.js","function":"RejectionStream._write","line":46,"method":"_write","native":false},{"column":139,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false},{"column":5,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"writeOrBuffer","line":381,"method":null,"native":false},{"column":11,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_writable.js","function":"Writable.write","line":302,"method":"write","native":false},{"column":20,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"DerivedLogger.ondata","line":629,"method":"ondata","native":false},{"column":35,"file":"node:events","function":"DerivedLogger.emit","line":531,"method":"emit","native":false},{"column":12,"file":"/usr/src/app/node_modules/winston/node_modules/readable-stream/lib/_stream_readable.js","function":"addChunk","line":279,"method":null,"native":false}]}
{"timestamp":"2025-08-20 16:35:56","level":"error","message":"uncaughtException: Route.get() requires a callback function but got a [object Undefined]\nError: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/professionRoutes.js:19:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:58:26)","error":{},"stack":"Error: Route.get() requires a callback function but got a [object Undefined]\n    at Route.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/route.js:216:15)\n    at proto.<computed> [as get] (/usr/src/app/node_modules/express/lib/router/index.js:521:19)\n    at Object.<anonymous> (/usr/src/app/src/routes/professionRoutes.js:19:8)\n    at Module._compile (node:internal/modules/cjs/loader:1469:14)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1548:10)\n    at Module.load (node:internal/modules/cjs/loader:1288:32)\n    at Module._load (node:internal/modules/cjs/loader:1104:12)\n    at Module.require (node:internal/modules/cjs/loader:1311:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (/usr/src/app/src/server.js:58:26)","exception":true,"date":"Wed Aug 20 2025 16:35:56 GMT+0000 (Coordinated Universal Time)","process":{"pid":219,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":161226752,"heapTotal":105353216,"heapUsed":77829400,"external":3226008,"arrayBuffers":98464}},"os":{"loadavg":[1.95,2.23,2.3],"uptime":10728.63},"trace":[{"column":15,"file":"/usr/src/app/node_modules/express/lib/router/route.js","function":"Route.<computed> [as get]","line":216,"method":"<computed> [as get]","native":false},{"column":19,"file":"/usr/src/app/node_modules/express/lib/router/index.js","function":"proto.<computed> [as get]","line":521,"method":"<computed> [as get]","native":false},{"column":8,"file":"/usr/src/app/src/routes/professionRoutes.js","function":null,"line":19,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1469,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Module._extensions..js","line":1548,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1288,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1104,"method":"_load","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":18,"file":"node:internal/modules/helpers","function":"require","line":179,"method":null,"native":false},{"column":26,"file":"/usr/src/app/src/server.js","function":null,"line":58,"method":null,"native":false}]}
