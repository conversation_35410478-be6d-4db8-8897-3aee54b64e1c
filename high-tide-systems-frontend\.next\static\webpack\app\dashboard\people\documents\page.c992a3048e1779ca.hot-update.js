"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/components/people/DocumentSharingManagementModal.js":
/*!*****************************************************************!*\
  !*** ./src/components/people/DocumentSharingManagementModal.js ***!
  \*****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog.jsx */ \"(app-pages-browser)/./src/components/ui/dialog.jsx\");\n/* harmony import */ var _components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button.js */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input.js */ \"(app-pages-browser)/./src/components/ui/Input.js\");\n/* harmony import */ var _components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Label.js */ \"(app-pages-browser)/./src/components/ui/Label.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Badge.js */ \"(app-pages-browser)/./src/components/ui/Badge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,CheckCircle,Eye,Plus,Search,Share2,User,UserCheck,UserX,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DocumentSharingManagementModal = (param)=>{\n    let { isOpen, onClose, document, onSuccess } = param;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoadingData, setIsLoadingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clients, setClients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentPermissions, setCurrentPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        users: [],\n        professions: [],\n        clients: []\n    });\n    const [newPermissions, setNewPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        users: [],\n        professions: [],\n        clients: []\n    });\n    const [userSearch, setUserSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [clientSearch, setClientSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentSharingManagementModal.useEffect\": ()=>{\n            if (isOpen && document) {\n                setIsLoadingData(true);\n                setError(null);\n                Promise.all([\n                    loadUsers(),\n                    loadProfessions(),\n                    loadClients(),\n                    loadCurrentPermissions()\n                ]).finally({\n                    \"DocumentSharingManagementModal.useEffect\": ()=>{\n                        setIsLoadingData(false);\n                    }\n                }[\"DocumentSharingManagementModal.useEffect\"]);\n            }\n        }\n    }[\"DocumentSharingManagementModal.useEffect\"], [\n        isOpen,\n        document\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setUsers(data.users || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar usuários:\", error);\n            setError(\"Erro ao carregar usuários\");\n        }\n    };\n    const loadProfessions = async ()=>{\n        try {\n            const response = await fetch('/api/professions', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setProfessions(data.professions || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            setError(\"Erro ao carregar profissões\");\n        }\n    };\n    const loadClients = async ()=>{\n        try {\n            const response = await fetch('/api/clients', {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                setClients(data.clients || data || []);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar clientes:\", error);\n            setError(\"Erro ao carregar clientes\");\n        }\n    };\n    const loadCurrentPermissions = async ()=>{\n        try {\n            const response = await fetch(\"/api/documents/\".concat(document.id, \"/permissions\"), {\n                headers: {\n                    'Authorization': \"Bearer \".concat(localStorage.getItem('token'))\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                const permissions = {\n                    users: data.users || [],\n                    professions: data.professions || [],\n                    clients: data.clients || []\n                };\n                setCurrentPermissions(permissions);\n                setNewPermissions(permissions);\n            } else {\n                setError(\"Erro ao carregar permissões atuais\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar permissões:\", error);\n            setError(\"Erro ao carregar permissões atuais\");\n        }\n    };\n    const handleAddProfession = ()=>{\n        if (selectedProfession && !newPermissions.professions.includes(selectedProfession)) {\n            setNewPermissions((prev)=>({\n                    ...prev,\n                    professions: [\n                        ...prev.professions,\n                        selectedProfession\n                    ]\n                }));\n            setSelectedProfession(\"\");\n        }\n    };\n    const handleRemoveProfession = (professionId)=>{\n        setNewPermissions((prev)=>({\n                ...prev,\n                professions: prev.professions.filter((id)=>id !== professionId)\n            }));\n    };\n    const handleAddUser = (userId)=>{\n        if (!newPermissions.users.includes(userId)) {\n            setNewPermissions((prev)=>({\n                    ...prev,\n                    users: [\n                        ...prev.users,\n                        userId\n                    ]\n                }));\n        }\n    };\n    const handleRemoveUser = (userId)=>{\n        setNewPermissions((prev)=>({\n                ...prev,\n                users: prev.users.filter((id)=>id !== userId)\n            }));\n    };\n    const handleAddClient = (clientId)=>{\n        if (!newPermissions.clients.includes(clientId)) {\n            setNewPermissions((prev)=>({\n                    ...prev,\n                    clients: [\n                        ...prev.clients,\n                        clientId\n                    ]\n                }));\n        }\n    };\n    const handleRemoveClient = (clientId)=>{\n        setNewPermissions((prev)=>({\n                ...prev,\n                clients: prev.clients.filter((id)=>id !== clientId)\n            }));\n    };\n    const handleSavePermissions = async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await fetch(\"/api/documents/\".concat(document.id, \"/permissions\"), {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"token\"))\n                },\n                body: JSON.stringify(newPermissions)\n            });\n            if (response.ok) {\n                onSuccess();\n            } else {\n                const errorData = await response.json();\n                setError(errorData.message || \"Erro ao salvar permissões\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao salvar permissões:\", error);\n            setError(\"Erro ao salvar permissões\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getCurrentUserNames = ()=>{\n        return currentPermissions.users.map((id)=>{\n            const user = users.find((u)=>u.id === id);\n            return (user === null || user === void 0 ? void 0 : user.fullName) || id;\n        });\n    };\n    const getCurrentProfessionNames = ()=>{\n        return currentPermissions.professions.map((id)=>{\n            const profession = professions.find((p)=>p.id === id);\n            return (profession === null || profession === void 0 ? void 0 : profession.name) || id;\n        });\n    };\n    const getCurrentClientNames = ()=>{\n        return currentPermissions.clients.map((id)=>{\n            const client = clients.find((c)=>c.id === id);\n            return (client === null || client === void 0 ? void 0 : client.login) || id;\n        });\n    };\n    const getSelectedUserNames = ()=>{\n        return newPermissions.users.map((id)=>{\n            const user = users.find((u)=>u.id === id);\n            return (user === null || user === void 0 ? void 0 : user.fullName) || id;\n        });\n    };\n    const getSelectedProfessionNames = ()=>{\n        return newPermissions.professions.map((id)=>{\n            const profession = professions.find((p)=>p.id === id);\n            return (profession === null || profession === void 0 ? void 0 : profession.name) || id;\n        });\n    };\n    const getSelectedClientNames = ()=>{\n        return newPermissions.clients.map((id)=>{\n            const client = clients.find((c)=>c.id === id);\n            return (client === null || client === void 0 ? void 0 : client.login) || id;\n        });\n    };\n    // Filtrar usuários disponíveis (não selecionados e não em profissões selecionadas)\n    const availableUsers = users.filter((user)=>{\n        const isSelected = newPermissions.users.includes(user.id);\n        const isInSelectedProfession = newPermissions.professions.includes(user.professionId);\n        return !isSelected && !isInSelectedProfession;\n    });\n    const filteredUsers = availableUsers.filter((user)=>user.fullName.toLowerCase().includes(userSearch.toLowerCase()) || user.email.toLowerCase().includes(userSearch.toLowerCase()));\n    const filteredClients = clients.filter((client)=>!newPermissions.clients.includes(client.id) && (client.login.toLowerCase().includes(clientSearch.toLowerCase()) || client.email.toLowerCase().includes(clientSearch.toLowerCase())));\n    // Verificar se há mudanças\n    const hasChanges = JSON.stringify(currentPermissions) !== JSON.stringify(newPermissions);\n    if (isLoadingData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n            open: isOpen,\n            onOpenChange: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n                className: \"max-w-5xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 20,\n                                    className: \"text-orange-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 272,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Gerenciar Compartilhamentos: \",\n                                document === null || document === void 0 ? void 0 : document.filename\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center text-gray-500 dark:text-gray-400\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Carregando dados...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                        lineNumber: 276,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                lineNumber: 269,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-6xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        className: \"text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 20,\n                                className: \"text-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Gerenciar Compartilhamentos: \",\n                            document === null || document === void 0 ? void 0 : document.filename\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800/30\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 text-red-700 dark:text-red-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                lineNumber: 300,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium\",\n                                children: \"Erro:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                        lineNumber: 299,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800/30\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            size: 18\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Compartilhamentos Atuais\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 p-4 rounded-lg border border-blue-100 dark:border-blue-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 317,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-blue-700 dark:text-blue-300\",\n                                                            children: [\n                                                                \"Profiss\\xf5es (\",\n                                                                currentPermissions.professions.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        getCurrentProfessionNames().map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        size: 10,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    name\n                                                                ]\n                                                            }, \"current-profession-\".concat(index), true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 21\n                                                            }, undefined)),\n                                                        currentPermissions.professions.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-600 dark:text-blue-400 text-sm italic\",\n                                                            children: \"Nenhuma profiss\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 p-4 rounded-lg border border-green-100 dark:border-green-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-green-700 dark:text-green-300\",\n                                                            children: [\n                                                                \"Usu\\xe1rios (\",\n                                                                currentPermissions.users.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        getCurrentUserNames().slice(0, 3).map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        size: 10,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 345,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    name\n                                                                ]\n                                                            }, \"current-user-\".concat(index), true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 21\n                                                            }, undefined)),\n                                                        currentPermissions.users.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300\",\n                                                            children: [\n                                                                \"+\",\n                                                                currentPermissions.users.length - 3,\n                                                                \" mais\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        currentPermissions.users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-600 dark:text-green-400 text-sm italic\",\n                                                            children: \"Nenhum usu\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white dark:bg-gray-800 p-4 rounded-lg border border-purple-100 dark:border-purple-800/50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"text-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-purple-700 dark:text-purple-300\",\n                                                            children: [\n                                                                \"Clientes (\",\n                                                                currentPermissions.clients.length,\n                                                                \")\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        getCurrentClientNames().slice(0, 3).map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        size: 10,\n                                                                        className: \"mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    name\n                                                                ]\n                                                            }, \"current-client-\".concat(index), true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 21\n                                                            }, undefined)),\n                                                        currentPermissions.clients.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            variant: \"secondary\",\n                                                            className: \"text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300\",\n                                                            children: [\n                                                                \"+\",\n                                                                currentPermissions.clients.length - 3,\n                                                                \" mais\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        currentPermissions.clients.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-purple-600 dark:text-purple-400 text-sm italic\",\n                                                            children: \"Nenhum cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"text-lg font-semibold text-blue-700 dark:text-blue-300\",\n                                                    children: \"Profiss\\xf5es\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                                            value: selectedProfession,\n                                                            onChange: (e)=>setSelectedProfession(e.target.value),\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Selecionar profiss\\xe3o\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                professions.filter((prof)=>!newPermissions.professions.includes(prof.id)).map((profession)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: profession.id,\n                                                                        children: profession.name\n                                                                    }, profession.id, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 25\n                                                                    }, undefined))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 398,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            onClick: handleAddProfession,\n                                                            disabled: !selectedProfession,\n                                                            size: \"sm\",\n                                                            className: \"px-3 bg-blue-500 hover:bg-blue-600\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: newPermissions.professions.map((professionId)=>{\n                                                        const profession = professions.find((p)=>p.id === professionId);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            size: 14,\n                                                                            className: \"text-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-blue-800 dark:text-blue-200\",\n                                                                            children: (profession === null || profession === void 0 ? void 0 : profession.name) || professionId\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveProfession(professionId),\n                                                                    className: \"p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors\",\n                                                                    title: \"Remover profiss\\xe3o\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, professionId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"text-lg font-semibold text-green-700 dark:text-green-300\",\n                                                    children: \"Usu\\xe1rios\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            placeholder: \"Buscar usu\\xe1rios...\",\n                                                            value: userSearch,\n                                                            onChange: (e)=>setUserSearch(e.target.value),\n                                                            className: \"pl-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-48 overflow-y-auto border rounded-lg p-2 space-y-1 bg-gray-50 dark:bg-gray-800/50\",\n                                                    children: filteredUsers.length > 0 ? filteredUsers.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-2 rounded hover:bg-white dark:hover:bg-gray-700 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                                            children: user.fullName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                                            children: user.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 471,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleAddUser(user.id),\n                                                                    className: \"p-1 text-green-500 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 rounded transition-colors\",\n                                                                    title: \"Adicionar usu\\xe1rio\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, user.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 23\n                                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-4 text-gray-500 dark:text-gray-400 text-sm\",\n                                                        children: userSearch ? \"Nenhum usuário encontrado\" : \"Todos os usuários já estão selecionados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: newPermissions.users.filter((userId)=>{\n                                                        const user = users.find((u)=>u.id === userId);\n                                                        return user && !newPermissions.professions.includes(user.professionId);\n                                                    }).map((userId)=>{\n                                                        const user = users.find((u)=>u.id === userId);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800/30 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            size: 14,\n                                                                            className: \"text-green-500 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 507,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-green-800 dark:text-green-200 truncate\",\n                                                                                    children: (user === null || user === void 0 ? void 0 : user.fullName) || userId\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                                    lineNumber: 509,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-green-600 dark:text-green-300 truncate\",\n                                                                                    children: user === null || user === void 0 ? void 0 : user.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                                    lineNumber: 512,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveUser(userId),\n                                                                    className: \"p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors flex-shrink-0\",\n                                                                    title: \"Remover usu\\xe1rio\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, userId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 25\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    size: 20,\n                                                    className: \"text-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"text-lg font-semibold text-purple-700 dark:text-purple-300\",\n                                                    children: \"Clientes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 533,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            placeholder: \"Buscar clientes...\",\n                                                            value: clientSearch,\n                                                            onChange: (e)=>setClientSearch(e.target.value),\n                                                            className: \"pl-10\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"max-h-48 overflow-y-auto border rounded-lg p-2 space-y-1 bg-gray-50 dark:bg-gray-800/50\",\n                                                    children: filteredClients.length > 0 ? filteredClients.map((client)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-2 rounded hover:bg-white dark:hover:bg-gray-700 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                                            children: client.login\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                                            children: client.email\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 558,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleAddClient(client.id),\n                                                                    className: \"p-1 text-purple-500 hover:text-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors\",\n                                                                    title: \"Adicionar cliente\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 567,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, client.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, undefined)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-4 text-gray-500 dark:text-gray-400 text-sm\",\n                                                        children: clientSearch ? \"Nenhum cliente encontrado\" : \"Todos os clientes já estão selecionados\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: newPermissions.clients.map((clientId)=>{\n                                                        const client = clients.find((c)=>c.id === clientId);\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 flex-1 min-w-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            size: 14,\n                                                                            className: \"text-purple-500 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm font-medium text-purple-800 dark:text-purple-200 truncate\",\n                                                                                    children: (client === null || client === void 0 ? void 0 : client.login) || clientId\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                                    lineNumber: 587,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-purple-600 dark:text-purple-300 truncate\",\n                                                                                    children: client === null || client === void 0 ? void 0 : client.email\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                                    lineNumber: 590,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleRemoveClient(clientId),\n                                                                    className: \"p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors flex-shrink-0\",\n                                                                    title: \"Remover cliente\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        size: 14\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, clientId, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, undefined),\n                        hasChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800/30\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 text-amber-700 dark:text-amber-300\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_CheckCircle_Eye_Plus_Search_Share2_User_UserCheck_UserX_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                        lineNumber: 614,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Altera\\xe7\\xf5es pendentes:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                        lineNumber: 615,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            newPermissions.professions.length - currentPermissions.professions.length,\n                                            \" profiss\\xf5es,\",\n                                            newPermissions.users.length - currentPermissions.users.length,\n                                            \" usu\\xe1rios,\",\n                                            newPermissions.clients.length - currentPermissions.clients.length,\n                                            \" clientes\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                        lineNumber: 616,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                lineNumber: 613,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 612,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    disabled: isLoading,\n                                    className: \"min-w-[100px]\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 627,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    onClick: handleSavePermissions,\n                                    disabled: isLoading || !hasChanges,\n                                    moduleColor: \"people\",\n                                    className: \"min-w-[120px] bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    isLoading: isLoading,\n                                    children: isLoading ? \"Salvando...\" : \"Salvar Alterações\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                                    lineNumber: 636,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                            lineNumber: 626,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n            lineNumber: 289,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentSharingManagementModal.js\",\n        lineNumber: 288,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentSharingManagementModal, \"YjiZI3WqYvJWb+9dIHBqtAtTdX0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_8__.useAuth\n    ];\n});\n_c = DocumentSharingManagementModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentSharingManagementModal);\nvar _c;\n$RefreshReg$(_c, \"DocumentSharingManagementModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/people/DocumentSharingManagementModal.js\n"));

/***/ })

});