"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import "./profile.css";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { userService } from "@/app/modules/admin/services/userService";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { personsService } from "@/app/modules/people/services/personsService";
import ModulePreferences from "./components/ModulePreferences";
import UserNotificationPreferences from "./components/UserNotificationPreferences";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Building,
  Briefcase,
  Shield,
  Save,
  Loader2,
  Clock,
  Award,
  Key,
  UserCheck,
  Activity,
  Lock,
  CheckCircle,
  AlertCircle,
  ArrowL<PERSON><PERSON>,
  LayoutDashboard,
  Settings
} from "lucide-react";
import {
  ModuleHeader,
  ModuleInput,
  ModuleFormGroup,
  ModalButton,
  ModuleTabs
} from "@/components/ui";
import ProfileModuleSelect from "./ProfileModuleSelect";
import ClientProfileImageUpload from "@/components/forms/ClientProfileImageUpload";

const ProfilePage = () => {
  const router = useRouter();
  const { user, refreshUserData } = useAuth();
  const { toast_success, toast_error } = useToast();

  // Determinar o tipo de usuário (User ou Client) - Verificamos diretamente no JSX
  // Inicializar o estado do formulário com valores vazios
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    birthDate: "",
    professionId: "",
    branchId: "",
    person: {
      fullName: "",
      phone: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      birthDate: ""
    }
  });
  // Estado para controlar o carregamento e salvamento
  // const [isLoading, setIsLoading] = useState(false); // Não utilizado
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [professions, setProfessions] = useState([]);
  const [branches, setBranches] = useState([]);
  const [isLoadingProfessions, setIsLoadingProfessions] = useState(false);
  const [isLoadingBranches, setIsLoadingBranches] = useState(false);
  const profileImageUploadRef = useRef(null);

  // Carregar dados do usuário
  useEffect(() => {
    if (user) {
      // Verificar o tipo de usuário dentro do useEffect
      const userIsClient = user.role === 'CLIENT';

      if (userIsClient && user.persons && user.persons[0]) {
        // Formulário para clientes
        const person = user.persons[0];
        setFormData(prevData => ({
          ...prevData,
          email: user.email || "",
          person: {
            ...prevData.person,
            fullName: person.fullName || "",
            phone: person.phone || "",
            address: person.address || "",
            neighborhood: person.neighborhood || "",
            city: person.city || "",
            state: person.state || "",
            postalCode: person.postalCode || "",
            birthDate: person.birthDate ? new Date(person.birthDate).toISOString().split('T')[0] : "",
          }
        }));
      } else {
        // Formulário para usuários normais
        setFormData(prevData => ({
          ...prevData,
          fullName: user.fullName || "",
          email: user.email || "",
          phone: user.phone || "",
          address: user.address || "",
          neighborhood: user.neighborhood || "",
          city: user.city || "",
          state: user.state || "",
          postalCode: user.postalCode || "",
          birthDate: user.birthDate ? new Date(user.birthDate).toISOString().split('T')[0] : "",
          professionId: user.professionId || "",
          branchId: user.branchId || ""
        }));
      }
    }
  }, [user]); // Remover isClient da lista de dependências

  // Carregar profissões
  useEffect(() => {
    const loadProfessions = async () => {
      setIsLoadingProfessions(true);
      try {
        const data = await professionsService.getProfessions({ active: true });
        setProfessions(data);
      } catch (error) {
        console.error("Erro ao carregar profissões:", error);
        toast_error("Erro ao carregar profissões");
      } finally {
        setIsLoadingProfessions(false);
      }
    };

    loadProfessions();
  }, [toast_error]);

  // Carregar unidades
  useEffect(() => {
    const loadBranches = async () => {
      // Se o usuário não tiver companyId (como no caso de SYSTEM_ADMIN), definimos branches como um array vazio
      if (!user?.companyId) {
        setBranches({ branches: [] });
        return;
      }

      setIsLoadingBranches(true);
      try {
        const data = await branchService.getBranches({
          companyId: user.companyId,
          active: true
        });
        // Garantir que data.branches existe, caso contrário, usar um array vazio
        setBranches(data && data.branches ? data : { branches: [] });
      } catch (error) {
        console.error("Erro ao carregar unidades:", error);
        toast_error("Erro ao carregar unidades");
        // Em caso de erro, definir branches como um array vazio
        setBranches({ branches: [] });
      } finally {
        setIsLoadingBranches(false);
      }
    };

    loadBranches();
  }, [user, toast_error]);

  // Função para lidar com mudanças nos campos do formulário
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Verificar se é um campo de pessoa
    if (name.startsWith('person.')) {
      const personField = name.replace('person.', '');
      setFormData(prev => ({
        ...prev,
        person: {
          ...prev.person,
          [personField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  // Manipular upload de imagem de perfil
  const handleProfileImageUpload = async (file) => {
    if (!file || !user?.id) return;

    setIsUploadingImage(true);
    try {
      console.log("Iniciando upload de imagem para o usuário", user.id);
      console.log("Arquivo selecionado:", file.name, file.type, file.size);

      // Upload da imagem para o usuário
      await userService.uploadProfileImage(user.id, file);

      // Atualizar dados do usuário no contexto
      await refreshUserData();

      toast_success({
        title: "Sucesso",
        message: "Sua foto de perfil foi atualizada com sucesso!"
      });
    } catch (error) {
      console.error("Erro ao fazer upload da imagem:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível atualizar sua foto de perfil. Tente novamente mais tarde."
      });
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Função para salvar o perfil
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // Não precisamos mais verificar se há uma nova imagem de perfil para upload
      // pois isso é tratado separadamente pela função handleProfileImageUpload

      // Verificar o tipo de usuário dentro da função
      const userIsClient = user?.role === 'CLIENT';

      if (userIsClient && user.persons && user.persons[0]) {
        // Atualização para clientes
        // Preparar dados para atualização do cliente
        const clientPayload = {
          email: formData.email
        };

        // Preparar dados para atualização da pessoa associada
        const personPayload = {
          fullName: formData.person.fullName,
          phone: formData.person.phone ? formData.person.phone.replace(/\D/g, "") : null,
          address: formData.person.address,
          neighborhood: formData.person.neighborhood,
          city: formData.person.city,
          state: formData.person.state,
          postalCode: formData.person.postalCode,
          birthDate: formData.person.birthDate || null
        };

        // Atualizar cliente
        await clientsService.updateClient(user.id, clientPayload);

        // Atualizar pessoa associada ao cliente
        await personsService.updatePerson(user.persons[0].id, personPayload);
      } else {
        // Atualização para usuários normais
        // Preparar dados para atualização
        const payload = {
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone ? formData.phone.replace(/\D/g, "") : null,
          address: formData.address,
          neighborhood: formData.neighborhood,
          city: formData.city,
          state: formData.state,
          postalCode: formData.postalCode,
          birthDate: formData.birthDate || null,
          professionId: formData.professionId || null,
          branchId: formData.branchId || null
        };

        // Atualizar perfil
        await userService.update(user.id, payload);
      }

      // Atualizar dados do usuário no contexto
      await refreshUserData();

      toast_success("Perfil atualizado com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      toast_error("Erro ao atualizar perfil. Por favor, tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };

  // Função para formatar o papel do usuário
  const formatUserRole = (role) => {
    switch (role) {
      case "SYSTEM_ADMIN":
        return "Administrador do Sistema";
      case "COMPANY_ADMIN":
        return "Administrador da Empresa";
      case "EMPLOYEE":
        return "Funcionário";
      default:
        return role;
    }
  };

  // Estado para controlar a aba ativa
  const [activeTab, setActiveTab] = useState("profile");

  // Configuração das abas
  const tabsConfig = [
    {
      id: "profile",
      label: "Perfil",
      icon: <User size={18} />
    },
    {
      id: "preferences",
      label: "Minhas Preferências",
      icon: <Settings size={18} />
    }
  ];

  // Renderizar o componente
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={() => router.push('/dashboard')}
          className="flex items-center gap-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-200 rounded-lg transition-colors shadow-sm"
        >
          <LayoutDashboard size={18} />
          <span>Voltar ao Dashboard</span>
        </button>
      </div>

      {/* Banner de perfil com gradiente */}
      <div className="relative bg-gradient-to-r from-slate-600 to-slate-800 dark:from-slate-700 dark:to-slate-900 rounded-xl shadow-lg overflow-hidden mb-8">
        <div className="absolute inset-0 bg-pattern opacity-10"></div>

        <div className="relative z-10 p-8 flex flex-col md:flex-row items-center md:items-start gap-6">
          {/* Foto de perfil com borda */}
          <div className="flex-shrink-0 rounded-full p-1 bg-white/20 backdrop-blur-sm shadow-xl relative group">
            <div className="h-32 w-32 rounded-full overflow-hidden border-4 border-white/30 shadow-inner">
              {user?.profileImageFullUrl ? (
                <img
                  src={user.profileImageFullUrl}
                  alt={`Foto de perfil de ${user?.fullName || 'Usuário'}`}
                  className="h-full w-full object-cover"
                  onError={(e) => {
                    console.error("Erro ao carregar imagem:", e.target.src);
                    e.target.onerror = null;
                    e.target.style.display = 'none';
                    e.target.parentNode.innerHTML = `<div class="flex items-center justify-center w-full h-full bg-slate-300 dark:bg-slate-700"><User size={40} className="text-slate-600 dark:text-slate-400" /></div>`;
                  }}
                />
              ) : (
                <div className="flex items-center justify-center w-full h-full bg-slate-300 dark:bg-slate-700">
                  <User size={40} className="text-slate-600 dark:text-slate-400" />
                </div>
              )}
            </div>

            {/* Overlay para upload de imagem */}
            <button
              type="button"
              onClick={() => profileImageUploadRef.current?.click()}
              className="absolute inset-0 flex items-center justify-center bg-black/40 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              title="Alterar foto de perfil"
              disabled={isUploadingImage}
            >
              {isUploadingImage ? (
                <Loader2 size={24} className="text-white animate-spin" />
              ) : (
                <div className="flex flex-col items-center">
                  <UserCheck size={24} className="text-white mb-1" />
                  <span className="text-white text-xs font-medium">Alterar foto</span>
                </div>
              )}
            </button>
          </div>

          {/* Informações do usuário */}
          <div className="flex flex-col items-center md:items-start text-white">
            <h2 className="text-2xl font-bold mb-1">{user?.fullName}</h2>
            <p className="text-slate-200 mb-3">{user?.email}</p>

            <div className="flex flex-wrap gap-3 mt-2">
              <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                <Shield size={14} className="text-slate-200" />
                <span>{user?.role === 'CLIENT' ? 'Cliente' : formatUserRole(user?.role)}</span>
              </div>

              {user?.company && (
                <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                  <Building size={14} className="text-slate-200" />
                  <span>{user.company.name}</span>
                </div>
              )}

              {user?.login && (
                <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                  <Key size={14} className="text-slate-200" />
                  <span>{user.login}</span>
                </div>
              )}

              {user?.role === 'CLIENT' && user?.persons && user?.persons[0]?.cpf && (
                <div className="flex items-center gap-1.5 bg-white/10 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm">
                  <User size={14} className="text-slate-200" />
                  <span>CPF: {user.persons[0].cpf.replace(/^(\d{3})(\d{3})(\d{3})(\d{2})$/, "$1.$2.$3-$4")}</span>
                </div>
              )}
            </div>
          </div>

          {/* Espaço reservado para ações futuras */}
          <div className="absolute top-4 right-4">
          </div>
        </div>
      </div>

      {/* Abas de navegação */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden mb-6">
        <ModuleTabs
          tabs={tabsConfig}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          moduleColor="admin"
        />
      </div>

      {activeTab === "profile" ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Coluna da esquerda - Informações básicas e foto */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
            <h3 className="text-lg font-medium text-neutral-900 dark:text-neutral-100 mb-4 flex items-center gap-2">
              <UserCheck size={18} className="text-slate-500 dark:text-slate-400" />
              Informações da Conta
            </h3>

            {/* Upload de foto de perfil (oculto, acionado pelo botão no banner) */}
            <div className="hidden">
              <ClientProfileImageUpload
                ref={profileImageUploadRef}
                onImageSelected={handleProfileImageUpload}
              />
            </div>

            {/* Status da conta */}
            <div className="mb-6 p-4 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700">
              <div className="flex items-center gap-2 mb-3">
                {user?.active ? (
                  <CheckCircle size={18} className="text-green-500 dark:text-green-400" />
                ) : (
                  <AlertCircle size={18} className="text-red-500 dark:text-red-400" />
                )}
                <span className="font-medium text-neutral-800 dark:text-neutral-200">
                  Status: {user?.active ? 'Ativo' : 'Inativo'}
                </span>
              </div>

              {user?.lastLoginAt && (
                <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400 mb-2">
                  <Clock size={14} />
                  <span>Último acesso: {new Date(user.lastLoginAt).toLocaleString()}</span>
                </div>
              )}

              {user?.createdAt && (
                <div className="flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400">
                  <Calendar size={14} />
                  <span>Conta criada em: {new Date(user.createdAt).toLocaleDateString()}</span>
                </div>
              )}
            </div>

            {/* Permissões e módulos - Apenas para usuários, não para clientes */}
            {user?.role !== 'CLIENT' && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3 flex items-center gap-2">
                  <Lock size={14} className="text-neutral-500 dark:text-neutral-400" />
                  Módulos de Acesso
                </h4>

                <div className="flex flex-wrap gap-2">
                  {user?.modules?.map((module) => (
                    <span
                      key={module}
                      className="px-2.5 py-1 text-xs rounded-full bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300"
                    >
                      {module}
                    </span>
                  ))}
                  {(!user?.modules || user.modules.length === 0) && (
                    <span className="text-sm text-neutral-500 dark:text-neutral-400 italic">
                      Nenhum módulo atribuído
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Informações adicionais para clientes */}
            {user?.role === 'CLIENT' && user?.persons && user?.persons[0] && (
              <div className="mb-6">
                <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3 flex items-center gap-2">
                  <User size={14} className="text-neutral-500 dark:text-neutral-400" />
                  Informações Pessoais
                </h4>

                <div className="space-y-2 text-sm">
                  {user.persons[0].phone && (
                    <div className="flex items-center gap-2 text-neutral-600 dark:text-neutral-400">
                      <Phone size={14} />
                      <span>Telefone: {user.persons[0].phone}</span>
                    </div>
                  )}

                  {user.persons[0].birthDate && (
                    <div className="flex items-center gap-2 text-neutral-600 dark:text-neutral-400">
                      <Calendar size={14} />
                      <span>Data de Nascimento: {new Date(user.persons[0].birthDate).toLocaleDateString()}</span>
                    </div>
                  )}

                  {user.persons[0].address && (
                    <div className="flex items-start gap-2 text-neutral-600 dark:text-neutral-400">
                      <MapPin size={14} className="mt-0.5" />
                      <span>
                        {user.persons[0].address}
                        {user.persons[0].neighborhood && `, ${user.persons[0].neighborhood}`}
                        {user.persons[0].city && `, ${user.persons[0].city}`}
                        {user.persons[0].state && ` - ${user.persons[0].state}`}
                        {user.persons[0].postalCode && `, ${user.persons[0].postalCode}`}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Estatísticas */}
            <div>
              <h4 className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-3 flex items-center gap-2">
                <Activity size={14} className="text-neutral-500 dark:text-neutral-400" />
                Estatísticas
              </h4>

              <div className="grid grid-cols-2 gap-3">
                <div className="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 text-center">
                  <div className="text-2xl font-semibold text-slate-700 dark:text-slate-300">0</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Agendamentos</div>
                </div>
                <div className="p-3 bg-slate-50 dark:bg-slate-800/50 rounded-lg border border-slate-200 dark:border-slate-700 text-center">
                  <div className="text-2xl font-semibold text-slate-700 dark:text-slate-300">0</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">Pacientes</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Coluna da direita - Formulário de edição */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
            <h2 className="text-xl font-medium text-neutral-900 dark:text-neutral-100 mb-2 flex items-center gap-2">
              <Award size={20} className="text-slate-500 dark:text-slate-400" />
              Editar Informações
            </h2>
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mb-6">Atualize seus dados pessoais e profissionais</p>

            <form onSubmit={handleSubmit} className="space-y-6 profile-form">
              {/* Barra de progresso */}
              <div className="w-full bg-slate-100 dark:bg-slate-700 h-1.5 rounded-full overflow-hidden mb-6">
                <div className="bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-400 dark:to-slate-500 h-full rounded-full" style={{ width: '100%' }}></div>
              </div>
              {/* Nome completo */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Nome Completo"
                htmlFor={user?.role === 'CLIENT' ? "person.fullName" : "fullName"}
                icon={<User size={16} />}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.fullName" : "fullName"}
                  name={user?.role === 'CLIENT' ? "person.fullName" : "fullName"}
                  value={user?.role === 'CLIENT' ? formData.person?.fullName : formData.fullName}
                  onChange={handleChange}
                  placeholder="Seu nome completo"
                  required
                />
              </ModuleFormGroup>

              {/* Email */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Email"
                htmlFor="email"
                icon={<Mail size={16} />}
              >
                <ModuleInput
                  moduleColor="admin"
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  required
                />
              </ModuleFormGroup>

              {/* Telefone */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Telefone"
                htmlFor={user?.role === 'CLIENT' ? "person.phone" : "phone"}
                icon={<Phone size={16} />}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.phone" : "phone"}
                  name={user?.role === 'CLIENT' ? "person.phone" : "phone"}
                  value={user?.role === 'CLIENT' ? formData.person?.phone : formData.phone}
                  onChange={handleChange}
                  placeholder="(00) 00000-0000"
                />
              </ModuleFormGroup>

              {/* Data de Nascimento */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Data de Nascimento"
                htmlFor={user?.role === 'CLIENT' ? "person.birthDate" : "birthDate"}
                icon={<Calendar size={16} />}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.birthDate" : "birthDate"}
                  name={user?.role === 'CLIENT' ? "person.birthDate" : "birthDate"}
                  type="date"
                  value={user?.role === 'CLIENT' ? formData.person?.birthDate : formData.birthDate}
                  onChange={handleChange}
                />
              </ModuleFormGroup>

              {/* Profissão - Apenas para usuários normais */}
              {user?.role !== 'CLIENT' && (
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Profissão"
                  htmlFor="professionId"
                  icon={<Briefcase size={16} />}
                >
                  <ProfileModuleSelect
                    moduleColor="admin"
                    id="professionId"
                    name="professionId"
                    value={formData.professionId}
                    onChange={handleChange}
                    disabled={isLoadingProfessions}
                  >
                    <option value="">Selecione uma profissão</option>
                    {professions.map((profession) => (
                      <option key={profession.id} value={profession.id}>
                        {profession.name}
                      </option>
                    ))}
                  </ProfileModuleSelect>
                </ModuleFormGroup>
              )}

              {/* Unidade - Apenas para usuários normais */}
              {user?.role !== 'CLIENT' && (
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Unidade"
                  htmlFor="branchId"
                  icon={<Building size={16} />}
                >
                  <ProfileModuleSelect
                    moduleColor="admin"
                    id="branchId"
                    name="branchId"
                    value={formData.branchId}
                    onChange={handleChange}
                    disabled={isLoadingBranches}
                  >
                    <option value="">Selecione uma unidade</option>
                    {branches && branches.branches && Array.isArray(branches.branches) ?
                      branches.branches.map((branch) => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name}
                        </option>
                      ))
                    : null}
                  </ProfileModuleSelect>
                </ModuleFormGroup>
              )}

              <div className="relative mt-10 mb-6">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-200 dark:border-slate-700"></div>
                </div>
                <div className="relative flex justify-start">
                  <span className="bg-white dark:bg-gray-800 pr-3 text-lg font-medium text-neutral-900 dark:text-neutral-100 flex items-center gap-2">
                    <MapPin size={18} className="text-slate-500 dark:text-slate-400" />
                    Endereço
                  </span>
                </div>
              </div>

              {/* Endereço */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Endereço"
                htmlFor={user?.role === 'CLIENT' ? "person.address" : "address"}
                icon={<MapPin size={16} />}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.address" : "address"}
                  name={user?.role === 'CLIENT' ? "person.address" : "address"}
                  value={user?.role === 'CLIENT' ? formData.person?.address : formData.address}
                  onChange={handleChange}
                  placeholder="Rua, número, complemento"
                />
              </ModuleFormGroup>

              {/* Bairro */}
              <ModuleFormGroup
                moduleColor="admin"
                label="Bairro"
                htmlFor={user?.role === 'CLIENT' ? "person.neighborhood" : "neighborhood"}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.neighborhood" : "neighborhood"}
                  name={user?.role === 'CLIENT' ? "person.neighborhood" : "neighborhood"}
                  value={user?.role === 'CLIENT' ? formData.person?.neighborhood : formData.neighborhood}
                  onChange={handleChange}
                  placeholder="Seu bairro"
                />
              </ModuleFormGroup>

              {/* Cidade e Estado */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModuleFormGroup
                  moduleColor="admin"
                  label="Cidade"
                  htmlFor={user?.role === 'CLIENT' ? "person.city" : "city"}
                >
                  <ModuleInput
                    moduleColor="admin"
                    id={user?.role === 'CLIENT' ? "person.city" : "city"}
                    name={user?.role === 'CLIENT' ? "person.city" : "city"}
                    value={user?.role === 'CLIENT' ? formData.person?.city : formData.city}
                    onChange={handleChange}
                    placeholder="Sua cidade"
                  />
                </ModuleFormGroup>

                <ModuleFormGroup
                  moduleColor="admin"
                  label="Estado"
                  htmlFor={user?.role === 'CLIENT' ? "person.state" : "state"}
                >
                  <ModuleInput
                    moduleColor="admin"
                    id={user?.role === 'CLIENT' ? "person.state" : "state"}
                    name={user?.role === 'CLIENT' ? "person.state" : "state"}
                    value={user?.role === 'CLIENT' ? formData.person?.state : formData.state}
                    onChange={handleChange}
                    placeholder="Seu estado"
                  />
                </ModuleFormGroup>
              </div>

              {/* CEP */}
              <ModuleFormGroup
                moduleColor="admin"
                label="CEP"
                htmlFor={user?.role === 'CLIENT' ? "person.postalCode" : "postalCode"}
              >
                <ModuleInput
                  moduleColor="admin"
                  id={user?.role === 'CLIENT' ? "person.postalCode" : "postalCode"}
                  name={user?.role === 'CLIENT' ? "person.postalCode" : "postalCode"}
                  value={user?.role === 'CLIENT' ? formData.person?.postalCode : formData.postalCode}
                  onChange={handleChange}
                  placeholder="00000-000"
                />
              </ModuleFormGroup>

              {/* Botão de salvar */}
              <div className="relative mt-10 pt-6 border-t border-slate-200 dark:border-slate-700">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-slate-500 dark:text-slate-400">
                    Última atualização: {user?.updatedAt ? new Date(user.updatedAt).toLocaleString() : 'Nunca'}
                  </p>
                  <ModalButton
                    moduleColor="admin"
                    type="submit"
                    disabled={isSaving}
                    className="px-6 py-2.5 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 dark:from-slate-500 dark:to-slate-600 dark:hover:from-slate-600 dark:hover:to-slate-700 text-white shadow-md hover:shadow-lg transition-all duration-200"
                  >
                    {isSaving ? (
                      <>
                        <Loader2 size={16} className="animate-spin" />
                        <span>Salvando...</span>
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                        <span>Salvar Alterações</span>
                      </>
                    )}
                  </ModalButton>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      ) : (
        <div className="space-y-6">
          <ModulePreferences />
          <UserNotificationPreferences />
        </div>
      )}
    </div>
  );
};

export default ProfilePage;
