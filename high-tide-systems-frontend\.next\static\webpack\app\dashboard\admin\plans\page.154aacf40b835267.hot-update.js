"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _utils_pricing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/pricing */ \"(app-pages-browser)/./src/utils/pricing.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-arrow-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,ArrowUpCircle,Ban,Building,Calendar,CheckCircle,ChevronRight,CreditCard,Crown,DollarSign,Filter,Lock,Minus,Package,Plus,RefreshCw,Settings,Shield,Users,X,XCircle,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/modules/admin/services/plansService */ \"(app-pages-browser)/./src/app/modules/admin/services/plansService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/ModuleModal */ \"(app-pages-browser)/./src/components/ui/ModuleModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _planData_subscription, _planData_subscription1;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    console.log('[PlansPage] Inicializando página');\n    console.log('[PlansPage] Usuário atual:', user);\n    console.log('[PlansPage] Permissões:', user === null || user === void 0 ? void 0 : user.permissions);\n    console.log('[PlansPage] Módulos:', user === null || user === void 0 ? void 0 : user.modules);\n    console.log('[PlansPage] Role:', user === null || user === void 0 ? void 0 : user.role);\n    console.log('[PlansPage] CompanyId:', user === null || user === void 0 ? void 0 : user.companyId);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentCompany, setCurrentCompany] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // Adicionar após a declaração de outros estados\n    const [showUpgradeModal, setShowUpgradeModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPlanId, setSelectedPlanId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isAnnual, setIsAnnual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userCount, setUserCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(5);\n    const basePrice = 19.90; // Preço base por usuário\n    // Função para calcular desconto baseado na quantidade de usuários\n    const getDiscountByUserCount = (users)=>{\n        if (users >= 200) return 40;\n        if (users >= 100) return 35;\n        if (users >= 50) return 25;\n        if (users >= 20) return 15;\n        if (users >= 5) return 10;\n        return 0;\n    };\n    // Função para calcular preços\n    const calculatePrice = (users)=>{\n        const discount = getDiscountByUserCount(users);\n        const totalWithoutDiscount = users * basePrice;\n        const discountAmount = totalWithoutDiscount * (discount / 100);\n        const finalPrice = totalWithoutDiscount - discountAmount;\n        // Desconto adicional de 10% para pagamento anual à vista\n        const annualDiscount = 0.10;\n        const yearlyPriceWithDiscount = finalPrice * 12 * (1 - annualDiscount);\n        return {\n            totalWithoutDiscount,\n            discountAmount,\n            finalPrice,\n            discount,\n            monthlyPrice: finalPrice,\n            yearlyPrice: finalPrice * 12,\n            yearlyPriceWithDiscount,\n            annualDiscount: annualDiscount * 100\n        };\n    };\n    const pricing = calculatePrice(userCount);\n    // Opções de usuários predefinidas\n    const userOptions = [\n        1,\n        5,\n        20,\n        50,\n        100,\n        200\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadData = {\n                \"PlansPage.useEffect.loadData\": async ()=>{\n                    try {\n                        if (isSystemAdmin) {\n                            // Carregar lista de empresas para admin do sistema\n                            const companiesData = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n                            setCompanies(companiesData);\n                            // Se houver empresas, selecionar a primeira por padrão\n                            if (companiesData.length > 0 && !selectedCompanyId) {\n                                setSelectedCompanyId(companiesData[0].id);\n                            }\n                        } else if (user === null || user === void 0 ? void 0 : user.companyId) {\n                            // Para usuários não-admin, usar a empresa atual\n                            setSelectedCompanyId(user.companyId);\n                            try {\n                                const company = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCurrentCompany();\n                                setCurrentCompany(company);\n                            } catch (error) {\n                                console.error('[PlansPage] Erro ao carregar empresa atual:', error);\n                            }\n                        }\n                    } catch (error) {\n                        console.error('[PlansPage] Erro ao carregar dados iniciais:', error);\n                        toast_error('Erro ao carregar dados iniciais');\n                    }\n                }\n            }[\"PlansPage.useEffect.loadData\"];\n            loadData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        user,\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const loadPlanData = {\n                \"PlansPage.useEffect.loadPlanData\": async ()=>{\n                    if (!selectedCompanyId) return;\n                    setIsLoading(true);\n                    try {\n                        console.log('[PlansPage] Carregando dados do plano para companyId:', selectedCompanyId);\n                        const [plansData, subscriptionData] = await Promise.all([\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans(),\n                            _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(selectedCompanyId)\n                        ]);\n                        console.log('[PlansPage] Dados dos planos carregados:', plansData);\n                        console.log('[PlansPage] Dados da assinatura carregados:', subscriptionData);\n                        setPlans(plansData);\n                    } catch (error) {\n                        var _error_response;\n                        console.error('[PlansPage] Erro ao carregar dados dos planos:', error);\n                        console.error('[PlansPage] Detalhes do erro:', (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n                        toast_error('Erro ao carregar dados dos planos');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"PlansPage.useEffect.loadPlanData\"];\n            loadPlanData();\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const [planData, setPlanData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [availablePlans, setAvailablePlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para o modal de adicionar usuários\n    const [showAddUsersModal, setShowAddUsersModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [additionalUsersCount, setAdditionalUsersCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [couponCode, setCouponCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [couponValidation, setCouponValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isValidatingCoupon, setIsValidatingCoupon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cancelConfirmationText, setCancelConfirmationText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estados para o modal de confirmação de módulos\n    const [showModuleConfirmModal, setShowModuleConfirmModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [moduleAction, setModuleAction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null); // 'add' ou 'remove'\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Verificar se o usuário atual é um system_admin\n    console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_8__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n            // Se não há empresa selecionada e há empresas disponíveis, selecionar a primeira\n            if (!selectedCompanyId && response.length > 0) {\n                setSelectedCompanyId(response[0].id);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível carregar as empresas.\"\n            });\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Função para carregar dados do plano\n    const loadPlanData = async function() {\n        let forceRefresh = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        console.log('[DEBUG] ===== INICIANDO loadPlanData =====');\n        console.log('[DEBUG] forceRefresh:', forceRefresh);\n        console.log('[DEBUG] isSystemAdmin:', isSystemAdmin);\n        console.log('[DEBUG] selectedCompanyId:', selectedCompanyId);\n        console.log('[DEBUG] currentUser.companyId:', user === null || user === void 0 ? void 0 : user.companyId);\n        console.log('[DEBUG] Tem permissão admin.dashboard.view?', can('admin.dashboard.view'));\n        // Para system_admin, não carregar se não tiver empresa selecionada\n        if (isSystemAdmin && !selectedCompanyId) {\n            console.log('[DEBUG] System admin sem empresa selecionada, não carregando dados');\n            setIsLoading(false);\n            return;\n        }\n        setIsLoading(true);\n        try {\n            var _planResponse_modules, _planResponse_modules1, _planData_modules;\n            const companyId = isSystemAdmin ? selectedCompanyId : user === null || user === void 0 ? void 0 : user.companyId;\n            console.log('[DEBUG] Carregando dados do plano para empresa:', companyId, 'forceRefresh:', forceRefresh);\n            if (!companyId) {\n                console.error('[DEBUG] Nenhum companyId disponível para carregar dados do plano');\n                throw new Error('ID da empresa não disponível');\n            }\n            console.log('[DEBUG] Fazendo chamadas para API...');\n            const [planResponse, availablePlansResponse] = await Promise.all([\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getPlansData(companyId, forceRefresh),\n                _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.getAvailablePlans()\n            ]);\n            console.log('[DEBUG] ===== RESPOSTA RECEBIDA =====');\n            console.log('[DEBUG] planResponse completo:', JSON.stringify(planResponse, null, 2));\n            console.log('[DEBUG] availablePlansResponse completo:', JSON.stringify(availablePlansResponse, null, 2));\n            console.log('[DEBUG] Módulos ativos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules = planResponse.modules) === null || _planResponse_modules === void 0 ? void 0 : _planResponse_modules.map((m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")));\n            console.log('[DEBUG] Quantidade de módulos:', planResponse === null || planResponse === void 0 ? void 0 : (_planResponse_modules1 = planResponse.modules) === null || _planResponse_modules1 === void 0 ? void 0 : _planResponse_modules1.length);\n            console.log('[DEBUG] availablePlans.modules:', availablePlansResponse === null || availablePlansResponse === void 0 ? void 0 : availablePlansResponse.modules);\n            console.log('[DEBUG] Estado anterior planData:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map((m)=>m.moduleType));\n            setPlanData(planResponse);\n            setAvailablePlans(availablePlansResponse);\n            console.log('[DEBUG] ===== ESTADO ATUALIZADO =====');\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2, _error_response_data, _error_response3;\n            console.error(\"[DEBUG] ===== ERRO AO CARREGAR DADOS =====\");\n            console.error(\"Erro ao carregar dados do plano:\", error);\n            console.error(\"Error details:\", (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data);\n            console.error(\"Error status:\", (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status);\n            console.error(\"Error headers:\", (_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.headers);\n            toast_error({\n                title: \"Erro\",\n                message: ((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data = _error_response3.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Não foi possível carregar os dados do plano.\"\n            });\n        } finally{\n            setIsLoading(false);\n            console.log('[DEBUG] ===== FIM loadPlanData =====');\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin) {\n                loadCompanies();\n            } else {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        isSystemAdmin\n    ]);\n    // Recarregar dados quando a empresa selecionada mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (isSystemAdmin && selectedCompanyId) {\n                loadPlanData();\n            } else if (!isSystemAdmin) {\n                loadPlanData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId,\n        isSystemAdmin\n    ]);\n    // Monitor planData changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            var _planData_modules;\n            console.log('[DEBUG] ===== PLANDATA MUDOU =====');\n            console.log('[DEBUG] planData:', planData);\n            console.log('[DEBUG] Módulos no estado:', planData === null || planData === void 0 ? void 0 : (_planData_modules = planData.modules) === null || _planData_modules === void 0 ? void 0 : _planData_modules.map({\n                \"PlansPage.useEffect\": (m)=>\"\".concat(m.moduleType, \" (\").concat(m.active ? 'ATIVO' : 'INATIVO', \")\")\n            }[\"PlansPage.useEffect\"]));\n            console.log('[DEBUG] ================================');\n        }\n    }[\"PlansPage.useEffect\"], [\n        planData\n    ]);\n    // Função para abrir modal de adicionar usuários\n    const handleOpenAddUsersModal = ()=>{\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n        setShowAddUsersModal(true);\n    };\n    // Função para fechar modal de adicionar usuários\n    const handleCloseAddUsersModal = ()=>{\n        setShowAddUsersModal(false);\n        setAdditionalUsersCount(1);\n        setCouponCode('');\n        setCouponValidation(null);\n        setIsValidatingCoupon(false);\n    };\n    // Função para abrir modal de cancelamento\n    const handleOpenCancelModal = ()=>{\n        setCancelConfirmationText('');\n        setShowCancelModal(true);\n    };\n    // Função para fechar modal de cancelamento\n    const handleCloseCancelModal = ()=>{\n        setShowCancelModal(false);\n        setCancelConfirmationText('');\n    };\n    // Função para calcular o custo adicional usando a função centralizada\n    const calculateAdditionalCost = ()=>{\n        if (!planData) return {\n            additionalCost: 0,\n            costPerAdditionalUser: 19.90\n        };\n        const currentUsers = planData.subscription.userLimit;\n        const isAnnual = planData.subscription.billingCycle === 'YEARLY';\n        return (0,_utils_pricing__WEBPACK_IMPORTED_MODULE_3__.calculateAdditionalUsersCost)(currentUsers, additionalUsersCount, isAnnual);\n    };\n    // Função para calcular preço por usuário atual\n    const calculatePricePerUser = ()=>{\n        if (!planData) return 19.90;\n        const currentPrice = planData.subscription.pricePerMonth;\n        const currentUsers = planData.subscription.userLimit;\n        if (currentUsers > 0) {\n            return currentPrice / currentUsers;\n        }\n        return 19.90;\n    };\n    // Função para validar cupom\n    const validateCoupon = async (code)=>{\n        if (!code || code.trim() === '') {\n            setCouponValidation(null);\n            return;\n        }\n        setIsValidatingCoupon(true);\n        try {\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.validateCoupon(code.trim());\n            setCouponValidation({\n                valid: true,\n                coupon: response.coupon,\n                message: \"Cupom v\\xe1lido! \".concat(response.coupon.type === 'PERCENT' ? \"\".concat(response.coupon.value, \"% de desconto\") : \"R$ \".concat(response.coupon.value.toFixed(2), \" de desconto\"))\n            });\n        } catch (error) {\n            var _error_response_data, _error_response;\n            setCouponValidation({\n                valid: false,\n                message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Cupom inválido'\n            });\n        } finally{\n            setIsValidatingCoupon(false);\n        }\n    };\n    // Debounce para validação de cupom\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"PlansPage.useEffect.timer\": ()=>{\n                    if (couponCode.trim()) {\n                        validateCoupon(couponCode);\n                    } else {\n                        setCouponValidation(null);\n                    }\n                }\n            }[\"PlansPage.useEffect.timer\"], 500);\n            return ({\n                \"PlansPage.useEffect\": ()=>clearTimeout(timer)\n            })[\"PlansPage.useEffect\"];\n        }\n    }[\"PlansPage.useEffect\"], [\n        couponCode\n    ]);\n    // Função para adicionar usuários (confirmada pelo modal)\n    const handleAddUsers = async ()=>{\n        try {\n            setIsUpdating(true);\n            const newUserCount = planData.usage.currentUsers + additionalUsersCount;\n            // Validar cupom antes de prosseguir se foi informado\n            if (couponCode.trim() && (!couponValidation || !couponValidation.valid)) {\n                toast_error('Aguarde a validação do cupom ou remova-o para continuar');\n                return;\n            }\n            // Criar uma nova sessão de checkout do Stripe\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(planData.subscription.billingCycle.toLowerCase(), newUserCount, couponCode.trim() || null);\n            // Redirecionar para a página de checkout do Stripe\n            window.location.href = response.url;\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao atualizar usuários:', error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao atualizar usuários');\n        } finally{\n            setIsUpdating(false);\n            handleCloseAddUsersModal();\n        }\n    };\n    // Função para abrir modal de confirmação para adicionar módulo\n    const openAddModuleConfirmation = (moduleType)=>{\n        console.log('[DEBUG] openAddModuleConfirmation:', {\n            moduleType,\n            availablePlans\n        });\n        console.log('[DEBUG] availablePlans.modules:', availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules);\n        if (!(availablePlans === null || availablePlans === void 0 ? void 0 : availablePlans.modules)) {\n            console.error('[DEBUG] availablePlans.modules não está disponível');\n            toast_error({\n                title: \"Erro\",\n                message: \"Dados dos módulos não estão disponíveis. Tente recarregar a página.\"\n            });\n            return;\n        }\n        const moduleInfo = availablePlans.modules[moduleType];\n        console.log('[DEBUG] moduleInfo encontrado:', moduleInfo);\n        setSelectedModule({\n            type: moduleType,\n            info: moduleInfo\n        });\n        setModuleAction('add');\n        setShowModuleConfirmModal(true);\n    };\n    // Função para fechar modal de confirmação de módulos\n    const closeModuleConfirmModal = ()=>{\n        setShowModuleConfirmModal(false);\n        setModuleAction(null);\n        setSelectedModule(null);\n    };\n    // Função para confirmar a ação do módulo\n    const confirmModuleAction = async ()=>{\n        if (!selectedModule || !moduleAction) return;\n        closeModuleConfirmModal();\n        if (moduleAction === 'add') {\n            await handleAddModule(selectedModule.type);\n        } else if (moduleAction === 'remove') {\n            await handleRemoveModule(selectedModule.type);\n        }\n    };\n    // Função para adicionar módulo\n    const handleAddModule = async (moduleType)=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.addModule(moduleType, companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo adicionado ao plano com sucesso.\"\n            });\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            await loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao adicionar módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível adicionar o módulo ao plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para remover módulo\n    const handleRemoveModule = async (moduleType)=>{\n        console.log('[DEBUG] Iniciando remoção do módulo:', moduleType);\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            console.log('[DEBUG] Removendo módulo para empresa:', companyId);\n            const result = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.removeModule(moduleType, companyId);\n            console.log('[DEBUG] Resultado da remoção:', result);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Módulo removido do plano com sucesso.\"\n            });\n            console.log('[DEBUG] Aguardando invalidação de cache...');\n            // Aguardar um pouco para garantir que o cache foi invalidado\n            await new Promise((resolve)=>setTimeout(resolve, 500));\n            console.log('[DEBUG] Recarregando dados do plano...');\n            await loadPlanData(true); // Force refresh para evitar cache\n            console.log('[DEBUG] Dados recarregados');\n        } catch (error) {\n            console.error(\"Erro ao remover módulo:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível remover o módulo do plano.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para cancelar assinatura (confirmada pelo modal)\n    const handleCancelSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.cancelSubscription(companyId);\n            toast_success({\n                title: \"Assinatura Cancelada\",\n                message: \"Sua assinatura foi cancelada com sucesso. O acesso será mantido até o final do período pago.\"\n            });\n            handleCloseCancelModal();\n            loadPlanData(true); // Force refresh\n        } catch (error) {\n            console.error(\"Erro ao cancelar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível cancelar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Função para reativar assinatura\n    const handleReactivateSubscription = async ()=>{\n        setIsUpdating(true);\n        try {\n            const companyId = isSystemAdmin ? selectedCompanyId : null;\n            await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.reactivateSubscription(companyId);\n            toast_success({\n                title: \"Sucesso\",\n                message: \"Assinatura reativada com sucesso.\"\n            });\n            loadPlanData();\n        } catch (error) {\n            console.error(\"Erro ao reativar assinatura:\", error);\n            toast_error({\n                title: \"Erro\",\n                message: \"Não foi possível reativar a assinatura.\"\n            });\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    // Adicionar a função handleUpgrade após a função handleCancelSubscription\n    const handleUpgrade = async ()=>{\n        try {\n            const billingCycle = isAnnual ? 'yearly' : 'monthly';\n            const response = await _app_modules_admin_services_plansService__WEBPACK_IMPORTED_MODULE_7__.plansService.createCheckoutSession(billingCycle, userCount);\n            if (response.url) {\n                window.location.href = response.url;\n            } else {\n                throw new Error('URL de checkout não encontrada');\n            }\n        } catch (error) {\n            console.error('Erro ao iniciar checkout:', error);\n            toast_error('Erro ao iniciar processo de upgrade. Tente novamente.');\n        }\n    };\n    // Função para formatar status\n    const getStatusInfo = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return {\n                    label: 'Ativo',\n                    color: 'text-green-600 dark:text-green-400',\n                    bgColor: 'bg-green-100 dark:bg-green-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                };\n            case 'CANCELED':\n                return {\n                    label: 'Cancelado',\n                    color: 'text-red-600 dark:text-red-400',\n                    bgColor: 'bg-red-100 dark:bg-red-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                };\n            case 'PAST_DUE':\n                return {\n                    label: 'Em Atraso',\n                    color: 'text-yellow-600 dark:text-yellow-400',\n                    bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n            default:\n                return {\n                    label: status,\n                    color: 'text-gray-600 dark:text-gray-400',\n                    bgColor: 'bg-gray-100 dark:bg-gray-900/30',\n                    icon: _barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n                };\n        }\n    };\n    // Função para formatar ciclo de cobrança\n    const getBillingCycleLabel = (cycle)=>{\n        switch(cycle){\n            case 'MONTHLY':\n                return 'Mensal';\n            case 'YEARLY':\n                return 'Anual';\n            default:\n                return cycle;\n        }\n    };\n    // Após carregar planData (ou subscriptionData)\n    const isTrial = (planData === null || planData === void 0 ? void 0 : (_planData_subscription = planData.subscription) === null || _planData_subscription === void 0 ? void 0 : _planData_subscription.status) === 'TRIAL';\n    const trialEndDate = (planData === null || planData === void 0 ? void 0 : (_planData_subscription1 = planData.subscription) === null || _planData_subscription1 === void 0 ? void 0 : _planData_subscription1.endDate) ? new Date(planData.subscription.endDate).toLocaleDateString('pt-BR') : 'N/A';\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"animate-spin h-8 w-8 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 639,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2 text-gray-600 dark:text-gray-400\",\n                    children: \"Carregando dados do plano...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 640,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 638,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Mostrar mensagem para system_admin quando nenhuma empresa está selecionada\n    if (isSystemAdmin && !selectedCompanyId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 651,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie planos, usu\\xe1rios e m\\xf3dulos das assinaturas das empresas.\",\n                    moduleColor: \"admin\",\n                    filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 663,\n                                    columnNumber: 17\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 665,\n                                        columnNumber: 19\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 656,\n                            columnNumber: 15\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 655,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 649,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Selecione uma empresa\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 676,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"Escolha uma empresa no seletor acima para visualizar e gerenciar seu plano.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 679,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 674,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 648,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!planData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 22,\n                        className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 692,\n                        columnNumber: 17\n                    }, void 0),\n                    description: \"Gerencie seu plano, usu\\xe1rios e m\\xf3dulos da assinatura.\",\n                    moduleColor: \"admin\",\n                    filters: isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full sm:w-64\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                            moduleColor: \"admin\",\n                            value: selectedCompanyId,\n                            onChange: (e)=>setSelectedCompanyId(e.target.value),\n                            placeholder: \"Selecione uma empresa\",\n                            disabled: isLoadingCompanies,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    children: \"Selecione uma empresa\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 19\n                                }, void 0),\n                                companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: company.id,\n                                        children: company.name\n                                    }, company.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 707,\n                                        columnNumber: 21\n                                    }, void 0))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 698,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 697,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            className: \"mx-auto h-12 w-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"mt-2 text-sm font-medium text-gray-900 dark:text-gray-100\",\n                            children: \"Nenhum plano encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                            children: \"N\\xe3o foi poss\\xedvel encontrar informa\\xe7\\xf5es do plano para esta empresa.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 722,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 717,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 689,\n            columnNumber: 7\n        }, undefined);\n    }\n    const statusInfo = getStatusInfo(planData.subscription.status);\n    const StatusIcon = statusInfo.icon;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Gerenciamento de Planos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 742,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: isSystemAdmin ? \"Gerencie o plano, usu\\xe1rios e m\\xf3dulos da assinatura de \".concat(planData.company.name) : \"Gerencie seu plano, usuários e módulos da assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 741,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 737,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 736,\n                columnNumber: 7\n            }, undefined),\n            isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 759,\n                    columnNumber: 17\n                }, void 0),\n                description: \"Selecione uma empresa para visualizar e gerenciar seu plano.\",\n                moduleColor: \"admin\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full sm:w-64\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_9__.ModuleSelect, {\n                        moduleColor: \"admin\",\n                        value: selectedCompanyId,\n                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                        placeholder: \"Selecione uma empresa\",\n                        disabled: isLoadingCompanies,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: \"\",\n                                children: \"Selecione uma empresa\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 771,\n                                columnNumber: 17\n                            }, void 0),\n                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: company.id,\n                                    children: company.name\n                                }, company.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 773,\n                                    columnNumber: 19\n                                }, void 0))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 764,\n                        columnNumber: 15\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 763,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 757,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-yellow-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 789,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Plano Atual\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(statusInfo.bgColor, \" \").concat(statusInfo.color),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 793,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            statusInfo.label\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 792,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 787,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Empresa\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.company.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 800,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Ciclo de Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: getBillingCycleLabel(planData.subscription.billingCycle)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 808,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 799,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pre\\xe7o Mensal Total\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            planData.subscription.pricePerMonth.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 815,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3xima Cobran\\xe7a\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-medium text-gray-900 dark:text-gray-100\",\n                                                        children: planData.subscription.nextBillingDate ? new Date(planData.subscription.nextBillingDate).toLocaleDateString('pt-BR') : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 823,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 821,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 814,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 dark:bg-gray-900 rounded-lg p-4 space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Detalhamento dos Valores\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 834,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Usu\\xe1rios ativos:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 841,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                children: [\n                                                                    planData.usage.currentUsers,\n                                                                    \" de \",\n                                                                    planData.usage.userLimit\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 840,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                                children: \"Pre\\xe7o base (R$ 19,90/usu\\xe1rio):\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (planData.usage.userLimit * 19.90).toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 849,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 847,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    (()=>{\n                                                        const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                                        const discountAmount = planData.usage.userLimit * 19.90 * (currentDiscount / 100);\n                                                        const priceAfterDiscount = planData.usage.userLimit * 19.90 - discountAmount;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                currentDiscount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Desconto por volume (\",\n                                                                                currentDiscount,\n                                                                                \"%):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 863,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                discountAmount.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 862,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: \"Desconto anual (10%):\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 874,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: [\n                                                                                \"-R$ \",\n                                                                                (priceAfterDiscount * 0.10).toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 875,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 873,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                planData.subscription.appliedCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-600 dark:text-gray-400\",\n                                                                            children: [\n                                                                                \"Cupom aplicado (\",\n                                                                                planData.subscription.appliedCoupon.code,\n                                                                                \"):\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 883,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                                            children: planData.subscription.appliedCoupon.type === 'PERCENT' ? \"-\".concat(planData.subscription.appliedCoupon.value, \"%\") : \"-R$ \".concat(planData.subscription.appliedCoupon.value.toFixed(2))\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 886,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 882,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"border-t border-gray-200 dark:border-gray-700 pt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: \"Pre\\xe7o por usu\\xe1rio:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 897,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                                                children: [\n                                                                                    \"R$ \",\n                                                                                    (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                lineNumber: 898,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                        lineNumber: 896,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 895,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                planData.subscription.billingCycle === 'YEARLY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 mt-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center text-blue-700 dark:text-blue-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 907,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: \"Plano Anual Ativo\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                                    lineNumber: 908,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 906,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                            children: [\n                                                                                \"Voc\\xea economiza R$ \",\n                                                                                (priceAfterDiscount * 12 - planData.subscription.pricePerMonth * 12).toFixed(2),\n                                                                                \" por ano\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                            lineNumber: 910,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                    lineNumber: 905,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 839,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 833,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            planData.subscription.status === 'ACTIVE' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleOpenCancelModal,\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 929,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Cancelar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 924,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'CANCELED' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 938,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Reativar Plano\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 933,\n                                                columnNumber: 17\n                                            }, undefined) : planData.subscription.status === 'TRIAL' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowUpgradeModal(true),\n                                                disabled: isUpdating,\n                                                className: \"flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white text-sm font-medium rounded-md transition-colors disabled:opacity-50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 947,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Fazer Upgrade\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 942,\n                                                columnNumber: 17\n                                            }, undefined) : null,\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    const url = isSystemAdmin && selectedCompanyId ? \"/subscription/invoices?companyId=\".concat(selectedCompanyId) : '/subscription/invoices';\n                                                    router.push(url);\n                                                },\n                                                className: \"flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-md transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"mr-1 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    \"Ver Faturas\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 952,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 786,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 972,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Uso de Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 971,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleOpenAddUsersModal,\n                                        disabled: isUpdating || planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED',\n                                        className: \"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors \".concat(planData.subscription.status === 'TRIAL' || planData.subscription.status === 'CANCELED' ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 984,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            \"Adicionar Usu\\xe1rios\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 970,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Usu\\xe1rios Ativos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.currentUsers\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 991,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                        children: \"Limite de Usu\\xe1rios\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 998,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-2xl font-bold text-gray-900 dark:text-gray-100\",\n                                                        children: planData.usage.userLimit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 999,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 997,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 990,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-2.5 rounded-full transition-all duration-500\",\n                                            style: {\n                                                width: \"\".concat(planData.usage.userLimitUsage, \"%\"),\n                                                backgroundColor: planData.usage.userLimitUsage >= 90 ? '#EF4444' // Vermelho para uso >= 90%\n                                                 : planData.usage.userLimitUsage >= 75 ? '#F59E0B' // Amarelo para uso >= 75%\n                                                 : '#3B82F6' // Azul para uso < 75%\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1007,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1006,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.currentUsers,\n                                                    \" de \",\n                                                    planData.usage.userLimit,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1021,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 dark:text-gray-400\",\n                                                children: [\n                                                    planData.usage.userLimitUsage,\n                                                    \"% utilizado\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1024,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1020,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    planData.usage.userLimitUsage >= 90 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 p-2 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"inline-block mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1031,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Voc\\xea est\\xe1 pr\\xf3ximo do limite de usu\\xe1rios. Considere adicionar mais usu\\xe1rios ao seu plano.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1030,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 989,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 969,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 784,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100 flex items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                className: \"mr-2 h-5 w-5 text-purple-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1042,\n                                columnNumber: 11\n                            }, undefined),\n                            \"M\\xf3dulos da Assinatura\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1041,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: [\n                            planData.modules.map((module)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-5 w-5 text-green-500 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1052,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-gray-100\",\n                                                            children: getModuleName(module.moduleType)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1053,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1051,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-green-600 dark:text-green-400 font-medium\",\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-2\",\n                                            children: [\n                                                \"R$ \",\n                                                module.pricePerMonth.toFixed(2),\n                                                \"/m\\xeas\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1061,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                            children: [\n                                                \"Adicionado em \",\n                                                new Date(module.addedAt).toLocaleDateString('pt-BR')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1064,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, module.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1049,\n                                    columnNumber: 13\n                                }, undefined)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-blue-200 dark:border-blue-700 rounded-lg p-4 bg-blue-50 dark:bg-blue-900/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-5 w-5 text-blue-600 dark:text-blue-400 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1073,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-blue-900 dark:text-blue-100\",\n                                                children: \"Plano B\\xe1sico Completo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1074,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1072,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-800 dark:text-blue-200 mb-2\",\n                                        children: \"Seu plano j\\xe1 inclui todos os m\\xf3dulos essenciais para o funcionamento completo do sistema.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1078,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"text-sm text-blue-700 dark:text-blue-300 space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Administra\\xe7\\xe3o completa\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1082,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Sistema de agendamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Gerenciamento de pessoas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1084,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Relat\\xf3rios e dashboards\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1085,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Suporte t\\xe9cnico inclu\\xeddo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1086,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1046,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1040,\n                columnNumber: 7\n            }, undefined),\n            isTrial && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"w-6 h-6 text-yellow-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1095,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Voc\\xea est\\xe1 em per\\xedodo de avalia\\xe7\\xe3o (trial).\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1097,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"Aproveite para testar todos os recursos! O acesso ser\\xe1 limitado ap\\xf3s o t\\xe9rmino do trial.\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1099,\n                                        columnNumber: 103\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Data de t\\xe9rmino do trial:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" \",\n                                    trialEndDate,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1100,\n                                        columnNumber: 72\n                                    }, undefined),\n                                    \"Caso queira migrar para um plano completo, clique no bot\\xe3o abaixo.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1098,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowUpgradeModal(true),\n                                className: \"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\",\n                                children: \"Fazer Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 1103,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 1096,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1094,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showUpgradeModal,\n                onClose: ()=>setShowUpgradeModal(false),\n                title: \"Quantos usu\\xe1rios voc\\xea precisa?\",\n                size: \"lg\",\n                moduleColor: \"admin\",\n                footer: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row items-center justify-center gap-3 px-4 py-3 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 rounded-b-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowUpgradeModal(false),\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: \"Cancelar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1122,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleUpgrade,\n                            className: \"inline-flex items-center justify-center px-7 py-2 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                            children: [\n                                \"Confirmar Upgrade\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                    className: \"ml-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1133,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1128,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1121,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col gap-4 px-2 py-2 md:px-4 md:py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300 text-base md:text-lg mt-2 mb-2\",\n                                    children: \"Selecione a quantidade de usu\\xe1rios para ver o pre\\xe7o personalizado\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mt-1 mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-1 \".concat(!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Mensal\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1145,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsAnnual(!isAnnual),\n                                            className: \"relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 \".concat(isAnnual ? 'bg-orange-600' : 'bg-gray-300 dark:bg-gray-600'),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-block h-4 w-4 transform rounded-full bg-white transition \".concat(isAnnual ? 'translate-x-9' : 'translate-x-1')\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1150,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1146,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1 \".concat(isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'),\n                                            children: \"Anual\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1144,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1139,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center gap-2 mb-1\",\n                                    children: userOptions.map((option)=>{\n                                        const discount = getDiscountByUserCount(option);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUserCount(option),\n                                            className: \"relative p-5 min-w-[130px] min-h-[70px] rounded-xl border-2 transition-all text-center flex flex-col items-center justify-center gap-1 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 text-lg font-semibold \".concat(userCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600 bg-white dark:bg-gray-900 text-gray-900 dark:text-white'),\n                                            children: [\n                                                discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow\",\n                                                    children: [\n                                                        \"-\",\n                                                        discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: option\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1177,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400 font-normal\",\n                                                    children: option === 1 ? 'usuário' : 'usuários'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1178,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, option, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1163,\n                                            columnNumber: 19\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1159,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-1 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-600 dark:text-gray-300 mb-0.5\",\n                                            children: \"Ou digite uma quantidade personalizada:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1185,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-center w-32 md:w-44\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1188,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        min: \"1\",\n                                                        max: \"1000\",\n                                                        value: userCount,\n                                                        onChange: (e)=>setUserCount(Math.max(1, parseInt(e.target.value) || 1)),\n                                                        className: \"pl-10 pr-2 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 w-full text-center text-base\",\n                                                        placeholder: \"1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1189,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1187,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1184,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1158,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col md:flex-row md:items-center md:justify-between gap-3 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mt-1 border border-gray-200 dark:border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Usu\\xe1rios:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1206,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: userCount\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1207,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: [\n                                                        \"Pre\\xe7o por usu\\xe1rio \",\n                                                        isAnnual ? 'anual à vista' : 'mensal',\n                                                        \":\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: pricing.discount > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 line-through\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1214,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"ml-2 font-medium text-gray-900 dark:text-white\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    (isAnnual ? basePrice * (1 - pricing.discount / 100) * 12 * (1 - pricing.annualDiscount / 100) : basePrice * (1 - pricing.discount / 100)).toFixed(2).replace('.', ',')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 1215,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: [\n                                                            \"R$ \",\n                                                            (isAnnual ? basePrice * 12 * (1 - pricing.annualDiscount / 100) : basePrice).toFixed(2).replace('.', ',')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 1218,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1211,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1209,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        pricing.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto por quantidade:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1224,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.discount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1223,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Valor mensal sem desconto:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1229,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400 line-through\",\n                                                    children: [\n                                                        \"R$ \",\n                                                        pricing.totalWithoutDiscount.toFixed(2).replace('.', ',')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1228,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        isAnnual && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Desconto anual \\xe0 vista:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1234,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600 dark:text-green-400\",\n                                                    children: [\n                                                        \"-\",\n                                                        pricing.annualDiscount,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1233,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1204,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-end justify-center min-w-[180px]\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-1\",\n                                            children: \"Valor mensal:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1240,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-3xl font-bold text-orange-600 dark:text-orange-400\",\n                                            children: [\n                                                \"R$ \",\n                                                isAnnual ? (pricing.yearlyPriceWithDiscount / 12).toFixed(2).replace('.', ',') : pricing.monthlyPrice.toFixed(2).replace('.', ',')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1241,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1138,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1114,\n                columnNumber: 7\n            }, undefined),\n            showAddUsersModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-gray-900 p-8 rounded-xl shadow-lg max-w-4xl w-full border border-gray-200 dark:border-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white mb-4\",\n                                    children: \"Gerenciar limite de usu\\xe1rios\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1252,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-300\",\n                                    children: \"Ajuste o limite de usu\\xe1rios do seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1255,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1251,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-8\",\n                            children: [\n                                1,\n                                5,\n                                10,\n                                20\n                            ].map((option)=>{\n                                const newLimit = planData.usage.userLimit + option;\n                                const discount = getDiscountByUserCount(newLimit);\n                                const currentDiscount = getDiscountByUserCount(planData.usage.userLimit);\n                                const discountChange = discount - currentDiscount;\n                                const isValid = true;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setAdditionalUsersCount(option),\n                                    className: \"p-4 rounded-lg border-2 transition-all text-center relative \".concat(additionalUsersCount === option ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-400' : 'border-gray-200 dark:border-gray-700 hover:border-orange-300 dark:hover:border-orange-600'),\n                                    children: [\n                                        discountChange > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -top-2 -right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\",\n                                            children: [\n                                                \"+\",\n                                                discountChange,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1279,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-gray-900 dark:text-white\",\n                                            children: [\n                                                \"+\",\n                                                option\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1283,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: option === 1 ? 'usuário' : 'usuários'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1286,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, option, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1269,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1260,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                                    children: \"Cupom de desconto (opcional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1296,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: couponCode,\n                                            onChange: (e)=>setCouponCode(e.target.value.toUpperCase()),\n                                            placeholder: \"Digite o c\\xf3digo do cupom\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700 pr-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1300,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isValidatingCoupon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"animate-spin h-4 w-4 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1309,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1308,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1299,\n                                    columnNumber: 15\n                                }, undefined),\n                                couponValidation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 text-sm \".concat(couponValidation.valid ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'),\n                                    children: couponValidation.valid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1317,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1316,\n                                        columnNumber: 21\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"mr-1 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 1322,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            couponValidation.message\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 1321,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1314,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1295,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Limite atual:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1334,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1335,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1333,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Altera\\xe7\\xe3o:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1338,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: [\n                                                                \"+\",\n                                                                additionalUsersCount\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1339,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo limite:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: planData.usage.userLimit + additionalUsersCount\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1345,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Cupom aplicado:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1349,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.code\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1350,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1332,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Pre\\xe7o atual por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1359,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                (planData.subscription.pricePerMonth / planData.usage.userLimit).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1360,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1358,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Novo pre\\xe7o por usu\\xe1rio:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                                            children: [\n                                                                \"R$ \",\n                                                                calculateAdditionalCost().costPerAdditionalUser.toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1366,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1364,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Diferen\\xe7a mensal:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1371,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium \".concat(calculateAdditionalCost().additionalCost < 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'),\n                                                            children: [\n                                                                calculateAdditionalCost().additionalCost > 0 ? '+' : '',\n                                                                \"R$ \",\n                                                                Math.abs(calculateAdditionalCost().additionalCost).toFixed(2)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1370,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                couponValidation && couponValidation.valid && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-center pt-2 border-t border-gray-200 dark:border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                            children: \"Desconto do cupom:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1378,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-600 dark:text-green-400\",\n                                                            children: couponValidation.coupon.type === 'PERCENT' ? \"\".concat(couponValidation.coupon.value, \"%\") : \"R$ \".concat(couponValidation.coupon.value.toFixed(2))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 1377,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1357,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1331,\n                                    columnNumber: 15\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser < planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1392,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"Voc\\xea receber\\xe1 um desconto adicional por ter mais usu\\xe1rios!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1391,\n                                    columnNumber: 17\n                                }, undefined),\n                                calculateAdditionalCost().costPerAdditionalUser > planData.subscription.pricePerMonth / planData.usage.userLimit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1399,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O pre\\xe7o por usu\\xe1rio aumentar\\xe1 devido \\xe0 redu\\xe7\\xe3o do desconto por volume.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1398,\n                                    columnNumber: 17\n                                }, undefined),\n                                planData.usage.currentUsers > planData.usage.userLimit + additionalUsersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400 text-sm rounded-md\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"inline-block mr-1 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1406,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"O novo limite n\\xe3o pode ser menor que a quantidade atual de usu\\xe1rios (\",\n                                        planData.usage.currentUsers,\n                                        \").\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1405,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1330,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 text-center flex justify-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleCloseAddUsersModal,\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1413,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleAddUsers,\n                                    disabled: isUpdating || additionalUsersCount <= 0 || isValidatingCoupon || couponCode.trim() && (!couponValidation || !couponValidation.valid),\n                                    className: \"inline-flex items-center justify-center px-8 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors disabled:opacity-50\",\n                                    children: [\n                                        isValidatingCoupon ? 'Validando cupom...' : 'Confirmar Alteração',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_ArrowUpCircle_Ban_Building_Calendar_CheckCircle_ChevronRight_CreditCard_Crown_DollarSign_Filter_Lock_Minus_Package_Plus_RefreshCw_Settings_Shield_Users_X_XCircle_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 1425,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 1419,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 1412,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 1250,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 1249,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 734,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"otwcSAx1ZVpjp+PdiSfJL+hqVP8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = PlansPage;\n// Função auxiliar para obter nome do módulo\nconst getModuleName = (moduleType)=>{\n    const moduleNames = {\n        'BASIC': 'Módulo Básico',\n        'ADMIN': 'Administração',\n        'SCHEDULING': 'Agendamento',\n        'PEOPLE': 'Pessoas',\n        'REPORTS': 'Relatórios',\n        'CHAT': 'Chat',\n        'ABAPLUS': 'ABA+'\n    };\n    return moduleNames[moduleType] || moduleType;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});