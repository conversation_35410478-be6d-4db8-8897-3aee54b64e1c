"use client";

import React from "react";

/**
 * Componente de tabs para módulos com suporte a cores específicas
 *
 * @param {Object} props
 * @param {Array} props.tabs - Array de objetos de tab com {id, label, icon}
 * @param {string} props.activeTab - ID da tab ativa
 * @param {Function} props.onTabChange - Função chamada quando uma tab é clicada
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, etc)
 */
const ModuleTabs = ({
  tabs,
  activeTab,
  onTabChange,
  moduleColor = "admin",
}) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    people: {
      active: {
        text: "text-orange-800 dark:text-orange-100",
        border: "border-orange-500 dark:border-orange-400",
        bg: "bg-orange-50 dark:bg-orange-900/30",
        icon: "text-orange-600 dark:text-orange-300"
      },
      inactive: {
        text: "text-neutral-600 dark:text-neutral-400",
        hover: "hover:text-orange-800 dark:hover:text-orange-200 hover:bg-orange-50 dark:hover:bg-orange-900/20"
      }
    },
    scheduler: {
      active: {
        text: "text-purple-800 dark:text-purple-100",
        border: "border-purple-500 dark:border-purple-400",
        bg: "bg-purple-50 dark:bg-purple-900/30",
        icon: "text-purple-600 dark:text-purple-300"
      },
      inactive: {
        text: "text-neutral-600 dark:text-neutral-400",
        hover: "hover:text-purple-800 dark:hover:text-purple-200 hover:bg-purple-50 dark:hover:bg-purple-900/20"
      }
    },
    admin: {
      active: {
        text: "text-gray-800 dark:text-gray-100",
        border: "border-gray-500 dark:border-gray-400",
        bg: "bg-gray-50 dark:bg-gray-700/30",
        icon: "text-gray-600 dark:text-gray-300"
      },
      inactive: {
        text: "text-gray-600 dark:text-gray-400",
        hover: "hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/20"
      }
    },
    financial: {
      active: {
        text: "text-green-800 dark:text-green-100",
        border: "border-green-500 dark:border-green-400",
        bg: "bg-green-50 dark:bg-green-900/30",
        icon: "text-green-600 dark:text-green-300"
      },
      inactive: {
        text: "text-neutral-600 dark:text-neutral-400",
        hover: "hover:text-green-800 dark:hover:text-green-200 hover:bg-green-50 dark:hover:bg-green-900/20"
      }
    }
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.admin;

  return (
    <div className="mt-0">
      <div className="flex border-b border-neutral-200 dark:border-gray-700 overflow-x-auto">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors relative ${
              activeTab === tab.id
                ? `border-b-2 ${colors.active.border} ${colors.active.text} ${colors.active.bg}`
                : `${colors.inactive.text} ${colors.inactive.hover}`
            }`}
            onClick={(e) => {
              // Prevenir a propagação do evento para evitar submissão de formulários
              e.preventDefault();
              e.stopPropagation();
              onTabChange(tab.id);
            }}
          >
            {tab.icon && (
              <span className={activeTab === tab.id ? colors.active.icon : ""}>
                {tab.icon}
              </span>
            )}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default ModuleTabs;
