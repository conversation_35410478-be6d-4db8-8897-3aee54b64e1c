{"timestamp":"2025-08-08 02:11:29","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Fri Aug 08 2025 02:11:29 GMT+0000 (Coordinated Universal Time)","process":{"pid":1022,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":146214912,"heapTotal":53358592,"heapUsed":51211848,"external":3458380,"arrayBuffers":249843}},"os":{"loadavg":[1.81,1.92,1.96],"uptime":34558},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":327,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-08 02:11:29","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Fri Aug 08 2025 02:11:29 GMT+0000 (Coordinated Universal Time)","process":{"pid":1022,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":146264064,"heapTotal":53358592,"heapUsed":51280504,"external":3458444,"arrayBuffers":249867}},"os":{"loadavg":[1.81,1.92,1.96],"uptime":34558.01},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":327,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-11 21:11:13","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Mon Aug 11 2025 21:11:13 GMT+0000 (Coordinated Universal Time)","process":{"pid":273,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":153055232,"heapTotal":54669312,"heapUsed":50989880,"external":3581440,"arrayBuffers":372903}},"os":{"loadavg":[2.58,2.24,2.25],"uptime":11458.89},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":327,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-11 21:11:13","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:327:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Mon Aug 11 2025 21:11:13 GMT+0000 (Coordinated Universal Time)","process":{"pid":273,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":153124864,"heapTotal":54669312,"heapUsed":51063824,"external":3581504,"arrayBuffers":372927}},"os":{"loadavg":[2.58,2.24,2.25],"uptime":11458.9},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":327,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-11 21:11:13","level":"error","message":"unhandledRejection: connect ECONNREFUSED **********:6379\nError: connect ECONNREFUSED **********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1607:16)","error":{"errno":-111,"code":"ECONNREFUSED","syscall":"connect","address":"**********","port":6379},"stack":"Error: connect ECONNREFUSED **********:6379\n    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1607:16)","rejection":true,"date":"Mon Aug 11 2025 21:11:13 GMT+0000 (Coordinated Universal Time)","process":{"pid":273,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":153255936,"heapTotal":54669312,"heapUsed":51145664,"external":3581504,"arrayBuffers":307391}},"os":{"loadavg":[2.58,2.24,2.25],"uptime":11458.9},"trace":[{"column":16,"file":"node:net","function":"TCPConnectWrap.afterConnect [as oncomplete]","line":1607,"method":"afterConnect [as oncomplete]","native":false}]}
{"timestamp":"2025-08-12 04:08:11","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Tue Aug 12 2025 04:08:11 GMT+0000 (Coordinated Universal Time)","process":{"pid":138,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":147640320,"heapTotal":47468544,"heapUsed":45276904,"external":3391397,"arrayBuffers":117218}},"os":{"loadavg":[2.37,2.2,2.15],"uptime":35660.42},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":331,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-20 15:44:39","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Wed Aug 20 2025 15:44:39 GMT+0000 (Coordinated Universal Time)","process":{"pid":313,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":150712320,"heapTotal":52973568,"heapUsed":47680656,"external":3460087,"arrayBuffers":251550}},"os":{"loadavg":[2.36,2.39,2.4],"uptime":7651.73},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":331,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
{"timestamp":"2025-08-20 16:04:01","level":"error","message":"unhandledRejection: The client is closed\nError: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","error":{},"stack":"Error: The client is closed\n    at RedisSocket.quit (/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js:70:19)\n    at Commander.QUIT (/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js:260:71)\n    at CacheService.close (/usr/src/app/src/services/cacheService.js:70:25)\n    at process.<anonymous> (/usr/src/app/src/server.js:331:22)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","rejection":true,"date":"Wed Aug 20 2025 16:04:01 GMT+0000 (Coordinated Universal Time)","process":{"pid":383,"uid":0,"gid":0,"cwd":"/usr/src/app","execPath":"/usr/local/bin/node","version":"v20.17.0","argv":["/usr/local/bin/node","/usr/src/app/src/server.js","src/server.js"],"memoryUsage":{"rss":138338304,"heapTotal":43536384,"heapUsed":41696792,"external":3373142,"arrayBuffers":101117}},"os":{"loadavg":[3.7,2.66,2.32],"uptime":8813.52},"trace":[{"column":19,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/socket.js","function":"RedisSocket.quit","line":70,"method":"quit","native":false},{"column":71,"file":"/usr/src/app/node_modules/@redis/client/dist/lib/client/index.js","function":"Commander.QUIT","line":260,"method":"QUIT","native":false},{"column":25,"file":"/usr/src/app/src/services/cacheService.js","function":"CacheService.close","line":70,"method":"close","native":false},{"column":22,"file":"/usr/src/app/src/server.js","function":null,"line":331,"method":null,"native":false},{"column":5,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":95,"method":"processTicksAndRejections","native":false}]}
