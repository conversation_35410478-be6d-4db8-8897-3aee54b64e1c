"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button.js";
import Input from "@/components/ui/Input";
import Label from "@/components/ui/Label";
import { ModuleTextarea, ModuleSelect } from "@/components/ui";
import { Tag, Building, Globe, AlertCircle, Info, CheckCircle2 } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { companyService } from "@/app/modules/admin/services/companyService";

const CategoryFormModal = ({ isOpen, onClose, category, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    companyId: "",
    isGlobal: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [loadingCompanies, setLoadingCompanies] = useState(false);
  const [errors, setErrors] = useState({});

  // Verificar se o usuário é system_admin
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  // Carregar empresas se for system_admin
  useEffect(() => {
    const loadCompanies = async () => {
      if (isSystemAdmin) {
        setLoadingCompanies(true);
        try {
          const companiesData = await companyService.getCompaniesForSelect();
          setCompanies(companiesData);
        } catch (error) {
          console.error("Erro ao carregar empresas:", error);
        } finally {
          setLoadingCompanies(false);
        }
      }
    };

    if (isOpen && isSystemAdmin) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      if (category) {
        const isGlobal = category.companyId === null;
        setFormData({
          name: category.name || "",
          description: category.description || "",
          companyId: isGlobal ? "" : (category.companyId || ""),
          isGlobal: isGlobal
        });
      } else {
        setFormData({
          name: "",
          description: "",
          companyId: "",
          isGlobal: false
        });
      }
    }
  }, [isOpen, category]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome da categoria é obrigatório";
    }

    if (isSystemAdmin) {
      if (!formData.isGlobal && !formData.companyId) {
        newErrors.companyId = "Selecione uma empresa ou marque como categoria global";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const url = category ? `/api/documents/category/${category.id}` : '/api/documents/category';
      const method = category ? 'PUT' : 'POST';

      // Preparar dados para envio
      const dataToSend = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        companyId: formData.isGlobal ? null : (formData.companyId || null)
      };

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(dataToSend)
      });

      if (response.ok) {
        onSuccess();
        onClose();
      } else {
        const error = await response.json();
        if (error.errors) {
          setErrors(error.errors);
        } else {
          setErrors({ general: error.message || 'Erro ao salvar categoria' });
        }
      }
    } catch (error) {
      console.error("Erro ao salvar categoria:", error);
      setErrors({ general: 'Erro ao salvar categoria' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGlobalToggle = (isGlobal) => {
    setFormData(prev => ({
      ...prev,
      isGlobal,
      companyId: isGlobal ? "" : prev.companyId
    }));
    setErrors(prev => ({ ...prev, companyId: "" }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[95vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500">
        <DialogHeader className="pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg text-white">
              <Tag className="h-5 w-5" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white">
                {category ? "Editar Categoria" : "Nova Categoria"}
              </DialogTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {category ? "Modifique as informações da categoria" : "Crie uma nova categoria para organizar documentos"}
              </p>
            </div>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 pt-4">
          {/* Erro geral */}
          {errors.general && (
            <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
              <p className="text-sm text-red-700 dark:text-red-300">{errors.general}</p>
            </div>
          )}

          {/* Informações básicas */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Tag className="h-4 w-4 text-blue-600" />
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Informações Básicas</h3>
            </div>

            {/* Nome da categoria */}
            <div className="space-y-2">
              <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                Nome da Categoria *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, name: e.target.value }));
                  setErrors(prev => ({ ...prev, name: "" }));
                }}
                placeholder="Ex: Documentos Pessoais, Comprovantes, Contratos..."
                className={errors.name ? "border-red-500 focus:ring-red-500" : ""}
              />
              {errors.name && (
                <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            {/* Descrição */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium flex items-center gap-2">
                <span className="w-2 h-2 bg-gray-400 rounded-full"></span>
                Descrição
              </Label>
              <ModuleTextarea
                id="description"
                moduleColor="people"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Descreva o propósito desta categoria e que tipos de documentos ela deve conter..."
                rows={3}
                className="resize-none"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Uma boa descrição ajuda outros usuários a entender quando usar esta categoria
              </p>
            </div>
          </div>

          {/* Configurações de empresa (apenas para system_admin) */}
          {isSystemAdmin && (
            <div className="space-y-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-2 mb-4">
                <Building className="h-4 w-4 text-blue-600" />
                <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Configurações de Empresa</h3>
                <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs">
                  <Info className="h-3 w-3" />
                  System Admin
                </div>
              </div>

              {/* Toggle Global */}
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <button
                    type="button"
                    onClick={() => handleGlobalToggle(true)}
                    className={`flex items-center gap-3 p-3 rounded-lg border-2 transition-all ${
                      formData.isGlobal
                        ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-700'
                    }`}
                  >
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                      formData.isGlobal
                        ? 'border-green-500 bg-green-500'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {formData.isGlobal && <CheckCircle2 className="w-2 h-2 text-white" />}
                    </div>
                    <div className="text-left">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-gray-900 dark:text-white">Categoria Global</span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Disponível para todas as empresas do sistema
                      </p>
                    </div>
                  </button>
                </div>

                <div className="flex items-start gap-3">
                  <button
                    type="button"
                    onClick={() => handleGlobalToggle(false)}
                    className={`flex items-center gap-3 p-3 rounded-lg border-2 transition-all ${
                      !formData.isGlobal
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-700'
                    }`}
                  >
                    <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                      !formData.isGlobal
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300 dark:border-gray-600'
                    }`}>
                      {!formData.isGlobal && <CheckCircle2 className="w-2 h-2 text-white" />}
                    </div>
                    <div className="text-left">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-gray-900 dark:text-white">Categoria Específica</span>
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Vinculada a uma empresa específica
                      </p>
                    </div>
                  </button>
                </div>
              </div>

              {/* Seleção de empresa */}
              {!formData.isGlobal && (
                <div className="space-y-2 mt-4 p-3 bg-white dark:bg-gray-800 rounded-lg border">
                  <Label htmlFor="companyId" className="text-sm font-medium flex items-center gap-2">
                    <Building className="h-3 w-3 text-blue-600" />
                    Selecionar Empresa *
                  </Label>
                  <ModuleSelect
                    id="companyId"
                    moduleColor="people"
                    value={formData.companyId}
                    onChange={(e) => {
                      setFormData(prev => ({ ...prev, companyId: e.target.value }));
                      setErrors(prev => ({ ...prev, companyId: "" }));
                    }}
                    disabled={loadingCompanies}
                    className={errors.companyId ? "border-red-500 focus:ring-red-500" : ""}
                  >
                    <option value="">
                      {loadingCompanies ? "Carregando empresas..." : "Selecione uma empresa"}
                    </option>
                    {companies.map((company) => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </ModuleSelect>
                  {errors.companyId && (
                    <p className="text-xs text-red-600 dark:text-red-400 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.companyId}
                    </p>
                  )}
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    A categoria será visível apenas para usuários desta empresa
                  </p>
                </div>
              )}
            </div>
          )}



          {/* Botões */}
          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="min-w-[100px]"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="min-w-[120px] bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white"
            >
              {isLoading ? "Salvando..." : (category ? "Atualizar Categoria" : "Criar Categoria")}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryFormModal; 