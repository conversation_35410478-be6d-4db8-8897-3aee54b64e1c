"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/components/ui/ConfirmationDialog.js":
/*!*************************************************!*\
  !*** ./src/components/ui/ConfirmationDialog.js ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst ConfirmationDialog = (param)=>{\n    let { isOpen, onClose, onConfirm, title = \"Confirmar ação\", message = \"Tem certeza que deseja continuar?\", confirmText = \"Confirmar\", cancelText = \"Cancelar\", variant = \"warning\", moduleColor = \"scheduling\", appointmentData = null // Dados do agendamento para exibição detalhada\n     } = param;\n    _s();\n    // Definir estilos com base na variante e módulo\n    const getVariantStyles = ()=>{\n        switch(variant){\n            case \"danger\":\n                return {\n                    iconColor: \"text-red-500 dark:text-red-400\",\n                    iconBg: \"bg-red-100 dark:bg-red-900/30\",\n                    confirmBg: \"bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600\",\n                    borderColor: \"border-red-400 dark:border-red-500\",\n                    textColor: \"text-red-600 dark:text-red-300\",\n                    titleColor: \"text-red-800 dark:text-white\"\n                };\n            case \"info\":\n                if (moduleColor === \"people\") {\n                    return {\n                        iconColor: \"text-orange-500 dark:text-orange-400\",\n                        iconBg: \"bg-orange-100 dark:bg-orange-900/30\",\n                        confirmBg: \"bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 dark:from-orange-700 dark:to-amber-700 dark:hover:from-orange-600 dark:hover:to-amber-600\",\n                        borderColor: \"border-orange-400 dark:border-orange-500\",\n                        textColor: \"text-orange-600 dark:text-orange-300\",\n                        titleColor: \"text-orange-800 dark:text-white\"\n                    };\n                }\n                return {\n                    iconColor: \"text-purple-500 dark:text-purple-400\",\n                    iconBg: \"bg-purple-100 dark:bg-purple-900/30\",\n                    confirmBg: \"bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600\",\n                    borderColor: \"border-purple-400 dark:border-purple-500\",\n                    textColor: \"text-purple-600 dark:text-purple-300\",\n                    titleColor: \"text-purple-800 dark:text-white\"\n                };\n            case \"warning\":\n            default:\n                if (moduleColor === \"people\") {\n                    return {\n                        iconColor: \"text-orange-500 dark:text-orange-400\",\n                        iconBg: \"bg-orange-100 dark:bg-orange-900/30\",\n                        confirmBg: \"bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700 dark:from-orange-700 dark:to-amber-700 dark:hover:from-orange-600 dark:hover:to-amber-600\",\n                        borderColor: \"border-orange-400 dark:border-orange-500\",\n                        textColor: \"text-orange-600 dark:text-orange-300\",\n                        titleColor: \"text-orange-800 dark:text-white\"\n                    };\n                }\n                return {\n                    iconColor: \"text-purple-500 dark:text-purple-400\",\n                    iconBg: \"bg-purple-100 dark:bg-purple-900/30\",\n                    confirmBg: \"bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 dark:from-purple-700 dark:to-violet-700 dark:hover:from-purple-600 dark:hover:to-violet-600\",\n                    borderColor: \"border-purple-400 dark:border-purple-500\",\n                    textColor: \"text-purple-600 dark:text-purple-300\",\n                    titleColor: \"text-purple-800 dark:text-white\"\n                };\n        }\n    };\n    const variantStyles = getVariantStyles();\n    // Estado para controlar a montagem do componente no cliente\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Efeito para garantir que o portal só seja criado no lado do cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmationDialog.useEffect\": ()=>{\n            setMounted(true);\n            return ({\n                \"ConfirmationDialog.useEffect\": ()=>setMounted(false)\n            })[\"ConfirmationDialog.useEffect\"];\n        }\n    }[\"ConfirmationDialog.useEffect\"], []);\n    // Efeito para prevenir scroll quando o modal estiver aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ConfirmationDialog.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = '';\n            }\n            return ({\n                \"ConfirmationDialog.useEffect\": ()=>{\n                    document.body.style.overflow = '';\n                }\n            })[\"ConfirmationDialog.useEffect\"];\n        }\n    }[\"ConfirmationDialog.useEffect\"], [\n        isOpen\n    ]);\n    if (!isOpen || !mounted) return null;\n    // Usar createPortal para renderizar o modal no nível mais alto do DOM\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed inset-0 z-[15000] flex items-center justify-center overflow-y-auto\", \"pointer-events-auto\" // Garantir que os eventos de clique funcionem\n        ),\n        onClick: (e)=>{\n            e.stopPropagation();\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/60\",\n                onClick: (e)=>{\n                    e.stopPropagation(); // Impedir propagação do evento para o modal principal\n                    e.preventDefault(); // Adicionado preventDefault para garantir\n                    onClose();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative bg-background rounded-xl shadow-xl dark:shadow-black/50 w-full max-w-2xl z-[15050] border-2 \".concat(variantStyles.borderColor),\n                onClick: (e)=>{\n                    e.stopPropagation(); // Impedir propagação do evento para o modal principal\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: (e)=>{\n                                e.stopPropagation(); // Impedir propagação do evento\n                                e.preventDefault(); // Adicionado preventDefault para garantir\n                                onClose();\n                            },\n                            className: \"text-neutral-400 dark:text-gray-500 hover:text-neutral-600 dark:hover:text-gray-300 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4 mb-6 pb-4 border-b border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 rounded-full \".concat(variantStyles.iconBg),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-6 w-6 \".concat(variantStyles.iconColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-slate-800 dark:text-white\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                                children: \"Confirme os detalhes antes de prosseguir\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-neutral-600 dark:text-gray-300 mb-6 max-h-[60vh] overflow-y-auto pr-2\",\n                                children: appointmentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base font-medium text-slate-800 dark:text-white mb-4\",\n                                            children: message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-background rounded-lg p-4 border-2 border-purple-400 dark:border-purple-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-purple-800 dark:text-white mb-3\",\n                                                    children: \"Detalhes do Agendamento:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm\",\n                                                    children: [\n                                                        appointmentData.provider && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-purple-600 dark:text-purple-300\",\n                                                                    children: \"Profissional:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-800 dark:text-white font-medium\",\n                                                                    children: appointmentData.provider\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 161,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        appointmentData.patient && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-purple-600 dark:text-purple-300\",\n                                                                    children: \"Paciente:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-800 dark:text-white font-medium\",\n                                                                    children: appointmentData.patient\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 168,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        appointmentData.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-purple-600 dark:text-purple-300\",\n                                                                    children: \"Local:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-800 dark:text-white font-medium\",\n                                                                    children: appointmentData.location\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 175,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        appointmentData.service && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-purple-600 dark:text-purple-300\",\n                                                                    children: \"Servi\\xe7o:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-gray-800 dark:text-white font-medium\",\n                                                                    children: appointmentData.service\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                appointmentData.schedules && appointmentData.schedules.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-purple-600 dark:text-purple-300 block mb-3\",\n                                                            children: \"Hor\\xe1rios:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: appointmentData.schedules.map((schedule, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-white dark:bg-gray-600 px-4 py-3 rounded-lg border border-gray-200 dark:border-gray-500 shadow-sm\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-800 dark:text-white font-medium text-sm\",\n                                                                        children: schedule\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 27\n                                                                }, undefined))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: message.split('\\n').map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                            children: line.startsWith('•') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block pl-2 py-0.5 text-purple-600 dark:text-purple-400 font-medium\",\n                                                children: line\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                lineNumber: 206,\n                                                columnNumber: 23\n                                            }, undefined) : line.match(/^\\d+\\./) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block pl-2 py-0.5 font-medium\",\n                                                children: line\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 23\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: line.trim() === '' ? 'block py-1' : 'block py-0.5',\n                                                children: line\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                                lineNumber: 210,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                            lineNumber: 204,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation(); // Impedir propagação do evento\n                                            e.preventDefault(); // Adicionado preventDefault para garantir\n                                            onClose();\n                                        },\n                                        className: \"px-6 py-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200 font-medium\",\n                                        children: cancelText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            console.log('[CONFIRMATION-DIALOG] Botão confirmar clicado!');\n                                            console.log('[CONFIRMATION-DIALOG] onConfirm function:', typeof onConfirm);\n                                            e.stopPropagation();\n                                            e.preventDefault();\n                                            if (onConfirm) {\n                                                console.log('[CONFIRMATION-DIALOG] Chamando onConfirm...');\n                                                onConfirm();\n                                            } else {\n                                                console.error('[CONFIRMATION-DIALOG] onConfirm é undefined!');\n                                            }\n                                        },\n                                        className: \"px-6 py-2.5 text-white rounded-lg transition-all duration-200 font-medium shadow-lg \".concat(variantStyles.confirmBg),\n                                        children: confirmText\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ConfirmationDialog.js\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n    // Renderizar o modal usando um portal para garantir que ele fique acima de tudo\n    // Usamos um z-index maior que o do modal principal para garantir que ele fique por cima\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n_s(ConfirmationDialog, \"BShlRgxf1Xjno/mi6QXyq9ZqIDE=\");\n_c = ConfirmationDialog;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConfirmationDialog);\nvar _c;\n$RefreshReg$(_c, \"ConfirmationDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\n"));

/***/ })

});