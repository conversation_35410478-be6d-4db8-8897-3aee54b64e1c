"use client";

import React, { useState, useEffect, useRef } from "react";
import { createPortal } from 'react-dom';
import {
  FileText,
  Upload,
  Plus,
  Loader2,
  Trash,
  Eye,
  Download,
  ChevronDown
} from "lucide-react";
import { api } from "@/utils/api";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

const DocumentsTab = ({ personId, onClose, isCreating, onAddTempDocument, tempDocuments = [] }) => {
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);
  const fileInputRef = useRef(null);

  const [documents, setDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [needsSave, setNeedsSave] = useState(!personId);
  const [mounted, setMounted] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });

  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);

  // Upload state
  const [documentType, setDocumentType] = useState("");
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  useEffect(() => {
    if (personId) {
      loadDocuments();
    } else if (isCreating && tempDocuments.length > 0) {
      // Se estiver no modo de criação e houver documentos temporários, exibi-los
      const formattedTempDocs = tempDocuments.map((doc, index) => ({
        id: `temp-${index}`,
        fileName: doc.file.name,
        fileSize: doc.file.size,
        fileType: doc.file.type,
        documentType: doc.type,
        createdAt: new Date().toISOString(),
        isTemp: true
      }));

      setDocuments(formattedTempDocs);
      setIsLoading(false);
    } else {
      setIsLoading(false);
    }
  }, [personId, isCreating, tempDocuments]);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Calcular a posição do dropdown quando aberto
  useEffect(() => {
    if (dropdownOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        right: window.innerWidth - rect.right,
        width: Math.max(rect.width, 256) // Mínimo de 256px (w-64)
      });
    }
  }, [dropdownOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const loadDocuments = async () => {
    if (!personId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await api.get("/documents", {
        params: {
          targetId: personId,
          targetType: "person"
        }
      });
      setDocuments(response.data || []);
    } catch (err) {
      console.error("Error fetching documents:", err);
      setError("Não foi possível carregar os documentos.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenUploadModal = (type) => {
    setDocumentType(type);
    setSelectedFile(null);
    setUploadError(null);
    setDropdownOpen(false);

    // Trigger file input click
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
      setUploadError(null);
    }
  };

  const handleUploadDocument = async () => {
    if (!selectedFile || !documentType) {
      setUploadError("Selecione um arquivo e um tipo de documento");
      return;
    }

    setIsUploading(true);
    setUploadProgress(0);
    setUploadError(null);

    // Se estiver no modo de criação, armazenar o documento temporariamente
    if (isCreating) {
      console.log('Adicionando documento temporário:', { file: selectedFile, type: documentType });

      // Adicionar o documento à lista de documentos temporários
      onAddTempDocument && onAddTempDocument({ file: selectedFile, type: documentType });

      // Adicionar o documento à lista local para exibição
      const newTempDoc = {
        id: `temp-${Date.now()}`,
        fileName: selectedFile.name,
        fileSize: selectedFile.size,
        fileType: selectedFile.type,
        documentType: documentType,
        createdAt: new Date().toISOString(),
        isTemp: true
      };

      setDocuments(prev => [...prev, newTempDoc]);
      setSelectedFile(null);
      setDocumentType("");
      setIsUploading(false);
      return;
    }

    // Se não estiver no modo de criação, fazer o upload normalmente
    const formData = new FormData();
    formData.append("documents", selectedFile);
    formData.append("types", JSON.stringify([documentType]));

    try {
      await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
      });

      // Reload documents after successful upload
      loadDocuments();
      setSelectedFile(null);
      setDocumentType("");
    } catch (err) {
      console.error("Error uploading document:", err);
      setUploadError(err.response?.data?.message || "Erro ao fazer upload do documento");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteDocument = (documentId) => {
    setDocumentToDelete(documentId);
    setConfirmDialogOpen(true);
  };

  const confirmDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      await api.delete(`/documents/${documentToDelete}`);
      setDocuments(documents.filter(doc => doc.id !== documentToDelete));
      setConfirmDialogOpen(false);
      setDocumentToDelete(null);
    } catch (err) {
      console.error("Error deleting document:", err);
      setError("Não foi possível excluir o documento.");
    }
  };

  const handleViewDocument = (documentId) => {
    window.open(`/api/documents/${documentId}`, '_blank');
  };

  const documentTypes = [
    { id: "RG", label: "RG" },
    { id: "CPF", label: "CPF" },
    { id: "CNH", label: "Carteira de Motorista" },
    { id: "COMP_RESIDENCIA", label: "Comprovante de Residência" },
    { id: "CERTIDAO_NASCIMENTO", label: "Certidão de Nascimento" },
    { id: "CERTIDAO_CASAMENTO", label: "Certidão de Casamento" },
    { id: "CARTAO_VACINACAO", label: "Cartão de Vacinação" },
    { id: "PASSAPORTE", label: "Passaporte" },
    { id: "TITULO_ELEITOR", label: "Título de Eleitor" },
    { id: "CARTEIRA_TRABALHO", label: "Carteira de Trabalho" },
    { id: "OUTROS", label: "Outros" }
  ];

  const getDocumentTypeDisplay = (type) => {
    const found = documentTypes.find(docType => docType.id === type);
    return found ? found.label : type;
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  // Se não estiver no modo de criação e não tiver ID, exibir mensagem
  if (!personId && !isCreating) {
    return (
      <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4">
        <div className="flex items-center gap-2">
          <FileText size={24} />
          <h3 className="text-lg font-semibold">Documentos</h3>
        </div>
        <p className="text-center">Salve os dados básicos da pessoa antes de adicionar documentos.</p>
        <button
          onClick={() => onClose()}
          className="mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          Voltar para Informações
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">Documentos</h3>
          {isLoading ? (
            <Loader2 size={16} className="animate-spin text-neutral-400 dark:text-gray-500" />
          ) : (
            <span className="text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
              {documents.length}
            </span>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Dropdown menu for document type selection */}
          <div className="relative">
            <button
              ref={buttonRef}
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="flex items-center gap-2 px-3 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors"
            >
              <Upload size={16} />
              <span>Adicionar</span>
              <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
            </button>

            {/* Dropdown - renderizado via portal para evitar problemas de overflow */}
            {dropdownOpen && mounted && createPortal(
              <div
                ref={dropdownRef}
                className="fixed z-[14000] w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl border-2 border-orange-300 dark:border-orange-600 overflow-hidden"
                style={{
                  top: `${dropdownPosition.top}px`,
                  right: `${dropdownPosition.right}px`,
                  width: `${dropdownPosition.width}px`,
                }}
              >
                <div className="p-3 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-b-2 border-orange-400 dark:border-orange-500">
                  <h4 className="text-sm font-semibold text-orange-800 dark:text-orange-200 flex items-center gap-2">
                    <FileText size={16} className="text-orange-600 dark:text-orange-400" />
                    Selecione o tipo de documento
                  </h4>
                </div>
                <div className="max-h-96 overflow-y-auto">
                  {documentTypes.map(type => (
                    <button
                      key={type.id}
                      onClick={() => handleOpenUploadModal(type.id)}
                      className="w-full text-left px-4 py-3 hover:bg-orange-50 dark:hover:bg-orange-900/20 text-neutral-700 dark:text-gray-200 flex items-center gap-3 transition-colors border-b border-orange-100 dark:border-orange-800/30 last:border-b-0"
                    >
                      <div className="p-1 bg-orange-100 dark:bg-orange-900/30 rounded">
                        <FileText size={14} className="text-orange-600 dark:text-orange-400" />
                      </div>
                      <span className="font-medium">{type.label}</span>
                    </button>
                  ))}
                </div>
              </div>,
              document.body
            )}
          </div>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept=".pdf,.jpg,.jpeg,.png"
      />

      {/* Error message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400">
          {error}
          <button
            onClick={loadDocuments}
            className="ml-2 underline hover:no-underline"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* File upload preview */}
      {selectedFile && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm dark:shadow-md dark:shadow-black/20 p-4 border border-neutral-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400">
                <FileText size={20} />
              </div>
              <div>
                <p className="font-medium text-neutral-800 dark:text-gray-100">{selectedFile.name}</p>
                <p className="text-sm text-neutral-500 dark:text-gray-400">
                  {getDocumentTypeDisplay(documentType)} • {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={() => setSelectedFile(null)}
                className="p-2 text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors"
              >
                <Trash size={16} />
              </button>
            </div>
          </div>

          {uploadError && (
            <div className="mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded text-sm">
              {uploadError}
            </div>
          )}

          {isUploading && (
            <div className="mt-3 space-y-1">
              <div className="h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary-500 dark:bg-primary-600 rounded-full"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-right text-xs text-neutral-500 dark:text-gray-400">
                {uploadProgress}%
              </p>
            </div>
          )}

          <div className="mt-3 flex justify-end">
            <button
              onClick={handleUploadDocument}
              disabled={isUploading}
              className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              {isUploading ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Enviando...</span>
                </>
              ) : (
                <>
                  <Upload size={16} />
                  <span>Enviar documento</span>
                </>
              )}
            </button>
          </div>
        </div>
      )}

      {/* Document list */}
      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center">
          <div className="flex flex-col items-center">
            <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin mb-4" />
            <p className="text-neutral-600 dark:text-gray-300">Carregando documentos...</p>
          </div>
        </div>
      ) : documents.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center">
          <FileText size={48} className="text-neutral-300 dark:text-gray-600 mb-4" />
          <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2">Nenhum documento</h4>
          <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center">
            Adicione documentos importantes como RG, CPF, comprovantes e outros para esta pessoa.
          </p>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 max-w-2xl">
            {documentTypes.slice(0, 6).map(type => (
              <button
                key={type.id}
                onClick={() => handleOpenUploadModal(type.id)}
                className="flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
              >
                <span className="text-xs text-neutral-600 dark:text-gray-300 text-center">{type.label}</span>
                <Plus size={16} className="text-primary-500 dark:text-primary-400 mt-1" />
              </button>
            ))}
          </div>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
            <thead>
              <tr className="bg-neutral-50 dark:bg-gray-900">
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Arquivo
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Data de Upload
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
              {documents.map(document => (
                <tr key={document.id} className={`hover:bg-neutral-50 dark:hover:bg-gray-700 ${document.isTemp ? 'bg-yellow-50 dark:bg-yellow-900/20' : ''}`}>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-200">
                      {document.isTemp ? document.documentType : getDocumentTypeDisplay(document.type)}
                    </span>
                    {document.isTemp && (
                      <span className="ml-2 px-2 py-1 text-xs rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300">
                        Pendente
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 text-neutral-400 dark:text-gray-500 mr-2" />
                      <span className="text-neutral-700 dark:text-gray-200">
                        {document.isTemp ? document.fileName : document.filename}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-neutral-500 dark:text-gray-400">
                    {formatDate(document.createdAt)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right">
                    <div className="flex justify-end">
                      {document.isTemp ? (
                        <span className="text-xs text-amber-600 dark:text-amber-400">
                          Será salvo quando a pessoa for criada
                        </span>
                      ) : (
                        <>
                          <button
                            onClick={() => handleViewDocument(document.id)}
                            className="p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                            title="Visualizar"
                          >
                            <Eye size={16} />
                          </button>
                          <button
                            onClick={() => window.open(`/api/documents/${document.id}?download=true`, '_blank')}
                            className="p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                            title="Baixar"
                          >
                            <Download size={16} />
                          </button>
                          <button
                            onClick={() => handleDeleteDocument(document.id)}
                            className="p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                            title="Excluir"
                          >
                            <Trash size={16} />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => {
          setConfirmDialogOpen(false);
          setDocumentToDelete(null);
        }}
        onConfirm={confirmDeleteDocument}
        title="Excluir Documento"
        message="Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita."
        variant="danger"
        moduleColor="people"
        confirmText="Excluir"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default DocumentsTab;