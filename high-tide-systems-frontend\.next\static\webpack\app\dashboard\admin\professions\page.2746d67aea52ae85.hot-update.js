"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: \"Excluir permanentemente o grupo \".concat(group.name, \"?\")\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 638,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 644,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 652,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 702,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 785,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 701,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 839,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 879,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 908,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 909,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 901,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 917,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 931,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 938,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 929,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 962,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 957,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 956,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 979,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1013,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1012,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1024,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1027,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1023,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1022,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1039,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1043,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1037,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1054,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1055,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1053,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1051,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1071,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1069,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1068,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1082,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1080,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1106,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1092,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1091,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1116,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1123,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1139,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1132,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1115,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1114,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1010,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 952,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 951,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1163,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1158,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1178,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1201,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1200,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1212,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1215,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1211,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1210,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1228,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1226,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1225,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1240,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1239,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1246,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1237,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1256,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1257,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1255,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1254,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1274,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1266,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1265,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1290,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1299,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1289,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1288,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1198,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1154,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1330,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1355,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1361,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 835,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"z2ueIfMvpFqwLSBIlnDUAH37acU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ })

});