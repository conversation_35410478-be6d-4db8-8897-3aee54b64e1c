"use client";

import React, { useState, useEffect } from "react";
import {
  ArrowLeft,
  Edit,
  Plus,
  Trash,
  Power,
  User,
  Mail,
  Phone,
  Calendar,
  CreditCard,
  MapPin,
  UserPlus,
  Shield,
  CheckCircle,
  XCircle,
  RefreshCw,
  Loader2,
  Eye,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { personsService } from "@/app/modules/people/services/personsService";
import ClientFormModal from "@/components/people/ClientFormModal";
import PersonFormModal from "@/components/people/PersonFormModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import Link from "next/link";

const ClientDetailsPage = ({ clientId }) => {
  const router = useRouter();
  const [client, setClient] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Modals and dialogs state
  const [clientFormOpen, setClientFormOpen] = useState(false);
  const [personFormOpen, setPersonFormOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState(null);

  useEffect(() => {
    loadClientData();
  }, [clientId]);

  const loadClientData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = await clientsService.getClient(clientId);
      setClient(data);
    } catch (err) {
      console.error("Error fetching client details:", err);
      setError(
        "Não foi possível carregar os dados do cliente. Por favor, tente novamente."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  const formatCPF = (cpf) => {
    if (!cpf) return "N/A";

    // CPF format: 000.000.000-00
    const cpfNumbers = cpf.replace(/\D/g, "");
    return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (phone) => {
    if (!phone) return "N/A";

    // Phone format: (00) 00000-0000
    const phoneNumbers = phone.replace(/\D/g, "");
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  };

  const handleEditClient = () => {
    setClientFormOpen(true);
  };

  const handleEditPerson = (person) => {
    setSelectedPerson(person);
    setPersonFormOpen(true);
  };

  const handleAddPerson = () => {
    setSelectedPerson(null);
    setPersonFormOpen(true);
  };

  const handleTogglePersonStatus = (person) => {
    setSelectedPerson(person);
    setConfirmAction({
      type: "toggle-person-status",
      title: `${person.active ? "Desativar" : "Ativar"} Pessoa`,
      message: `Deseja ${person.active ? "desativar" : "ativar"} ${
        person.fullName
      }?`,
    });
    setConfirmDialogOpen(true);
  };

  const handleDeletePerson = (person) => {
    setSelectedPerson(person);
    setConfirmAction({
      type: "delete-person",
      title: "Excluir Pessoa",
      message: `Deseja excluir permanentemente ${person.fullName}?`,
      variant: "danger",
    });
    setConfirmDialogOpen(true);
  };

  const handleToggleClientStatus = () => {
    setConfirmAction({
      type: "toggle-client-status",
      title: `${client.active ? "Desativar" : "Ativar"} Cliente`,
      message: `Deseja ${client.active ? "desativar" : "ativar"} o cliente ${
        client.login
      }?`,
    });
    setConfirmDialogOpen(true);
  };

  const handleDeleteClient = () => {
    setConfirmAction({
      type: "delete-client",
      title: "Excluir Cliente",
      message: `Deseja excluir permanentemente o cliente ${client.login}?`,
      variant: "danger",
    });
    setConfirmDialogOpen(true);
  };

  const confirmActionHandler = async () => {
    try {
      if (confirmAction.type === "toggle-person-status") {
        await personsService.togglePersonStatus(selectedPerson.id);
      } else if (confirmAction.type === "delete-person") {
        await personsService.deletePerson(selectedPerson.id);
      } else if (confirmAction.type === "toggle-client-status") {
        await clientsService.toggleClientStatus(client.id);
      } else if (confirmAction.type === "delete-client") {
        await clientsService.deleteClient(client.id);
        router.push("/dashboard/people/clients");
        return; // No need to reload data
      }

      // Reload data after action
      loadClientData();
    } catch (error) {
      console.error("Error executing action:", error);
      setError("Ocorreu um erro ao executar esta ação.");
    }
  };

  const getGenderDisplay = (gender) => {
    if (!gender) return "Não informado";

    const genderMap = {
      M: "Masculino",
      F: "Feminino",
      O: "Outro",
    };

    return genderMap[gender] || gender;
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[300px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary-500 dark:text-primary-400 mb-4" />
        <p className="text-neutral-600 dark:text-neutral-300">Carregando dados do cliente...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300">
        <h3 className="text-lg font-medium mb-2">Erro</h3>
        <p>{error}</p>
        <div className="mt-4">
          <button
            onClick={() => router.push("/dashboard/people/clients")}
            className="px-4 py-2 bg-white dark:bg-gray-800 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            <span>Voltar para lista de clientes</span>
          </button>
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="bg-neutral-50 dark:bg-gray-800 p-6 rounded-lg border border-neutral-200 dark:border-gray-700 text-neutral-700 dark:text-neutral-300">
        <h3 className="text-lg font-medium mb-2">Cliente não encontrado</h3>
        <p>O cliente solicitado não foi encontrado.</p>
        <div className="mt-4">
          <button
            onClick={() => router.push("/dashboard/people/clients")}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
          >
            <ArrowLeft size={18} />
            <span>Voltar para lista de clientes</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center gap-2">
          <button
            onClick={() => router.push("/dashboard/people/clients")}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full"
          >
            <ArrowLeft size={18} className="text-neutral-800 dark:text-neutral-200" />
          </button>
          <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">
            Detalhes do Cliente
          </h1>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          <button
            onClick={handleEditClient}
            className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <Edit size={16} />
            <span>Editar Cliente</span>
          </button>

          <button
            onClick={handleToggleClientStatus}
            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${
              client.active
                ? "bg-amber-500 dark:bg-amber-600 text-white hover:bg-amber-600 dark:hover:bg-amber-700"
                : "bg-green-500 dark:bg-green-600 text-white hover:bg-green-600 dark:hover:bg-green-700"
            }`}
          >
            <Power size={16} />
            <span>{client.active ? "Desativar" : "Ativar"}</span>
          </button>

          <button
            onClick={handleDeleteClient}
            className="flex items-center gap-2 px-3 py-2 bg-red-500 dark:bg-red-600 text-white rounded-lg hover:bg-red-600 dark:hover:bg-red-700 transition-colors"
          >
            <Trash size={16} />
            <span>Excluir</span>
          </button>
        </div>
      </div>

      {/* Client Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 p-6 border border-neutral-100 dark:border-gray-700">
        <div className="flex flex-col md:flex-row md:items-center gap-6">
          <div className="flex-shrink-0 flex items-center justify-center w-20 h-20 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full">
            <User size={32} />
          </div>

          <div className="flex-1 space-y-4">
            <div>
              <h2 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">
                {client.login}
              </h2>
              <div className="flex items-center mt-1">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                    client.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                  }`}
                >
                  {client.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">Email</p>
                <div className="flex items-center gap-2 mt-1">
                  <Mail className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                  <p className="text-neutral-800 dark:text-neutral-200">{client.email}</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">Data de cadastro</p>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                  <p className="text-neutral-800 dark:text-neutral-200">
                    {formatDate(client.createdAt)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pacientes associados */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100">Pacientes</h3>
          <button
            onClick={handleAddPerson}
            className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <UserPlus size={16} />
            <span>Adicionar Paciente</span>
          </button>
        </div>

        {client.clientPersons && client.clientPersons.length > 0 ? (
          <div className="grid grid-cols-1 gap-4">
            {client.clientPersons.map((clientPerson) => {
              const person = clientPerson.person;
              return (
              <div
                key={person.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 p-6 border border-neutral-100 dark:border-gray-700"
              >
                <div className="flex flex-col md:flex-row md:items-start gap-6">
                  <div className="flex-shrink-0 flex items-center justify-center w-16 h-16 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-full relative">
                    <User size={24} />
                    {clientPerson.relationship === "Titular" && (
                      <div className="absolute -top-1 -right-1 bg-primary-500 dark:bg-primary-600 text-white p-1 rounded-full">
                        <Shield size={12} />
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
                      <div>
                        <div className="flex items-center gap-2">
                          <Link
                            href={`/dashboard/people/persons/${person.id}`}
                            className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 hover:text-primary-600 dark:hover:text-primary-400 hover:underline"
                          >
                            {person.fullName}
                          </Link>
                          <span className="text-sm text-neutral-500 dark:text-neutral-400">
                            ({clientPerson.relationship || "Sem relacionamento"})
                          </span>
                        </div>

                        <div className="flex items-center mt-1">
                          <span
                            className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                              person.active
                                ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                                : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                            }`}
                          >
                            {person.active ? (
                              <>
                                <CheckCircle size={12} />
                                <span>Ativo</span>
                              </>
                            ) : (
                              <>
                                <XCircle size={12} />
                                <span>Inativo</span>
                              </>
                            )}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Link
                          href={`/dashboard/people/persons/${person.id}`}
                          className="p-2 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Visualizar"
                        >
                          <Eye size={18} />
                        </Link>
                        <button
                          onClick={() => handleEditPerson(person)}
                          className="p-2 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                          title="Editar"
                        >
                          <Edit size={18} />
                        </button>

                        <button
                          onClick={() => handleTogglePersonStatus(person)}
                          className={`p-2 rounded-lg transition-colors ${
                            person.active
                              ? "text-amber-500 hover:text-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/20 dark:text-amber-400 dark:hover:text-amber-300"
                              : "text-green-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 dark:text-green-400 dark:hover:text-green-300"
                          }`}
                          title={person.active ? "Desativar" : "Ativar"}
                        >
                          <Power size={18} />
                        </button>

                        <button
                          onClick={() => handleDeletePerson(person)}
                          className="p-2 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                          title="Excluir"
                        >
                          <Trash size={18} />
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {person.cpf && (
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">CPF</p>
                          <div className="flex items-center gap-2 mt-1">
                            <CreditCard className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">
                              {formatCPF(person.cpf)}
                            </p>
                          </div>
                        </div>
                      )}

                      {person.birthDate && (
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">
                            Data de Nascimento
                          </p>
                          <div className="flex items-center gap-2 mt-1">
                            <Calendar className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">
                              {formatDate(person.birthDate)}
                            </p>
                          </div>
                        </div>
                      )}

                      {person.gender && (
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">Gênero</p>
                          <div className="flex items-center gap-2 mt-1">
                            <User className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">
                              {getGenderDisplay(person.gender)}
                            </p>
                          </div>
                        </div>
                      )}

                      {person.email && (
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">Email</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Mail className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">{person.email}</p>
                          </div>
                        </div>
                      )}

                      {person.phone && (
                        <div>
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">Telefone</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Phone className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">
                              {formatPhone(person.phone)}
                            </p>
                          </div>
                        </div>
                      )}

                      {person.address && (
                        <div className="md:col-span-2">
                          <p className="text-sm text-neutral-500 dark:text-neutral-400">Endereço</p>
                          <div className="flex items-center gap-2 mt-1">
                            <MapPin className="h-4 w-4 text-neutral-400 dark:text-neutral-500" />
                            <p className="text-neutral-800 dark:text-neutral-200">{person.address}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {person.notes && (
                      <div className="mt-4">
                        <p className="text-sm text-neutral-500 dark:text-neutral-400">Observações</p>
                        <p className="mt-1 text-neutral-700 dark:text-neutral-300 bg-neutral-50 dark:bg-gray-700 p-3 rounded-lg">
                          {person.notes}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        ) : (
          <div className="bg-neutral-50 dark:bg-gray-700 p-6 rounded-lg border border-neutral-200 dark:border-gray-600 text-center">
            <p className="text-neutral-600 dark:text-neutral-300 mb-4">
              Este cliente não possui pacientes associados.
            </p>
            <button
              onClick={handleAddPerson}
              className="inline-flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
            >
              <UserPlus size={16} />
              <span>Adicionar Paciente</span>
            </button>
          </div>
        )}
      </div>

      {/* Modals */}
      {clientFormOpen && (
        <ClientFormModal
          isOpen={clientFormOpen}
          onClose={() => setClientFormOpen(false)}
          client={client}
          onSuccess={() => {
            setClientFormOpen(false);
            loadClientData();
          }}
        />
      )}

      {personFormOpen && (
        <PersonFormModal
          isOpen={personFormOpen}
          onClose={() => setPersonFormOpen(false)}
          person={selectedPerson}
          initialClientId={clientId}
          onSuccess={() => {
            setPersonFormOpen(false);
            loadClientData();
          }}
        />
      )}

      {/* Confirmation Dialog */}
      {confirmDialogOpen && confirmAction && (
        <ConfirmationDialog
          isOpen={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
          onConfirm={confirmActionHandler}
          title={confirmAction.title}
          message={confirmAction.message}
          variant={confirmAction.variant || "warning"}
          moduleColor="people"
          confirmText={confirmAction.confirmText || "Confirmar"}
          cancelText={confirmAction.cancelText || "Cancelar"}
        />
      )}
    </div>
  );
};

export default ClientDetailsPage;