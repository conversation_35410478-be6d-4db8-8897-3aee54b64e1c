'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { scrollToElement } from '@/utils/scrollUtils';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle
} from '@/components/ui/dialog';

// Imagens do slider - 2 de cada módulo principal
const sliderImages = [
  // Módulo de Agendamento
  {
    id: 1,
    src: '/landing/imagem-agendarconsulta-agendamento.png',
    alt: 'Interface do módulo de agendamento - Agendamento de consultas',
    title: 'Agendamento de Consultas',
    description: 'Gerencie consultas e compromissos com facilidade e eficiência'
  },
  {
    id: 2,
    src: '/landing/imagem-horariostrabalho-agendamento.png',
    alt: 'Interface do módulo de agendamento - Horários de trabalho',
    title: 'Hor<PERSON><PERSON><PERSON> de Trabalho',
    description: 'Configure horários de trabalho personalizáveis para sua equipe'
  },
  {
    id: 10,
    src: '/landing/imagem-solicitacoes-agendamento.png',
    alt: 'Interface do módulo de agendamento - Solicitações de agendamento',
    title: 'Solicitações de Agendamento',
    description: 'Clientes solicitam horários e você aprova com total controle'
  },
  // Módulo de Pessoas
  {
    id: 3,
    src: '/landing/imagem-clientes-pessoas.png',
    alt: 'Interface do módulo de pessoas - Cadastro de clientes',
    title: 'Gestão de Clientes',
    description: 'Cadastro completo de pacientes e contatos com todas as informações necessárias'
  },
  {
    id: 4,
    src: '/landing/imagem-convenios-pessoas.png',
    alt: 'Interface do módulo de pessoas - Gestão de convênios',
    title: 'Gestão de Convênios',
    description: 'Gerencie convênios e planos de saúde dos seus pacientes'
  },
  // Módulo de Administração
  {
    id: 5,
    src: '/landing/imagem-configuracoes-admin.png',
    alt: 'Interface do módulo de administração - Configurações',
    title: 'Configurações do Sistema',
    description: 'Personalize o sistema conforme as necessidades do seu negócio'
  },
  {
    id: 6,
    src: '/landing/imagem-usuarios-admin.png',
    alt: 'Interface do módulo de administração - Gestão de usuários',
    title: 'Gestão de Usuários',
    description: 'Administre usuários, permissões e mantenha o controle total do sistema'
  },
  // Módulo de Relatórios
  {
    id: 7,
    src: '/landing/imagem-dashboard.png',
    alt: 'Interface do módulo de relatórios - Dashboard principal',
    title: 'Dashboard Interativo',
    description: 'Visualize todos os dados importantes da sua clínica em um único lugar'
  },
  {
    id: 8,
    src: '/landing/imagem-ocupacao-agendamento.png',
    alt: 'Interface do módulo de relatórios - Análise de ocupação',
    title: 'Análise de Ocupação',
    description: 'Dashboards e relatórios detalhados para análise de desempenho'
  },
  // Módulo de Chat
  {
    id: 9,
    src: '/landing/imagem-chat2.png',
    alt: 'Interface do módulo de chat - Chat principal',
    title: 'Chat Integrado',
    description: 'Comunicação em tempo real entre equipe e pacientes'
  }
];

const Hero = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const [fade, setFade] = useState(true);
  const [slideDir, setSlideDir] = useState(1);
  const sliderRef = useRef(null);
  const autoplayRef = useRef(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalSlide, setModalSlide] = useState(0);

  // Função para avançar para o próximo slide
  const nextSlide = () => {
    setSlideDir(1);
    setFade(false);
    setTimeout(() => {
      setCurrentSlide((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));
      setTimeout(() => setFade(true), 50);
    }, 400);
  };

  // Função para voltar para o slide anterior
  const prevSlide = () => {
    setSlideDir(-1);
    setFade(false);
    setTimeout(() => {
      setCurrentSlide((prev) => (prev === 0 ? sliderImages.length - 1 : prev - 1));
      setTimeout(() => setFade(true), 50);
    }, 400);
  };

  // Função para ir para um slide específico
  const goToSlide = (index) => {
    setSlideDir(1);
    setFade(false);
    setTimeout(() => {
      setCurrentSlide(index);
      setTimeout(() => setFade(true), 50);
    }, 400);
  };

  // Funções para navegação no modal
  const nextModalSlide = () => {
    setModalSlide((prev) => (prev === sliderImages.length - 1 ? 0 : prev + 1));
  };
  const prevModalSlide = () => {
    setModalSlide((prev) => (prev === 0 ? sliderImages.length - 1 : prev - 1));
  };
  const goToModalSlide = (index) => {
    setModalSlide(index);
  };

  // Configurar autoplay
  useEffect(() => {
    if (isPlaying && !isHovering) {
      autoplayRef.current = setInterval(() => {
        nextSlide();
      }, 3500); // Reduzido de 5000ms para 3500ms
    }

    return () => {
      if (autoplayRef.current) {
        clearInterval(autoplayRef.current);
      }
    };
  }, [isPlaying, isHovering, currentSlide]);

  // Manipuladores de eventos para hover
  const handleMouseEnter = () => {
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    setIsHovering(false);
  };

  // Alternar reprodução automática
  const toggleAutoplay = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <section className="relative pt-32 pb-20 md:pt-40 md:pb-28 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-gray-900 dark:to-gray-800 -z-10" />

      {/* Decorative elements */}
      <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300 -z-10" />
      <div className="absolute top-24 right-10 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob -z-10" />
      <div className="absolute top-36 left-10 w-72 h-72 bg-secondary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000 -z-10" />
      <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000 -z-10" />

      <div className="container mx-auto px-4 md:px-6">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* Text content */}
          <motion.div
            className="flex-1 text-center lg:text-left"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
              Gerencie seu negócio com <span className="text-primary-500 dark:text-primary-400">eficiência</span> e <span className="text-primary-500 dark:text-primary-400">simplicidade</span>
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto lg:mx-0">
              High Tide Systems é a solução completa para seu negócio! Gerencie clientes e pacientes, controle agendamentos com calendário intuitivo, administre usuários e permissões, mantenha comunicação através do chat integrado e personalize o sistema conforme suas necessidades. Tudo em uma única plataforma.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                type="button"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
                onClick={() => {
                  if (typeof window !== 'undefined') {
                    const el = document.getElementById('pricing');
                    if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }
                }}
              >
                Assinar
              </button>
              <Link
                href="/subscription/signup"
                passHref
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
              >
                Teste Gratuito
              </Link>
              <a
                href="#features"
                onClick={e => {
                  e.preventDefault();
                  if (typeof window !== 'undefined') {
                    const el = document.getElementById('features');
                    if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
                  }
                }}
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors cursor-pointer"
              >
                Saiba mais
              </a>
            </div>
          </motion.div>

          {/* Image Slider */}
          <motion.div
            className="flex-1 relative"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
            ref={sliderRef}
          >
            {/* Slider visual normal */}
            <div className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700 aspect-[16/10]">
              {/* Browser-like top bar */}
              <div className="h-8 bg-gray-100 dark:bg-gray-700 flex items-center px-4">
                <div className="flex space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div className="ml-4 flex-1 h-5 bg-gray-200 dark:bg-gray-600 rounded-full px-2 flex items-center justify-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate">hightide.site</div>
                </div>
              </div>
              {/* Slider content */}
              <div className="relative" style={{ height: "calc(100% - 2rem)" }}>
                {/* Fundo gradiente */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-primary-100 to-primary-200 dark:from-primary-900/20 dark:via-primary-800/20 dark:to-primary-700/20"></div>

                {/* Gradiente radial centralizado */}
                <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-current opacity-10 text-primary-400 dark:text-primary-600"></div>

                {/* Círculo translúcido - Top Right */}
                <div className="absolute top-0 right-0 w-32 h-32 rounded-full opacity-20 bg-primary-400 dark:bg-primary-600"></div>

                {/* Círculo translúcido - Bottom Left */}
                <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full opacity-20 bg-primary-300 dark:bg-primary-500"></div>

                {/* Elementos decorativos */}
                <div className="absolute top-4 right-4 w-16 h-16 rounded-full blur-xl bg-primary-200/30 dark:bg-primary-800/30"></div>
                <div className="absolute bottom-4 left-4 w-12 h-12 rounded-full blur-lg bg-primary-300/30 dark:bg-primary-700/30"></div>

                {/* Imagem ativa - clicável para expandir */}
                <div className="relative z-10 w-full h-full flex items-center justify-center p-6 overflow-hidden">
                  <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20 dark:border-gray-700/20 relative overflow-hidden w-full h-full flex items-center justify-center shadow-inner">
                    <div className="absolute inset-0 rounded-xl shadow-inner pointer-events-none"></div>
                    {sliderImages[currentSlide] && (
                      <img
                        src={sliderImages[currentSlide].src}
                        alt={sliderImages[currentSlide].alt}
                        className={`w-full h-full object-contain object-center rounded-lg transition-all duration-500 relative cursor-zoom-in
                          ${fade ? 'opacity-100 translate-x-0' : slideDir === 1 ? 'opacity-0 translate-x-12' : 'opacity-0 -translate-x-12'}`}
                        style={{
                          imageRendering: 'crisp-edges',
                          maxWidth: '100%',
                          maxHeight: '100%'
                        }}
                        loading="eager"
                        onClick={() => { setModalOpen(true); setModalSlide(currentSlide); }}
                        tabIndex={0}
                        aria-label="Expandir imagem do sistema"
                      />
                    )}
                    {/* Overlay com título e descrição */}
                    <div className="absolute bottom-0 left-0 p-6 right-0 z-20">
                      <div className="bg-black/60 backdrop-blur-sm rounded-lg p-4">
                        <h3 className="text-xl font-bold text-white mb-2">{sliderImages[currentSlide]?.title}</h3>
                        <p className="text-gray-200 text-sm">{sliderImages[currentSlide]?.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Botões de navegação */}
                <button
                  className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full text-white flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 bg-primary-500/80 hover:bg-primary-600/90"
                  onClick={prevSlide}
                  aria-label="Slide anterior"
                >
                  <ChevronLeft size={16} />
                </button>
                <button
                  className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full text-white flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 bg-primary-500/80 hover:bg-primary-600/90"
                  onClick={nextSlide}
                  aria-label="Próximo slide"
                >
                  <ChevronRight size={16} />
                </button>
                {/* Autoplay control */}
                <button
                  className="absolute right-2 bottom-2 w-8 h-8 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center text-white hover:bg-white/30 transition-colors z-20"
                  onClick={toggleAutoplay}
                  aria-label={isPlaying ? "Pausar apresentação" : "Iniciar apresentação"}
                >
                  {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                </button>
                {/* Dots navigation */}
                <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
                  {sliderImages.map((_, index) => (
                    <button
                      key={index}
                      className={`w-2 h-2 rounded-full transition-all ${
                        currentSlide === index
                          ? "w-6 bg-primary-500"
                          : "bg-primary-300 hover:bg-primary-400"
                      }`}
                      onClick={() => goToSlide(index)}
                      aria-label={`Ir para slide ${index + 1}`}
                    />
                  ))}
                </div>
                {/* Floating elements */}
                <div className="absolute top-2 left-2 w-3 h-3 rounded-full animate-pulse bg-primary-400/60 dark:bg-primary-400/40"></div>
                <div className="absolute top-6 right-10 w-2 h-2 rounded-full animate-pulse delay-300 bg-primary-300/60 dark:bg-primary-300/40"></div>
                <div className="absolute bottom-6 left-7 w-2 h-2 rounded-full animate-pulse delay-500 bg-primary-500/60 dark:bg-primary-500/40"></div>
              </div>
            </div>
            {/* Modal de imagem expandida */}
            <Dialog open={modalOpen} onOpenChange={setModalOpen}>
              <DialogContent className="max-w-4xl w-[80vw] max-h-[80vh] flex flex-col items-center justify-center bg-white dark:bg-gray-900 rounded-2xl shadow-2xl p-0 border-0">
                <DialogTitle className="sr-only">{sliderImages[modalSlide]?.title || 'Visualização de imagem do sistema'}</DialogTitle>
                <div className="flex flex-col items-center justify-center w-full h-full">
                  <div className="relative flex flex-row items-center justify-center w-full min-h-[40vh]" style={{minHeight:'40vh'}}>
                    {/* Botão de fechar sobre a imagem, canto superior direito */}
                    <button
                      onClick={() => setModalOpen(false)}
                      className="absolute z-50 top-2 right-2 w-10 h-10 flex items-center justify-center rounded-full bg-black/80 text-white shadow-lg hover:bg-black focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all"
                      style={{boxShadow:'0 4px 24px rgba(0,0,0,0.25)'}}
                      aria-label="Fechar modal"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    {/* Seta esquerda */}
                    <button
                      className="z-30 w-10 h-10 rounded-full bg-black/60 text-white flex items-center justify-center hover:bg-black/80 transition-colors mr-2"
                      onClick={prevModalSlide}
                      aria-label="Slide anterior"
                      tabIndex={modalOpen ? 0 : -1}
                      style={{flex:'0 0 auto'}}>
                      <ChevronLeft size={24} />
                    </button>
                    {/* Imagem centralizada */}
                    <img
                      src={sliderImages[modalSlide]?.src}
                      alt={sliderImages[modalSlide]?.alt}
                      className="rounded-2xl shadow-xl object-contain max-w-[78vw] max-h-[70vh] border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 mx-auto"
                      style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.25)' }}
                    />
                    {/* Seta direita */}
                    <button
                      className="z-30 w-10 h-10 rounded-full bg-black/60 text-white flex items-center justify-center hover:bg-black/80 transition-colors ml-2"
                      onClick={nextModalSlide}
                      aria-label="Próximo slide"
                      tabIndex={modalOpen ? 0 : -1}
                      style={{flex:'0 0 auto'}}>
                      <ChevronRight size={24} />
                    </button>
                  </div>
                  <div className="mt-4 text-center">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{sliderImages[modalSlide]?.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">{sliderImages[modalSlide]?.description}</p>
                  </div>
                  {/* Dots navigation no modal */}
                  <div className="flex space-x-2 mt-4">
                    {sliderImages.map((_, index) => (
                      <button
                        key={index}
                        className={`w-3 h-3 rounded-full transition-all border-2 ${
                          modalSlide === index
                            ? "bg-primary-500 border-primary-500"
                            : "bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-primary-300"
                        }`}
                        onClick={() => goToModalSlide(index)}
                        aria-label={`Ir para slide ${index + 1}`}
                        tabIndex={modalOpen ? 0 : -1}
                      />
                    ))}
                  </div>
                </div>
              </DialogContent>
            </Dialog>
            {/* Fim do modal */}
            {/* Decorative elements */}
            <div className="absolute -z-10 -right-4 -bottom-4 w-full h-full bg-gradient-to-br from-primary-100/50 to-primary-200/50 dark:from-primary-800/30 dark:to-primary-900/30 rounded-2xl"></div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
