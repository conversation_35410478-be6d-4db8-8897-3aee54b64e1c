"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/components/people/ContactsTab.js":
/*!**********************************************!*\
  !*** ./src/components/people/ContactsTab.js ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,FileText,Loader2,Mail,Phone,Trash,User,UserPlus,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _app_modules_people_services_contactsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/modules/people/services/contactsService */ \"(app-pages-browser)/./src/app/modules/people/services/contactsService.js\");\n/* harmony import */ var _components_common_MaskedInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/common/MaskedInput */ \"(app-pages-browser)/./src/components/common/MaskedInput.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ContactsTab = (param)=>{\n    let { personId, onClose, isCreating = false, onAddTempContact, tempContacts = [] } = param;\n    _s();\n    const [contacts, setContacts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [contactFormOpen, setContactFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedContact, setSelectedContact] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contactToDelete, setContactToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [notesModalOpen, setNotesModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedNotes, setSelectedNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        notes: \"\"\n    });\n    // Contact form state\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        relationship: \"\",\n        email: \"\",\n        phone: \"\",\n        notes: \"\"\n    });\n    const [formErrors, setFormErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ContactsTab.useEffect\": ()=>{\n            if (personId) {\n                loadContacts();\n            }\n        }\n    }[\"ContactsTab.useEffect\"], [\n        personId\n    ]);\n    const loadContacts = async ()=>{\n        if (!personId) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const data = await _app_modules_people_services_contactsService__WEBPACK_IMPORTED_MODULE_3__.contactsService.getContactsByPerson(personId);\n            setContacts(data || []);\n        } catch (err) {\n            console.error(\"Error fetching contacts:\", err);\n            setError(\"Não foi possível carregar os contatos.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleOpenContactForm = function() {\n        let contact = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : null;\n        if (contact) {\n            setFormData({\n                name: contact.name || \"\",\n                relationship: contact.relationship || \"\",\n                email: contact.email || \"\",\n                phone: contact.phone || \"\",\n                notes: contact.notes || \"\"\n            });\n            setSelectedContact(contact);\n        } else {\n            setFormData({\n                name: \"\",\n                relationship: \"\",\n                email: \"\",\n                phone: \"\",\n                notes: \"\"\n            });\n            setSelectedContact(null);\n        }\n        setFormErrors({});\n        setContactFormOpen(true);\n    };\n    const handleCloseContactForm = ()=>{\n        setContactFormOpen(false);\n        setSelectedContact(null);\n        setFormData({\n            name: \"\",\n            relationship: \"\",\n            email: \"\",\n            phone: \"\",\n            notes: \"\"\n        });\n        setFormErrors({});\n    };\n    const handleDeleteContact = (contact)=>{\n        setContactToDelete(contact.id);\n        setConfirmDialogOpen(true);\n    };\n    const handleViewNotes = (contact)=>{\n        setSelectedNotes({\n            name: contact.name,\n            notes: contact.notes || \"\"\n        });\n        setNotesModalOpen(true);\n    };\n    const confirmDeleteContact = async ()=>{\n        if (!contactToDelete) return;\n        try {\n            await _app_modules_people_services_contactsService__WEBPACK_IMPORTED_MODULE_3__.contactsService.deleteContact(contactToDelete);\n            setContacts(contacts.filter((c)=>c.id !== contactToDelete));\n            setConfirmDialogOpen(false);\n            setContactToDelete(null);\n        } catch (err) {\n            console.error(\"Error deleting contact:\", err);\n            setError(\"Não foi possível excluir o contato.\");\n        }\n    };\n    const handleFormChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        // Clear error when field is modified\n        if (formErrors[name]) {\n            setFormErrors((prev)=>({\n                    ...prev,\n                    [name]: undefined\n                }));\n        }\n    };\n    const validateContactForm = ()=>{\n        const errors = {};\n        if (!formData.name.trim()) {\n            errors.name = \"Nome é obrigatório\";\n        }\n        if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            errors.email = \"Email inválido\";\n        }\n        setFormErrors(errors);\n        return Object.keys(errors).length === 0;\n    };\n    const handleSubmitContact = async (e)=>{\n        e.preventDefault();\n        if (!validateContactForm()) {\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            const payload = {\n                personId: personId,\n                name: formData.name,\n                relationship: formData.relationship || null,\n                email: formData.email || null,\n                phone: formData.phone ? formData.phone.replace(/\\D/g, \"\") : null,\n                notes: formData.notes || null\n            };\n            if (isCreating) {\n                // Modo de criação - adicionar contato temporário\n                if (onAddTempContact) {\n                    onAddTempContact({\n                        id: \"temp-\".concat(Date.now()),\n                        ...payload\n                    });\n                }\n                handleCloseContactForm();\n            } else {\n                // Modo de edição - salvar no banco de dados\n                if (selectedContact) {\n                    // Update existing contact\n                    await _app_modules_people_services_contactsService__WEBPACK_IMPORTED_MODULE_3__.contactsService.updateContact(selectedContact.id, payload);\n                } else {\n                    // Create new contact\n                    await _app_modules_people_services_contactsService__WEBPACK_IMPORTED_MODULE_3__.contactsService.createContact(payload);\n                }\n                // Reload contacts and close form\n                loadContacts();\n                handleCloseContactForm();\n            }\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error saving contact:\", err);\n            setFormErrors({\n                submit: ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Erro ao salvar contato\"\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const formatPhone = (phone)=>{\n        if (!phone) return \"N/A\";\n        // Phone format: (00) 00000-0000\n        const phoneNumbers = phone.replace(/\\D/g, '');\n        return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n    };\n    // Se não estiver no modo de criação e não tiver personId, mostrar mensagem\n    if (!isCreating && !personId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 222,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Contatos\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center\",\n                    children: \"Salve os dados b\\xe1sicos da pessoa antes de adicionar contatos.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onClose(),\n                    className: \"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                    children: \"Voltar para Informa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n            lineNumber: 220,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                                children: \"Contatos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined),\n                            isCreating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\",\n                                children: tempContacts.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 242,\n                                columnNumber: 13\n                            }, undefined) : isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: 16,\n                                className: \"animate-spin text-neutral-400 dark:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\",\n                                children: contacts.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleOpenContactForm(),\n                        className: \"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Adicionar Contato\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 238,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\",\n                children: [\n                    error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadContacts,\n                        className: \"ml-2 underline hover:no-underline\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 267,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, undefined),\n            isCreating ? // Modo de criação - mostrar contatos temporários\n            tempContacts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 48,\n                        className: \"text-neutral-300 dark:text-gray-600 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 281,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\",\n                        children: \"Nenhum contato\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 282,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\",\n                        children: \"Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de refer\\xeancia.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 283,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleOpenContactForm(),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 290,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Adicionar Contato\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 291,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 286,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 280,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"bg-neutral-50 dark:bg-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Nome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 299,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Relacionamento\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 302,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Telefone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"A\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 311,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 298,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 297,\n                            columnNumber: 15\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\",\n                            children: tempContacts.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-neutral-50 dark:hover:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\",\n                                                        children: contact.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-neutral-700 dark:text-gray-200\",\n                                                        children: contact.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 320,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 319,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\",\n                                                children: contact.relationship || \"Não especificado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 328,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 327,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: contact.email ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 text-neutral-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-primary-600 dark:text-primary-400\",\n                                                        children: contact.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 334,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400 dark:text-gray-500\",\n                                                children: \"N\\xe3o informado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 341,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: contact.phone ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3 w-3 text-neutral-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-700 dark:text-gray-300\",\n                                                        children: formatPhone(contact.phone)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 346,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400 dark:text-gray-500\",\n                                                children: \"N\\xe3o informado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 353,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 344,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleOpenContactForm(contact),\n                                                        className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                        title: \"Editar\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteContact(contact),\n                                                        className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                        title: \"Excluir\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 357,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 356,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, contact.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 316,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 296,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 295,\n                columnNumber: 11\n            }, undefined) : isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 32,\n                            className: \"text-primary-500 dark:text-primary-400 animate-spin mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600 dark:text-gray-300\",\n                            children: \"Carregando contatos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 382,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 381,\n                columnNumber: 9\n            }, undefined) : contacts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 48,\n                        className: \"text-neutral-300 dark:text-gray-600 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 389,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\",\n                        children: \"Nenhum contato\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\",\n                        children: \"Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de refer\\xeancia.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleOpenContactForm(),\n                        className: \"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 398,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Adicionar Contato\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 394,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 388,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"bg-neutral-50 dark:bg-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Nome\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 407,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Relacionamento\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 410,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Email\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 413,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Telefone\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 416,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"A\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 419,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\",\n                            children: contacts.map((contact)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-neutral-50 dark:hover:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\",\n                                                        children: contact.name.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-neutral-700 dark:text-gray-200\",\n                                                        children: contact.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 428,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\",\n                                                children: contact.relationship || \"Não especificado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 436,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 435,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: contact.email ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-3 w-3 text-neutral-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"mailto:\".concat(contact.email),\n                                                        className: \"text-primary-600 dark:text-primary-400 hover:underline\",\n                                                        children: contact.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 442,\n                                                columnNumber: 23\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400 dark:text-gray-500\",\n                                                children: \"N\\xe3o informado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 449,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: contact.phone ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-3 w-3 text-neutral-400 dark:text-gray-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"tel:\".concat(contact.phone),\n                                                        className: \"text-neutral-700 dark:text-gray-300\",\n                                                        children: formatPhone(contact.phone)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 454,\n                                                columnNumber: 23\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-neutral-400 dark:text-gray-500\",\n                                                children: \"N\\xe3o informado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 461,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 452,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleOpenContactForm(contact),\n                                                        className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                        title: \"Editar\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewNotes(contact),\n                                                        className: \"p-1 \".concat(contact.notes ? 'text-neutral-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400' : 'text-neutral-300 dark:text-gray-600 cursor-not-allowed'),\n                                                        title: contact.notes ? \"Ver Observações\" : \"Sem Observações\",\n                                                        disabled: !contact.notes,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDeleteContact(contact),\n                                                        className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                        title: \"Excluir\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 465,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 464,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, contact.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                    lineNumber: 426,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                    lineNumber: 404,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, undefined),\n            contactFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[13000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50\",\n                        onClick: handleCloseContactForm\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 500,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed left-[50%] top-[50%] z-[13050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-orange-300 dark:border-orange-600 bg-background shadow-lg duration-200 rounded-xl max-w-2xl max-h-[90vh] flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pb-4 border-b-2 border-orange-400 dark:border-orange-500 flex-shrink-0 px-6 pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg text-white\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-orange-800 dark:text-white border-l-4 border-orange-400 dark:border-orange-500 pl-3\",\n                                                    children: selectedContact ? 'Editar Contato' : 'Adicionar Contato'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3\",\n                                                    children: selectedContact ? 'Modifique as informações do contato' : 'Adicione um novo contato'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col h-full max-h-[calc(90vh-200px)]\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b-2 border-orange-400 dark:border-orange-500 pb-2 mb-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-lg font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-orange-500 pl-3\",\n                                                            children: \"Informa\\xe7\\xf5es do Contato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-neutral-600 dark:text-gray-300 pl-3\",\n                                                            children: \"Dados de contato da pessoa:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            formErrors.submit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        size: 16\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formErrors.submit\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 535,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                                id: \"contact-form\",\n                                                onSubmit: handleSubmitContact,\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                htmlFor: \"name\",\n                                                                children: \"Nome *\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                            lineNumber: 548,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 547,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"name\",\n                                                                        name: \"name\",\n                                                                        type: \"text\",\n                                                                        value: formData.name,\n                                                                        onChange: handleFormChange,\n                                                                        className: \"block w-full pl-10 pr-3 py-2 border \".concat(formErrors.name ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600', \" rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"),\n                                                                        placeholder: \"Nome do contato\",\n                                                                        disabled: isSubmitting\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 550,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            formErrors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-xs text-red-600 dark:text-red-400\",\n                                                                children: formErrors.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 39\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ModuleFormGroup, {\n                                                        moduleColor: \"people\",\n                                                        label: \"Relacionamento\",\n                                                        htmlFor: \"relationship\",\n                                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 568,\n                                                            columnNumber: 25\n                                                        }, void 0),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.ModuleSelect, {\n                                                            moduleColor: \"people\",\n                                                            id: \"relationship\",\n                                                            name: \"relationship\",\n                                                            value: formData.relationship,\n                                                            onChange: handleFormChange,\n                                                            placeholder: \"Selecione o relacionamento\",\n                                                            disabled: isSubmitting,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"\",\n                                                                    children: \"Selecione\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 579,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"C\\xf4njuge\",\n                                                                    children: \"C\\xf4njuge\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Filho/Filha\",\n                                                                    children: \"Filho/Filha\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Pai/M\\xe3e\",\n                                                                    children: \"Pai/M\\xe3e\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Irm\\xe3o/Irm\\xe3\",\n                                                                    children: \"Irm\\xe3o/Irm\\xe3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Av\\xf4/Av\\xf3\",\n                                                                    children: \"Av\\xf4/Av\\xf3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 584,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Tio/Tia\",\n                                                                    children: \"Tio/Tia\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 585,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Primo/Prima\",\n                                                                    children: \"Primo/Prima\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Amigo\",\n                                                                    children: \"Amigo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 587,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Colega\",\n                                                                    children: \"Colega\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"Outro\",\n                                                                    children: \"Outro\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                    lineNumber: 589,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                htmlFor: \"email\",\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                            lineNumber: 599,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 598,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        id: \"email\",\n                                                                        name: \"email\",\n                                                                        type: \"email\",\n                                                                        value: formData.email,\n                                                                        onChange: handleFormChange,\n                                                                        className: \"block w-full pl-10 pr-3 py-2 border \".concat(formErrors.email ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600', \" rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"),\n                                                                        placeholder: \"<EMAIL>\",\n                                                                        disabled: isSubmitting\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            formErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-1 text-xs text-red-600 dark:text-red-400\",\n                                                                children: formErrors.email\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 40\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                htmlFor: \"phone\",\n                                                                children: \"Telefone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_MaskedInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        type: \"phone\",\n                                                                        value: formData.phone,\n                                                                        onChange: (e)=>handleFormChange({\n                                                                                target: {\n                                                                                    name: \"phone\",\n                                                                                    value: e.target.value\n                                                                                }\n                                                                            }),\n                                                                        placeholder: \"(00) 00000-0000\",\n                                                                        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\n                                                                        disabled: isSubmitting\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\n                                                                htmlFor: \"notes\",\n                                                                children: \"Observa\\xe7\\xf5es\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute top-3 left-3 flex items-start pointer-events-none\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-5 w-5 text-gray-400 dark:text-gray-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                            lineNumber: 644,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 643,\n                                                                        columnNumber: 21\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                        id: \"notes\",\n                                                                        name: \"notes\",\n                                                                        value: formData.notes,\n                                                                        onChange: handleFormChange,\n                                                                        className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none\",\n                                                                        placeholder: \"Observa\\xe7\\xf5es sobre o contato\",\n                                                                        rows: 3,\n                                                                        disabled: isSubmitting\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 21\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                        lineNumber: 638,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 541,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 522,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-3 border-t-2 border-orange-400 dark:border-orange-500 pt-4 flex-shrink-0 px-6 pb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: handleCloseContactForm,\n                                                className: \"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\",\n                                                disabled: isSubmitting,\n                                                children: \"Cancelar\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 664,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                form: \"contact-form\",\n                                                onClick: handleSubmitContact,\n                                                className: \"px-4 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors flex items-center gap-2\",\n                                                disabled: isSubmitting,\n                                                children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            size: 16,\n                                                            className: \"animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Salvando...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: selectedContact ? \"Atualizar\" : \"Salvar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                            lineNumber: 687,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                                lineNumber: 672,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 521,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 502,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, undefined),\n            notesModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-black/50\",\n                        onClick: ()=>setNotesModalOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 700,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[11050]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-neutral-800 dark:text-gray-100\",\n                                        children: [\n                                            \"Observa\\xe7\\xf5es de \",\n                                            selectedNotes.name\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setNotesModalOpen(false),\n                                        className: \"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_FileText_Loader2_Mail_Phone_Trash_User_UserPlus_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 711,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 707,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 703,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg border border-neutral-200 dark:border-gray-600 text-neutral-700 dark:text-gray-300 min-h-[100px] whitespace-pre-wrap\",\n                                        children: selectedNotes.notes || \"Nenhuma observação disponível.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 flex justify-end\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setNotesModalOpen(false),\n                                            className: \"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\",\n                                            children: \"Fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                            lineNumber: 721,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                        lineNumber: 720,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                                lineNumber: 715,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                        lineNumber: 702,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 699,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>{\n                    setConfirmDialogOpen(false);\n                    setContactToDelete(null);\n                },\n                onConfirm: confirmDeleteContact,\n                title: \"Excluir Contato\",\n                message: \"Tem certeza que deseja excluir este contato? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\",\n                variant: \"danger\",\n                moduleColor: \"people\",\n                confirmText: \"Excluir\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n                lineNumber: 734,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\ContactsTab.js\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ContactsTab, \"JUnRERwu5cD85ZWmv2fA+UPN8Oo=\");\n_c = ContactsTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ContactsTab);\nvar _c;\n$RefreshReg$(_c, \"ContactsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/people/ContactsTab.js\n"));

/***/ })

});