"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/components/people/DocumentsTab.js":
/*!***********************************************!*\
  !*** ./src/components/people/DocumentsTab.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,Download,Eye,FileText,Loader2,Plus,Trash,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst DocumentsTab = (param)=>{\n    let { personId, onClose, isCreating, onAddTempDocument, tempDocuments = [] } = param;\n    _s();\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const buttonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [needsSave, setNeedsSave] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!personId);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dropdownPosition, setDropdownPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        top: 0,\n        right: 0,\n        width: 0\n    });\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmDialogOpen, setConfirmDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentToDelete, setDocumentToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Upload state\n    const [documentType, setDocumentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadError, setUploadError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentsTab.useEffect\": ()=>{\n            if (personId) {\n                loadDocuments();\n            } else if (isCreating && tempDocuments.length > 0) {\n                // Se estiver no modo de criação e houver documentos temporários, exibi-los\n                const formattedTempDocs = tempDocuments.map({\n                    \"DocumentsTab.useEffect.formattedTempDocs\": (doc, index)=>({\n                            id: \"temp-\".concat(index),\n                            fileName: doc.file.name,\n                            fileSize: doc.file.size,\n                            fileType: doc.file.type,\n                            documentType: doc.type,\n                            createdAt: new Date().toISOString(),\n                            isTemp: true\n                        })\n                }[\"DocumentsTab.useEffect.formattedTempDocs\"]);\n                setDocuments(formattedTempDocs);\n                setIsLoading(false);\n            } else {\n                setIsLoading(false);\n            }\n        }\n    }[\"DocumentsTab.useEffect\"], [\n        personId,\n        isCreating,\n        tempDocuments\n    ]);\n    // Montar o componente apenas no cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentsTab.useEffect\": ()=>{\n            setMounted(true);\n            return ({\n                \"DocumentsTab.useEffect\": ()=>setMounted(false)\n            })[\"DocumentsTab.useEffect\"];\n        }\n    }[\"DocumentsTab.useEffect\"], []);\n    // Calcular a posição do dropdown quando aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentsTab.useEffect\": ()=>{\n            if (dropdownOpen && buttonRef.current) {\n                const rect = buttonRef.current.getBoundingClientRect();\n                setDropdownPosition({\n                    top: rect.bottom + window.scrollY,\n                    right: window.innerWidth - rect.right,\n                    width: Math.max(rect.width, 256) // Mínimo de 256px (w-64)\n                });\n            }\n        }\n    }[\"DocumentsTab.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DocumentsTab.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"DocumentsTab.useEffect.handleClickOutside\": (event)=>{\n                    if (buttonRef.current && !buttonRef.current.contains(event.target) && dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setDropdownOpen(false);\n                    }\n                }\n            }[\"DocumentsTab.useEffect.handleClickOutside\"];\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"DocumentsTab.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"DocumentsTab.useEffect\"];\n        }\n    }[\"DocumentsTab.useEffect\"], []);\n    const loadDocuments = async ()=>{\n        if (!personId) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.get(\"/documents\", {\n                params: {\n                    targetId: personId,\n                    targetType: \"person\"\n                }\n            });\n            setDocuments(response.data || []);\n        } catch (err) {\n            console.error(\"Error fetching documents:\", err);\n            setError(\"Não foi possível carregar os documentos.\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleOpenUploadModal = (type)=>{\n        setDocumentType(type);\n        setSelectedFile(null);\n        setUploadError(null);\n        setDropdownOpen(false);\n        // Trigger file input click\n        if (fileInputRef.current) {\n            fileInputRef.current.click();\n        }\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files[0]) {\n            setSelectedFile(e.target.files[0]);\n            setUploadError(null);\n        }\n    };\n    const handleUploadDocument = async ()=>{\n        if (!selectedFile || !documentType) {\n            setUploadError(\"Selecione um arquivo e um tipo de documento\");\n            return;\n        }\n        setIsUploading(true);\n        setUploadProgress(0);\n        setUploadError(null);\n        // Se estiver no modo de criação, armazenar o documento temporariamente\n        if (isCreating) {\n            console.log('Adicionando documento temporário:', {\n                file: selectedFile,\n                type: documentType\n            });\n            // Adicionar o documento à lista de documentos temporários\n            onAddTempDocument && onAddTempDocument({\n                file: selectedFile,\n                type: documentType\n            });\n            // Adicionar o documento à lista local para exibição\n            const newTempDoc = {\n                id: \"temp-\".concat(Date.now()),\n                fileName: selectedFile.name,\n                fileSize: selectedFile.size,\n                fileType: selectedFile.type,\n                documentType: documentType,\n                createdAt: new Date().toISOString(),\n                isTemp: true\n            };\n            setDocuments((prev)=>[\n                    ...prev,\n                    newTempDoc\n                ]);\n            setSelectedFile(null);\n            setDocumentType(\"\");\n            setIsUploading(false);\n            return;\n        }\n        // Se não estiver no modo de criação, fazer o upload normalmente\n        const formData = new FormData();\n        formData.append(\"documents\", selectedFile);\n        formData.append(\"types\", JSON.stringify([\n            documentType\n        ]));\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.post(\"/documents/upload?targetId=\".concat(personId, \"&targetType=person\"), formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                onUploadProgress: (progressEvent)=>{\n                    const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n                    setUploadProgress(percentCompleted);\n                }\n            });\n            // Reload documents after successful upload\n            loadDocuments();\n            setSelectedFile(null);\n            setDocumentType(\"\");\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Error uploading document:\", err);\n            setUploadError(((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.message) || \"Erro ao fazer upload do documento\");\n        } finally{\n            setIsUploading(false);\n        }\n    };\n    const handleDeleteDocument = (documentId)=>{\n        setDocumentToDelete(documentId);\n        setConfirmDialogOpen(true);\n    };\n    const confirmDeleteDocument = async ()=>{\n        if (!documentToDelete) return;\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_3__.api.delete(\"/documents/\".concat(documentToDelete));\n            setDocuments(documents.filter((doc)=>doc.id !== documentToDelete));\n            setConfirmDialogOpen(false);\n            setDocumentToDelete(null);\n        } catch (err) {\n            console.error(\"Error deleting document:\", err);\n            setError(\"Não foi possível excluir o documento.\");\n        }\n    };\n    const handleViewDocument = (documentId)=>{\n        window.open(\"/api/documents/\".concat(documentId), '_blank');\n    };\n    const documentTypes = [\n        {\n            id: \"RG\",\n            label: \"RG\"\n        },\n        {\n            id: \"CPF\",\n            label: \"CPF\"\n        },\n        {\n            id: \"CNH\",\n            label: \"Carteira de Motorista\"\n        },\n        {\n            id: \"COMP_RESIDENCIA\",\n            label: \"Comprovante de Residência\"\n        },\n        {\n            id: \"CERTIDAO_NASCIMENTO\",\n            label: \"Certidão de Nascimento\"\n        },\n        {\n            id: \"CERTIDAO_CASAMENTO\",\n            label: \"Certidão de Casamento\"\n        },\n        {\n            id: \"CARTAO_VACINACAO\",\n            label: \"Cartão de Vacinação\"\n        },\n        {\n            id: \"PASSAPORTE\",\n            label: \"Passaporte\"\n        },\n        {\n            id: \"TITULO_ELEITOR\",\n            label: \"Título de Eleitor\"\n        },\n        {\n            id: \"CARTEIRA_TRABALHO\",\n            label: \"Carteira de Trabalho\"\n        },\n        {\n            id: \"OUTROS\",\n            label: \"Outros\"\n        }\n    ];\n    const getDocumentTypeDisplay = (type)=>{\n        const found = documentTypes.find((docType)=>docType.id === type);\n        return found ? found.label : type;\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"N/A\";\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_5__.format)(new Date(dateString), \"dd/MM/yyyy\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_6__.ptBR\n            });\n        } catch (error) {\n            return \"Data inválida\";\n        }\n    };\n    // Se não estiver no modo de criação e não tiver ID, exibir mensagem\n    if (!personId && !isCreating) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"Documentos\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center\",\n                    children: \"Salve os dados b\\xe1sicos da pessoa antes de adicionar documentos.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onClose(),\n                    className: \"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\",\n                    children: \"Voltar para Informa\\xe7\\xf5es\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                    lineNumber: 267,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n            lineNumber: 261,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-neutral-800 dark:text-white\",\n                                children: \"Documentos\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                size: 16,\n                                className: \"animate-spin text-neutral-400 dark:text-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\",\n                                children: documents.length\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    ref: buttonRef,\n                                    onClick: ()=>setDropdownOpen(!dropdownOpen),\n                                    className: \"flex items-center gap-2 px-3 py-2 bg-orange-500 dark:bg-orange-600 text-white rounded-lg hover:bg-orange-600 dark:hover:bg-orange-700 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 16\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Adicionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            size: 14,\n                                            className: \"transform transition-transform \".concat(dropdownOpen ? 'rotate-180' : '')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined),\n                                dropdownOpen && mounted && /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: dropdownRef,\n                                    className: \"fixed z-[14000] w-64 bg-white dark:bg-gray-800 rounded-lg shadow-xl border-2 border-orange-300 dark:border-orange-600 overflow-hidden\",\n                                    style: {\n                                        top: \"\".concat(dropdownPosition.top, \"px\"),\n                                        right: \"\".concat(dropdownPosition.right, \"px\"),\n                                        width: \"\".concat(dropdownPosition.width, \"px\")\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 border-b-2 border-orange-400 dark:border-orange-500\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-semibold text-orange-800 dark:text-orange-200 flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        size: 16,\n                                                        className: \"text-orange-600 dark:text-orange-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    \"Selecione o tipo de documento\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"max-h-96 overflow-y-auto\",\n                                            children: documentTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleOpenUploadModal(type.id),\n                                                    className: \"w-full text-left px-4 py-3 hover:bg-orange-50 dark:hover:bg-orange-900/20 text-neutral-700 dark:text-gray-200 flex items-center gap-3 transition-colors border-b border-orange-100 dark:border-orange-800/30 last:border-b-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-1 bg-orange-100 dark:bg-orange-900/30 rounded\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 14,\n                                                                className: \"text-orange-600 dark:text-orange-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: type.label\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, type.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, undefined), document.body)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"file\",\n                ref: fileInputRef,\n                className: \"hidden\",\n                onChange: handleFileChange,\n                accept: \".pdf,.jpg,.jpeg,.png\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 343,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\",\n                children: [\n                    error,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: loadDocuments,\n                        className: \"ml-2 underline hover:no-underline\",\n                        children: \"Tentar novamente\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 355,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 353,\n                columnNumber: 9\n            }, undefined),\n            selectedFile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg shadow-sm dark:shadow-md dark:shadow-black/20 p-4 border border-neutral-200 dark:border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-neutral-800 dark:text-gray-100\",\n                                                children: selectedFile.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                lineNumber: 373,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-neutral-500 dark:text-gray-400\",\n                                                children: [\n                                                    getDocumentTypeDisplay(documentType),\n                                                    \" • \",\n                                                    (selectedFile.size / 1024 / 1024).toFixed(2),\n                                                    \" MB\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                lineNumber: 374,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSelectedFile(null),\n                                    className: \"p-2 text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 385,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, undefined),\n                    uploadError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded text-sm\",\n                        children: uploadError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, undefined),\n                    isUploading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full bg-primary-500 dark:bg-primary-600 rounded-full\",\n                                    style: {\n                                        width: \"\".concat(uploadProgress, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                    lineNumber: 399,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 398,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-right text-xs text-neutral-500 dark:text-gray-400\",\n                                children: [\n                                    uploadProgress,\n                                    \"%\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 397,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleUploadDocument,\n                            disabled: isUploading,\n                            className: \"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2 disabled:opacity-50\",\n                            children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 16,\n                                        className: \"animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Enviando...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 419,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 423,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Enviar documento\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 424,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 410,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 366,\n                columnNumber: 9\n            }, undefined),\n            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            size: 32,\n                            className: \"text-primary-500 dark:text-primary-400 animate-spin mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-neutral-600 dark:text-gray-300\",\n                            children: \"Carregando documentos...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 437,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 434,\n                columnNumber: 9\n            }, undefined) : documents.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 48,\n                        className: \"text-neutral-300 dark:text-gray-600 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 442,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\",\n                        children: \"Nenhum documento\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\",\n                        children: \"Adicione documentos importantes como RG, CPF, comprovantes e outros para esta pessoa.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 444,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 sm:grid-cols-3 gap-2 max-w-2xl\",\n                        children: documentTypes.slice(0, 6).map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleOpenUploadModal(type.id),\n                                className: \"flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-neutral-600 dark:text-gray-300 text-center\",\n                                        children: type.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 454,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-primary-500 dark:text-primary-400 mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, type.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 449,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                        lineNumber: 447,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 441,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"bg-neutral-50 dark:bg-gray-900\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Tipo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 465,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Arquivo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"Data de Upload\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\",\n                                        children: \"A\\xe7\\xf5es\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                lineNumber: 464,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 463,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\",\n                            children: documents.map((document1)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"hover:bg-neutral-50 dark:hover:bg-gray-700 \".concat(document1.isTemp ? 'bg-yellow-50 dark:bg-yellow-900/20' : ''),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-200\",\n                                                    children: document1.isTemp ? document1.documentType : getDocumentTypeDisplay(document1.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                document1.isTemp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"ml-2 px-2 py-1 text-xs rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300\",\n                                                    children: \"Pendente\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 482,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 text-neutral-400 dark:text-gray-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-neutral-700 dark:text-gray-200\",\n                                                        children: document1.isTemp ? document1.fileName : document1.filename\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap text-neutral-500 dark:text-gray-400\",\n                                            children: formatDate(document1.createdAt)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 500,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"px-4 py-3 whitespace-nowrap text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end\",\n                                                children: document1.isTemp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-amber-600 dark:text-amber-400\",\n                                                    children: \"Ser\\xe1 salvo quando a pessoa for criada\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 25\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewDocument(document1.id),\n                                                            className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                            title: \"Visualizar\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                            lineNumber: 511,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(\"/api/documents/\".concat(document1.id, \"?download=true\"), '_blank'),\n                                                            className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                                            title: \"Baixar\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDeleteDocument(document1.id),\n                                                            className: \"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                                            title: \"Excluir\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_Download_Eye_FileText_Loader2_Plus_Trash_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                size: 16\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                                lineNumber: 504,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, document1.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                                    lineNumber: 481,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                    lineNumber: 462,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 461,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isOpen: confirmDialogOpen,\n                onClose: ()=>{\n                    setConfirmDialogOpen(false);\n                    setDocumentToDelete(null);\n                },\n                onConfirm: confirmDeleteDocument,\n                title: \"Excluir Documento\",\n                message: \"Tem certeza que deseja excluir este documento? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\",\n                variant: \"danger\",\n                moduleColor: \"people\",\n                confirmText: \"Excluir\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n                lineNumber: 544,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\people\\\\DocumentsTab.js\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentsTab, \"bhUnZhNZn+mSZy+2/SHZLlsk3jA=\");\n_c = DocumentsTab;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DocumentsTab);\nvar _c;\n$RefreshReg$(_c, \"DocumentsTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/people/DocumentsTab.js\n"));

/***/ })

});