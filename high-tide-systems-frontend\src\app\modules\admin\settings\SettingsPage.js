"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  Loader2,
  AlertCircle,
  Settings,
  Database,
  Shield,
  Server,
  Building,
  Mail,
  Globe,
  Download,
  Trash,
  RefreshCw,
  SlidersHorizontal,
} from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import BranchesTab from "@/components/settings/BranchesTab";
import CompanyManagementTab from "@/components/settings/CompanyManagementTab";
import EmailSettingsTab from "@/components/settings/EmailSettingsTab";

import { ModuleHeader, ModuleInput, ModuleSelect, ModuleFormGroup, ModuleLabel, ModuleTable, ModuleTabs } from "@/components/ui";
import PreferencesPanel from "@/components/admin/PreferencesPanel";

// Componente SecurityTab
const SecurityTab = ({ isSystemAdmin, isCompanyAdmin }) => {
  const { toast_success, toast_error } = useToast();
  const [securitySettings, setSecuritySettings] = useState({
    requireUppercase: true,
    requireNumber: true,
    requireSpecialChar: false,
    minPasswordLength: 8,
    passwordExpirationDays: 90,
    limitLoginAttempts: true,
    maxLoginAttempts: 5,
    enforceIpTracking: true,
    enforceSessionTimeout: true,
    sessionTimeoutMinutes: 30
  });
  const [originalSettings, setOriginalSettings] = useState({});
  const [securityLogs, setSecurityLogs] = useState([]);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [isLoadingLogs, setIsLoadingLogs] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalLogs, setTotalLogs] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Carregar configurações de segurança
  useEffect(() => {
    loadSecuritySettings();
    loadSecurityLogs();
  }, []);

  const loadSecuritySettings = async () => {
    try {
      // Para system_admin, verificar se há empresa selecionada
      const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');
      if (currentUser?.role === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {
        // Se system_admin não tem empresa selecionada, não carregar configurações
        setSecuritySettings({
          requireUppercase: true,
          requireNumber: true,
          requireSpecialChar: false,
          minPasswordLength: 8,
          passwordExpirationDays: 90,
          limitLoginAttempts: true,
          maxLoginAttempts: 5,
          enforceIpTracking: true,
          enforceSessionTimeout: true,
          sessionTimeoutMinutes: 30
        });
        setOriginalSettings({});
        return;
      }

      const response = await api.get('/settings/security');
      if (response.data.success) {
        setSecuritySettings(response.data.data);
        setOriginalSettings(response.data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar configurações de segurança:', error);
      // Em caso de erro, usar configurações padrão
      setSecuritySettings({
        requireUppercase: true,
        requireNumber: true,
        requireSpecialChar: false,
        minPasswordLength: 8,
        passwordExpirationDays: 90,
        limitLoginAttempts: true,
        maxLoginAttempts: 5,
        enforceIpTracking: true,
        enforceSessionTimeout: true,
        sessionTimeoutMinutes: 30
      });
      setOriginalSettings({});
    } finally {
      setIsLoadingSettings(false);
    }
  };

  const loadSecurityLogs = async (page = currentPage, perPage = itemsPerPage) => {
    try {
      const response = await api.get(`/settings/security/logs?page=${page}&limit=${perPage}`);
      if (response.data.success) {
        setSecurityLogs(response.data.data.logs);
        setTotalLogs(response.data.data.pagination.total);
        setTotalPages(response.data.data.pagination.pages);
        setCurrentPage(response.data.data.pagination.page);
      }
    } catch (error) {
      console.error('Erro ao carregar logs de segurança:', error);
      setSecurityLogs([]);
    } finally {
      setIsLoadingLogs(false);
    }
  };

  const saveSecuritySettings = async () => {
    try {
      setIsLoadingSettings(true);

      // Para system_admin, verificar se há empresa selecionada
      const selectedCompanyId = localStorage.getItem('systemAdminSelectedCompany');
      if (currentUser?.role === 'SYSTEM_ADMIN' && (!selectedCompanyId || selectedCompanyId === 'null')) {
        toast_error('Selecione uma empresa para salvar as configurações de segurança');
        return;
      }

      // Identificar apenas os campos que mudaram
      const changedSettings = {};
      Object.keys(securitySettings).forEach(key => {
        if (securitySettings[key] !== originalSettings[key]) {
          changedSettings[key] = securitySettings[key];
        }
      });

      // Se nada mudou, não fazer requisição
      if (Object.keys(changedSettings).length === 0) {
        toast_success('Nenhuma alteração detectada');
        return;
      }

      const response = await api.put('/settings/security', changedSettings);
      if (response.data.success) {
        toast_success('Configurações de segurança salvas com sucesso!');
        setOriginalSettings(securitySettings);
        loadSecurityLogs(1, itemsPerPage);
      }
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
      toast_error('Erro ao salvar configurações de segurança');
    } finally {
      setIsLoadingSettings(false);
    }
  };

  const handleSettingChange = (key, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="space-y-6">
      <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2">
        <Shield className="h-5 w-5 text-gray-500 dark:text-gray-400" />
        Configurações de Segurança
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <div>
            <ModuleLabel moduleColor="admin">
              Política de Senhas
            </ModuleLabel>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireUppercase"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.requireUppercase}
                  onChange={(e) => handleSettingChange('requireUppercase', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="requireUppercase" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Exigir pelo menos uma letra maiúscula
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireNumber"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.requireNumber}
                  onChange={(e) => handleSettingChange('requireNumber', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="requireNumber" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Exigir pelo menos um número
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireSpecialChar"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.requireSpecialChar}
                  onChange={(e) => handleSettingChange('requireSpecialChar', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="requireSpecialChar" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Exigir pelo menos um caractere especial
                </label>
              </div>
            </div>
          </div>

          <div>
            <ModuleLabel htmlFor="minPasswordLength" moduleColor="admin">
              Tamanho mínimo da senha
            </ModuleLabel>
            <ModuleInput
              id="minPasswordLength"
              moduleColor="admin"
              type="number"
              min="6"
              max="32"
              value={securitySettings.minPasswordLength}
              onChange={(e) => handleSettingChange('minPasswordLength', parseInt(e.target.value))}
              disabled={!isSystemAdmin && !isCompanyAdmin}
            />
          </div>

          <div>
            <ModuleLabel htmlFor="passwordExpiration" moduleColor="admin">
              Expiração de senha (dias)
            </ModuleLabel>
            <ModuleInput
              id="passwordExpiration"
              moduleColor="admin"
              type="number"
              min="0"
              max="365"
              value={securitySettings.passwordExpirationDays}
              onChange={(e) => handleSettingChange('passwordExpirationDays', parseInt(e.target.value))}
              disabled={!isSystemAdmin && !isCompanyAdmin}
            />
            <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
              0 = Sem expiração
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <ModuleLabel moduleColor="admin">
              Segurança de Login
            </ModuleLabel>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="limitLoginAttempts"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.limitLoginAttempts}
                  onChange={(e) => handleSettingChange('limitLoginAttempts', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="limitLoginAttempts" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Limitar tentativas de login
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enforceIpTracking"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.enforceIpTracking}
                  onChange={(e) => handleSettingChange('enforceIpTracking', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="enforceIpTracking" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Rastrear IPs de login
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enforceSessionTimeout"
                  className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                  checked={securitySettings.enforceSessionTimeout}
                  onChange={(e) => handleSettingChange('enforceSessionTimeout', e.target.checked)}
                  disabled={!isSystemAdmin && !isCompanyAdmin}
                />
                <label htmlFor="enforceSessionTimeout" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  Encerrar sessões inativas
                </label>
              </div>
            </div>
          </div>

          <div>
            <ModuleLabel htmlFor="maxLoginAttempts" moduleColor="admin">
              Máximo de tentativas de login
            </ModuleLabel>
            <ModuleInput
              id="maxLoginAttempts"
              moduleColor="admin"
              type="number"
              min="3"
              max="10"
              value={securitySettings.maxLoginAttempts}
              onChange={(e) => handleSettingChange('maxLoginAttempts', parseInt(e.target.value))}
              disabled={!isSystemAdmin && !isCompanyAdmin}
            />
          </div>

          <div>
            <ModuleLabel htmlFor="sessionTimeout" moduleColor="admin">
              Tempo de inatividade até logout (minutos)
            </ModuleLabel>
            <ModuleInput
              id="sessionTimeout"
              moduleColor="admin"
              type="number"
              min="5"
              max="240"
              value={securitySettings.sessionTimeoutMinutes}
              onChange={(e) => handleSettingChange('sessionTimeoutMinutes', parseInt(e.target.value))}
              disabled={!isSystemAdmin && !isCompanyAdmin}
            />
          </div>
        </div>
      </div>
            <div className="flex justify-end mt-6">
        <button
          onClick={saveSecuritySettings}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors"
          disabled={(!isSystemAdmin && !isCompanyAdmin) || isLoadingSettings}
        >
          {isLoadingSettings ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Shield className="h-4 w-4" />
          )}
          <span>Salvar Configurações</span>
        </button>
      </div>

      {/* Logs de Segurança */}
      <div className="mt-8">
        <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4">
          <Shield className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          Logs de Segurança
        </h3>

        {isLoadingLogs ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-gray-500" />
          </div>
        ) : (
          <ModuleTable
            moduleColor="admin"
            title="Logs de Segurança"
            columns={[
              { header: 'Data/Hora', field: 'createdAt', width: '15%' },
              { header: 'Usuário', field: 'user', width: '15%' },
              { header: 'Ação', field: 'action', width: '15%' },
              { header: 'Detalhes', field: 'details', width: '35%' },
              { header: 'Status', field: 'status', width: '10%' },
              { header: 'IP', field: 'ipAddress', width: '10%' }
            ]}
            data={securityLogs}
            isLoading={isLoadingLogs}
            emptyMessage="Nenhum log de segurança encontrado"
            emptyIcon={<Shield size={24} />}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalLogs}
            onPageChange={(page) => loadSecurityLogs(page, itemsPerPage)}
            showPagination={true}
            tableId="security-logs-table"
            enableColumnToggle={true}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={(newItemsPerPage) => {
              setItemsPerPage(newItemsPerPage);
              loadSecurityLogs(1, newItemsPerPage);
            }}
            renderRow={(log, index, moduleColors, visibleColumns) => (
              <tr key={log.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes('createdAt') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {new Date(log.createdAt).toLocaleString('pt-BR')}
                  </td>
                )}
                {visibleColumns.includes('user') && (
                  <td className="px-4 py-4 text-sm text-neutral-800 dark:text-neutral-100">
                    {log.user?.fullName || (log.details?.identifier ? log.details.identifier : 'Sistema')}
                  </td>
                )}
                {visibleColumns.includes('action') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {log.action === 'LOGIN_ATTEMPT' ? 'Tentativa de Login' :
                     log.action === 'SECURITY_SETTINGS_UPDATED' ? 'Configurações Alteradas' :
                     log.action === 'ACCOUNT_LOCKED' ? 'Conta Bloqueada' :
                     log.action === 'PASSWORD_EXPIRED' ? 'Senha Expirada' :
                     log.action}
                  </td>
                )}
                {visibleColumns.includes('details') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {log.action === 'SECURITY_SETTINGS_UPDATED' && log.details?.description ? (
                      <div className="text-xs">
                        <span className="font-medium">Alterações:</span>
                        <div className="mt-1">
                          {log.details.description}
                        </div>
                        {log.details.source && (
                          <div className="mt-1 text-neutral-400 dark:text-neutral-500">
                            Local: {log.details.source}
                          </div>
                        )}
                      </div>
                    ) : log.action === 'SECURITY_SETTINGS_UPDATED' && log.details?.updatedFields ? (
                      <div className="text-xs">
                        <span className="font-medium">Configurações de Segurança:</span>
                        <div className="mt-1">
                          {log.details.updatedFields.map(field => {
                            const fieldNames = {
                              requireUppercase: 'Exigir letra maiúscula',
                              requireNumber: 'Exigir número',
                              requireSpecialChar: 'Exigir caractere especial',
                              minPasswordLength: 'Tamanho mínimo da senha',
                              passwordExpirationDays: 'Expiração de senha',
                              limitLoginAttempts: 'Limitar tentativas de login',
                              maxLoginAttempts: 'Máximo de tentativas',
                              enforceIpTracking: 'Rastrear IPs',
                              enforceSessionTimeout: 'Timeout de sessão',
                              sessionTimeoutMinutes: 'Tempo de inatividade',
                              accountLockoutDuration: 'Duração do bloqueio'
                            };
                            return fieldNames[field] || field;
                          }).join(', ')}
                        </div>
                      </div>
                    ) : log.action === 'LOGIN_ATTEMPT' && log.details?.identifier ? (
                      <span className="text-xs">Email: {log.details.identifier}</span>
                    ) : (
                      <span className="text-xs text-neutral-400">-</span>
                    )}
                  </td>
                )}
                {visibleColumns.includes('status') && (
                  <td className="px-4 py-4">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      log.status === 'SUCCESS' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400' :
                      log.status === 'FAILURE' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400' :
                      'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-400'
                    }`}>
                      {log.status === 'SUCCESS' ? 'Sucesso' :
                       log.status === 'FAILURE' ? 'Falhou' :
                       log.status === 'WARNING' ? 'Aviso' :
                       log.status}
                    </span>
                  </td>
                )}
                {visibleColumns.includes('ipAddress') && (
                  <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                    {log.ipAddress || '-'}
                  </td>
                )}
              </tr>
            )}
          />
        )}
      </div>


    </div>
  );
};

const SettingsPage = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("general");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [generalSettings, setGeneralSettings] = useState({
    siteName: "High Tide",
    siteUrl: "https://hightide.site",
    adminEmail: "<EMAIL>",
    allowRegistration: true,
    defaultTimeZone: "America/Sao_Paulo",
    dateFormat: "DD/MM/YYYY",
    timeFormat: "24h",
    backupEnabled: true,
    backupFrequency: "daily",
    backupTime: "01:00",
    maxFileSize: 5, // Em MB
    allowedFileTypes: "jpg,png,pdf,doc,docx,xls,xlsx",
    logRetention: 90, // Dias
  });

  // Define user role constants
  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";
  const isCompanyAdmin = user?.role === "COMPANY_ADMIN";

  // Determine which tabs are available based on user role
  const availableTabs = {
    general: isSystemAdmin || isCompanyAdmin,
    companies: isSystemAdmin || isCompanyAdmin,
    branches: isSystemAdmin || isCompanyAdmin,
    email: isSystemAdmin || isCompanyAdmin,
    backup: isSystemAdmin, // Only system admin can see backup settings
    security: isSystemAdmin || isCompanyAdmin,
    preferencias: true, // Nova aba
  };

  // Load data for the active tab
  useEffect(() => {
    // Auto-select the first available tab if the current active tab is not available
    const tabKeys = Object.keys(availableTabs);
    const firstAvailableTab = tabKeys.find((tab) => availableTabs[tab]);

    if (!availableTabs[activeTab] && firstAvailableTab) {
      setActiveTab(firstAvailableTab);
      return;
    }

    if (activeTab === "general") {
      loadGeneralSettings();
    }
  }, [activeTab, user]);

  // Load general settings
  const loadGeneralSettings = async () => {
    setIsLoading(true);
    try {
      // This would typically be an API call
      // For now, we'll just simulate loading with a timeout
      setTimeout(() => {
        setGeneralSettings({
          siteName: "High Tide",
          siteUrl: "https://dentrodascasinhas.com.br",
          adminEmail: "<EMAIL>",
          allowRegistration: true,
          defaultTimeZone: "America/Sao_Paulo",
          dateFormat: "DD/MM/YYYY",
          timeFormat: "24h",
          backupEnabled: true,
          backupFrequency: "daily",
          backupTime: "01:00",
          maxFileSize: 5,
          allowedFileTypes: "jpg,png,pdf,doc,docx,xls,xlsx",
          logRetention: 90,
        });
        setIsLoading(false);
      }, 500);
    } catch (error) {
      console.error("Error loading settings:", error);
      setError("Falha ao carregar configurações");
      setIsLoading(false);
    }
  };

  // Function to save general settings
  const saveGeneralSettings = async () => {
    setIsLoading(true);
    try {
      // This would be an API call in production
      // For now, we just simulate with a timeout
      setTimeout(() => {
        alert("Configurações salvas com sucesso!");
        setIsLoading(false);
      }, 500);
    } catch (error) {
      console.error("Error saving settings:", error);
      setError("Falha ao salvar configurações");
      setIsLoading(false);
    }
  };

  // Get tab label based on user role
  const getTabLabel = (tabKey) => {
    const labels = {
      general: "Geral",
      companies: isSystemAdmin ? "Empresas" : "Minha Empresa",
      branches: "Unidades",
      email: "Email",
      backup: "Backup",
      security: "Segurança",
      preferencias: "Preferências",
    };

    return labels[tabKey] || tabKey;
  };

  // Get tab icon
  const getTabIcon = (tabKey) => {
    const icons = {
      general: <Settings className="h-5 w-5" />,
      companies: <Building className="h-5 w-5" />,
      branches: <Building className="h-5 w-5" />,
      email: <Mail className="h-5 w-5" />,
      backup: <Database className="h-5 w-5" />,
      security: <Shield className="h-5 w-5" />,
      preferencias: <SlidersHorizontal className="h-5 w-5" />,
    };

    return icons[tabKey] || <Settings className="h-5 w-5" />;
  };

  // Preparar as tabs para o componente ModuleTabs
  const tabsConfig = Object.keys(availableTabs)
    .filter(tabKey => availableTabs[tabKey])
    .map(tabKey => ({
      id: tabKey,
      label: getTabLabel(tabKey),
      icon: getTabIcon(tabKey)
    }));

  // Import tutorial steps from tutorialMapping
  const settingsTutorialSteps = useMemo(() => {
    // Import dynamically to avoid circular dependencies
    const tutorialMap = require('@/tutorials/tutorialMapping').default;
    return tutorialMap['/dashboard/admin/settings'] || [];
  }, []);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
          <Settings size={24} className="mr-2 text-gray-600 dark:text-gray-400" />
          Configurações do Sistema
        </h1>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-admin-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4">
        <ModuleTabs
          tabs={tabsConfig}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          moduleColor="admin"
        />
      </div>

      {/* Content Area */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 overflow-hidden">

        {/* Content Area */}
        <div className="p-6">
          {error && (
            <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
              <AlertCircle size={18} />
              <span>{error}</span>
            </div>
          )}

          {isLoading &&
          activeTab !== "companies" &&
          activeTab !== "branches" &&
          activeTab !== "email" ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500 dark:text-gray-400" />
            </div>
          ) : (
            <>
              {/* General Settings */}
              {activeTab === "general" && (
                <div className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2">
                          <Settings className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          Configurações Básicas
                        </h3>

                        <div className="space-y-4">
                          <ModuleFormGroup>
                            <ModuleLabel
                              htmlFor="siteName"
                              moduleColor="admin"
                            >
                              Nome do Site
                            </ModuleLabel>
                            <ModuleInput
                              id="siteName"
                              moduleColor="admin"
                              type="text"
                              value={generalSettings.siteName}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  siteName: e.target.value,
                                })
                              }
                              disabled={!isSystemAdmin}
                            />
                          </ModuleFormGroup>

                          <ModuleFormGroup>
                            <ModuleLabel
                              htmlFor="siteUrl"
                              moduleColor="admin"
                            >
                              URL do Site
                            </ModuleLabel>
                            <ModuleInput
                              id="siteUrl"
                              moduleColor="admin"
                              type="url"
                              value={generalSettings.siteUrl}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  siteUrl: e.target.value,
                                })
                              }
                              disabled={!isSystemAdmin}
                            />
                          </ModuleFormGroup>

                          <div>
                            <label
                              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                              htmlFor="adminEmail"
                            >
                              Email do Administrador
                            </label>
                            <input
                              id="adminEmail"
                              type="email"
                              value={generalSettings.adminEmail}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  adminEmail: e.target.value,
                                })
                              }
                              className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                              disabled={!isSystemAdmin}
                            />
                          </div>

                          {isSystemAdmin && (
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id="allowRegistration"
                                checked={generalSettings.allowRegistration}
                                onChange={(e) =>
                                  setGeneralSettings({
                                    ...generalSettings,
                                    allowRegistration: e.target.checked,
                                  })
                                }
                                className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                              />
                              <label
                                htmlFor="allowRegistration"
                                className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                              >
                                Permitir cadastro de novos usuários
                              </label>
                            </div>
                          )}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2">
                          <Globe className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                          Localização e Formato
                        </h3>

                        <div className="space-y-4">
                          <div>
                            <label
                              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                              htmlFor="defaultTimeZone"
                            >
                              Fuso Horário Padrão
                            </label>
                            <select
                              id="defaultTimeZone"
                              value={generalSettings.defaultTimeZone}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  defaultTimeZone: e.target.value,
                                })
                              }
                              className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                            >
                              <option value="America/Sao_Paulo">
                                América/São Paulo
                              </option>
                              <option value="America/Recife">
                                América/Recife
                              </option>
                              <option value="America/Manaus">
                                América/Manaus
                              </option>
                              <option value="America/Belem">
                                América/Belém
                              </option>
                            </select>
                          </div>

                          <div>
                            <label
                              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                              htmlFor="dateFormat"
                            >
                              Formato de Data
                            </label>
                            <select
                              id="dateFormat"
                              value={generalSettings.dateFormat}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  dateFormat: e.target.value,
                                })
                              }
                              className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                            >
                              <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                              <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                              <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                            </select>
                          </div>

                          <div>
                            <label
                              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                              htmlFor="timeFormat"
                            >
                              Formato de Hora
                            </label>
                            <select
                              id="timeFormat"
                              value={generalSettings.timeFormat}
                              onChange={(e) =>
                                setGeneralSettings({
                                  ...generalSettings,
                                  timeFormat: e.target.value,
                                })
                              }
                              className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                            >
                              <option value="24h">24h (ex: 14:30)</option>
                              <option value="12h">12h (ex: 2:30 PM)</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>

                    {isSystemAdmin && (
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2">
                            <Database className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                            Backup
                          </h3>

                          <div className="space-y-4">
                            <div className="flex items-center">
                              <input
                                type="checkbox"
                                id="backupEnabled"
                                checked={generalSettings.backupEnabled}
                                onChange={(e) =>
                                  setGeneralSettings({
                                    ...generalSettings,
                                    backupEnabled: e.target.checked,
                                  })
                                }
                                className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                              />
                              <label
                                htmlFor="backupEnabled"
                                className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                              >
                                Ativar backup automático
                              </label>
                            </div>

                            {generalSettings.backupEnabled && (
                              <>
                                <div>
                                  <label
                                    className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                                    htmlFor="backupFrequency"
                                  >
                                    Frequência
                                  </label>
                                  <select
                                    id="backupFrequency"
                                    value={generalSettings.backupFrequency}
                                    onChange={(e) =>
                                      setGeneralSettings({
                                        ...generalSettings,
                                        backupFrequency: e.target.value,
                                      })
                                    }
                                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                                  >
                                    <option value="daily">Diário</option>
                                    <option value="weekly">Semanal</option>
                                    <option value="monthly">Mensal</option>
                                  </select>
                                </div>

                                <div>
                                  <label
                                    className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                                    htmlFor="backupTime"
                                  >
                                    Horário
                                  </label>
                                  <input
                                    id="backupTime"
                                    type="time"
                                    value={generalSettings.backupTime}
                                    onChange={(e) =>
                                      setGeneralSettings({
                                        ...generalSettings,
                                        backupTime: e.target.value,
                                      })
                                    }
                                    className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                                  />
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 mb-4 flex items-center gap-2">
                            <Server className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                            Armazenamento e Logs
                          </h3>

                          <div className="space-y-4">
                            <div>
                              <label
                                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                                htmlFor="maxFileSize"
                              >
                                Tamanho máximo de arquivo (MB)
                              </label>
                              <input
                                id="maxFileSize"
                                type="number"
                                min="1"
                                max="100"
                                value={generalSettings.maxFileSize}
                                onChange={(e) =>
                                  setGeneralSettings({
                                    ...generalSettings,
                                    maxFileSize: parseInt(e.target.value),
                                  })
                                }
                                className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                              />
                            </div>

                            <div>
                              <label
                                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                                htmlFor="allowedFileTypes"
                              >
                                Tipos de arquivo permitidos
                              </label>
                              <input
                                id="allowedFileTypes"
                                type="text"
                                value={generalSettings.allowedFileTypes}
                                onChange={(e) =>
                                  setGeneralSettings({
                                    ...generalSettings,
                                    allowedFileTypes: e.target.value,
                                  })
                                }
                                className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                                placeholder="jpg,png,pdf,doc,..."
                              />
                              <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
                                Separados por vírgula, sem pontos ou espaços
                              </p>
                            </div>

                            <div>
                              <label
                                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
                                htmlFor="logRetention"
                              >
                                Período de retenção de logs (dias)
                              </label>
                              <input
                                id="logRetention"
                                type="number"
                                min="1"
                                max="365"
                                value={generalSettings.logRetention}
                                onChange={(e) =>
                                  setGeneralSettings({
                                    ...generalSettings,
                                    logRetention: parseInt(e.target.value),
                                  })
                                }
                                className="block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-gray-500 dark:focus:ring-gray-600 focus:border-primary-500 dark:focus:border-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end">
                    <button
                      onClick={saveGeneralSettings}
                      className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors"
                      disabled={!isSystemAdmin && !isCompanyAdmin}
                    >
                      <Loader2
                        className={`h-4 w-4 ${
                          isLoading ? "animate-spin" : "hidden"
                        }`}
                      />
                      <span>Salvar Configurações</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Companies Tab */}
              {activeTab === "companies" && <CompanyManagementTab />}

              {/* Branches Tab */}
              {activeTab === "branches" && <BranchesTab />}

              {/* Email Tab */}
              {activeTab === "email" && <EmailSettingsTab />}

              {/* Backup Tab - Only for System Admin */}
              {activeTab === "backup" && isSystemAdmin && (
                <div className="space-y-6">
                  <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2">
                    <Database className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    Configurações de Backup
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <ModuleLabel
                          htmlFor="backupStorage"
                          moduleColor="admin"
                        >
                          Armazenamento de Backup
                        </ModuleLabel>
                        <ModuleSelect
                          id="backupStorage"
                          moduleColor="admin"
                          defaultValue="local"
                        >
                          <option value="local">Servidor Local</option>
                          <option value="s3">Amazon S3</option>
                          <option value="azure">Azure Blob Storage</option>
                          <option value="google">Google Cloud Storage</option>
                        </ModuleSelect>
                      </div>

                      <div>
                        <ModuleLabel
                          htmlFor="backupDirectory"
                          moduleColor="admin"
                        >
                          Diretório Local
                        </ModuleLabel>
                        <ModuleInput
                          id="backupDirectory"
                          moduleColor="admin"
                          type="text"
                          defaultValue="/var/backups/dentrodascasinhas"
                          placeholder="/var/backups/dentrodascasinhas"
                        />
                      </div>

                      <div>
                        <ModuleLabel
                          htmlFor="backupRetention"
                          moduleColor="admin"
                        >
                          Retenção de Backups (dias)
                        </ModuleLabel>
                        <ModuleInput
                          id="backupRetention"
                          moduleColor="admin"
                          type="number"
                          defaultValue="30"
                          min="1"
                          max="365"
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <ModuleLabel
                          htmlFor="backupCompression"
                          moduleColor="admin"
                        >
                          Compressão
                        </ModuleLabel>
                        <ModuleSelect
                          id="backupCompression"
                          moduleColor="admin"
                          defaultValue="gzip"
                        >
                          <option value="gzip">GZIP</option>
                          <option value="bzip2">BZIP2</option>
                          <option value="none">Sem compressão</option>
                        </ModuleSelect>
                      </div>

                      <div>
                        <ModuleLabel moduleColor="admin">
                          Criptografia
                        </ModuleLabel>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="encryptBackup"
                            className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                          />
                          <label
                            htmlFor="encryptBackup"
                            className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                          >
                            Ativar criptografia de backup
                          </label>
                        </div>
                      </div>

                      <div>
                        <ModuleLabel moduleColor="admin">
                          Notificações de Backup
                        </ModuleLabel>
                        <div className="space-y-2">
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="notifySuccess"
                              className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                              defaultChecked
                            />
                            <label
                              htmlFor="notifySuccess"
                              className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                            >
                              Notificar backups bem-sucedidos
                            </label>
                          </div>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="notifyFailure"
                              className="h-4 w-4 text-gray-600 dark:text-gray-500 focus:ring-gray-500 dark:focus:ring-gray-600 rounded bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                              defaultChecked
                            />
                            <label
                              htmlFor="notifyFailure"
                              className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
                            >
                              Notificar falhas de backup
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Lista de Backups com ModuleTable */}
                  <div className="mt-8">
                    <h3 className="text-base font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2 mb-4">
                      <Database className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      Backups Disponíveis
                    </h3>

                    <ModuleTable
                      moduleColor="admin"
                      title="Histórico de Backups"
                      columns={[
                        { header: 'Nome', field: 'name', width: '25%' },
                        { header: 'Data', field: 'date', width: '15%' },
                        { header: 'Tamanho', field: 'size', width: '10%' },
                        { header: 'Status', field: 'status', width: '10%' },
                        { header: 'Tipo', field: 'type', width: '15%' },
                        { header: 'Ações', field: 'actions', width: '15%', sortable: false }
                      ]}
                      data={[
                        { id: 1, name: 'backup_completo_20240601', date: '01/06/2024 01:00', size: '1.2 GB', status: 'Concluído', type: 'Completo' },
                        { id: 2, name: 'backup_completo_20240531', date: '31/05/2024 01:00', size: '1.1 GB', status: 'Concluído', type: 'Completo' },
                        { id: 3, name: 'backup_completo_20240530', date: '30/05/2024 01:00', size: '1.1 GB', status: 'Concluído', type: 'Completo' },
                        { id: 4, name: 'backup_incremental_20240529', date: '29/05/2024 01:00', size: '250 MB', status: 'Concluído', type: 'Incremental' },
                        { id: 5, name: 'backup_completo_20240528', date: '28/05/2024 01:00', size: '1.0 GB', status: 'Concluído', type: 'Completo' }
                      ]}
                      isLoading={false}
                      emptyMessage="Nenhum backup encontrado"
                      emptyIcon={<Database size={24} />}
                      currentPage={1}
                      totalPages={1}
                      totalItems={5}
                      showPagination={false}
                      tableId="admin-backups-table"
                      enableColumnToggle={true}
                      renderRow={(backup, index, moduleColors, visibleColumns) => (
                        <tr key={backup.id} className={moduleColors.hoverBg}>
                          {visibleColumns.includes('name') && (
                            <td className="px-4 py-4">
                              <div className="flex items-center">
                                <Database className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-2" />
                                <span className="text-sm font-medium text-neutral-800 dark:text-neutral-100">{backup.name}</span>
                              </div>
                            </td>
                          )}
                          {visibleColumns.includes('date') && (
                            <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                              {backup.date}
                            </td>
                          )}
                          {visibleColumns.includes('size') && (
                            <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                              {backup.size}
                            </td>
                          )}
                          {visibleColumns.includes('status') && (
                            <td className="px-4 py-4">
                              <span className="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400">
                                {backup.status}
                              </span>
                            </td>
                          )}
                          {visibleColumns.includes('type') && (
                            <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                              {backup.type}
                            </td>
                          )}
                          {visibleColumns.includes('actions') && (
                            <td className="px-4 py-4 text-right">
                              <div className="flex justify-end gap-2">
                                <button
                                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-gray-500 dark:hover:text-gray-400 transition-colors"
                                  title="Restaurar"
                                >
                                  <RefreshCw size={16} />
                                </button>
                                <button
                                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                                  title="Download"
                                >
                                  <Download size={16} />
                                </button>
                                <button
                                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                                  title="Excluir"
                                >
                                  <Trash size={16} />
                                </button>
                              </div>
                            </td>
                          )}
                        </tr>
                      )}
                    />
                  </div>

                  <div className="mt-6 flex gap-3">
                    <button className="flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors">
                      <Database className="h-4 w-4" />
                      <span>Executar Backup Agora</span>
                    </button>
                    <button className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600 text-white rounded-lg hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-600 dark:hover:to-gray-700 transition-colors">
                      <Settings className="h-4 w-4" />
                      <span>Salvar Configurações</span>
                    </button>
                  </div>
                </div>
              )}

              {/* Security Tab */}
              {activeTab === "security" && (
                <SecurityTab 
                  isSystemAdmin={isSystemAdmin}
                  isCompanyAdmin={isCompanyAdmin}
                />
              )}

              {/* Preferencias Tab */}
              {activeTab === "preferencias" && <PreferencesPanel />}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
