"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Loader2,
  Shield,
  AlertCircle,
  Settings,
  Users,
  DollarSign,
  Calendar,
  CheckSquare,
  ChevronDown,
  ChevronRight,
  Info,
  Search,
  UserCheck,
  Bell,
} from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import { userService } from "@/app/modules/admin/services/userService";
import {
  PERMISSIONS_CONFIG,
  getAllPermissions,
  getActiveModules,
} from "@/utils/permissionConfig";

const PermissionsModal = ({ isOpen, onClose, user, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [selectedPermissions, setSelectedPermissions] = useState([]);
  const [expandedModules, setExpandedModules] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPermissions, setFilteredPermissions] = useState([]);

  // Carregar permissões do usuário ao abrir o modal
  useEffect(() => {
    if (user && isOpen) {
      setIsLoading(true);

      // Reset completo do estado primeiro
      setExpandedModules({});
      setSearchTerm("");
      setError("");
      setFilteredPermissions(getAllPermissions());

      // Se usuário já tem módulos/permissões, configuramos o estado inicial
      if (user.permissions) {
        setSelectedPermissions(user.permissions);
      } else {
        // Se não tiver permissões, inicializamos com array vazio
        setSelectedPermissions([]);
      }

      setIsLoading(false);
    }
  }, [user, isOpen]);

  // Filtragem de permissões baseada na busca
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredPermissions(getAllPermissions());
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = getAllPermissions().filter(
      (permission) =>
        permission.name.toLowerCase().includes(lowerSearch) ||
        permission.description.toLowerCase().includes(lowerSearch) ||
        permission.id.toLowerCase().includes(lowerSearch) ||
        PERMISSIONS_CONFIG[permission.moduleId].name
          .toLowerCase()
          .includes(lowerSearch)
    );

    setFilteredPermissions(filtered);

    // Expande módulos apenas quando há busca ativa
    const modulesToExpand = {};
    filtered.forEach((permission) => {
      modulesToExpand[permission.moduleId] = true;
    });

    setExpandedModules(modulesToExpand);
  }, [searchTerm]);

  const isAdmin = currentUser?.modules?.includes("ADMIN");

  // Verificar se o usuário tem uma permissão específica
  const hasPermission = (permissionId) => {
    return selectedPermissions.includes(permissionId);
  };

  // Alternar uma permissão específica
  const togglePermission = (permissionId) => {
    setSelectedPermissions((prev) => {
      if (prev.includes(permissionId)) {
        return prev.filter((id) => id !== permissionId);
      } else {
        return [...prev, permissionId];
      }
    });
  };

  // Alternar todas as permissões de um módulo - usando a mesma lógica do UserPermissionsTab
  const toggleModulePermissions = (moduleId) => {
    let modulePermissions = filteredPermissions.filter(
      (p) => p.moduleId === moduleId && !p.id.startsWith('notifications.')
    );
    
    // Se for BASIC, excluir permissões que também existem em ADMIN
    if (moduleId === 'BASIC') {
      const adminPermissionIds = filteredPermissions
        .filter(p => p.moduleId === 'ADMIN')
        .map(p => p.id);
      
      modulePermissions = modulePermissions.filter(
        p => !adminPermissionIds.includes(p.id)
      );
    }
    
    const modulePermissionIds = modulePermissions.map(p => p.id);
    const allSelected = modulePermissionIds.every(id => selectedPermissions.includes(id));
    
    if (allSelected) {
      setSelectedPermissions(prev => 
        prev.filter(id => !modulePermissionIds.includes(id))
      );
    } else {
      setSelectedPermissions(prev => 
        [...new Set([...prev, ...modulePermissionIds])]
      );
    }
  };

  // Alternar a expansão de um módulo
  const toggleModuleExpansion = (moduleId) => {
    setExpandedModules((prev) => ({
      ...prev,
      [moduleId]: !prev[moduleId],
    }));
  };

  // Salvar permissões
  const handleSave = async () => {
    setIsSaving(true);
    setError("");

    try {
      await userService.updatePermissions(user.id, selectedPermissions);

      onSuccess();
    } catch (error) {
      console.error("Erro ao atualizar permissões:", error);
      setError(error.response?.data?.message || "Erro ao atualizar permissões");
    } finally {
      setIsSaving(false);
    }
  };

  // Obter o ícone do módulo
  const getModuleIcon = (moduleId) => {
    const icons = {
      ADMIN: <Settings className="h-5 w-5" />,
      RH: <Users className="h-5 w-5" />,
      FINANCIAL: <DollarSign className="h-5 w-5" />,
      SCHEDULING: <Calendar className="h-5 w-5" />,
      PEOPLE: <UserCheck className="h-5 w-5" />,
      BASIC: <CheckSquare className="h-5 w-5" />,
    };

    return icons[moduleId] || <Shield className="h-5 w-5" />;
  };

  // Renderizar as permissões de um módulo
  const renderModulePermissions = (moduleId) => {
    const module = PERMISSIONS_CONFIG[moduleId];
    if (!module) return null;

    // Se estiver filtrando, mostrar apenas as permissões que correspondem à busca
    let permissions = searchTerm
      ? module.permissions.filter((p) =>
          filteredPermissions.some((fp) => fp.id === p.id)
        )
      : module.permissions;

    // Se for BASIC, remover permissões que também existem em ADMIN ou NOTIFICATIONS
    if (moduleId === 'BASIC') {
      const adminPermissionIds = PERMISSIONS_CONFIG['ADMIN']?.permissions.map(p => p.id) || [];
      const notificationPermissionIds = filteredPermissions
        .filter(p => p.id.startsWith('notifications.'))
        .map(p => p.id);
      
      permissions = permissions.filter(
        p => !adminPermissionIds.includes(p.id) && !notificationPermissionIds.includes(p.id)
      );
    }

    if (permissions.length === 0) return null;

    const allPermissionsSelected = permissions.every((p) =>
      selectedPermissions.includes(p.id)
    );

    const somePermissionsSelected = permissions.some((p) =>
      selectedPermissions.includes(p.id)
    );

    return (
      <div key={moduleId} className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
        {/* Cabeçalho do módulo */}
        <div
          className={`p-4 flex items-center justify-between cursor-pointer border-b dark:border-gray-700 ${
            moduleId === "ADMIN" ? "bg-gray-200 dark:bg-gray-800/60" :
            moduleId === "PEOPLE" ? "bg-orange-300 dark:bg-orange-700/60" :
            moduleId === "SCHEDULING" ? "bg-purple-100 dark:bg-purple-900/30" :
            moduleId === "BASIC" ? "bg-gray-100 dark:bg-gray-800/40" :
            "bg-neutral-50 dark:bg-gray-800"
          }`}
          onClick={() => toggleModuleExpansion(moduleId)}
        >
          <div className="flex items-center gap-3">
            <div
              className={`p-2 rounded-full ${
                somePermissionsSelected
                  ? allPermissionsSelected
                    ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                    : "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400"
                  : "bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400"
              }`}
            >
              {getModuleIcon(moduleId)}
            </div>
            <div>
              <h3 className="font-medium text-neutral-800 dark:text-gray-200">{module.name}</h3>
              <p className="text-sm text-neutral-500 dark:text-gray-400">
                {somePermissionsSelected
                  ? `${
                      selectedPermissions.filter((p) =>
                        permissions.some((mp) => mp.id === p)
                      ).length
                    } de ${permissions.length} permissões selecionadas`
                  : "Nenhuma permissão selecionada"}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                toggleModulePermissions(moduleId);
              }}
              className={`px-3 py-1 rounded text-sm font-medium ${
                allPermissionsSelected
                  ? "bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
                  : "bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700"
              }`}
            >
              {allPermissionsSelected ? "Desmarcar todas" : "Selecionar todas"}
            </button>

            {expandedModules[moduleId] ? (
              <ChevronDown className="text-neutral-600 dark:text-gray-400" />
            ) : (
              <ChevronRight className="text-neutral-600 dark:text-gray-400" />
            )}
          </div>
        </div>

        {/* Lista de permissões do módulo */}
        {expandedModules[moduleId] && (
          <div className="p-4 divide-y dark:divide-gray-700 dark:bg-gray-850">
            {permissions.map((permission) => (
              <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-0.5">
                    <input
                      type="checkbox"
                      id={permission.id}
                      checked={hasPermission(permission.id)}
                      onChange={() => togglePermission(permission.id)}
                      className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-primary-500 focus:ring-primary-500 dark:bg-gray-700 dark:checked:bg-primary-500"
                    />
                  </div>

                  <div className="flex-1">
                    <label
                      htmlFor={permission.id}
                      className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                    >
                      {permission.name}
                    </label>
                    <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                      {permission.description}
                    </p>
                    <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                      ID: {permission.id}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (typeof window === "undefined") return null;
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50 dark:bg-black/70" onClick={onClose}></div>

      <div className="fixed left-[50%] top-[50%] z-[12050] w-full translate-x-[-50%] translate-y-[-50%] border-2 border-gray-300 dark:border-gray-600 bg-background shadow-lg duration-200 rounded-xl max-w-5xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b-2 border-gray-400 dark:border-gray-500 flex-shrink-0">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-slate-500 to-slate-600 rounded-lg text-white">
              <Shield className="h-5 w-5" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-slate-800 dark:text-white border-l-4 border-gray-400 dark:border-gray-500 pl-3">
                Gerenciar Permissões
              </h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 pl-3">
                Configure as permissões específicas do usuário
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="flex-1 overflow-y-auto p-6 bg-background min-h-0">
          {error && (
            <div className="mb-6 p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg flex items-center gap-2 dark:bg-red-900/20 dark:border-red-800/50 dark:text-red-400">
              <AlertCircle size={18} />
              <span>{error}</span>
            </div>
          )}

          <div className="mb-6">
            {/* Nome do usuário em destaque */}
            <h4 className="text-xl font-bold text-neutral-800 dark:text-white mb-1 border-l-4 border-slate-500 pl-3">
              {user?.fullName}
            </h4>
            <p className="text-sm text-neutral-600 dark:text-gray-400 mb-4">
              Configure as permissões específicas que este usuário terá acesso
              dentro de cada módulo:
            </p>

            <div className="bg-amber-50 border border-amber-200 p-2 rounded-lg flex items-start gap-2 mb-6 dark:bg-amber-900/20 dark:border-amber-800/50">
              <div className="flex-shrink-0 mt-0.5">
                <Info className="h-4 w-4 text-amber-500 dark:text-amber-400" />
              </div>
              <div>
                <h5 className="text-sm font-medium text-amber-800 dark:text-amber-300">Importante</h5>
                <p className="text-xs text-amber-700 dark:text-amber-400">
                  As permissões só serão aplicadas se o usuário também tiver
                  acesso ao módulo correspondente. Certifique-se de que o
                  usuário tenha os módulos necessários atribuídos.
                </p>
              </div>
            </div>

            {/* Barra de pesquisa */}
            <div className="relative mb-6">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-neutral-400 dark:text-gray-500" />
              </div>
              <input
                type="text"
                placeholder="Buscar permissões..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-gray-600 bg-white dark:bg-gray-700 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:text-gray-200 dark:placeholder-gray-400"
              />
              {searchTerm && (
                <button
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setSearchTerm("")}
                >
                  <X className="h-5 w-5 text-neutral-400 hover:text-neutral-600 dark:text-gray-500 dark:hover:text-gray-400" />
                </button>
              )}
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-primary-500 dark:text-primary-400" />
            </div>
          ) : (
            <div className="space-y-6">
              {/* Seção Módulos Atribuídos */}
              <div className="bg-transparent border border-gray-300 dark:border-gray-600 p-3 rounded-lg mb-6">
                <h4 className="font-medium text-neutral-700 dark:text-gray-300 mb-2">
                  Módulos Atribuídos
                </h4>
                <div className="flex flex-wrap gap-2">
                  {user?.modules?.filter(moduleId => getActiveModules().includes(moduleId)).map((moduleId) => (
                    <div
                      key={moduleId}
                      className={`px-3 py-1.5 border rounded-full flex items-center gap-2 ${
                        moduleId === "ADMIN" ? "bg-gray-200 dark:bg-gray-800/60 border-gray-300 dark:border-gray-700/60" :
                        moduleId === "SCHEDULING" ? "bg-purple-100 dark:bg-purple-900/30 border-purple-300 dark:border-purple-700" :
                        moduleId === "PEOPLE" ? "bg-orange-300 dark:bg-orange-700/60 border-orange-400 dark:border-orange-600/70" :
                        moduleId === "BASIC" ? "bg-gray-100 dark:bg-gray-800/40 border-gray-300 dark:border-gray-600" :
                        "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
                      }`}
                    >
                      {getModuleIcon(moduleId)}
                      <span className="text-sm dark:text-gray-300">
                        {PERMISSIONS_CONFIG[moduleId]?.name || moduleId}
                      </span>
                    </div>
                  ))}
                  {(!user?.modules || user.modules.filter(m => getActiveModules().includes(m)).length === 0) && (
                    <p className="text-sm text-neutral-500 dark:text-gray-400">
                      Nenhum módulo atribuído
                    </p>
                  )}
                </div>
              </div>

              {/* Lista de permissões por módulo */}
              {searchTerm ? (
                getActiveModules()
                  .filter((moduleId) =>
                    PERMISSIONS_CONFIG[moduleId] &&
                    PERMISSIONS_CONFIG[moduleId].permissions.some((p) =>
                      filteredPermissions.some((fp) => fp.id === p.id) && !fp.id.startsWith('notifications.')
                    )
                  )
                  .map((moduleId) => renderModulePermissions(moduleId))
              ) : (
                getActiveModules()
                  .filter(moduleId => PERMISSIONS_CONFIG[moduleId])
                  .map((moduleId) => renderModulePermissions(moduleId))
              )}

              {/* Seção especial para permissões de notificação */}
              {(() => {
                const notificationPermissions = filteredPermissions.filter(
                  (p) => p.id.startsWith('notifications.')
                );
                
                if (notificationPermissions.length > 0) {
                  return (
                    <div className="mb-6 border rounded-lg overflow-hidden dark:border-gray-700">
                      <div
                        className="relative bg-neutral-50 dark:bg-gray-800 p-4 flex items-center justify-between border-b dark:border-gray-700 cursor-pointer overflow-hidden"
                        onClick={() => toggleModuleExpansion('NOTIFICATIONS')}
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/20 via-blue-500/20 to-purple-500/20"></div>
                        <div className="flex items-center gap-3 relative z-10">
                          <div className={`p-2 rounded-full ${
                            notificationPermissions.some(p => selectedPermissions.includes(p.id))
                              ? notificationPermissions.every(p => selectedPermissions.includes(p.id))
                                ? "bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400"
                                : "bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400"
                              : "bg-neutral-100 text-neutral-600 dark:bg-gray-700 dark:text-gray-400"
                          }`}>
                            <Bell className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-medium text-neutral-800 dark:text-gray-200">Notificações</h3>
                            <p className="text-sm text-neutral-500 dark:text-gray-400">
                              {notificationPermissions.some(p => selectedPermissions.includes(p.id))
                                ? `${selectedPermissions.filter(p => p.startsWith('notifications.')).length} de ${notificationPermissions.length} permissões selecionadas`
                                : "Nenhuma permissão selecionada"}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3 relative z-10">
                          <button
                            type="button"
                            onClick={(e) => {
                              e.stopPropagation();
                              const notificationPermissionIds = notificationPermissions.map(p => p.id);
                              const allSelected = notificationPermissionIds.every(id => selectedPermissions.includes(id));
                              
                              if (allSelected) {
                                setSelectedPermissions(prev => 
                                  prev.filter(id => !notificationPermissionIds.includes(id))
                                );
                              } else {
                                setSelectedPermissions(prev => 
                                  [...new Set([...prev, ...notificationPermissionIds])]
                                );
                              }
                            }}
                            className={`px-3 py-1 rounded text-sm font-medium ${
                              notificationPermissions.every(p => selectedPermissions.includes(p.id))
                                ? "bg-red-500 text-white hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
                                : "bg-slate-500 text-white hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700"
                            }`}
                          >
                            {notificationPermissions.every(p => selectedPermissions.includes(p.id)) ? "Desmarcar todas" : "Selecionar todas"}
                          </button>
                          {expandedModules['NOTIFICATIONS'] ? (
                            <ChevronDown className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-neutral-500 dark:text-gray-400" />
                          )}
                        </div>
                      </div>
                      {expandedModules['NOTIFICATIONS'] && (
                        <div className="p-4 divide-y divide-gray-200 dark:divide-gray-700 dark:bg-gray-850">
                          {notificationPermissions.map((permission) => (
                            <div key={permission.id} className="py-3 first:pt-0 last:pb-0">
                              <div className="flex items-start gap-3">
                                <div className="flex-shrink-0 mt-0.5">
                                  <input
                                    type="checkbox"
                                    id={permission.id}
                                    checked={selectedPermissions.includes(permission.id)}
                                    onChange={() => togglePermission(permission.id)}
                                    className="h-5 w-5 rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:checked:bg-blue-600"
                                  />
                                </div>
                                <div className="flex-1">
                                  <label
                                    htmlFor={permission.id}
                                    className="block font-medium text-neutral-800 dark:text-gray-200 cursor-pointer"
                                  >
                                    {permission.name}
                                  </label>
                                  <p className="mt-1 text-sm text-neutral-600 dark:text-gray-400">
                                    {permission.description}
                                  </p>
                                  <div className="mt-1 text-xs text-neutral-500 dark:text-gray-500">
                                    ID: {permission.id}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                }
                return null;
              })()}

              {/* Mensagem quando não há resultados na busca */}
              {searchTerm && filteredPermissions.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-neutral-500 dark:text-gray-400">
                    Nenhuma permissão encontrada para "{searchTerm}"
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center border-t-2 border-gray-300 dark:border-gray-600 pt-4 px-6 pb-4 flex-shrink-0">
          <div className="text-sm text-neutral-600 dark:text-gray-400">
            <span className="font-medium">{(() => {
              // Contar apenas permissões dos módulos ativos
              const activeModules = getActiveModules();
              const activePermissions = selectedPermissions.filter(permId => {
                // Incluir permissões de notificação
                if (permId.startsWith('notifications.')) {
                  return true;
                }
                // Incluir apenas permissões de módulos ativos
                return filteredPermissions.some(p => 
                  p.id === permId && activeModules.includes(p.moduleId)
                );
              });
              
              return activePermissions.length;
            })()}</span>{" "}
            permissões selecionadas
          </div>

          <div className="flex gap-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
              disabled={isSaving}
            >
              Cancelar
            </button>
            <button
              type="button"
              onClick={handleSave}
              className="px-4 py-2 bg-slate-500 hover:bg-slate-600 dark:bg-slate-600 dark:hover:bg-slate-700 text-white rounded-lg transition-colors flex items-center gap-2"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <Shield size={16} />
                  <span>Salvar Permissões</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermissionsModal;