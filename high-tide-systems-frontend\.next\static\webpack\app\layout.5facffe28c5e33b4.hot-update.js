"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"571adeaef65f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFybWFuXFxEZXNrdG9wXFxQcm9qZXRvIFhcXGhpZ2gtdGlkZS1zeXN0ZW1zLWZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1NzFhZGVhZWY2NWZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/navigation/QuickNav.js":
/*!***********************************************!*\
  !*** ./src/components/navigation/QuickNav.js ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/QuickNavContext */ \"(app-pages-browser)/./src/contexts/QuickNavContext.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/handshake.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-no-axes-column-increasing.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Archive,BarChart,Bookmark,Bug,Calendar,ChevronRight,Clock,CreditCard,Database,FileText,Handshake,Home,Info,LayoutDashboard,MapPin,Package,Palette,Search,Settings,Shield,Star,UserPlus,Users,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/usePermissions */ \"(app-pages-browser)/./src/hooks/usePermissions.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mapeamento de módulos e suas rotas\nconst navigationItems = [\n    {\n        category: 'Módulos',\n        items: [\n            {\n                title: 'Dashboard',\n                description: 'Visão geral do sistema',\n                path: '/dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 47,\n                    columnNumber: 15\n                }, undefined),\n                permission: null,\n                moduleColor: 'blue' // Cor azul para o dashboard\n            },\n            {\n                title: 'Administração',\n                description: 'Configurações do sistema',\n                path: '/dashboard/admin/introduction',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 55,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            },\n            {\n                title: 'Pessoas',\n                description: 'Gerenciar pacientes e contatos',\n                path: '/dashboard/people/persons',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 62,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.view'\n            },\n            {\n                title: 'Agendamento',\n                description: 'Gerenciar calendário e agendamentos',\n                path: '/dashboard/scheduler/calendar',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 69,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.view'\n            }\n        ]\n    },\n    {\n        category: 'Administração',\n        items: [\n            {\n                title: 'Usuários',\n                description: 'Gerenciar usuários do sistema',\n                path: '/dashboard/admin/users',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 81,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.users.view'\n            },\n            {\n                title: 'Profissões e Grupos',\n                description: 'Gerenciar profissões e grupos',\n                path: '/dashboard/admin/professions',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 88,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.professions.view'\n            },\n            {\n                title: 'Relatório de Bugs',\n                description: 'Visualizar relatórios de bugs do sistema',\n                path: '/dashboard/admin/bug-reports',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 95,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view',\n                role: 'SYSTEM_ADMIN'\n            },\n            {\n                title: 'Planos',\n                description: 'Gerenciar planos e assinaturas',\n                path: '/dashboard/admin/plans',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 103,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            },\n            {\n                title: 'Configurações',\n                description: 'Configurações gerais do sistema',\n                path: '/dashboard/admin/settings',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 110,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.config.edit'\n            },\n            {\n                title: 'Logs',\n                description: 'Visualizar logs do sistema',\n                path: '/dashboard/admin/logs',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 117,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            },\n            {\n                title: 'Backup',\n                description: 'Gerenciar backups do sistema',\n                path: '/dashboard/admin/backup',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 124,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            },\n            {\n                title: 'Dashboard',\n                description: 'Painel administrativo',\n                path: '/dashboard/admin/introduction',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 131,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view'\n            },\n            {\n                title: 'Afiliados',\n                description: 'Gerenciar sistema de afiliados',\n                path: '/dashboard/admin/affiliates',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 138,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'admin.dashboard.view',\n                role: 'SYSTEM_ADMIN'\n            }\n        ]\n    },\n    {\n        category: 'Pessoas',\n        items: [\n            {\n                title: 'Clientes',\n                description: 'Gerenciar clientes e contas',\n                path: '/dashboard/people/clients',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 151,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.clients.view'\n            },\n            {\n                title: 'Pacientes',\n                description: 'Gerenciar cadastro de pacientes',\n                path: '/dashboard/people/persons',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 158,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.view'\n            },\n            {\n                title: 'Documentos',\n                description: 'Gerenciar documentos e categorias',\n                path: '/dashboard/people/documents',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 165,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.documents.view'\n            },\n            {\n                title: 'Convênios',\n                description: 'Gerenciar convênios e planos',\n                path: '/dashboard/people/insurances',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 172,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.insurances.view'\n            },\n            {\n                title: 'Limites de Convênio',\n                description: 'Configurar limites de atendimento por convênio',\n                path: '/dashboard/people/insurance-limits',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 179,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.insurance-limits.view'\n            },\n            {\n                title: 'Dashboard',\n                description: 'Análise e estatísticas de pessoas',\n                path: '/dashboard/people/dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 186,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.dashboard.view'\n            }\n        ]\n    },\n    {\n        category: 'Agendamento',\n        items: [\n            {\n                title: 'Agendar Consulta',\n                description: 'Visualizar e gerenciar agendamentos',\n                path: '/dashboard/scheduler/calendar',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 198,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.view'\n            },\n            {\n                title: 'Horários de Trabalho',\n                description: 'Configurar horários de atendimento',\n                path: '/dashboard/scheduler/working-hours',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 205,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.working-hours.view'\n            },\n            {\n                title: 'Tipos de Serviço',\n                description: 'Gerenciar tipos de serviço disponíveis',\n                path: '/dashboard/scheduler/service-types',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 212,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.service-types.view'\n            },\n            {\n                title: 'Solicitações',\n                description: 'Gerenciar solicitações de agendamento',\n                path: '/dashboard/scheduler/appointment-requests',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 219,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.appointment-requests.view'\n            },\n            {\n                title: 'Localizações',\n                description: 'Gerenciar locais de atendimento',\n                path: '/dashboard/scheduler/locations',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 226,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.locations.view'\n            },\n            {\n                title: 'Ocupação',\n                description: 'Visualizar ocupação dos profissionais',\n                path: '/dashboard/scheduler/occupation',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 233,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.occupation.view'\n            },\n            {\n                title: 'Relatório',\n                description: 'Relatórios e estatísticas de agendamentos',\n                path: '/dashboard/scheduler/appointments-report',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 240,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.appointments-report.view'\n            },\n            {\n                title: 'Dashboard',\n                description: 'Análise de agendamentos e estatísticas',\n                path: '/dashboard/scheduler/appointments-dashboard',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 247,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.appointments-dashboard.view'\n            }\n        ]\n    },\n    {\n        category: 'Ações Rápidas',\n        items: [\n            {\n                title: 'Novo Paciente',\n                description: 'Cadastrar um novo paciente',\n                path: '/dashboard/people/persons?action=new',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 259,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'people.persons.edit',\n                moduleColor: 'orange'\n            },\n            {\n                title: 'Novo Agendamento',\n                description: 'Criar um novo agendamento',\n                path: '/dashboard/scheduler/calendar?action=new',\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: 18\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 267,\n                    columnNumber: 15\n                }, undefined),\n                permission: 'scheduling.calendar.edit',\n                moduleColor: 'purple'\n            }\n        ]\n    }\n];\nconst QuickNav = ()=>{\n    _s();\n    const { isOpen, closeQuickNav, searchTerm, setSearchTerm, navigationHistory, addToHistory } = (0,_contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav)();\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedIndex, setSelectedIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const searchInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { can } = (0,_hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    // Filtrar itens com base no termo de pesquisa\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (!isOpen) return;\n            // Filtrar itens com base no termo de pesquisa e permissões\n            const filtered = [];\n            // Adicionar histórico de navegação se houver termo de pesquisa\n            if (navigationHistory.length > 0) {\n                const historyItems = navigationHistory.filter({\n                    \"QuickNav.useEffect.historyItems\": (item)=>item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())\n                }[\"QuickNav.useEffect.historyItems\"]);\n                if (historyItems.length > 0) {\n                    filtered.push({\n                        category: 'Recentes',\n                        items: historyItems\n                    });\n                }\n            }\n            // Filtrar itens de navegação\n            navigationItems.forEach({\n                \"QuickNav.useEffect\": (category)=>{\n                    const items = category.items.filter({\n                        \"QuickNav.useEffect.items\": (item)=>// Verificar permissão\n                            (item.permission === null || can(item.permission)) && // Verificar termo de pesquisa\n                            (searchTerm === '' || item.title.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())) && // Verificar role específico\n                            (!item.role || item.role === (user === null || user === void 0 ? void 0 : user.role))\n                    }[\"QuickNav.useEffect.items\"]);\n                    if (items.length > 0) {\n                        filtered.push({\n                            category: category.category,\n                            items: items\n                        });\n                    }\n                }\n            }[\"QuickNav.useEffect\"]);\n            setFilteredItems(filtered);\n            setSelectedIndex(0);\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen,\n        searchTerm,\n        navigationHistory,\n        can,\n        user === null || user === void 0 ? void 0 : user.role\n    ]);\n    // Focar no input de pesquisa quando o QuickNav é aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (isOpen && searchInputRef.current) {\n                setTimeout({\n                    \"QuickNav.useEffect\": ()=>{\n                        searchInputRef.current.focus();\n                    }\n                }[\"QuickNav.useEffect\"], 100);\n            }\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen\n    ]);\n    // Lidar com navegação por teclado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"QuickNav.useEffect\": ()=>{\n            if (!isOpen) return;\n            const handleKeyDown = {\n                \"QuickNav.useEffect.handleKeyDown\": (e)=>{\n                    // Calcular o número total de itens\n                    const totalItems = filteredItems.reduce({\n                        \"QuickNav.useEffect.handleKeyDown.totalItems\": (acc, category)=>acc + category.items.length\n                    }[\"QuickNav.useEffect.handleKeyDown.totalItems\"], 0);\n                    switch(e.key){\n                        case 'ArrowDown':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"QuickNav.useEffect.handleKeyDown\": (prev)=>(prev + 1) % totalItems\n                            }[\"QuickNav.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'ArrowUp':\n                            e.preventDefault();\n                            setSelectedIndex({\n                                \"QuickNav.useEffect.handleKeyDown\": (prev)=>(prev - 1 + totalItems) % totalItems\n                            }[\"QuickNav.useEffect.handleKeyDown\"]);\n                            break;\n                        case 'Enter':\n                            e.preventDefault();\n                            handleSelectItem();\n                            break;\n                    }\n                }\n            }[\"QuickNav.useEffect.handleKeyDown\"];\n            window.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"QuickNav.useEffect\": ()=>{\n                    window.removeEventListener('keydown', handleKeyDown);\n                }\n            })[\"QuickNav.useEffect\"];\n        }\n    }[\"QuickNav.useEffect\"], [\n        isOpen,\n        filteredItems,\n        selectedIndex\n    ]);\n    // Função para selecionar um item\n    const handleSelectItem = ()=>{\n        // Encontrar o item selecionado\n        let currentIndex = 0;\n        let selectedItem = null;\n        for (const category of filteredItems){\n            for (const item of category.items){\n                if (currentIndex === selectedIndex) {\n                    selectedItem = item;\n                    break;\n                }\n                currentIndex++;\n            }\n            if (selectedItem) break;\n        }\n        if (selectedItem) {\n            // Adicionar ao histórico\n            addToHistory(selectedItem);\n            // Navegar para a rota\n            router.push(selectedItem.path);\n            // Fechar o QuickNav\n            closeQuickNav();\n        }\n    };\n    // Função para lidar com clique em um item\n    const handleItemClick = (item)=>{\n        // Adicionar ao histórico\n        addToHistory(item);\n        // Navegar para a rota\n        router.push(item.path);\n        // Fechar o QuickNav\n        closeQuickNav();\n    };\n    // Não renderizar nada se o QuickNav não estiver aberto\n    if (!isOpen) return null;\n    // Renderizar o QuickNav usando um portal\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_4__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-[13000] flex items-start justify-center pt-[15vh] bg-black/50 backdrop-blur-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl bg-background rounded-xl shadow-2xl overflow-hidden border-2 border-blue-500 dark:border-blue-400\",\n            onClick: (e)=>e.stopPropagation(),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 border-b-2 border-blue-500 dark:border-blue-400 flex items-center gap-3 bg-background\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded text-xs font-medium border border-blue-200 dark:border-blue-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Ctrl + K\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 424,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 423,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500\",\n                                    size: 18\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: searchInputRef,\n                                    type: \"text\",\n                                    placeholder: \"Pesquisar...\",\n                                    value: searchTerm,\n                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                    className: \"w-full pl-10 pr-4 py-2 bg-background border border-blue-500 dark:border-blue-400 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 427,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeQuickNav,\n                            className: \"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 p-1 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                size: 20\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 439,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 422,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-h-[60vh] overflow-y-auto bg-background\",\n                    children: filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 text-center text-gray-500 dark:text-gray-400\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Nenhum resultado encontrado\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 451,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                        lineNumber: 450,\n                        columnNumber: 13\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: filteredItems.map((category, categoryIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-bold text-slate-800 dark:text-white px-3 py-2 mb-2 border-l-4 border-blue-500\",\n                                        children: category.category\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                        lineNumber: 457,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: category.items.map((item, itemIndex)=>{\n                                            // Calcular o índice global do item\n                                            let globalIndex = 0;\n                                            for(let i = 0; i < categoryIndex; i++){\n                                                globalIndex += filteredItems[i].items.length;\n                                            }\n                                            globalIndex += itemIndex;\n                                            // Verificar se este é o item selecionado\n                                            const isSelected = globalIndex === selectedIndex;\n                                            // Determinar a cor do ícone com base na categoria ou item específico\n                                            let iconColorClass = 'text-gray-400';\n                                            // Verificar se o item tem uma cor específica definida\n                                            if (item.moduleColor === 'blue') {\n                                                iconColorClass = 'text-blue-500 dark:text-blue-400';\n                                            } else if (item.moduleColor === 'orange') {\n                                                iconColorClass = 'text-orange-500 dark:text-orange-400';\n                                            } else if (item.moduleColor === 'purple') {\n                                                iconColorClass = 'text-purple-500 dark:text-purple-400';\n                                            } else if (category.category === 'Agendamento') {\n                                                iconColorClass = 'text-purple-500 dark:text-purple-400';\n                                            } else if (category.category === 'Pessoas') {\n                                                iconColorClass = 'text-orange-500 dark:text-orange-400';\n                                            } else if (category.category === 'Administração') {\n                                                iconColorClass = 'text-gray-500 dark:text-gray-400';\n                                            } else if (category.category === 'Ações Rápidas') {\n                                                iconColorClass = 'text-blue-500 dark:text-blue-400';\n                                            } else if (category.category === 'Recentes') {\n                                                iconColorClass = 'text-gray-400 dark:text-gray-500';\n                                            } else if (category.category === 'Módulos') {\n                                                // Para a categoria Módulos, usar cores específicas por item\n                                                if (item.title === 'Dashboard') {\n                                                    iconColorClass = 'text-blue-500 dark:text-blue-400';\n                                                } else if (item.title === 'Agendamento') {\n                                                    iconColorClass = 'text-purple-500 dark:text-purple-400';\n                                                } else if (item.title === 'Pessoas') {\n                                                    iconColorClass = 'text-orange-500 dark:text-orange-400';\n                                                } else if (item.title === 'Administração') {\n                                                    iconColorClass = 'text-gray-500 dark:text-gray-400';\n                                                }\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-3 py-2 rounded-lg flex items-center gap-3 cursor-pointer transition-colors \".concat(isSelected ? 'bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700' : 'hover:bg-gray-50 dark:hover:bg-slate-700/50 border border-transparent'),\n                                                onClick: ()=>handleItemClick(item),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(iconColorClass, \" \").concat(isSelected ? 'bg-blue-100 dark:bg-blue-900/50' : 'bg-gray-100 dark:bg-slate-700', \" border border-blue-500 dark:border-blue-400\"),\n                                                        children: category.category === 'Recentes' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 31\n                                                        }, undefined) : item.icon || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 520,\n                                                            columnNumber: 44\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-800 dark:text-gray-200 truncate\",\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 truncate\",\n                                                                children: item.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-400 dark:text-gray-500\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Archive_BarChart_Bookmark_Bug_Calendar_ChevronRight_Clock_CreditCard_Database_FileText_Handshake_Home_Info_LayoutDashboard_MapPin_Package_Palette_Search_Settings_Shield_Star_UserPlus_Users_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                            size: 16\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, \"\".concat(item.path, \"-\").concat(itemIndex), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                                lineNumber: 507,\n                                                columnNumber: 25\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                        lineNumber: 461,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, categoryIndex, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                lineNumber: 456,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                        lineNumber: 454,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 448,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-3 border-t-2 border-blue-500 dark:border-blue-400 bg-background text-xs text-gray-500 dark:text-gray-400 flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded bg-background border border-blue-500 dark:border-blue-400 text-foreground font-medium\",\n                                            children: \"↑\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded bg-background border border-blue-500 dark:border-blue-400 text-foreground font-medium\",\n                                            children: \"↓\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 553,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"navegar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 554,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded bg-background border border-blue-500 dark:border-blue-400 text-foreground font-medium\",\n                                            children: \"Enter\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"selecionar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"px-1.5 py-0.5 rounded bg-background border border-blue-500 dark:border-blue-400 text-foreground font-medium\",\n                                            children: \"Esc\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-1\",\n                                            children: \"fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                            lineNumber: 564,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                \"Pressione \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"Ctrl+K\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 569,\n                                    columnNumber: 23\n                                }, undefined),\n                                \" ou \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium\",\n                                    children: \"/\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                                    lineNumber: 569,\n                                    columnNumber: 70\n                                }, undefined),\n                                \" para abrir\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n            lineNumber: 417,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\navigation\\\\QuickNav.js\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, undefined), document.body);\n};\n_s(QuickNav, \"0KHJTRM4BuH8wXVWn4JmpGAf538=\", false, function() {\n    return [\n        _contexts_QuickNavContext__WEBPACK_IMPORTED_MODULE_3__.useQuickNav,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_usePermissions__WEBPACK_IMPORTED_MODULE_5__.usePermissions,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth\n    ];\n});\n_c = QuickNav;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (QuickNav);\nvar _c;\n$RefreshReg$(_c, \"QuickNav\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/QuickNav.js\n"));

/***/ })

});