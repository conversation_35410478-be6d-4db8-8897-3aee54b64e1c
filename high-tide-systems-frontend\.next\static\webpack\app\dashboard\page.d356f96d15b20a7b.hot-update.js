"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/services/professionsService.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/services/professionsService.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   professionsService: () => (/* binding */ professionsService)\n/* harmony export */ });\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _app_services_exportService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/services/exportService */ \"(app-pages-browser)/./src/app/services/exportService.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n// src/app/modules/admin/services/professionsService.js\n\n\n\n\n/**\r\n * Serviço para gerenciar operações relacionadas a profissões\r\n */ const professionsService = {\n    /**\r\n   * Obtém a lista de profissões com filtros\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {string} params.search - Termo de busca\r\n   * @param {string} params.groupId - Filtro por grupo\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @param {string} params.companyId - Filtro por empresa\r\n   * @param {Array} params.professionIds - Filtro por IDs específicos de profissões\r\n   * @returns {Promise<Array>} Lista de profissões\r\n   */ async getProfessions () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        try {\n            // Extrair professionIds do objeto params para tratamento especial\n            const { professionIds, ...otherParams } = params;\n            // Criar objeto de parâmetros base\n            let queryParams = {\n                ...otherParams\n            };\n            // Adicionar professionIds como parâmetros separados com notação de array\n            if (professionIds && professionIds.length > 0) {\n                // Garantir que professionIds seja um array\n                const professionIdsArray = Array.isArray(professionIds) ? professionIds : [\n                    professionIds\n                ];\n                // Adicionar cada ID como um parâmetro separado\n                professionIdsArray.forEach((id, index)=>{\n                    // Usar a notação de array para compatibilidade com a API\n                    queryParams[\"professionIds[\".concat(index, \"]\")] = id;\n                });\n                console.log(\"Filtrando por múltiplos IDs de profissões:\", professionIdsArray);\n            }\n            // Log para debug\n            console.log(\"Parâmetros de filtro para profissões:\", queryParams);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions\", {\n                params: queryParams\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar profissões:\", error);\n            throw error;\n        }\n    },\n    /**\r\n   * Obtém uma profissão pelo ID\r\n   * @param {string} professionId - ID da profissão\r\n   * @returns {Promise<Object>} Dados da profissão\r\n   */ async getProfessionById (professionId) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions/\".concat(professionId));\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar profiss\\xe3o \".concat(professionId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Cria uma nova profissão\r\n   * @param {Object} professionData - Dados da profissão\r\n   * @returns {Promise<Object>} Profissão criada\r\n   */ async createProfession (professionData) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/professions\", professionData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao criar profissão:\", error);\n            throw error;\n        }\n    },\n    /**\r\n   * Atualiza uma profissão existente\r\n   * @param {string} professionId - ID da profissão\r\n   * @param {Object} professionData - Dados atualizados da profissão\r\n   * @returns {Promise<Object>} Profissão atualizada\r\n   */ async updateProfession (professionId, professionData) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.put(\"/professions/\".concat(professionId), professionData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao atualizar profiss\\xe3o \".concat(professionId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Exclui uma profissão\r\n   * @param {string} professionId - ID da profissão\r\n   * @returns {Promise<void>}\r\n   */ async deleteProfession (professionId) {\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.delete(\"/professions/\".concat(professionId));\n        } catch (error) {\n            console.error(\"Erro ao excluir profiss\\xe3o \".concat(professionId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Obtém a lista de grupos de profissões\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {string} params.search - Termo de busca\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @param {string} params.companyId - Filtro por empresa\r\n   * @param {Array} params.groupIds - Filtro por IDs específicos de grupos\r\n   * @param {string} params.sortField - Campo para ordenação\r\n   * @param {string} params.sortDirection - Direção da ordenação\r\n   * @returns {Promise<Array>} Lista de grupos de profissões\r\n   */ async getProfessionGroups () {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        try {\n            // Extrair groupIds do objeto params para tratamento especial\n            const { groupIds, ...otherParams } = params;\n            // Criar objeto de parâmetros base\n            let queryParams = {\n                ...otherParams\n            };\n            // Adicionar groupIds como parâmetros separados com notação de array\n            if (groupIds && groupIds.length > 0) {\n                // Garantir que groupIds seja um array\n                const groupIdsArray = Array.isArray(groupIds) ? groupIds : [\n                    groupIds\n                ];\n                // Adicionar cada ID como um parâmetro separado\n                groupIdsArray.forEach((id, index)=>{\n                    // Usar a notação de array para compatibilidade com a API\n                    queryParams[\"groupIds[\".concat(index, \"]\")] = id;\n                });\n                console.log(\"Filtrando por múltiplos IDs de grupos:\", groupIdsArray);\n            }\n            // Log para debug\n            console.log(\"Parâmetros de filtro para grupos:\", queryParams);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions/groups\", {\n                params: queryParams\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar grupos de profissões:\", error);\n            throw error;\n        }\n    },\n    /**\r\n   * Obtém um grupo de profissões pelo ID\r\n   * @param {string} groupId - ID do grupo\r\n   * @returns {Promise<Object>} Dados do grupo\r\n   */ async getProfessionGroupById (groupId) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions/groups/\".concat(groupId));\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar grupo de profiss\\xf5es \".concat(groupId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Cria um novo grupo de profissões\r\n   * @param {Object} groupData - Dados do grupo\r\n   * @returns {Promise<Object>} Grupo criado\r\n   */ async createProfessionGroup (groupData) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.post(\"/professions/groups\", groupData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao criar grupo de profissões:\", error);\n            throw error;\n        }\n    },\n    /**\r\n   * Atualiza um grupo de profissões existente\r\n   * @param {string} groupId - ID do grupo\r\n   * @param {Object} groupData - Dados atualizados do grupo\r\n   * @returns {Promise<Object>} Grupo atualizado\r\n   */ async updateProfessionGroup (groupId, groupData) {\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.put(\"/professions/groups/\".concat(groupId), groupData);\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao atualizar grupo de profiss\\xf5es \".concat(groupId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Exclui um grupo de profissões\r\n   * @param {string} groupId - ID do grupo\r\n   * @returns {Promise<void>}\r\n   */ async deleteProfessionGroup (groupId) {\n        try {\n            await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.delete(\"/professions/groups/\".concat(groupId));\n        } catch (error) {\n            console.error(\"Erro ao excluir grupo de profiss\\xf5es \".concat(groupId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Obtém a lista de usuários de uma profissão específica\r\n   * @param {string} professionId - ID da profissão\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @returns {Promise<Object>} Dados da profissão e lista de usuários\r\n   */ async getProfessionUsers (professionId) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions/\".concat(professionId, \"/users\"), {\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar usu\\xe1rios da profiss\\xe3o \".concat(professionId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Obtém a lista de profissões de um grupo específico\r\n   * @param {string} groupId - ID do grupo\r\n   * @param {Object} params - Parâmetros de filtro\r\n   * @param {boolean} params.active - Filtro por status (ativo/inativo)\r\n   * @returns {Promise<Object>} Dados do grupo e lista de profissões\r\n   */ async getGroupProfessions (groupId) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        try {\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_0__.api.get(\"/professions/groups/\".concat(groupId, \"/professions\"), {\n                params\n            });\n            return response.data;\n        } catch (error) {\n            console.error(\"Erro ao buscar profiss\\xf5es do grupo \".concat(groupId, \":\"), error);\n            throw error;\n        }\n    },\n    /**\r\n   * Exporta a lista de profissões com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */ async exportProfessions (filters) {\n        let exportFormat = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"xlsx\";\n        try {\n            // Obter os dados filtrados\n            const data = await professionsService.getProfessions({\n                ...filters\n            });\n            // Timestamp atual para o subtítulo\n            const timestamp = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), \"dd/MM/yyyy 'às' HH:mm\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n            });\n            // Definição das colunas com formatação\n            const columns = [\n                {\n                    key: \"name\",\n                    header: \"Nome\"\n                },\n                {\n                    key: \"description\",\n                    header: \"Descrição\"\n                },\n                {\n                    key: \"groupName\",\n                    header: \"Grupo\"\n                },\n                {\n                    key: \"companyName\",\n                    header: \"Empresa\"\n                },\n                {\n                    key: \"active\",\n                    header: \"Status\",\n                    format: (value)=>value ? \"Ativo\" : \"Inativo\",\n                    align: \"center\",\n                    width: 20\n                },\n                {\n                    key: \"createdAt\",\n                    header: \"Data de Cadastro\",\n                    type: \"date\"\n                }\n            ];\n            // Preparar os dados para exportação\n            const preparedData = data.map((profession)=>{\n                var _profession_group, _profession_company;\n                return {\n                    name: profession.name || \"\",\n                    description: profession.description || \"\",\n                    groupName: ((_profession_group = profession.group) === null || _profession_group === void 0 ? void 0 : _profession_group.name) || \"\",\n                    companyName: ((_profession_company = profession.company) === null || _profession_company === void 0 ? void 0 : _profession_company.name) || \"\",\n                    active: profession.active,\n                    createdAt: profession.createdAt || \"\"\n                };\n            });\n            // Filtros aplicados para subtítulo\n            let subtitleParts = [];\n            if (filters.search) subtitleParts.push('Busca: \"'.concat(filters.search, '\"'));\n            if (filters.professionIds && filters.professionIds.length > 0) {\n                subtitleParts.push(\"Profiss\\xf5es espec\\xedficas: \".concat(filters.professionIds.length, \" selecionadas\"));\n            }\n            if (filters.groupId) {\n                subtitleParts.push(\"Grupo: \".concat(filters.groupName || filters.groupId));\n            }\n            if (filters.active !== undefined) {\n                subtitleParts.push(\"Status: \".concat(filters.active ? \"Ativas\" : \"Inativas\"));\n            }\n            if (filters.companyId) {\n                subtitleParts.push(\"Empresa: \".concat(filters.companyName || filters.companyId));\n            }\n            // Construir o subtítulo\n            let subtitle = \"Exportado em: \".concat(timestamp);\n            if (subtitleParts.length > 0) {\n                subtitle += \" | Filtros: \".concat(subtitleParts.join(\", \"));\n            }\n            // Exportar os dados\n            return await _app_services_exportService__WEBPACK_IMPORTED_MODULE_1__.exportService.exportData(preparedData, {\n                format: exportFormat,\n                filename: \"profissoes\",\n                columns,\n                title: \"Lista de Profissões\",\n                subtitle\n            });\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n            return false;\n        }\n    },\n    /**\r\n   * Exporta a lista de grupos de profissões com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */ async exportProfessionGroups (filters) {\n        let exportFormat = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"xlsx\";\n        try {\n            // Obter os dados filtrados\n            const data = await professionsService.getProfessionGroups({\n                ...filters\n            });\n            // Timestamp atual para o subtítulo\n            const timestamp = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(new Date(), \"dd/MM/yyyy 'às' HH:mm\", {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n            });\n            // Definição das colunas com formatação\n            const columns = [\n                {\n                    key: \"name\",\n                    header: \"Nome\"\n                },\n                {\n                    key: \"description\",\n                    header: \"Descrição\"\n                },\n                {\n                    key: \"companyName\",\n                    header: \"Empresa\"\n                },\n                {\n                    key: \"professionsCount\",\n                    header: \"Nº de Profissões\"\n                },\n                {\n                    key: \"active\",\n                    header: \"Status\",\n                    format: (value)=>value ? \"Ativo\" : \"Inativo\",\n                    align: \"center\",\n                    width: 20\n                },\n                {\n                    key: \"createdAt\",\n                    header: \"Data de Cadastro\",\n                    type: \"date\"\n                }\n            ];\n            // Preparar os dados para exportação\n            const preparedData = data.map((group)=>{\n                var _group_company, _group_professions;\n                return {\n                    name: group.name || \"\",\n                    description: group.description || \"\",\n                    companyName: ((_group_company = group.company) === null || _group_company === void 0 ? void 0 : _group_company.name) || \"\",\n                    professionsCount: ((_group_professions = group.professions) === null || _group_professions === void 0 ? void 0 : _group_professions.length) || 0,\n                    active: group.active,\n                    createdAt: group.createdAt || \"\"\n                };\n            });\n            // Filtros aplicados para subtítulo\n            let subtitleParts = [];\n            if (filters.search) subtitleParts.push('Busca: \"'.concat(filters.search, '\"'));\n            if (filters.groupIds && filters.groupIds.length > 0) {\n                subtitleParts.push(\"Grupos espec\\xedficos: \".concat(filters.groupIds.length, \" selecionados\"));\n            }\n            if (filters.active !== undefined) {\n                subtitleParts.push(\"Status: \".concat(filters.active ? \"Ativos\" : \"Inativos\"));\n            }\n            if (filters.companyId) {\n                subtitleParts.push(\"Empresa: \".concat(filters.companyName || filters.companyId));\n            }\n            // Construir o subtítulo\n            let subtitle = \"Exportado em: \".concat(timestamp);\n            if (subtitleParts.length > 0) {\n                subtitle += \" | Filtros: \".concat(subtitleParts.join(\", \"));\n            }\n            // Exportar os dados\n            return await _app_services_exportService__WEBPACK_IMPORTED_MODULE_1__.exportService.exportData(preparedData, {\n                format: exportFormat,\n                filename: \"grupos-profissoes\",\n                columns,\n                title: \"Lista de Grupos de Profissões\",\n                subtitle\n            });\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n            return false;\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\n"));

/***/ })

});