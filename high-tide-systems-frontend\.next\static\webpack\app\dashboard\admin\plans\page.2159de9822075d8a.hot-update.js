"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/components/settings/BranchFormModal.js":
/*!****************************************************!*\
  !*** ./src/components/settings/BranchFormModal.js ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog.jsx */ \"(app-pages-browser)/./src/components/ui/dialog.jsx\");\n/* harmony import */ var _components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Button.js */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Input */ \"(app-pages-browser)/./src/components/ui/Input.js\");\n/* harmony import */ var _components_ui_Label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Label */ \"(app-pages-browser)/./src/components/ui/Label.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building,Clock,Info,Loader2,Mail,MapPin,Phone,Settings,Shield,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/admin/services/branchService */ \"(app-pages-browser)/./src/app/modules/admin/services/branchService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _components_common_AddressForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/common/AddressForm */ \"(app-pages-browser)/./src/components/common/AddressForm.js\");\n/* harmony import */ var _hooks_useCep__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useCep */ \"(app-pages-browser)/./src/hooks/useCep.js\");\n/* harmony import */ var _components_workingHours_BranchWorkingHoursForm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workingHours/BranchWorkingHoursForm */ \"(app-pages-browser)/./src/components/workingHours/BranchWorkingHoursForm.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_common_MaskedInput__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/common/MaskedInput */ \"(app-pages-browser)/./src/components/common/MaskedInput.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst BranchFormModal = (param)=>{\n    let { isOpen, onClose, branch, onSuccess } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_13__.useToast)();\n    const workingHoursFormRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = (0,_hooks_useCep__WEBPACK_IMPORTED_MODULE_11__.useCep)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        code: \"\",\n        description: \"\",\n        address: \"\",\n        neighborhood: \"\",\n        city: \"\",\n        state: \"\",\n        postalCode: \"\",\n        phone: \"\",\n        email: \"\",\n        isHeadquarters: false,\n        companyId: \"\",\n        defaultWorkingHours: null,\n        applyToUsers: false\n    });\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadingCompanies, setLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"basic\");\n    const isSystemAdmin = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN';\n    // Carregar empresas se for system_admin\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BranchFormModal.useEffect\": ()=>{\n            const loadCompanies = {\n                \"BranchFormModal.useEffect.loadCompanies\": async ()=>{\n                    if (isSystemAdmin) {\n                        setLoadingCompanies(true);\n                        try {\n                            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_9__.companyService.getCompanies({\n                                active: true,\n                                limit: 100\n                            });\n                            setCompanies(response.companies || []);\n                        } catch (error) {\n                            console.error(\"Erro ao carregar empresas:\", error);\n                        } finally{\n                            setLoadingCompanies(false);\n                        }\n                    }\n                }\n            }[\"BranchFormModal.useEffect.loadCompanies\"];\n            if (isOpen && isSystemAdmin) {\n                loadCompanies();\n            }\n        }\n    }[\"BranchFormModal.useEffect\"], [\n        isOpen,\n        isSystemAdmin\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BranchFormModal.useEffect\": ()=>{\n            if (isOpen) {\n                setErrors({});\n                setActiveTab(\"basic\");\n                if (branch) {\n                    setFormData({\n                        name: branch.name || \"\",\n                        code: branch.code || \"\",\n                        description: branch.description || \"\",\n                        address: branch.address || \"\",\n                        neighborhood: branch.neighborhood || \"\",\n                        city: branch.city || \"\",\n                        state: branch.state || \"\",\n                        postalCode: branch.postalCode || \"\",\n                        phone: branch.phone || \"\",\n                        email: branch.email || \"\",\n                        isHeadquarters: branch.isHeadquarters || false,\n                        companyId: branch.companyId || (user === null || user === void 0 ? void 0 : user.companyId) || \"\",\n                        defaultWorkingHours: branch.defaultWorkingHours || null,\n                        applyToUsers: false\n                    });\n                    // Se branch existe mas não tem horários padrão, carregá-los da API\n                    if (branch.id && !branch.defaultWorkingHours) {\n                        loadDefaultWorkingHours(branch.id);\n                    }\n                } else {\n                    setFormData({\n                        name: \"\",\n                        code: \"\",\n                        description: \"\",\n                        address: \"\",\n                        neighborhood: \"\",\n                        city: \"\",\n                        state: \"\",\n                        postalCode: \"\",\n                        phone: \"\",\n                        email: \"\",\n                        isHeadquarters: false,\n                        companyId: (user === null || user === void 0 ? void 0 : user.companyId) || \"\",\n                        defaultWorkingHours: null,\n                        applyToUsers: false\n                    });\n                }\n            }\n        }\n    }[\"BranchFormModal.useEffect\"], [\n        isOpen,\n        branch,\n        user\n    ]);\n    const loadDefaultWorkingHours = async (branchId)=>{\n        try {\n            const data = await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_8__.branchService.getDefaultWorkingHours(branchId);\n            setFormData((prev)=>({\n                    ...prev,\n                    defaultWorkingHours: data.defaultWorkingHours\n                }));\n        } catch (error) {\n            console.error(\"Erro ao carregar horários padrão:\", error);\n        }\n    };\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.name.trim()) {\n            newErrors.name = \"Nome da unidade é obrigatório\";\n        }\n        if (!formData.address.trim()) {\n            newErrors.address = \"Endereço é obrigatório\";\n        }\n        if (isSystemAdmin && !formData.companyId) {\n            newErrors.companyId = \"Empresa é obrigatória\";\n        }\n        if (formData.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(formData.email)) {\n            newErrors.email = \"Email deve ser válido\";\n        }\n        if (formData.phone && !/^\\d{10,11}$/.test(formData.phone.replace(/\\D/g, ''))) {\n            newErrors.phone = \"Telefone deve ter 10 ou 11 dígitos\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleWorkingHoursChange = (workingHours)=>{\n        if (workingHours) {\n            setFormData((prev)=>({\n                    ...prev,\n                    defaultWorkingHours: workingHours\n                }));\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            return;\n        }\n        // Se estiver na aba de horários de trabalho, validar os horários\n        if (activeTab === \"workingHours\" && workingHoursFormRef.current) {\n            const isWorkingHoursValid = workingHoursFormRef.current.validateAllTimeSlots();\n            if (!isWorkingHoursValid) {\n                toast_error({\n                    title: \"Erro de validação\",\n                    message: \"Verifique os horários de trabalho e corrija os erros antes de salvar.\"\n                });\n                return;\n            }\n        }\n        setIsLoading(true);\n        try {\n            const payload = {\n                name: formData.name.trim(),\n                code: formData.code.trim() || undefined,\n                description: formData.description.trim() || undefined,\n                address: formData.address.trim(),\n                neighborhood: formData.neighborhood.trim() || undefined,\n                city: formData.city.trim() || undefined,\n                state: formData.state.trim() || undefined,\n                postalCode: formData.postalCode.trim() || undefined,\n                phone: formData.phone ? formData.phone.replace(/\\D/g, '') : undefined,\n                email: formData.email.trim() || undefined,\n                isHeadquarters: formData.isHeadquarters,\n                companyId: formData.companyId,\n                defaultWorkingHours: formData.defaultWorkingHours,\n                applyToUsers: formData.applyToUsers\n            };\n            if (branch) {\n                await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_8__.branchService.updateBranch(branch.id, payload);\n                // Se applyToUsers é true, aplicar horários aos usuários\n                if (formData.applyToUsers && formData.defaultWorkingHours) {\n                    try {\n                        await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_8__.branchService.applyWorkingHoursToUsers(branch.id);\n                        toast_success({\n                            title: \"Horários aplicados\",\n                            message: \"Horários de trabalho aplicados com sucesso aos usuários da unidade\"\n                        });\n                    } catch (error) {\n                        console.error(\"Erro ao aplicar horários aos usuários:\", error);\n                        toast_error({\n                            title: \"Erro\",\n                            message: \"Erro ao aplicar horários de trabalho aos usuários\"\n                        });\n                    }\n                }\n            } else {\n                await _app_modules_admin_services_branchService__WEBPACK_IMPORTED_MODULE_8__.branchService.createBranch(payload);\n            }\n            onSuccess();\n            onClose();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao salvar unidade:\", error);\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) {\n                const apiErrors = {};\n                error.response.data.errors.forEach((err)=>{\n                    apiErrors[err.param] = err.msg;\n                });\n                setErrors(apiErrors);\n            } else {\n                var _error_response_data1, _error_response1;\n                setErrors({\n                    general: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || \"Erro ao salvar unidade\"\n                });\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-6xl max-h-[95vh] flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"pb-4 border-b border-gray-200 dark:border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-2 bg-gradient-to-br from-gray-400 to-gray-500 rounded-lg text-white\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog_jsx__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                        className: \"text-xl font-semibold text-gray-800 dark:text-white\",\n                                        children: branch ? \"Editar Unidade\" : \"Nova Unidade\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mt-1\",\n                                        children: branch ? \"Modifique as informações da unidade\" : \"Configure uma nova unidade/filial da empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                        lineNumber: 266,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                    lineNumber: 265,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex border-b border-gray-200 dark:border-gray-700 -mx-6 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"basic\"),\n                            className: \"px-4 py-3 font-medium text-sm transition-colors duration-200 border-b-2 \".concat(activeTab === \"basic\" ? 'text-gray-600 border-gray-500' : 'text-gray-500 border-transparent hover:text-gray-600 hover:border-gray-400'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setActiveTab(\"workingHours\"),\n                            className: \"px-4 py-3 font-medium text-sm transition-colors duration-200 border-b-2 \".concat(activeTab === \"workingHours\" ? 'text-slate-600 border-slate-500' : 'text-gray-500 border-transparent hover:text-slate-500 hover:border-slate-300'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Hor\\xe1rios de Trabalho\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                            lineNumber: 296,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"flex-1 flex flex-col min-h-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-y-auto space-y-6 pt-4\",\n                            children: [\n                                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-600 dark:text-red-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 dark:text-red-300\",\n                                            children: errors.general\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, undefined),\n                                activeTab === \"basic\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-slate-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                            children: \"Informa\\xe7\\xf5es B\\xe1sicas\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 327,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            htmlFor: \"name\",\n                                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-slate-500 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"Nome da Unidade *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            id: \"name\",\n                                                            value: formData.name,\n                                                            onChange: (e)=>{\n                                                                setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        name: e.target.value\n                                                                    }));\n                                                                setErrors((prev)=>({\n                                                                        ...prev,\n                                                                        name: \"\"\n                                                                    }));\n                                                            },\n                                                            placeholder: \"Ex: Filial Centro, Matriz S\\xe3o Paulo...\",\n                                                            className: errors.name ? \"border-red-500 focus:ring-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 336,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.name\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            htmlFor: \"code\",\n                                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 357,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"C\\xf3digo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            id: \"code\",\n                                                            value: formData.code,\n                                                            onChange: (e)=>{\n                                                                setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        code: e.target.value\n                                                                    }));\n                                                                setErrors((prev)=>({\n                                                                        ...prev,\n                                                                        code: \"\"\n                                                                    }));\n                                                            },\n                                                            placeholder: \"Ex: FIL001, MTZ\",\n                                                            className: errors.code ? \"border-red-500 focus:ring-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"C\\xf3digo \\xfanico para identifica\\xe7\\xe3o interna da unidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            htmlFor: \"description\",\n                                                            className: \"text-sm font-medium flex items-center gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                \"Descri\\xe7\\xe3o\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleTextarea, {\n                                                            id: \"description\",\n                                                            moduleColor: \"admin\",\n                                                            value: formData.description,\n                                                            onChange: (e)=>setFormData((prev)=>({\n                                                                        ...prev,\n                                                                        description: e.target.value\n                                                                    })),\n                                                            placeholder: \"Breve descri\\xe7\\xe3o sobre a unidade, sua fun\\xe7\\xe3o e caracter\\xedsticas...\",\n                                                            rows: 3,\n                                                            className: \"resize-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: \"Informa\\xe7\\xf5es adicionais que ajudem a identificar esta unidade\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3 p-3 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    id: \"isHeadquarters\",\n                                                                    checked: formData.isHeadquarters,\n                                                                    onChange: (e)=>setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                isHeadquarters: e.target.checked\n                                                                            })),\n                                                                    className: \"h-4 w-4 text-slate-600 border-gray-300 rounded focus:ring-slate-500\",\n                                                                    disabled: branch && branch.isHeadquarters\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    htmlFor: \"isHeadquarters\",\n                                                                    className: \"text-sm flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-amber-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Definir como matriz/sede principal\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 21\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-amber-700 dark:text-amber-300 ml-6\",\n                                                            children: \"Apenas uma unidade pode ser matriz por empresa. Isso alterar\\xe1 automaticamente o status de outras unidades.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 411,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            className: \"h-4 w-4 text-slate-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                            children: \"Endere\\xe7o e Contato\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 421,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_AddressForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        formData: formData,\n                                                        setFormData: setFormData,\n                                                        errors: errors,\n                                                        isLoading: isLoading,\n                                                        moduleColor: \"admin\",\n                                                        fieldMapping: {\n                                                            cep: 'postalCode',\n                                                            logradouro: 'address',\n                                                            bairro: 'neighborhood',\n                                                            localidade: 'city',\n                                                            uf: 'state'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    htmlFor: \"phone\",\n                                                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 449,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Telefone\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 448,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 z-10\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleMaskedInput, {\n                                                                            moduleColor: \"admin\",\n                                                                            mask: \"(99) 99999-9999\",\n                                                                            replacement: {\n                                                                                9: /[0-9]/\n                                                                            },\n                                                                            value: formData.phone,\n                                                                            onChange: (e)=>{\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        phone: e.target.value\n                                                                                    }));\n                                                                                setErrors((prev)=>({\n                                                                                        ...prev,\n                                                                                        phone: \"\"\n                                                                                    }));\n                                                                            },\n                                                                            placeholder: \"(00) 00000-0000\",\n                                                                            className: \"pl-10 \".concat(errors.phone ? \"border-red-500 focus:ring-red-500\" : \"\"),\n                                                                            error: !!errors.phone\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 470,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        errors.phone\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 469,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    htmlFor: \"email\",\n                                                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-gray-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        \"Email\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 482,\n                                                                            columnNumber: 23\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            id: \"email\",\n                                                                            type: \"email\",\n                                                                            value: formData.email,\n                                                                            onChange: (e)=>{\n                                                                                setFormData((prev)=>({\n                                                                                        ...prev,\n                                                                                        email: e.target.value\n                                                                                    }));\n                                                                                setErrors((prev)=>({\n                                                                                        ...prev,\n                                                                                        email: \"\"\n                                                                                    }));\n                                                                            },\n                                                                            placeholder: \"<EMAIL>\",\n                                                                            className: \"pl-10 \".concat(errors.email ? \"border-red-500 focus:ring-red-500\" : \"\")\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 23\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 481,\n                                                                    columnNumber: 21\n                                                                }, undefined),\n                                                                errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 497,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        errors.email\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4 p-4 bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900/20 dark:to-slate-800/20 rounded-lg border border-slate-200 dark:border-slate-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-2 mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-slate-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 508,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                                    children: \"Configura\\xe7\\xf5es de Empresa\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 px-2 py-1 bg-slate-100 dark:bg-slate-900/30 text-slate-700 dark:text-slate-300 rounded-full text-xs\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 511,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"System Admin\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    htmlFor: \"companyId\",\n                                                                    className: \"text-sm font-medium flex items-center gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-slate-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 518,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        \"Selecionar Empresa *\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                                                    id: \"companyId\",\n                                                                    moduleColor: \"admin\",\n                                                                    value: formData.companyId,\n                                                                    onChange: (e)=>{\n                                                                        setFormData((prev)=>({\n                                                                                ...prev,\n                                                                                companyId: e.target.value\n                                                                            }));\n                                                                        setErrors((prev)=>({\n                                                                                ...prev,\n                                                                                companyId: \"\"\n                                                                            }));\n                                                                    },\n                                                                    disabled: loadingCompanies,\n                                                                    className: errors.companyId ? \"border-red-500 focus:ring-red-500\" : \"\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: loadingCompanies ? \"Carregando empresas...\" : \"Selecione uma empresa\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: company.id,\n                                                                                children: company.name\n                                                                            }, company.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 536,\n                                                                                columnNumber: 27\n                                                                            }, undefined))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                errors.companyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-red-600 dark:text-red-400 flex items-center gap-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        errors.companyId\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 516,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined),\n                                activeTab === \"workingHours\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-100 dark:bg-blue-900/40 rounded-lg\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-600 dark:text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2\",\n                                                                children: \"Hor\\xe1rios de Funcionamento\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-700 dark:text-blue-300 mb-4\",\n                                                                children: \"Configure os hor\\xe1rios padr\\xe3o de funcionamento desta unidade. Estes hor\\xe1rios servir\\xe3o como base para agendamentos e podem ser aplicados aos usu\\xe1rios.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-4 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 573,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-800 dark:text-blue-200\",\n                                                                                children: [\n                                                                                    Object.keys(formData.defaultWorkingHours || {}).filter((day)=>formData.defaultWorkingHours[day] && formData.defaultWorkingHours[day].length > 0).length,\n                                                                                    \" dias configurados\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 574,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 rounded-full\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-3 w-3 text-blue-600\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 581,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-800 dark:text-blue-200\",\n                                                                                children: \"Hor\\xe1rios flex\\xedveis por dia\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 582,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 558,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 557,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid gap-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"xl:col-span-3 space-y-6\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900/50 dark:to-gray-900/50 px-6 py-4 border-b border-gray-200 dark:border-gray-700\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-slate-600 dark:text-slate-400\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                    lineNumber: 598,\n                                                                                    columnNumber: 27\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                    className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                                    children: \"Configura\\xe7\\xe3o Semanal\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                    lineNumber: 599,\n                                                                                    columnNumber: 27\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                            children: \"Configure os hor\\xe1rios para cada dia da semana\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 601,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 596,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workingHours_BranchWorkingHoursForm__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    ref: workingHoursFormRef,\n                                                                    defaultWorkingHours: formData.defaultWorkingHours,\n                                                                    onChange: handleWorkingHoursChange,\n                                                                    isLoading: isLoading,\n                                                                    onValidationChange: (isValid)=>{\n                                                                    // Opcional: Usar para indicador visual\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 593,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"xl:col-span-1 space-y-6\",\n                                                    children: branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-5 border border-green-200 dark:border-green-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3 mb-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 bg-green-100 dark:bg-green-900/40 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600 dark:text-green-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 628,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-semibold text-gray-900 dark:text-white\",\n                                                                        children: \"Aplicar Hor\\xe1rios\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                type: \"checkbox\",\n                                                                                id: \"applyToUsers\",\n                                                                                checked: formData.applyToUsers,\n                                                                                onChange: (e)=>setFormData((prev)=>({\n                                                                                            ...prev,\n                                                                                            applyToUsers: e.target.checked\n                                                                                        })),\n                                                                                className: \"h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mt-1\",\n                                                                                disabled: isLoading\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 635,\n                                                                                columnNumber: 27\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Label__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                        htmlFor: \"applyToUsers\",\n                                                                                        className: \"text-sm font-medium text-gray-900 dark:text-white cursor-pointer\",\n                                                                                        children: \"Aplicar aos usu\\xe1rios existentes\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                        lineNumber: 644,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-green-700 dark:text-green-300 mt-2 leading-relaxed\",\n                                                                                        children: \"Os hor\\xe1rios configurados ser\\xe3o aplicados automaticamente a todos os usu\\xe1rios desta unidade.\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                        lineNumber: 647,\n                                                                                        columnNumber: 29\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                lineNumber: 643,\n                                                                                columnNumber: 27\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    formData.applyToUsers && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-3 bg-amber-50 dark:bg-amber-900/30 border border-amber-200 dark:border-amber-700 rounded-lg\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-start gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                    lineNumber: 656,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-amber-700 dark:text-amber-300 leading-relaxed\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                                            children: \"Aten\\xe7\\xe3o:\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                            lineNumber: 658,\n                                                                                            columnNumber: 33\n                                                                                        }, undefined),\n                                                                                        \" Esta a\\xe7\\xe3o substituir\\xe1 os hor\\xe1rios individuais dos usu\\xe1rios.\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                                    lineNumber: 657,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                            lineNumber: 655,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                    lineNumber: 622,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700 mt-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"button\",\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    disabled: isLoading,\n                                    animated: false,\n                                    className: \"min-w-[100px] border-slate-300 text-slate-600 hover:border-slate-400 hover:text-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800 focus:border-slate-300 focus:text-slate-600 active:border-slate-300 active:text-slate-600 transition-colors duration-200\",\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 675,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    type: \"submit\",\n                                    disabled: isLoading,\n                                    animated: false,\n                                    className: \"min-w-[120px] bg-gradient-to-r from-slate-500 to-slate-600 hover:from-slate-600 hover:to-slate-700 focus:from-slate-500 focus:to-slate-600 text-white border-0 transition-colors duration-200\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building_Clock_Info_Loader2_Mail_MapPin_Phone_Settings_Shield_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-4 w-4 animate-spin mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                                lineNumber: 693,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            \"Salvando...\"\n                                        ]\n                                    }, void 0, true) : branch ? \"Atualizar Unidade\" : \"Criar Unidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                                    lineNumber: 685,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                            lineNumber: 674,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n            lineNumber: 264,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\settings\\\\BranchFormModal.js\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BranchFormModal, \"rll5FU64X+Hy5tJxpcpq1Wwjl50=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_13__.useToast,\n        _hooks_useCep__WEBPACK_IMPORTED_MODULE_11__.useCep\n    ];\n});\n_c = BranchFormModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BranchFormModal);\nvar _c;\n$RefreshReg$(_c, \"BranchFormModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/settings/BranchFormModal.js\n"));

/***/ })

});