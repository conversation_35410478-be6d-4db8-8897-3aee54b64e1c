"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/people/documents/page",{

/***/ "(app-pages-browser)/./src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js":
/*!***************************************************************************!*\
  !*** ./src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/tutorial/TutorialManager */ \"(app-pages-browser)/./src/components/tutorial/TutorialManager.js\");\n/* harmony import */ var _components_tutorial_TutorialTriggerButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/tutorial/TutorialTriggerButton */ \"(app-pages-browser)/./src/components/tutorial/TutorialTriggerButton.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_multi_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/multi-select */ \"(app-pages-browser)/./src/components/ui/multi-select.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Edit,Filter,Plus,RefreshCw,Search,Shield,Tag,Trash,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _app_modules_people_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/app/modules/people/services/insuranceServiceLimitService */ \"(app-pages-browser)/./src/app/modules/people/services/insuranceServiceLimitService.js\");\n/* harmony import */ var _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/modules/people/services/insurancesService */ \"(app-pages-browser)/./src/app/modules/people/services/insurancesService.js\");\n/* harmony import */ var _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/modules/people/services/personsService */ \"(app-pages-browser)/./src/app/modules/people/services/personsService.js\");\n/* harmony import */ var _app_modules_scheduler_services_serviceTypeService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/modules/scheduler/services/serviceTypeService */ \"(app-pages-browser)/./src/app/modules/scheduler/services/serviceTypeService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_people_InsuranceLimitFormModal__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/people/InsuranceLimitFormModal */ \"(app-pages-browser)/./src/components/people/InsuranceLimitFormModal.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_common_ShareButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/common/ShareButton */ \"(app-pages-browser)/./src/components/common/ShareButton.js\");\n/* harmony import */ var _components_people_InsuranceLimitsFilters__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/people/InsuranceLimitsFilters */ \"(app-pages-browser)/./src/components/people/InsuranceLimitsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Tutorial steps para a página de limites de convênio\nconst insuranceLimitsTutorialSteps = [\n    {\n        title: \"Limites de Convênio\",\n        content: \"Esta tela permite configurar os limites de utilização de serviços por convênio para cada paciente.\",\n        selector: \"h1\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Adicionar Novo Limite\",\n        content: \"Clique aqui para adicionar um novo limite de convênio.\",\n        selector: \"button:has(span:contains('Novo Limite'))\",\n        position: \"left\"\n    },\n    {\n        title: \"Filtrar Limites\",\n        content: \"Use esta barra de pesquisa para encontrar limites específicos pelo nome do paciente ou convênio.\",\n        selector: \"input[placeholder*='Buscar']\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtrar por Pacientes\",\n        content: \"Selecione um ou mais pacientes para filtrar os limites.\",\n        selector: \"div:has(> div > label:contains('Filtrar por Pacientes'))\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtrar por Convênio\",\n        content: \"Filtre os limites por convênio específico.\",\n        selector: \"select:nth-of-type(1)\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Filtrar por Tipo de Serviço\",\n        content: \"Filtre os limites por tipo de serviço específico.\",\n        selector: \"select:nth-of-type(2)\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Exportar Dados\",\n        content: \"Exporte a lista de limites em diferentes formatos usando este botão.\",\n        selector: \".export-button\",\n        position: \"bottom\"\n    },\n    {\n        title: \"Gerenciar Limites\",\n        content: \"Edite ou exclua limites existentes usando os botões de ação na tabela.\",\n        selector: \"table\",\n        position: \"top\"\n    }\n];\nconst InsuranceLimitsPage = ()=>{\n    var _selectedLimit_Person, _selectedLimit_person, _selectedLimit_Insurance, _selectedLimit_insurance;\n    _s();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth)();\n    const { addToast, toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const isAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"ADMIN\" || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    const [limits, setLimits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        insurances: [],\n        serviceTypes: [],\n        persons: []\n    });\n    const [personOptions, setPersonOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingPersonOptions, setIsLoadingPersonOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [persons, setPersons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [insurances, setInsurances] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [serviceTypes, setServiceTypes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLimit, setSelectedLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [limitFormOpen, setLimitFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(limits.map((l)=>l.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    const [isLoadingFilters, setIsLoadingFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estados para paginação\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalItems, setTotalItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para ordenação\n    const [sortField, setSortField] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"patient\");\n    const [sortDirection, setSortDirection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"asc\");\n    // Função para carregar opções de pacientes para o multi-select\n    const loadPersonOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"InsuranceLimitsPage.useCallback[loadPersonOptions]\": async ()=>{\n            setIsLoadingPersonOptions(true);\n            try {\n                // Carregar todos os pacientes para o multi-select (com limite maior)\n                const response = await _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__.personsService.getPersons({\n                    limit: 100,\n                    active: true // Apenas pacientes ativos por padrão\n                });\n                // Extrair os pacientes da resposta, garantindo que temos um array válido\n                const personsArray = (response === null || response === void 0 ? void 0 : response.persons) || (response === null || response === void 0 ? void 0 : response.people) || [];\n                const options = personsArray.map({\n                    \"InsuranceLimitsPage.useCallback[loadPersonOptions]\": (person)=>({\n                            value: person.id,\n                            label: person.fullName,\n                            // Guardar o nome para ordenação\n                            sortName: person.fullName ? person.fullName.toLowerCase() : ''\n                        })\n                }[\"InsuranceLimitsPage.useCallback[loadPersonOptions]\"]) || [];\n                // Ordenar as opções alfabeticamente pelo nome\n                const sortedOptions = options.sort({\n                    \"InsuranceLimitsPage.useCallback[loadPersonOptions].sortedOptions\": (a, b)=>a.sortName.localeCompare(b.sortName, 'pt-BR', {\n                            sensitivity: 'base'\n                        })\n                }[\"InsuranceLimitsPage.useCallback[loadPersonOptions].sortedOptions\"]);\n                console.log('Opções de pacientes ordenadas:', sortedOptions);\n                setPersonOptions(sortedOptions);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de pacientes:\", error);\n                setPersonOptions([]);\n            } finally{\n                setIsLoadingPersonOptions(false);\n            }\n        }\n    }[\"InsuranceLimitsPage.useCallback[loadPersonOptions]\"], []);\n    // Função para carregar empresas (apenas para system_admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const response = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_12__.companyService.getCompaniesForSelect();\n            setCompanies(response);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    // Carregar dados iniciais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InsuranceLimitsPage.useEffect\": ()=>{\n            loadLimits(1, filters, sortField, sortDirection);\n        }\n    }[\"InsuranceLimitsPage.useEffect\"], []);\n    // Detectar parâmetros de URL e abrir modal de edição se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InsuranceLimitsPage.useEffect\": ()=>{\n            const insuranceLimitId = searchParams.get('insuranceLimitId');\n            const openModal = searchParams.get('openModal');\n            const mode = searchParams.get('mode');\n            if (insuranceLimitId && openModal === 'true' && mode === 'edit') {\n                // Buscar o limite específico nos dados carregados\n                const targetLimit = limits.find({\n                    \"InsuranceLimitsPage.useEffect.targetLimit\": (limit)=>limit.id === insuranceLimitId\n                }[\"InsuranceLimitsPage.useEffect.targetLimit\"]);\n                if (targetLimit) {\n                    setSelectedLimit(targetLimit);\n                    setLimitFormOpen(true);\n                } else if (limits.length > 0) {\n                    // Se não encontrou nos dados carregados, mas já temos dados,\n                    // pode ser que o limite não esteja na página atual\n                    console.warn(\"Limite \".concat(insuranceLimitId, \" n\\xe3o encontrado nos dados carregados\"));\n                }\n            }\n        }\n    }[\"InsuranceLimitsPage.useEffect\"], [\n        searchParams,\n        limits\n    ]);\n    // Estado para itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Carregar limites de convênio\n    const loadLimits = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentPage, filtersToUse = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : filters, sort = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : sortField || 'patient', direction = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : sortDirection || 'asc', perPage = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a direção seja uma string válida\n            const validDirection = direction && [\n                'asc',\n                'desc'\n            ].includes(direction.toLowerCase()) ? direction.toLowerCase() : 'asc';\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentPage(pageNumber);\n            // Buscar TODOS os limites de convênio (sem paginação no backend)\n            const response = await _app_modules_people_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_8__.insuranceServiceLimitService.getAllLimits({\n                search: filtersToUse.search || undefined,\n                personIds: filtersToUse.persons.length > 0 ? filtersToUse.persons : undefined,\n                insuranceId: filtersToUse.insurances.length > 0 ? filtersToUse.insurances[0] : undefined,\n                serviceTypeId: filtersToUse.serviceTypes.length > 0 ? filtersToUse.serviceTypes[0] : undefined,\n                companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,\n                sortField: sort || 'patient',\n                sortDirection: validDirection\n            });\n            console.log('Parâmetros enviados para o serviço:', {\n                search: filtersToUse.search || undefined,\n                personIds: filtersToUse.persons.length > 0 ? filtersToUse.persons : undefined,\n                insuranceId: filtersToUse.insurances.length > 0 ? filtersToUse.insurances[0] : undefined,\n                serviceTypeId: filtersToUse.serviceTypes.length > 0 ? filtersToUse.serviceTypes[0] : undefined,\n                companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,\n                sortField: sort || 'patient',\n                sortDirection: validDirection\n            });\n            // Verificar se temos os dados no formato esperado\n            if (response && typeof response === 'object') {\n                // Extrair todos os limites\n                let allLimits = response.limits || [];\n                // A ordenação agora é feita no backend, não precisamos ordenar manualmente aqui\n                // Apenas registramos a ordenação para debug\n                console.log(\"Ordena\\xe7\\xe3o aplicada: campo=\".concat(sort || 'patient', \", dire\\xe7\\xe3o=\").concat(validDirection));\n                // Calcular o total de itens e páginas\n                const total = allLimits.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual no lado do cliente\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedLimits = allLimits.slice(startIndex, endIndex);\n                // Verificar se a página atual é válida\n                if (pageNumber > 1 && paginatedLimits.length === 0 && allLimits.length > 0) {\n                    // Se a página atual não tem itens, mas existem itens em outras páginas,\n                    // voltar para a primeira página\n                    console.log(\"P\\xe1gina \".concat(pageNumber, \" est\\xe1 vazia, voltando para a p\\xe1gina 1\"));\n                    setCurrentPage(1);\n                    const firstPageLimits = allLimits.slice(0, perPage);\n                    setLimits(firstPageLimits);\n                } else {\n                    // Atualizar o estado com os dados paginados manualmente\n                    setLimits(paginatedLimits); // Apenas os 10 itens da página atual\n                }\n                setTotalItems(total);\n                setTotalPages(pages);\n                console.log(\"Pagina\\xe7\\xe3o manual: P\\xe1gina \".concat(pageNumber, \", exibindo \").concat(paginatedLimits.length, \" itens (\").concat(startIndex + 1, \"-\").concat(Math.min(endIndex, total), \") de \").concat(total, \" total\"));\n            } else {\n                console.error('Dados de limites inválidos:', response);\n                setLimits([]);\n                setTotalItems(0);\n                setTotalPages(1);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar limites de convênio:\", error);\n            toast_error(\"Erro ao carregar limites de convênio\");\n            setLimits([]);\n            setTotalItems(0);\n            setTotalPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar opções para os filtros\n    const loadFilterOptions = async ()=>{\n        setIsLoadingFilters(true);\n        try {\n            // Carregar pessoas\n            try {\n                const personsData = await _app_modules_people_services_personsService__WEBPACK_IMPORTED_MODULE_10__.personsService.getPersons({\n                    limit: 100\n                });\n                console.log('Dados de pessoas recebidos:', personsData);\n                // Garantir que temos um array válido\n                if (personsData && typeof personsData === 'object') {\n                    // Verificar a estrutura dos dados retornados e extrair o array de pessoas\n                    const personsArray = Array.isArray(personsData) ? personsData : personsData.persons ? personsData.persons : personsData.people ? personsData.people : personsData.data ? personsData.data : [];\n                    console.log('Array de pessoas extraído:', personsArray);\n                    // Garantir que estamos definindo um array\n                    setPersons(Array.isArray(personsArray) ? personsArray : []);\n                } else {\n                    console.error('Dados de pessoas inválidos:', personsData);\n                    setPersons([]);\n                }\n            } catch (personError) {\n                console.error('Erro ao carregar pessoas:', personError);\n                setPersons([]);\n            }\n            // Carregar convênios\n            try {\n                const insurancesData = await _app_modules_people_services_insurancesService__WEBPACK_IMPORTED_MODULE_9__.insurancesService.getInsurances();\n                console.log('Dados de convênios recebidos:', insurancesData);\n                // Garantir que temos um array válido\n                if (insurancesData && typeof insurancesData === 'object') {\n                    // Verificar a estrutura dos dados retornados e extrair o array de convênios\n                    const insurancesArray = Array.isArray(insurancesData) ? insurancesData : insurancesData.insurances ? insurancesData.insurances : insurancesData.data ? insurancesData.data : [];\n                    console.log('Array de convênios extraído:', insurancesArray);\n                    // Garantir que estamos definindo um array\n                    setInsurances(Array.isArray(insurancesArray) ? insurancesArray : []);\n                } else {\n                    console.error('Dados de convênios inválidos:', insurancesData);\n                    setInsurances([]);\n                }\n            } catch (insuranceError) {\n                console.error('Erro ao carregar convênios:', insuranceError);\n                setInsurances([]);\n            }\n            // Carregar tipos de serviço\n            try {\n                const serviceTypesData = await _app_modules_scheduler_services_serviceTypeService__WEBPACK_IMPORTED_MODULE_11__.serviceTypeService.getServiceTypes();\n                console.log('Dados de tipos de serviço recebidos:', serviceTypesData);\n                // Garantir que temos um array válido\n                if (serviceTypesData && typeof serviceTypesData === 'object') {\n                    // Verificar a estrutura dos dados retornados e extrair o array de tipos de serviço\n                    const serviceTypesArray = Array.isArray(serviceTypesData) ? serviceTypesData : serviceTypesData.serviceTypes ? serviceTypesData.serviceTypes : serviceTypesData.data ? serviceTypesData.data : [];\n                    console.log('Array de tipos de serviço extraído:', serviceTypesArray);\n                    // Garantir que estamos definindo um array\n                    setServiceTypes(Array.isArray(serviceTypesArray) ? serviceTypesArray : []);\n                } else {\n                    console.error('Dados de tipos de serviço inválidos:', serviceTypesData);\n                    setServiceTypes([]);\n                }\n            } catch (serviceTypeError) {\n                console.error('Erro ao carregar tipos de serviço:', serviceTypeError);\n                setServiceTypes([]);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar opções de filtro:\", error);\n            toast_error(\"Erro ao carregar opções de filtro\");\n        } finally{\n            setIsLoadingFilters(false);\n        }\n    };\n    const handleSearch = (searchFilters)=>{\n        loadLimits(1, searchFilters, sortField, sortDirection);\n    };\n    const handlePageChange = (page)=>{\n        loadLimits(page, filters, sortField, sortDirection);\n    };\n    // Função para lidar com a mudança de ordenação\n    const handleSortChange = (field, direction)=>{\n        console.log(\"Alterando ordena\\xe7\\xe3o: campo=\".concat(field, \", dire\\xe7\\xe3o=\").concat(direction), {\n            tipoField: typeof field,\n            tipoDirection: typeof direction,\n            valorField: field,\n            valorDirection: direction\n        });\n        // Garantir que a direção seja uma string válida\n        const validDirection = direction && [\n            'asc',\n            'desc'\n        ].includes(direction.toLowerCase()) ? direction.toLowerCase() : 'asc';\n        console.log(\"Dire\\xe7\\xe3o normalizada: \".concat(validDirection));\n        setSortField(field);\n        setSortDirection(validDirection);\n        loadLimits(currentPage, filters, field, validDirection);\n    };\n    const handleEditLimit = (limit)=>{\n        setSelectedLimit(limit);\n        setLimitFormOpen(true);\n    };\n    const handleDeleteLimit = (limit)=>{\n        setSelectedLimit(limit);\n        setConfirmationDialogOpen(true);\n    };\n    const confirmDeleteLimit = async ()=>{\n        try {\n            await _app_modules_people_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_8__.insuranceServiceLimitService.deleteLimit(selectedLimit.id);\n            toast_success(\"Limite de convênio excluído com sucesso\");\n            loadLimits();\n            setConfirmationDialogOpen(false);\n        } catch (error) {\n            console.error(\"Erro ao excluir limite de convênio:\", error);\n            toast_error(\"Erro ao excluir limite de convênio\");\n        }\n    };\n    const handleExport = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Encontrar os nomes dos filtros selecionados para o subtítulo da exportação\n            let insuranceName, serviceTypeName;\n            if (filters.insurances.length > 0) {\n                const selectedInsurance = insurances.find((i)=>i.id === filters.insurances[0]);\n                insuranceName = selectedInsurance ? selectedInsurance.name : undefined;\n            }\n            if (filters.serviceTypes.length > 0) {\n                const selectedServiceType = serviceTypes.find((s)=>s.id === filters.serviceTypes[0]);\n                serviceTypeName = selectedServiceType ? selectedServiceType.name : undefined;\n            }\n            const success = await _app_modules_people_services_insuranceServiceLimitService__WEBPACK_IMPORTED_MODULE_8__.insuranceServiceLimitService.exportInsuranceLimits({\n                search: filters.search || undefined,\n                personIds: filters.persons.length > 0 ? filters.persons : undefined,\n                insuranceId: filters.insurances.length > 0 ? filters.insurances[0] : undefined,\n                insuranceName,\n                serviceTypeId: filters.serviceTypes.length > 0 ? filters.serviceTypes[0] : undefined,\n                serviceTypeName,\n                companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,\n                sortField,\n                sortDirection\n            }, format);\n            if (success) {\n                toast_success(\"Dados exportados com sucesso no formato \".concat(format.toUpperCase()));\n            } else {\n                toast_error(\"Erro ao exportar dados\");\n            }\n        } catch (error) {\n            console.error(\"Erro ao exportar dados:\", error);\n            toast_error(\"Erro ao exportar dados\");\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-orange-600 dark:text-orange-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Limites de Conv\\xeanio\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir limites selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onExport: handleExport,\n                                isExporting: isExporting,\n                                disabled: isLoading || limits.length === 0,\n                                className: \"text-orange-700 dark:text-orange-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedLimit(null);\n                                    setLimitFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Limite\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-people-icon dark:text-module-people-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                    lineNumber: 530,\n                    columnNumber: 15\n                }, void 0),\n                description: \"Configure os limites de utiliza\\xe7\\xe3o de servi\\xe7os por conv\\xeanio para cada paciente. Utilize os filtros abaixo para encontrar limites espec\\xedficos.\",\n                tutorialSteps: insuranceLimitsTutorialSteps,\n                tutorialName: \"insurance-limits-overview\",\n                moduleColor: \"people\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_InsuranceLimitsFilters__WEBPACK_IMPORTED_MODULE_19__.InsuranceLimitsFilters, {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: handleSearch\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                    lineNumber: 536,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 528,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleTable, {\n                moduleColor: \"people\",\n                title: \"Lista de Limites de Conv\\xeanios\",\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadLimits(),\n                    className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        size: 18\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                        lineNumber: 554,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                    lineNumber: 549,\n                    columnNumber: 11\n                }, void 0),\n                columns: [\n                    {\n                        header: '',\n                        field: 'select',\n                        width: '50px',\n                        sortable: false\n                    },\n                    {\n                        header: 'Paciente',\n                        field: 'patient',\n                        width: '25%',\n                        // Função de acesso personalizada para ordenação\n                        accessor: (limit)=>{\n                            var _limit_Person, _limit_person;\n                            return (limit === null || limit === void 0 ? void 0 : (_limit_Person = limit.Person) === null || _limit_Person === void 0 ? void 0 : _limit_Person.fullName) || (limit === null || limit === void 0 ? void 0 : (_limit_person = limit.person) === null || _limit_person === void 0 ? void 0 : _limit_person.fullName) || '';\n                        }\n                    },\n                    {\n                        header: 'Convênio',\n                        field: 'insurance',\n                        width: '20%',\n                        accessor: (limit)=>{\n                            var _limit_Insurance, _limit_insurance;\n                            return (limit === null || limit === void 0 ? void 0 : (_limit_Insurance = limit.Insurance) === null || _limit_Insurance === void 0 ? void 0 : _limit_Insurance.name) || (limit === null || limit === void 0 ? void 0 : (_limit_insurance = limit.insurance) === null || _limit_insurance === void 0 ? void 0 : _limit_insurance.name) || '';\n                        }\n                    },\n                    {\n                        header: 'Tipo de Serviço',\n                        field: 'serviceType',\n                        width: '25%',\n                        accessor: (limit)=>{\n                            var _limit_ServiceType, _limit_serviceType;\n                            return (limit === null || limit === void 0 ? void 0 : (_limit_ServiceType = limit.ServiceType) === null || _limit_ServiceType === void 0 ? void 0 : _limit_ServiceType.name) || (limit === null || limit === void 0 ? void 0 : (_limit_serviceType = limit.serviceType) === null || _limit_serviceType === void 0 ? void 0 : _limit_serviceType.name) || '';\n                        }\n                    },\n                    {\n                        header: 'Limite Mensal',\n                        field: 'monthlyLimit',\n                        width: '15%'\n                    },\n                    {\n                        header: 'Ações',\n                        field: 'actions',\n                        className: 'text-right',\n                        width: '15%',\n                        sortable: false\n                    }\n                ],\n                data: limits,\n                isLoading: isLoading,\n                emptyMessage: \"Nenhum limite de conv\\xeanio encontrado\",\n                emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                    size: 24\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                    lineNumber: 584,\n                    columnNumber: 20\n                }, void 0),\n                tableId: \"people-insurance-limits-table\",\n                enableColumnToggle: true,\n                defaultSortField: \"patient\",\n                defaultSortDirection: \"asc\",\n                currentPage: currentPage,\n                totalPages: totalPages,\n                totalItems: totalItems,\n                onPageChange: handlePageChange,\n                onSort: handleSortChange,\n                sortField: sortField,\n                sortDirection: sortDirection,\n                showPagination: true,\n                itemsPerPage: itemsPerPage,\n                onItemsPerPageChange: (newItemsPerPage)=>{\n                    setItemsPerPage(newItemsPerPage);\n                    loadLimits(1, filters, sortField, sortDirection, newItemsPerPage);\n                },\n                selectedIds: selectedIds,\n                onSelectAll: handleSelectAll,\n                renderRow: (limit, index, moduleColors, visibleColumns)=>{\n                    var _limit_Person, _limit_person, _limit_Insurance, _limit_insurance, _limit_ServiceType, _limit_serviceType, _limit_Person1, _limit_person1;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleCheckbox, {\n                                    moduleColor: \"people\",\n                                    checked: selectedIds.includes(limit.id),\n                                    onChange: (e)=>handleSelectOne(limit.id, e.target.checked),\n                                    name: \"select-limit-\".concat(limit.id)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 608,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 607,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('patient') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                                lineNumber: 620,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 619,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                children: (limit === null || limit === void 0 ? void 0 : (_limit_Person = limit.Person) === null || _limit_Person === void 0 ? void 0 : _limit_Person.fullName) || (limit === null || limit === void 0 ? void 0 : (_limit_person = limit.person) === null || _limit_person === void 0 ? void 0 : _limit_person.fullName) || \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                                lineNumber: 623,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 622,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 618,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('insurance') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 634,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-700 dark:text-neutral-300 truncate\",\n                                            children: (limit === null || limit === void 0 ? void 0 : (_limit_Insurance = limit.Insurance) === null || _limit_Insurance === void 0 ? void 0 : _limit_Insurance.name) || (limit === null || limit === void 0 ? void 0 : (_limit_insurance = limit.insurance) === null || _limit_insurance === void 0 ? void 0 : _limit_insurance.name) || \"N/A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 635,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 633,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 632,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('serviceType') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 645,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-700 dark:text-neutral-300 truncate\",\n                                            children: (limit === null || limit === void 0 ? void 0 : (_limit_ServiceType = limit.ServiceType) === null || _limit_ServiceType === void 0 ? void 0 : _limit_ServiceType.name) || (limit === null || limit === void 0 ? void 0 : (_limit_serviceType = limit.serviceType) === null || _limit_serviceType === void 0 ? void 0 : _limit_serviceType.name) || \"N/A\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 646,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 644,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 643,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('monthlyLimit') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 656,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-700 dark:text-neutral-300\",\n                                            children: (limit === null || limit === void 0 ? void 0 : limit.monthlyLimit) > 0 ? \"\".concat(limit.monthlyLimit, \"x por m\\xeas\") : \"Ilimitado\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 657,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 655,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 654,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-4 py-4 text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-end gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_ShareButton__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            itemType: \"insurance-limit\",\n                                            itemId: limit.id,\n                                            itemTitle: \"Limite: \".concat((limit === null || limit === void 0 ? void 0 : (_limit_Person1 = limit.Person) === null || _limit_Person1 === void 0 ? void 0 : _limit_Person1.fullName) || (limit === null || limit === void 0 ? void 0 : (_limit_person1 = limit.person) === null || _limit_person1 === void 0 ? void 0 : _limit_person1.fullName) || 'Paciente'),\n                                            size: \"xs\",\n                                            variant: \"ghost\",\n                                            className: \"text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 667,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditLimit(limit),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                                lineNumber: 680,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 675,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleDeleteLimit(limit),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\",\n                                            title: \"Excluir\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Edit_Filter_Plus_RefreshCw_Search_Shield_Tag_Trash_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                                lineNumber: 687,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                            lineNumber: 682,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                    lineNumber: 666,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, limit.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                        lineNumber: 605,\n                        columnNumber: 11\n                    }, void 0);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 545,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmDeleteLimit,\n                title: \"Excluir Limite de Conv\\xeanio\",\n                message: \"Tem certeza que deseja excluir o limite para \".concat((selectedLimit === null || selectedLimit === void 0 ? void 0 : (_selectedLimit_Person = selectedLimit.Person) === null || _selectedLimit_Person === void 0 ? void 0 : _selectedLimit_Person.fullName) || (selectedLimit === null || selectedLimit === void 0 ? void 0 : (_selectedLimit_person = selectedLimit.person) === null || _selectedLimit_person === void 0 ? void 0 : _selectedLimit_person.fullName) || 'este paciente', \" com o conv\\xeanio \").concat((selectedLimit === null || selectedLimit === void 0 ? void 0 : (_selectedLimit_Insurance = selectedLimit.Insurance) === null || _selectedLimit_Insurance === void 0 ? void 0 : _selectedLimit_Insurance.name) || (selectedLimit === null || selectedLimit === void 0 ? void 0 : (_selectedLimit_insurance = selectedLimit.insurance) === null || _selectedLimit_insurance === void 0 ? void 0 : _selectedLimit_insurance.name) || 'selecionado', \"?\"),\n                variant: \"danger\",\n                moduleColor: \"people\",\n                confirmText: \"Excluir\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 697,\n                columnNumber: 7\n            }, undefined),\n            limitFormOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_people_InsuranceLimitFormModal__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                isOpen: limitFormOpen,\n                onClose: ()=>setLimitFormOpen(false),\n                limit: selectedLimit,\n                onSuccess: ()=>{\n                    setLimitFormOpen(false);\n                    loadLimits();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 711,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_tutorial_TutorialManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\people\\\\InsuranceLimitsPage\\\\InsuranceLimitsPage.js\",\n        lineNumber: 486,\n        columnNumber: 5\n    }, undefined);\n};\n_s(InsuranceLimitsPage, \"nJu2wZPlYO9mrRkV7nLCrKACAe8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_14__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = InsuranceLimitsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InsuranceLimitsPage);\nvar _c;\n$RefreshReg$(_c, \"InsuranceLimitsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js\n"));

/***/ })

});