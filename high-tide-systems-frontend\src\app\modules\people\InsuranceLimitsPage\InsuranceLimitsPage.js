"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleSelect, ModuleInput, ModuleTable, ModuleCheckbox } from "@/components/ui";
import MultiSelect from "@/components/ui/multi-select";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Shield,
  CreditCard,
  User,
  Tag,
  Clock
} from "lucide-react";
import { insuranceServiceLimitService } from "@/app/modules/people/services/insuranceServiceLimitService";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { personsService } from "@/app/modules/people/services/personsService";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import InsuranceLimitFormModal from "@/components/people/InsuranceLimitFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import ShareButton from "@/components/common/ShareButton";
import { InsuranceLimitsFilters } from "@/components/people/InsuranceLimitsFilters";

// Tutorial steps para a página de limites de convênio
const insuranceLimitsTutorialSteps = [
  {
    title: "Limites de Convênio",
    content: "Esta tela permite configurar os limites de utilização de serviços por convênio para cada paciente.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Limite",
    content: "Clique aqui para adicionar um novo limite de convênio.",
    selector: "button:has(span:contains('Novo Limite'))",
    position: "left"
  },
  {
    title: "Filtrar Limites",
    content: "Use esta barra de pesquisa para encontrar limites específicos pelo nome do paciente ou convênio.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Pacientes",
    content: "Selecione um ou mais pacientes para filtrar os limites.",
    selector: "div:has(> div > label:contains('Filtrar por Pacientes'))",
    position: "bottom"
  },
  {
    title: "Filtrar por Convênio",
    content: "Filtre os limites por convênio específico.",
    selector: "select:nth-of-type(1)",
    position: "bottom"
  },
  {
    title: "Filtrar por Tipo de Serviço",
    content: "Filtre os limites por tipo de serviço específico.",
    selector: "select:nth-of-type(2)",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de limites em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Limites",
    content: "Edite ou exclua limites existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const InsuranceLimitsPage = () => {
  const { user: currentUser } = useAuth();
  const { addToast, toast_success, toast_error } = useToast();
  const searchParams = useSearchParams();
  const isAdmin = currentUser?.role === "ADMIN" || currentUser?.role === "SYSTEM_ADMIN";
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const [limits, setLimits] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    companies: [],
    insurances: [],
    serviceTypes: [],
    persons: []
  });
  const [personOptions, setPersonOptions] = useState([]);
  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);
  const [persons, setPersons] = useState([]);
  const [insurances, setInsurances] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedLimit, setSelectedLimit] = useState(null);
  const [limitFormOpen, setLimitFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedIds(limits.map(l => l.id));
    } else {
      setSelectedIds([]);
    }
  };

  const handleSelectOne = (id, checked) => {
    setSelectedIds(prev => checked ? [...prev, id] : prev.filter(i => i !== id));
  };
  const [isLoadingFilters, setIsLoadingFilters] = useState(false);
  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  // Estado para ordenação
  const [sortField, setSortField] = useState("patient");
  const [sortDirection, setSortDirection] = useState("asc");

  // Função para carregar opções de pacientes para o multi-select
  const loadPersonOptions = useCallback(async () => {
    setIsLoadingPersonOptions(true);
    try {
      // Carregar todos os pacientes para o multi-select (com limite maior)
      const response = await personsService.getPersons({
        limit: 100, // Limite maior para ter mais opções
        active: true // Apenas pacientes ativos por padrão
      });

      // Extrair os pacientes da resposta, garantindo que temos um array válido
      const personsArray = response?.persons || response?.people || [];

      const options = personsArray.map(person => ({
        value: person.id,
        label: person.fullName,
        // Guardar o nome para ordenação
        sortName: person.fullName ? person.fullName.toLowerCase() : ''
      })) || [];

      // Ordenar as opções alfabeticamente pelo nome
      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName, 'pt-BR', { sensitivity: 'base' }));

      console.log('Opções de pacientes ordenadas:', sortedOptions);
      setPersonOptions(sortedOptions);
    } catch (error) {
      console.error("Erro ao carregar opções de pacientes:", error);
      setPersonOptions([]);
    } finally {
      setIsLoadingPersonOptions(false);
    }
  }, []);

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    loadLimits(1, filters, sortField, sortDirection);
  }, []);

  // Detectar parâmetros de URL e abrir modal de edição se necessário
  useEffect(() => {
    const insuranceLimitId = searchParams.get('insuranceLimitId');
    const openModal = searchParams.get('openModal');
    const mode = searchParams.get('mode');

    if (insuranceLimitId && openModal === 'true' && mode === 'edit') {
      // Buscar o limite específico nos dados carregados
      const targetLimit = limits.find(limit => limit.id === insuranceLimitId);
      
      if (targetLimit) {
        setSelectedLimit(targetLimit);
        setLimitFormOpen(true);
      } else if (limits.length > 0) {
        // Se não encontrou nos dados carregados, mas já temos dados,
        // pode ser que o limite não esteja na página atual
        console.warn(`Limite ${insuranceLimitId} não encontrado nos dados carregados`);
      }
    }
  }, [searchParams, limits]);

  // Estado para itens por página
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Carregar limites de convênio
  const loadLimits = async (
    page = currentPage,
    filtersToUse = filters,
    sort = sortField || 'patient',
    direction = sortDirection || 'asc',
    perPage = itemsPerPage
  ) => {
    setIsLoading(true);
    try {
      // Garantir que a direção seja uma string válida
      const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())
        ? direction.toLowerCase()
        : 'asc';

      // Garantir que a página é um número
      const pageNumber = parseInt(page, 10);

      // Atualizar o estado da página atual
      setCurrentPage(pageNumber);

      // Buscar TODOS os limites de convênio (sem paginação no backend)
      const response = await insuranceServiceLimitService.getAllLimits({
        search: filtersToUse.search || undefined,
        personIds: filtersToUse.persons.length > 0 ? filtersToUse.persons : undefined,
        insuranceId: filtersToUse.insurances.length > 0 ? filtersToUse.insurances[0] : undefined,
        serviceTypeId: filtersToUse.serviceTypes.length > 0 ? filtersToUse.serviceTypes[0] : undefined,
        companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,
        sortField: sort || 'patient',
        sortDirection: validDirection
      });

      console.log('Parâmetros enviados para o serviço:', {
        search: filtersToUse.search || undefined,
        personIds: filtersToUse.persons.length > 0 ? filtersToUse.persons : undefined,
        insuranceId: filtersToUse.insurances.length > 0 ? filtersToUse.insurances[0] : undefined,
        serviceTypeId: filtersToUse.serviceTypes.length > 0 ? filtersToUse.serviceTypes[0] : undefined,
        companyId: filtersToUse.companies.length > 0 ? filtersToUse.companies[0] : undefined,
        sortField: sort || 'patient',
        sortDirection: validDirection
      });

      // Verificar se temos os dados no formato esperado
      if (response && typeof response === 'object') {
        // Extrair todos os limites
        let allLimits = response.limits || [];

        // A ordenação agora é feita no backend, não precisamos ordenar manualmente aqui
        // Apenas registramos a ordenação para debug
        console.log(`Ordenação aplicada: campo=${sort || 'patient'}, direção=${validDirection}`);

        // Calcular o total de itens e páginas
        const total = allLimits.length;
        const pages = Math.ceil(total / perPage) || 1;

        // Aplicar paginação manual no lado do cliente
        const startIndex = (pageNumber - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedLimits = allLimits.slice(startIndex, endIndex);

        // Verificar se a página atual é válida
        if (pageNumber > 1 && paginatedLimits.length === 0 && allLimits.length > 0) {
          // Se a página atual não tem itens, mas existem itens em outras páginas,
          // voltar para a primeira página
          console.log(`Página ${pageNumber} está vazia, voltando para a página 1`);
          setCurrentPage(1);
          const firstPageLimits = allLimits.slice(0, perPage);
          setLimits(firstPageLimits);
        } else {
          // Atualizar o estado com os dados paginados manualmente
          setLimits(paginatedLimits); // Apenas os 10 itens da página atual
        }

        setTotalItems(total);
        setTotalPages(pages);

        console.log(`Paginação manual: Página ${pageNumber}, exibindo ${paginatedLimits.length} itens (${startIndex+1}-${Math.min(endIndex, total)}) de ${total} total`);
      } else {
        console.error('Dados de limites inválidos:', response);
        setLimits([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Erro ao carregar limites de convênio:", error);
      toast_error("Erro ao carregar limites de convênio");
      setLimits([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar opções para os filtros
  const loadFilterOptions = async () => {
    setIsLoadingFilters(true);
    try {
      // Carregar pessoas
      try {
        const personsData = await personsService.getPersons({ limit: 100 });
        console.log('Dados de pessoas recebidos:', personsData);

        // Garantir que temos um array válido
        if (personsData && typeof personsData === 'object') {
          // Verificar a estrutura dos dados retornados e extrair o array de pessoas
          const personsArray = Array.isArray(personsData) ? personsData :
                              personsData.persons ? personsData.persons :
                              personsData.people ? personsData.people :
                              personsData.data ? personsData.data : [];

          console.log('Array de pessoas extraído:', personsArray);
          // Garantir que estamos definindo um array
          setPersons(Array.isArray(personsArray) ? personsArray : []);
        } else {
          console.error('Dados de pessoas inválidos:', personsData);
          setPersons([]);
        }
      } catch (personError) {
        console.error('Erro ao carregar pessoas:', personError);
        setPersons([]);
      }

      // Carregar convênios
      try {
        const insurancesData = await insurancesService.getInsurances();
        console.log('Dados de convênios recebidos:', insurancesData);

        // Garantir que temos um array válido
        if (insurancesData && typeof insurancesData === 'object') {
          // Verificar a estrutura dos dados retornados e extrair o array de convênios
          const insurancesArray = Array.isArray(insurancesData) ? insurancesData :
                                 insurancesData.insurances ? insurancesData.insurances :
                                 insurancesData.data ? insurancesData.data : [];

          console.log('Array de convênios extraído:', insurancesArray);
          // Garantir que estamos definindo um array
          setInsurances(Array.isArray(insurancesArray) ? insurancesArray : []);
        } else {
          console.error('Dados de convênios inválidos:', insurancesData);
          setInsurances([]);
        }
      } catch (insuranceError) {
        console.error('Erro ao carregar convênios:', insuranceError);
        setInsurances([]);
      }

      // Carregar tipos de serviço
      try {
        const serviceTypesData = await serviceTypeService.getServiceTypes();
        console.log('Dados de tipos de serviço recebidos:', serviceTypesData);

        // Garantir que temos um array válido
        if (serviceTypesData && typeof serviceTypesData === 'object') {
          // Verificar a estrutura dos dados retornados e extrair o array de tipos de serviço
          const serviceTypesArray = Array.isArray(serviceTypesData) ? serviceTypesData :
                                   serviceTypesData.serviceTypes ? serviceTypesData.serviceTypes :
                                   serviceTypesData.data ? serviceTypesData.data : [];

          console.log('Array de tipos de serviço extraído:', serviceTypesArray);
          // Garantir que estamos definindo um array
          setServiceTypes(Array.isArray(serviceTypesArray) ? serviceTypesArray : []);
        } else {
          console.error('Dados de tipos de serviço inválidos:', serviceTypesData);
          setServiceTypes([]);
        }
      } catch (serviceTypeError) {
        console.error('Erro ao carregar tipos de serviço:', serviceTypeError);
        setServiceTypes([]);
      }
    } catch (error) {
      console.error("Erro ao carregar opções de filtro:", error);
      toast_error("Erro ao carregar opções de filtro");
    } finally {
      setIsLoadingFilters(false);
    }
  };

  const handleSearch = (searchFilters) => {
    loadLimits(1, searchFilters, sortField, sortDirection);
  };

  const handlePageChange = (page) => {
    loadLimits(page, filters, sortField, sortDirection);
  };

  // Função para lidar com a mudança de ordenação
  const handleSortChange = (field, direction) => {
    console.log(`Alterando ordenação: campo=${field}, direção=${direction}`, {
      tipoField: typeof field,
      tipoDirection: typeof direction,
      valorField: field,
      valorDirection: direction
    });

    // Garantir que a direção seja uma string válida
    const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())
      ? direction.toLowerCase()
      : 'asc';

    console.log(`Direção normalizada: ${validDirection}`);

    setSortField(field);
    setSortDirection(validDirection);
    loadLimits(currentPage, filters, field, validDirection);
  };

  const handleEditLimit = (limit) => {
    setSelectedLimit(limit);
    setLimitFormOpen(true);
  };

  const handleDeleteLimit = (limit) => {
    setSelectedLimit(limit);
    setConfirmationDialogOpen(true);
  };

  const confirmDeleteLimit = async () => {
    try {
      await insuranceServiceLimitService.deleteLimit(selectedLimit.id);
      toast_success("Limite de convênio excluído com sucesso");
      loadLimits();
      setConfirmationDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir limite de convênio:", error);
      toast_error("Erro ao excluir limite de convênio");
    }
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Encontrar os nomes dos filtros selecionados para o subtítulo da exportação
      let insuranceName, serviceTypeName;

      if (filters.insurances.length > 0) {
        const selectedInsurance = insurances.find(i => i.id === filters.insurances[0]);
        insuranceName = selectedInsurance ? selectedInsurance.name : undefined;
      }

      if (filters.serviceTypes.length > 0) {
        const selectedServiceType = serviceTypes.find(s => s.id === filters.serviceTypes[0]);
        serviceTypeName = selectedServiceType ? selectedServiceType.name : undefined;
      }

      const success = await insuranceServiceLimitService.exportInsuranceLimits({
        search: filters.search || undefined,
        personIds: filters.persons.length > 0 ? filters.persons : undefined,
        insuranceId: filters.insurances.length > 0 ? filters.insurances[0] : undefined,
        insuranceName,
        serviceTypeId: filters.serviceTypes.length > 0 ? filters.serviceTypes[0] : undefined,
        serviceTypeName,
        companyId: filters.companies.length > 0 ? filters.companies[0] : undefined,
        sortField,
        sortDirection
      }, format);

      if (success) {
        toast_success(`Dados exportados com sucesso no formato ${format.toUpperCase()}`);
      } else {
        toast_error("Erro ao exportar dados");
      }
    } catch (error) {
      console.error("Erro ao exportar dados:", error);
      toast_error("Erro ao exportar dados");
    } finally {
      setIsExporting(false);
    }
  };



  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <CreditCard size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Limites de Convênio
        </h1>

        <div className="flex items-center gap-2">
          {selectedIds.length > 0 && (
            <button
              onClick={() => console.log('Excluir limites selecionados:', selectedIds)}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all"
              title="Excluir selecionados"
            >
              <Trash size={18} />
              <span className="font-medium">Excluir Selecionados ({selectedIds.length})</span>
            </button>
          )}
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || limits.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          {/* Botão de adicionar */}
          <button
            onClick={() => {
              setSelectedLimit(null);
              setLimitFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Limite</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros e Busca"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Configure os limites de utilização de serviços por convênio para cada paciente. Utilize os filtros abaixo para encontrar limites específicos."
        tutorialSteps={insuranceLimitsTutorialSteps}
        tutorialName="insurance-limits-overview"
        moduleColor="people"
        filters={
          <InsuranceLimitsFilters
            filters={filters}
            onFiltersChange={setFilters}
            onSearch={handleSearch}
          />
        }
      />

      {/* Tabela de Limites de Convênio */}
      <ModuleTable
        moduleColor="people"
        title="Lista de Limites de Convênios"
        headerContent={
          <button
            onClick={() => loadLimits()}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-module-people-primary dark:hover:text-module-people-primary-dark transition-colors"
            title="Atualizar lista"
          >
            <RefreshCw size={18} />
          </button>
        }
        columns={[
          { header: '', field: 'select', width: '50px', sortable: false },
          {
            header: 'Paciente',
            field: 'patient',
            width: '25%',
            // Função de acesso personalizada para ordenação
            accessor: (limit) => limit?.Person?.fullName || limit?.person?.fullName || ''
          },
          {
            header: 'Convênio',
            field: 'insurance',
            width: '20%',
            accessor: (limit) => limit?.Insurance?.name || limit?.insurance?.name || ''
          },
          {
            header: 'Tipo de Serviço',
            field: 'serviceType',
            width: '25%',
            accessor: (limit) => limit?.ServiceType?.name || limit?.serviceType?.name || ''
          },
          { header: 'Limite Mensal', field: 'monthlyLimit', width: '15%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={limits} // Já contém apenas os 10 itens da página atual
        isLoading={isLoading}
        emptyMessage="Nenhum limite de convênio encontrado"
        emptyIcon={<Shield size={24} />}
        tableId="people-insurance-limits-table"
        enableColumnToggle={true}
        defaultSortField="patient"
        defaultSortDirection="asc"
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onSort={handleSortChange}
        sortField={sortField}
        sortDirection={sortDirection}
        showPagination={true}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={(newItemsPerPage) => {
          setItemsPerPage(newItemsPerPage);
          loadLimits(1, filters, sortField, sortDirection, newItemsPerPage);
        }}
        selectedIds={selectedIds}
        onSelectAll={handleSelectAll}
        renderRow={(limit, index, moduleColors, visibleColumns) => (
          <tr key={limit.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('select') && (
              <td className="px-6 py-4 text-center">
                <ModuleCheckbox
                  moduleColor="people"
                  checked={selectedIds.includes(limit.id)}
                  onChange={(e) => handleSelectOne(limit.id, e.target.checked)}
                  name={`select-limit-${limit.id}`}
                />
              </td>
            )}
            {visibleColumns.includes('patient') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center">
                    <User size={20} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {limit?.Person?.fullName || limit?.person?.fullName || "N/A"}
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('insurance') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300 truncate">
                    {limit?.Insurance?.name || limit?.insurance?.name || "N/A"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('serviceType') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <Tag className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300 truncate">
                    {limit?.ServiceType?.name || limit?.serviceType?.name || "N/A"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('monthlyLimit') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300">
                    {limit?.monthlyLimit > 0 ? `${limit.monthlyLimit}x por mês` : "Ilimitado"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right">
                <div className="flex items-center justify-end gap-2">
                  <ShareButton
                    itemType="insurance-limit"
                    itemId={limit.id}
                    itemTitle={`Limite: ${limit?.Person?.fullName || limit?.person?.fullName || 'Paciente'}`}
                    size="xs"
                    variant="ghost"
                    className="text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400"
                  />
                  <button
                    onClick={() => handleEditLimit(limit)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteLimit(limit)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmDeleteLimit}
        title="Excluir Limite de Convênio"
        message={`Tem certeza que deseja excluir o limite para ${selectedLimit?.Person?.fullName || selectedLimit?.person?.fullName || 'este paciente'} com o convênio ${selectedLimit?.Insurance?.name || selectedLimit?.insurance?.name || 'selecionado'}?`}
        variant="danger"
        moduleColor="people"
        confirmText="Excluir"
        cancelText="Cancelar"
      />

      {/* Limit Form Modal */}
      {limitFormOpen && (
        <InsuranceLimitFormModal
          isOpen={limitFormOpen}
          onClose={() => setLimitFormOpen(false)}
          limit={selectedLimit}
          onSuccess={() => {
            setLimitFormOpen(false);
            loadLimits();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default InsuranceLimitsPage;
