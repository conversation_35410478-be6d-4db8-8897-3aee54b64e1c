"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/bug-reports/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js":
/*!*****************************************************!*\
  !*** ./src/app/dashboard/admin/bug-reports/page.js ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Edit,Eye,Filter,RefreshCw,Search,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ModuleHeader */ \"(app-pages-browser)/./src/components/ui/ModuleHeader.js\");\n/* harmony import */ var _components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/ModuleTable */ \"(app-pages-browser)/./src/components/ui/ModuleTable.js\");\n/* harmony import */ var _components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/bug-report/BugReportsFilters */ \"(app-pages-browser)/./src/components/bug-report/BugReportsFilters.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/bugReportService */ \"(app-pages-browser)/./src/services/bugReportService.js\");\n/* harmony import */ var _components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/BugDetailsModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugDetailsModal.js\");\n/* harmony import */ var _components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/BugEditModal */ \"(app-pages-browser)/./src/app/dashboard/admin/bug-reports/components/BugEditModal.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst BugReportsPage = ()=>{\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { toast_error, toast_success } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [bugs, setBugs] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedBug, setSelectedBug] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetailsModal, setShowDetailsModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: '',\n        status: [],\n        priority: [],\n        category: [],\n        companies: []\n    });\n    // Carregar dados\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BugReportsPage.useEffect\": ()=>{\n            if (!user) return;\n            if (user.role !== 'SYSTEM_ADMIN') {\n                router.push('/dashboard');\n                return;\n            }\n            loadBugReports();\n        }\n    }[\"BugReportsPage.useEffect\"], [\n        user,\n        router,\n        filters,\n        pagination.page\n    ]);\n    const loadBugReports = async ()=>{\n        try {\n            setLoading(true);\n            const params = {\n                page: pagination.page,\n                limit: pagination.limit,\n                ...filters\n            };\n            // Remove filtros vazios\n            Object.keys(params).forEach((key)=>{\n                if (Array.isArray(params[key]) && params[key].length === 0) {\n                    delete params[key];\n                } else if (params[key] === '') {\n                    delete params[key];\n                }\n            });\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.listAll(params);\n            if (response.data) {\n                setBugs(response.data.bugReports);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: response.data.pagination.total,\n                        totalPages: response.data.pagination.totalPages\n                    }));\n            }\n        } catch (error) {\n            console.error('Erro ao carregar bug reports:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao carregar relatórios de bugs'\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadStats = async ()=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.getStats();\n            if (response.data) {\n                setStats(response.data);\n            }\n        } catch (error) {\n            console.error('Erro ao carregar estatísticas:', error);\n        }\n    };\n    if ((user === null || user === void 0 ? void 0 : user.role) !== 'SYSTEM_ADMIN') {\n        return null;\n    }\n    const handleViewBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowDetailsModal(true);\n    };\n    const handleEditBug = (bug)=>{\n        setSelectedBug(bug);\n        setShowEditModal(true);\n    };\n    const handleSaveBug = async (updatedBug)=>{\n        try {\n            const response = await _services_bugReportService__WEBPACK_IMPORTED_MODULE_8__.bugReportService.updateStatus(updatedBug.id, {\n                status: updatedBug.status,\n                priority: updatedBug.priority,\n                adminNotes: updatedBug.adminNotes\n            });\n            if (response.data) {\n                setBugs((prev)=>prev.map((bug)=>bug.id === updatedBug.id ? {\n                            ...bug,\n                            ...updatedBug\n                        } : bug));\n                setShowEditModal(false);\n                setSelectedBug(null);\n                toast_success({\n                    title: 'Sucesso',\n                    message: 'Bug atualizado com sucesso'\n                });\n            }\n        } catch (error) {\n            console.error('Erro ao atualizar bug:', error);\n            toast_error({\n                title: 'Erro',\n                message: 'Erro ao atualizar o bug'\n            });\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case 'OPEN':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 157,\n                    columnNumber: 16\n                }, undefined);\n            case 'IN_PROGRESS':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 159,\n                    columnNumber: 16\n                }, undefined);\n            case 'RESOLVED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 161,\n                    columnNumber: 16\n                }, undefined);\n            case 'CLOSED':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 163,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4 bg-gray-500 rounded-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 165,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        const colors = {\n            'CRITICAL': 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800',\n            'HIGH': 'text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:border-orange-800',\n            'MEDIUM': 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:text-yellow-400 dark:border-yellow-800',\n            'LOW': 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\n        };\n        return colors[priority] || 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800';\n    };\n    const getPriorityLabel = (priority)=>{\n        const labels = {\n            'CRITICAL': 'Crítica',\n            'HIGH': 'Alta',\n            'MEDIUM': 'Média',\n            'LOW': 'Baixa'\n        };\n        return labels[priority] || 'Desconhecida';\n    };\n    const columns = [\n        {\n            header: 'Status',\n            field: 'status',\n            width: '120px'\n        },\n        {\n            header: 'Bug',\n            field: 'title',\n            width: 'auto'\n        },\n        {\n            header: 'Prioridade',\n            field: 'priority',\n            width: '120px'\n        },\n        {\n            header: 'Reportado por',\n            field: 'reportedBy',\n            width: '200px'\n        },\n        {\n            header: 'Data',\n            field: 'createdAt',\n            width: '120px'\n        },\n        {\n            header: 'Ações',\n            field: 'actions',\n            width: '120px',\n            sortable: false\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 28,\n                            className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900 dark:text-white\",\n                                    children: \"Relat\\xf3rios de Bugs\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Gerencie todos os bugs reportados pelos usu\\xe1rios do sistema\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleHeader__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                title: \"Filtros e Busca\",\n                description: \"Utilize os filtros abaixo para encontrar bugs espec\\xedficos.\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 218,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                customButtons: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadBugReports(),\n                        disabled: loading,\n                        className: \"flex items-center gap-2 px-3 py-1 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors disabled:opacity-50\",\n                        title: \"Buscar\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, void 0),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"hidden sm:inline\",\n                                children: \"Buscar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 222,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 221,\n                    columnNumber: 11\n                }, void 0),\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_bug_report_BugReportsFilters__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    filters: filters,\n                    onFiltersChange: setFilters,\n                    onSearch: ()=>{}\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 234,\n                    columnNumber: 11\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleTable__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                moduleColor: \"admin\",\n                title: \"Lista de Bugs Reportados\",\n                data: bugs,\n                columns: columns,\n                isLoading: loading,\n                emptyMessage: \"Nenhum bug reportado ainda.\",\n                emptyIcon: \"\\uD83D\\uDC1B\",\n                enableColumnToggle: true,\n                headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>loadBugReports(),\n                    disabled: loading,\n                    className: \"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50\",\n                    title: \"Atualizar lista\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        size: 16,\n                        className: \"text-gray-600 dark:text-gray-300 \".concat(loading ? 'animate-spin' : '')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                    lineNumber: 252,\n                    columnNumber: 11\n                }, void 0),\n                renderRow: (bug, _index, moduleColors, visibleColumns)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                        className: moduleColors.hoverBg,\n                        children: [\n                            visibleColumns.includes('status') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        getStatusIcon(bug.status),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: bug.status === 'OPEN' ? 'Aberto' : bug.status === 'IN_PROGRESS' ? 'Em Progresso' : bug.status === 'RESOLVED' ? 'Resolvido' : 'Fechado'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 268,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 267,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('title') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs cursor-help\",\n                                            title: bug.description,\n                                            children: bug.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('priority') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-2 py-1 text-xs font-medium rounded-full border \".concat(getPriorityColor(bug.priority)),\n                                    children: getPriorityLabel(bug.priority)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 294,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('reportedBy') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"font-medium text-gray-900 dark:text-white\",\n                                            children: bug.reportedBy.fullName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                            children: bug.company.name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 303,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 302,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('createdAt') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\",\n                                children: new Date(bug.createdAt).toLocaleDateString('pt-BR')\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, void 0),\n                            visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-6 py-4 whitespace-nowrap text-right\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleViewBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\",\n                                            title: \"Visualizar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 324,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 319,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleEditBug(bug),\n                                            className: \"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\",\n                                            title: \"Editar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Edit_Eye_Filter_RefreshCw_Search_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                                lineNumber: 331,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                            lineNumber: 326,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, void 0)\n                        ]\n                    }, bug.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                        lineNumber: 265,\n                        columnNumber: 11\n                    }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugDetailsModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: showDetailsModal,\n                onClose: ()=>{\n                    setShowDetailsModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BugEditModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setSelectedBug(null);\n                },\n                bug: selectedBug,\n                onSave: handleSaveBug\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\dashboard\\\\admin\\\\bug-reports\\\\page.js\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, undefined);\n};\n_s(BugReportsPage, \"q81YIsVpT1m/rqoFz1uU63xXAZc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = BugReportsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BugReportsPage);\nvar _c;\n$RefreshReg$(_c, \"BugReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/admin/bug-reports/page.js\n"));

/***/ })

});