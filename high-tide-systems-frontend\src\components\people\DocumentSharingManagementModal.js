"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog.jsx";
import Button from "@/components/ui/Button.js";
import Input from "@/components/ui/Input.js";
import Label from "@/components/ui/Label.js";
import { ModuleSelect } from "@/components/ui";
import Badge from "@/components/ui/Badge.js";
import { X, Users, User, Building, Search, Plus, Eye, UserCheck, UserX, Share2, AlertCircle, CheckCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const DocumentSharingManagementModal = ({ isOpen, onClose, document, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [users, setUsers] = useState([]);
  const [professions, setProfessions] = useState([]);
  const [clients, setClients] = useState([]);
  const [currentPermissions, setCurrentPermissions] = useState({
    users: [],
    professions: [],
    clients: []
  });
  const [newPermissions, setNewPermissions] = useState({
    users: [],
    professions: [],
    clients: []
  });
  const [userSearch, setUserSearch] = useState("");
  const [clientSearch, setClientSearch] = useState("");
  const [selectedProfession, setSelectedProfession] = useState("");
  const [error, setError] = useState(null);

  // Carregar dados iniciais
  useEffect(() => {
    if (isOpen && document) {
      setIsLoadingData(true);
      setError(null);
      Promise.all([
        loadUsers(),
        loadProfessions(),
        loadClients(),
        loadCurrentPermissions()
      ]).finally(() => {
        setIsLoadingData(false);
      });
    }
  }, [isOpen, document]);

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar usuários:", error);
      setError("Erro ao carregar usuários");
    }
  };

  const loadProfessions = async () => {
    try {
      const response = await fetch('/api/professions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setProfessions(data.professions || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar profissões:", error);
      setError("Erro ao carregar profissões");
    }
  };

  const loadClients = async () => {
    try {
      const response = await fetch('/api/clients', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setClients(data.clients || data || []);
      }
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      setError("Erro ao carregar clientes");
    }
  };

  const loadCurrentPermissions = async () => {
    try {
      const response = await fetch(`/api/documents/${document.id}/permissions`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        const permissions = {
          users: data.users || [],
          professions: data.professions || [],
          clients: data.clients || []
        };
        setCurrentPermissions(permissions);
        setNewPermissions(permissions);
      } else {
        setError("Erro ao carregar permissões atuais");
      }
    } catch (error) {
      console.error("Erro ao carregar permissões:", error);
      setError("Erro ao carregar permissões atuais");
    }
  };

  const handleAddProfession = () => {
    if (selectedProfession && !newPermissions.professions.includes(selectedProfession)) {
      setNewPermissions(prev => ({
        ...prev,
        professions: [...prev.professions, selectedProfession]
      }));
      setSelectedProfession("");
    }
  };

  const handleRemoveProfession = (professionId) => {
    setNewPermissions(prev => ({
      ...prev,
      professions: prev.professions.filter(id => id !== professionId)
    }));
  };

  const handleAddUser = (userId) => {
    if (!newPermissions.users.includes(userId)) {
      setNewPermissions(prev => ({
        ...prev,
        users: [...prev.users, userId]
      }));
    }
  };

  const handleRemoveUser = (userId) => {
    setNewPermissions(prev => ({
      ...prev,
      users: prev.users.filter(id => id !== userId)
    }));
  };

  const handleAddClient = (clientId) => {
    if (!newPermissions.clients.includes(clientId)) {
      setNewPermissions(prev => ({
        ...prev,
        clients: [...prev.clients, clientId]
      }));
    }
  };

  const handleRemoveClient = (clientId) => {
    setNewPermissions(prev => ({
      ...prev,
      clients: prev.clients.filter(id => id !== clientId)
    }));
  };

  const handleSavePermissions = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/documents/${document.id}/permissions`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem("token")}`
        },
        body: JSON.stringify(newPermissions)
      });

      if (response.ok) {
        onSuccess();
      } else {
        const errorData = await response.json();
        setError(errorData.message || "Erro ao salvar permissões");
      }
    } catch (error) {
      console.error("Erro ao salvar permissões:", error);
      setError("Erro ao salvar permissões");
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentUserNames = () => {
    return currentPermissions.users.map(id => {
      const user = users.find(u => u.id === id);
      return user?.fullName || id;
    });
  };

  const getCurrentProfessionNames = () => {
    return currentPermissions.professions.map(id => {
      const profession = professions.find(p => p.id === id);
      return profession?.name || id;
    });
  };

  const getCurrentClientNames = () => {
    return currentPermissions.clients.map(id => {
      const client = clients.find(c => c.id === id);
      return client?.login || id;
    });
  };

  const getSelectedUserNames = () => {
    return newPermissions.users.map(id => {
      const user = users.find(u => u.id === id);
      return user?.fullName || id;
    });
  };

  const getSelectedProfessionNames = () => {
    return newPermissions.professions.map(id => {
      const profession = professions.find(p => p.id === id);
      return profession?.name || id;
    });
  };

  const getSelectedClientNames = () => {
    return newPermissions.clients.map(id => {
      const client = clients.find(c => c.id === id);
      return client?.login || id;
    });
  };

  // Filtrar usuários disponíveis (não selecionados e não em profissões selecionadas)
  const availableUsers = users.filter(user => {
    const isSelected = newPermissions.users.includes(user.id);
    const isInSelectedProfession = newPermissions.professions.includes(user.professionId);
    return !isSelected && !isInSelectedProfession;
  });

  const filteredUsers = availableUsers.filter(user => 
    user.fullName.toLowerCase().includes(userSearch.toLowerCase()) ||
    user.email.toLowerCase().includes(userSearch.toLowerCase())
  );

  const filteredClients = clients.filter(client => 
    !newPermissions.clients.includes(client.id) &&
    (client.login.toLowerCase().includes(clientSearch.toLowerCase()) ||
     client.email.toLowerCase().includes(clientSearch.toLowerCase()))
  );

  // Verificar se há mudanças
  const hasChanges = JSON.stringify(currentPermissions) !== JSON.stringify(newPermissions);

  if (isLoadingData) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2">
              <Share2 size={20} className="text-orange-500" />
              Gerenciar Compartilhamentos: {document?.filename}
            </DialogTitle>
          </DialogHeader>
          <div className="flex items-center justify-center py-12">
            <div className="flex flex-col items-center text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mb-2"></div>
              <p>Carregando dados...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto border-2 border-orange-400 dark:border-orange-500">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-slate-800 dark:text-white flex items-center gap-2">
            <Share2 size={20} className="text-orange-500" />
            Gerenciar Compartilhamentos: {document?.filename}
          </DialogTitle>
        </DialogHeader>

        {error && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800/30">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
              <AlertCircle size={16} />
              <span className="font-medium">Erro:</span>
              <span>{error}</span>
            </div>
          </div>
        )}

        <div className="space-y-6">
          {/* Resumo Atual */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800/30">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center gap-2">
              <Eye size={18} />
              Compartilhamentos Atuais
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-blue-100 dark:border-blue-800/50">
                <div className="flex items-center gap-2 mb-3">
                  <Users size={16} className="text-blue-500" />
                  <span className="font-semibold text-blue-700 dark:text-blue-300">
                    Profissões ({currentPermissions.professions.length})
                  </span>
                </div>
                <div className="space-y-2">
                  {getCurrentProfessionNames().map((name, index) => (
                    <Badge key={`current-profession-${index}`} variant="secondary" className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
                      <Users size={10} className="mr-1" />
                      {name}
                    </Badge>
                  ))}
                  {currentPermissions.professions.length === 0 && (
                    <span className="text-blue-600 dark:text-blue-400 text-sm italic">Nenhuma profissão</span>
                  )}
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-green-100 dark:border-green-800/50">
                <div className="flex items-center gap-2 mb-3">
                  <User size={16} className="text-green-500" />
                  <span className="font-semibold text-green-700 dark:text-green-300">
                    Usuários ({currentPermissions.users.length})
                  </span>
                </div>
                <div className="space-y-2">
                  {getCurrentUserNames().slice(0, 3).map((name, index) => (
                    <Badge key={`current-user-${index}`} variant="secondary" className="text-xs bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                      <User size={10} className="mr-1" />
                      {name}
                    </Badge>
                  ))}
                  {currentPermissions.users.length > 3 && (
                    <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">
                      +{currentPermissions.users.length - 3} mais
                    </Badge>
                  )}
                  {currentPermissions.users.length === 0 && (
                    <span className="text-green-600 dark:text-green-400 text-sm italic">Nenhum usuário</span>
                  )}
                </div>
              </div>
              
              <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border border-purple-100 dark:border-purple-800/50">
                <div className="flex items-center gap-2 mb-3">
                  <Building size={16} className="text-purple-500" />
                  <span className="font-semibold text-purple-700 dark:text-purple-300">
                    Clientes ({currentPermissions.clients.length})
                  </span>
                </div>
                <div className="space-y-2">
                  {getCurrentClientNames().slice(0, 3).map((name, index) => (
                    <Badge key={`current-client-${index}`} variant="secondary" className="text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
                      <Building size={10} className="mr-1" />
                      {name}
                    </Badge>
                  ))}
                  {currentPermissions.clients.length > 3 && (
                    <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300">
                      +{currentPermissions.clients.length - 3} mais
                    </Badge>
                  )}
                  {currentPermissions.clients.length === 0 && (
                    <span className="text-purple-600 dark:text-purple-400 text-sm italic">Nenhum cliente</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Gerenciamento de Permissões */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profissões */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30">
                <Users size={20} className="text-blue-500" />
                <Label className="text-lg font-semibold text-blue-700 dark:text-blue-300">Profissões</Label>
              </div>
              
              <div className="space-y-4">
                <div className="flex gap-2">
                  <ModuleSelect
                    value={selectedProfession}
                    onChange={(e) => setSelectedProfession(e.target.value)}
                    className="flex-1"
                  >
                    <option value="">Selecionar profissão</option>
                    {professions
                      .filter(prof => !newPermissions.professions.includes(prof.id))
                      .map(profession => (
                        <option key={profession.id} value={profession.id}>
                          {profession.name}
                        </option>
                      ))}
                  </ModuleSelect>
                  <Button
                    onClick={handleAddProfession}
                    disabled={!selectedProfession}
                    size="sm"
                    className="px-3 bg-blue-500 hover:bg-blue-600"
                  >
                    <Plus size={16} />
                  </Button>
                </div>

                {/* Lista de profissões selecionadas */}
                <div className="space-y-2">
                  {newPermissions.professions.map(professionId => {
                    const profession = professions.find(p => p.id === professionId);
                    return (
                      <div key={professionId} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800/30 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                        <div className="flex items-center gap-2">
                          <Users size={14} className="text-blue-500" />
                          <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                            {profession?.name || professionId}
                          </span>
                        </div>
                        <button
                          onClick={() => handleRemoveProfession(professionId)}
                          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                          title="Remover profissão"
                        >
                          <X size={14} />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Usuários */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800/30">
                <User size={20} className="text-green-500" />
                <Label className="text-lg font-semibold text-green-700 dark:text-green-300">Usuários</Label>
              </div>
              
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar usuários..."
                    value={userSearch}
                    onChange={(e) => setUserSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Lista de usuários disponíveis */}
                <div className="max-h-48 overflow-y-auto border rounded-lg p-2 space-y-1 bg-gray-50 dark:bg-gray-800/50">
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map(user => (
                      <div key={user.id} className="flex items-center justify-between p-2 rounded hover:bg-white dark:hover:bg-gray-700 transition-colors">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {user.fullName}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {user.email}
                          </p>
                        </div>
                        <button
                          onClick={() => handleAddUser(user.id)}
                          className="p-1 text-green-500 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-900/20 rounded transition-colors"
                          title="Adicionar usuário"
                        >
                          <UserCheck size={14} />
                        </button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                      {userSearch ? "Nenhum usuário encontrado" : "Todos os usuários já estão selecionados"}
                    </div>
                  )}
                </div>

                {/* Usuários selecionados */}
                <div className="space-y-2">
                  {newPermissions.users
                    .filter(userId => {
                      const user = users.find(u => u.id === userId);
                      return user && !newPermissions.professions.includes(user.professionId);
                    })
                    .map(userId => {
                      const user = users.find(u => u.id === userId);
                      return (
                        <div key={userId} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800/30 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <User size={14} className="text-green-500 flex-shrink-0" />
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-green-800 dark:text-green-200 truncate">
                                {user?.fullName || userId}
                              </p>
                              <p className="text-xs text-green-600 dark:text-green-300 truncate">
                                {user?.email}
                              </p>
                            </div>
                          </div>
                          <button
                            onClick={() => handleRemoveUser(userId)}
                            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors flex-shrink-0"
                            title="Remover usuário"
                          >
                            <UserX size={14} />
                          </button>
                        </div>
                      );
                    })}
                </div>
              </div>
            </div>

            {/* Clientes */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30">
                <Building size={20} className="text-purple-500" />
                <Label className="text-lg font-semibold text-purple-700 dark:text-purple-300">Clientes</Label>
              </div>
              
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Buscar clientes..."
                    value={clientSearch}
                    onChange={(e) => setClientSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Lista de clientes disponíveis */}
                <div className="max-h-48 overflow-y-auto border rounded-lg p-2 space-y-1 bg-gray-50 dark:bg-gray-800/50">
                  {filteredClients.length > 0 ? (
                    filteredClients.map(client => (
                      <div key={client.id} className="flex items-center justify-between p-2 rounded hover:bg-white dark:hover:bg-gray-700 transition-colors">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                            {client.login}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                            {client.email}
                          </p>
                        </div>
                        <button
                          onClick={() => handleAddClient(client.id)}
                          className="p-1 text-purple-500 hover:text-purple-700 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded transition-colors"
                          title="Adicionar cliente"
                        >
                          <UserCheck size={14} />
                        </button>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
                      {clientSearch ? "Nenhum cliente encontrado" : "Todos os clientes já estão selecionados"}
                    </div>
                  )}
                </div>

                {/* Clientes selecionados */}
                <div className="space-y-2">
                  {newPermissions.clients.map(clientId => {
                    const client = clients.find(c => c.id === clientId);
                    return (
                      <div key={clientId} className="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <Building size={14} className="text-purple-500 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-purple-800 dark:text-purple-200 truncate">
                              {client?.login || clientId}
                            </p>
                            <p className="text-xs text-purple-600 dark:text-purple-300 truncate">
                              {client?.email}
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => handleRemoveClient(clientId)}
                          className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors flex-shrink-0"
                          title="Remover cliente"
                        >
                          <UserX size={14} />
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Resumo das Mudanças */}
          {hasChanges && (
            <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg border border-amber-200 dark:border-amber-800/30">
              <div className="flex items-center gap-2 text-amber-700 dark:text-amber-300">
                <CheckCircle size={16} />
                <span className="font-medium">Alterações pendentes:</span>
                <span>
                  {newPermissions.professions.length - currentPermissions.professions.length} profissões, 
                  {newPermissions.users.length - currentPermissions.users.length} usuários, 
                  {newPermissions.clients.length - currentPermissions.clients.length} clientes
                </span>
              </div>
            </div>
          )}

          {/* Botões */}
          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="min-w-[100px]"
            >
              Cancelar
            </Button>
            <Button
              type="button"
              onClick={handleSavePermissions}
              disabled={isLoading || !hasChanges}
              moduleColor="people"
              className="min-w-[120px] bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 disabled:opacity-50 disabled:cursor-not-allowed"
              isLoading={isLoading}
            >
              {isLoading ? "Salvando..." : "Salvar Alterações"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentSharingManagementModal; 