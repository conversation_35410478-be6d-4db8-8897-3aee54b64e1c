"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/components/ui/ModuleModal.js":
/*!******************************************!*\
  !*** ./src/components/ui/ModuleModal.js ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Info,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _CustomScrollArea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CustomScrollArea */ \"(app-pages-browser)/./src/components/ui/CustomScrollArea.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n/**\r\n * Componente de modal genérico com suporte para diferentes temas de módulos\r\n *\r\n * @param {Object} props\r\n * @param {boolean} props.isOpen - Se o modal está aberto\r\n * @param {Function} props.onClose - Função para fechar o modal\r\n * @param {string} props.title - Título do modal\r\n * @param {React.ReactNode} props.icon - Ícone do título (opcional)\r\n * @param {React.ReactNode} props.children - Conteúdo do modal\r\n * @param {string} props.size - Tamanho do modal (sm, md, lg, xl, full)\r\n * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, etc)\r\n * @param {React.ReactNode} props.footer - Conteúdo do rodapé (opcional)\r\n * @param {boolean} props.preventClose - Impede o fechamento do modal ao clicar fora ou no X\r\n * @param {string} props.variant - Variante do modal (default, info, success, warning, danger)\r\n * @param {boolean} props.showCloseButton - Mostrar botão de fechar no cabeçalho\r\n * @param {Function} props.onInteractOutside - Função chamada quando o usuário interage fora do modal\r\n * @param {string} props.description - Descrição opcional do modal\r\n * @param {boolean} props.animateExit - Se true, anima a saída do modal (padrão: true)\r\n * @param {React.ReactNode} props.headerActions - Ações adicionais no cabeçalho (opcional)\r\n */ const ModuleModal = (param)=>{\n    let { isOpen, onClose, title, icon, children, size = 'md', moduleColor = 'default', footer, preventClose = false, variant = 'default', showCloseButton = true, onInteractOutside, description, animateExit = true, headerActions } = param;\n    _s();\n    // Estado local para controlar a visibilidade do modal\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(isOpen);\n    // Efeito para sincronizar o estado isVisible com a prop isOpen\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleModal.useEffect\": ()=>{\n            if (isOpen) {\n                setIsVisible(true);\n            } else {\n                // Forçar o fechamento do modal quando isOpen mudar para false\n                setIsVisible(false);\n            }\n        }\n    }[\"ModuleModal.useEffect\"], [\n        isOpen\n    ]);\n    // Função para lidar com o fechamento do modal\n    const handleClose = ()=>{\n        // Sempre forçar o fechamento do modal, independente do estado atual\n        // Primeiro, tornar o modal invisível imediatamente\n        setIsVisible(false);\n        // Depois, chamar onClose se existir\n        if (onClose) {\n            try {\n                onClose();\n            } catch (error) {\n                console.error('Erro ao fechar o modal:', error);\n            }\n        }\n    };\n    // Função para lidar com o final da animação de saída\n    const handleAnimationComplete = ()=>{\n        if (!isOpen) {\n            setIsVisible(false);\n        }\n    };\n    // Refs para acessibilidade\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const titleId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"modal-title-\".concat(Math.random().toString(36).substring(2, 11)));\n    const descriptionId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"modal-description-\".concat(Math.random().toString(36).substring(2, 11)));\n    // Refs para acessibilidade\n    const initialFocusRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Montar o componente apenas no cliente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleModal.useEffect\": ()=>{\n            setMounted(true);\n            return ({\n                \"ModuleModal.useEffect\": ()=>setMounted(false)\n            })[\"ModuleModal.useEffect\"];\n        }\n    }[\"ModuleModal.useEffect\"], []);\n    // Efeito para prevenir scroll quando o modal estiver aberto\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleModal.useEffect\": ()=>{\n            if (isOpen) {\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = '';\n            }\n            return ({\n                \"ModuleModal.useEffect\": ()=>{\n                    document.body.style.overflow = '';\n                }\n            })[\"ModuleModal.useEffect\"];\n        }\n    }[\"ModuleModal.useEffect\"], [\n        isOpen\n    ]);\n    // Efeito para definir o foco inicial quando o modal abre\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleModal.useEffect\": ()=>{\n            if (!isOpen) return;\n            // Definir o foco inicial apenas uma vez quando o modal abre\n            // Usando setTimeout para garantir que o DOM esteja completamente renderizado\n            const focusTimer = setTimeout({\n                \"ModuleModal.useEffect.focusTimer\": ()=>{\n                    var _modalRef_current, _modalRef_current1;\n                    // Se temos um ref específico para focar, use-o\n                    if (initialFocusRef.current) {\n                        initialFocusRef.current.focus();\n                        return;\n                    }\n                    // Procurar por um input para focar primeiro (melhor experiência do usuário)\n                    const firstInput = (_modalRef_current = modalRef.current) === null || _modalRef_current === void 0 ? void 0 : _modalRef_current.querySelector('input, textarea, select');\n                    if (firstInput) {\n                        firstInput.focus();\n                        return;\n                    }\n                    // Caso contrário, procurar elementos focáveis exceto o botão de fechar\n                    const focusableElements = (_modalRef_current1 = modalRef.current) === null || _modalRef_current1 === void 0 ? void 0 : _modalRef_current1.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n                    if (focusableElements && focusableElements.length > 0) {\n                        // Encontrar o primeiro elemento que não seja o botão de fechar\n                        const nonCloseButton = Array.from(focusableElements).find({\n                            \"ModuleModal.useEffect.focusTimer.nonCloseButton\": (el)=>!(el.tagName === 'BUTTON' && el.getAttribute('aria-label') === 'Fechar')\n                        }[\"ModuleModal.useEffect.focusTimer.nonCloseButton\"]);\n                        if (nonCloseButton) {\n                            nonCloseButton.focus();\n                        } else {\n                            focusableElements[0].focus();\n                        }\n                    }\n                }\n            }[\"ModuleModal.useEffect.focusTimer\"], 50); // Pequeno delay para garantir que o DOM esteja pronto\n            return ({\n                \"ModuleModal.useEffect\": ()=>clearTimeout(focusTimer)\n            })[\"ModuleModal.useEffect\"];\n        }\n    }[\"ModuleModal.useEffect\"], [\n        isOpen,\n        initialFocusRef\n    ]);\n    // Efeito separado para gerenciar o trap de foco e tecla ESC\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModuleModal.useEffect\": ()=>{\n            if (!isOpen) return;\n            // Handler para tecla ESC e trap de foco\n            const handleKeyDown = {\n                \"ModuleModal.useEffect.handleKeyDown\": (e)=>{\n                    // Fechar com ESC\n                    if (e.key === 'Escape' && !preventClose) {\n                        handleClose();\n                        return;\n                    }\n                    // Trap focus apenas para a tecla Tab\n                    if (e.key === 'Tab') {\n                        var _modalRef_current;\n                        const focusableElements = (_modalRef_current = modalRef.current) === null || _modalRef_current === void 0 ? void 0 : _modalRef_current.querySelectorAll('button:not([tabindex=\"-1\"]), [href], input, select, textarea, [tabindex]:not([tabindex=\"-1\"])');\n                        if (!focusableElements || focusableElements.length === 0) return;\n                        const firstElement = focusableElements[0];\n                        const lastElement = focusableElements[focusableElements.length - 1];\n                        // Se pressionar Shift+Tab no primeiro elemento, vá para o último\n                        if (e.shiftKey && document.activeElement === firstElement) {\n                            e.preventDefault();\n                            lastElement.focus();\n                        } else if (!e.shiftKey && document.activeElement === lastElement) {\n                            e.preventDefault();\n                            firstElement.focus();\n                        }\n                    }\n                }\n            }[\"ModuleModal.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"ModuleModal.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"ModuleModal.useEffect\"];\n        }\n    }[\"ModuleModal.useEffect\"], [\n        isOpen,\n        handleClose,\n        preventClose\n    ]);\n    if (!mounted) return null;\n    // Determinar a largura do modal com base no tamanho\n    const sizeClasses = {\n        sm: 'max-w-md',\n        md: 'max-w-xl',\n        lg: 'max-w-4xl',\n        xl: 'max-w-6xl',\n        full: 'max-w-full mx-4'\n    };\n    // Configurações de cores por módulo\n    const moduleColors = {\n        default: {\n            header: 'bg-white dark:bg-gray-800',\n            headerBorder: 'border-neutral-200 dark:border-gray-700',\n            title: 'text-neutral-800 dark:text-white',\n            iconColor: 'text-primary-500 dark:text-primary-400'\n        },\n        people: {\n            header: 'bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600',\n            headerBorder: 'border-orange-400 dark:border-orange-700',\n            title: 'text-white',\n            iconColor: 'text-white'\n        },\n        scheduler: {\n            header: 'bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600',\n            headerBorder: 'border-purple-400 dark:border-purple-700',\n            title: 'text-white',\n            iconColor: 'text-white'\n        },\n        admin: {\n            header: 'bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-500 dark:to-gray-600',\n            headerBorder: 'border-gray-300 dark:border-gray-600',\n            title: 'text-white',\n            iconColor: 'text-white'\n        },\n        financial: {\n            header: 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600',\n            headerBorder: 'border-green-400 dark:border-green-700',\n            title: 'text-white',\n            iconColor: 'text-white'\n        },\n        chat: {\n            header: 'bg-gradient-to-r from-cyan-500 to-cyan-700 dark:from-cyan-600 dark:to-cyan-800',\n            headerBorder: 'border-cyan-400 dark:border-cyan-700',\n            title: 'text-white',\n            iconColor: 'text-white'\n        }\n    };\n    // Variantes do modal\n    const variantStyles = {\n        default: {},\n        info: {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                lineNumber: 247,\n                columnNumber: 13\n            }, undefined),\n            iconColor: 'text-blue-500 dark:text-blue-400'\n        },\n        success: {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                lineNumber: 251,\n                columnNumber: 13\n            }, undefined),\n            iconColor: 'text-green-500 dark:text-green-400'\n        },\n        warning: {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                lineNumber: 255,\n                columnNumber: 13\n            }, undefined),\n            iconColor: 'text-amber-500 dark:text-amber-400'\n        },\n        danger: {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                lineNumber: 259,\n                columnNumber: 13\n            }, undefined),\n            iconColor: 'text-red-500 dark:text-red-400'\n        }\n    };\n    // Obter as cores do módulo atual\n    const colors = moduleColors[moduleColor] || moduleColors.default;\n    const variantConfig = variantStyles[variant] || variantStyles.default;\n    // Função para lidar com cliques no overlay\n    const handleOverlayClick = (e)=>{\n        // Sempre parar a propagação para evitar eventos duplicados\n        e.stopPropagation();\n        if (preventClose) {\n            if (onInteractOutside) {\n                onInteractOutside(e);\n            }\n            return;\n        }\n        handleClose();\n    };\n    // Mapeamento de animações por módulo\n    const moduleAnimations = {\n        default: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20\n            }\n        },\n        people: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20,\n                rotate: -2\n            }\n        },\n        scheduler: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20,\n                rotate: 2\n            }\n        },\n        admin: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20,\n                x: -20\n            }\n        },\n        financial: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20,\n                x: 20\n            }\n        },\n        chat: {\n            exit: {\n                opacity: 0,\n                scale: 0.9,\n                y: -20,\n                rotate: 1\n            }\n        }\n    };\n    // Obter a animação do módulo atual\n    const moduleAnimation = moduleAnimations[moduleColor] || moduleAnimations.default;\n    // Variantes de animação para o modal\n    const overlayVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                duration: 0.2\n            }\n        },\n        exit: {\n            opacity: 0,\n            transition: {\n                duration: 0.3,\n                ease: 'easeOut'\n            }\n        }\n    };\n    const modalVariants = {\n        hidden: {\n            opacity: 0,\n            scale: 0.95,\n            y: 10\n        },\n        visible: {\n            opacity: 1,\n            scale: 1,\n            y: 0,\n            transition: {\n                type: 'spring',\n                damping: 25,\n                stiffness: 300,\n                duration: 0.3\n            }\n        },\n        exit: {\n            ...moduleAnimation.exit,\n            transition: {\n                duration: 0.35,\n                ease: [\n                    0.32,\n                    0.72,\n                    0,\n                    1\n                ] // Curva de easing personalizada para uma saída mais suave\n            }\n        }\n    };\n    // Usar createPortal para renderizar o modal no nível mais alto do DOM\n    const modalContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n        mode: \"wait\",\n        onExitComplete: handleAnimationComplete,\n        children: isVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto p-4 md:p-6\",\n            role: \"dialog\",\n            \"aria-modal\": \"true\",\n            \"aria-labelledby\": titleId.current,\n            \"aria-describedby\": description ? descriptionId.current : undefined,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm\",\n                    onClick: handleOverlayClick,\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    exit: animateExit ? \"exit\" : \"hidden\",\n                    variants: overlayVariants\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                    lineNumber: 378,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    ref: modalRef,\n                    className: \"relative bg-background rounded-xl shadow-xl dark:shadow-black/50 \".concat(sizeClasses[size], \" w-full max-h-[98vh] flex flex-col z-[11050] \").concat(moduleColor === 'chat' ? 'border-2 border-cyan-500 dark:border-cyan-400' : '', \" overflow-hidden\"),\n                    onClick: (e)=>e.stopPropagation(),\n                    initial: \"hidden\",\n                    animate: \"visible\",\n                    exit: animateExit ? \"exit\" : \"hidden\",\n                    variants: modalVariants,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center px-6 py-4 border-b-2 \".concat(moduleColor === 'chat' ? 'border-cyan-500 dark:border-cyan-400' : colors.headerBorder, \" \").concat(colors.header, \" rounded-t-xl\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: colors.iconColor,\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        variant !== 'default' && !icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: variantConfig.iconColor,\n                                            children: variantConfig.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            id: titleId.current,\n                                            className: \"text-xl font-semibold \".concat(colors.title),\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                            lineNumber: 409,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                    lineNumber: 398,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        headerActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: headerActions\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                            lineNumber: 415,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        showCloseButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                // Sempre prevenir o comportamento padrão e parar a propagação\n                                                e.preventDefault();\n                                                e.stopPropagation();\n                                                // Sempre fechar o modal, independente de preventClose\n                                                handleClose();\n                                            },\n                                            // Adicionar tabIndex negativo para evitar que o botão receba foco automaticamente\n                                            tabIndex: \"-1\",\n                                            className: \"\".concat(moduleColor === 'default' ? 'text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300' : 'text-white/80 hover:text-white', \" p-2 rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 \").concat(moduleColor === 'scheduler' ? 'focus:ring-purple-500 dark:focus:ring-purple-400' : moduleColor === 'people' ? 'focus:ring-orange-500 dark:focus:ring-orange-400' : moduleColor === 'admin' ? 'focus:ring-slate-500 dark:focus:ring-slate-400' : moduleColor === 'financial' ? 'focus:ring-green-500 dark:focus:ring-green-400' : moduleColor === 'chat' ? 'focus:ring-cyan-500 dark:focus:ring-cyan-400' : 'focus:ring-primary-500'),\n                                            disabled: preventClose,\n                                            \"aria-label\": \"Fechar\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Info_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                                lineNumber: 435,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                            lineNumber: 420,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                                    lineNumber: 413,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                            lineNumber: 397,\n                            columnNumber: 9\n                        }, undefined),\n                        description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: descriptionId.current,\n                            className: \"px-6 pt-4 text-neutral-600 dark:text-neutral-300 text-sm\",\n                            children: description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                            lineNumber: 442,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CustomScrollArea__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"flex-1\",\n                            moduleColor: moduleColor,\n                            isModal: true,\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                            lineNumber: 448,\n                            columnNumber: 9\n                        }, undefined),\n                        footer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-t-2 \".concat(moduleColor === 'chat' ? 'border-cyan-500 dark:border-cyan-400' : 'border-neutral-200 dark:border-gray-700', \" bg-background rounded-b-xl\"),\n                            children: footer\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n            lineNumber: 370,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\components\\\\ui\\\\ModuleModal.js\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, undefined);\n    // Renderizar o modal usando um portal para garantir que ele fique acima de tudo\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal)(modalContent, document.body);\n};\n_s(ModuleModal, \"sbfDsVLQSKMDc7BKaJXl3tKqD+8=\");\n_c = ModuleModal;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModuleModal);\nvar _c;\n$RefreshReg$(_c, \"ModuleModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/ModuleModal.js\n"));

/***/ })

});