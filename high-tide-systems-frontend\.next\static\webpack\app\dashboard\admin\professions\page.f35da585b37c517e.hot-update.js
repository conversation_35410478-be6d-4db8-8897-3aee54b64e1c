"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: 'Tem certeza que deseja excluir o grupo \"'.concat(group.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 638,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 644,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 652,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 703,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 727,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 728,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 730,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 721,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 741,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 742,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 743,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 736,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 756,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 748,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 768,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 769,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 763,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 778,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 779,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 772,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 702,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 786,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 785,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 701,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 839,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 840,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 837,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 852,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 858,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 874,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 875,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 867,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 879,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 896,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 897,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 908,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 909,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 901,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 836,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 917,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 923,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_13__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 931,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_14__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 938,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 929,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 915,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 962,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 957,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 956,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 979,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1013,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1012,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1025,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1024,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1028,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1027,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1023,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1022,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1039,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1043,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1037,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1054,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1055,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1053,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1051,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1071,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1069,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1068,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1082,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1083,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1081,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1080,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1100,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1101,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1106,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1092,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1091,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1116,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1124,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1123,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1139,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1133,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1132,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1115,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1114,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1010,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 952,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 951,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1163,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1158,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1178,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1201,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1200,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1213,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1212,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1216,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1215,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1211,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1210,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1228,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1226,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1225,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1240,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1241,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1239,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1246,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1237,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1256,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1257,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1255,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1254,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1274,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1280,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1266,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1265,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1296,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1290,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1306,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1300,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1299,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1289,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1288,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1198,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1154,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1153,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1319,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1330,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1355,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: \"Confirmar a\\xe7\\xe3o\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1361,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 835,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"z2ueIfMvpFqwLSBIlnDUAH37acU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ })

});