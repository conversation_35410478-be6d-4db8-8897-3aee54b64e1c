"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  X,
  Calendar,
  FileType,
  Building,
  User,
  Tag,
  RotateCcw
} from "lucide-react";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

const DocumentsFilters = ({
  filters,
  onFiltersChange,
  onSearch,
  categories = [],
  companies = [],
  users = [],
  className = ""
}) => {
  const [localFilters, setLocalFilters] = useState({
    search: "",
    category: "",
    company: "",
    user: "",
    fileType: "",
    dateFrom: "",
    dateTo: "",
    ...filters
  });

  // File types baseados nos mimeTypes mais comuns
  const fileTypes = [
    { value: "image", label: "Imagens", mimeTypes: ["image/*"] },
    { value: "pdf", label: "PDF", mimeTypes: ["application/pdf"] },
    { value: "word", label: "Word", mimeTypes: ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"] },
    { value: "excel", label: "Excel", mimeTypes: ["application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"] },
    { value: "video", label: "Vídeos", mimeTypes: ["video/*"] },
    { value: "audio", label: "Áudios", mimeTypes: ["audio/*"] },
    { value: "text", label: "Texto", mimeTypes: ["text/*"] }
  ];

  // Sync local filters with external filters
  useEffect(() => {
    setLocalFilters(prev => ({ ...prev, ...filters }));
  }, [filters]);

  const handleFilterChange = (key, value) => {
    const newFilters = {
      ...localFilters,
      [key]: value
    };
    setLocalFilters(newFilters);
    
    // Remove empty filters
    const cleanFilters = Object.fromEntries(
      Object.entries(newFilters).filter(([_, value]) => value !== "" && value !== null)
    );
    
    onFiltersChange(cleanFilters);
  };

  const clearFilters = () => {
    const emptyFilters = {
      search: "",
      category: "",
      company: "",
      user: "",
      fileType: "",
      dateFrom: "",
      dateTo: "",
      minSize: "",
      maxSize: ""
    };
    setLocalFilters(emptyFilters);
    onFiltersChange({});
  };

  const hasActiveFilters = Object.values(localFilters).some(value => value !== "" && value !== null);

  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
      return format(new Date(dateString), "yyyy-MM-dd");
    } catch {
      return dateString;
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-500 dark:text-gray-400" />
          <h3 className="font-medium text-gray-900 dark:text-white">Filtros Avançados</h3>
          {hasActiveFilters && (
            <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full">
              {Object.values(localFilters).filter(value => value !== "" && value !== null).length} ativo(s)
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <button
              onClick={clearFilters}
              className="flex items-center gap-1 px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              title="Limpar todos os filtros"
            >
              <RotateCcw className="h-3 w-3" />
              Limpar
            </button>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {/* Basic Search - Always Visible */}
      <div className="p-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Buscar documentos por nome..."
            value={localFilters.search}
            onChange={(e) => handleFilterChange("search", e.target.value)}
            className="w-full pl-4 pr-10 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
          />
          {localFilters.search && (
            <button
              onClick={() => handleFilterChange("search", "")}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters - Expandable */}
      {isExpanded && (
        <div className="px-4 pb-4 space-y-4 border-t border-gray-200 dark:border-gray-700">
          {/* Row 1: Category, Company, User */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
            {/* Category Filter */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Tag className="h-4 w-4" />
                Categoria
              </label>
              <ModuleSelect
                moduleColor="people"
                value={localFilters.category}
                onChange={(e) => handleFilterChange("category", e.target.value)}
              >
                <option value="">Todas as categorias</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {/* Company Filter */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Building className="h-4 w-4" />
                Empresa
              </label>
              <ModuleSelect
                moduleColor="people"
                value={localFilters.company}
                onChange={(e) => handleFilterChange("company", e.target.value)}
              >
                <option value="">Todas as empresas</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {/* User Filter */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <User className="h-4 w-4" />
                Criado por
              </label>
              <ModuleSelect
                moduleColor="people"
                value={localFilters.user}
                onChange={(e) => handleFilterChange("user", e.target.value)}
              >
                <option value="">Todos os usuários</option>
                {users.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          </div>

          {/* Row 2: File Type and Date Range */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* File Type Filter */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <FileType className="h-4 w-4" />
                Tipo de arquivo
              </label>
              <ModuleSelect
                moduleColor="people"
                value={localFilters.fileType}
                onChange={(e) => handleFilterChange("fileType", e.target.value)}
              >
                <option value="">Todos os tipos</option>
                {fileTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {/* Date From */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Calendar className="h-4 w-4" />
                Data inicial
              </label>
              <input
                type="date"
                value={formatDate(localFilters.dateFrom)}
                onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>

            {/* Date To */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Calendar className="h-4 w-4" />
                Data final
              </label>
              <input
                type="date"
                value={formatDate(localFilters.dateTo)}
                onChange={(e) => handleFilterChange("dateTo", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>
          </div>

          {/* Row 3: File Size Range */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Min Size */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Tamanho mínimo (KB)
              </label>
              <input
                type="number"
                placeholder="Ex: 100"
                value={localFilters.minSize}
                onChange={(e) => handleFilterChange("minSize", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>

            {/* Max Size */}
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Tamanho máximo (KB)
              </label>
              <input
                type="number"
                placeholder="Ex: 10000"
                value={localFilters.maxSize}
                onChange={(e) => handleFilterChange("maxSize", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
            </div>
          </div>

          {/* Active Filters Tags */}
          {hasActiveFilters && (
            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filtros ativos:</p>
              <div className="flex flex-wrap gap-2">
                {Object.entries(localFilters).map(([key, value]) => {
                  if (!value) return null;
                  
                  let label = "";
                  let displayValue = value;
                  
                  switch (key) {
                    case "search":
                      label = "Busca";
                      break;
                    case "category":
                      label = "Categoria";
                      const category = categories.find(c => c.id === value);
                      displayValue = category ? category.name : value;
                      break;
                    case "company":
                      label = "Empresa";
                      const company = companies.find(c => c.id === value);
                      displayValue = company ? company.name : value;
                      break;
                    case "user":
                      label = "Usuário";
                      const user = users.find(u => u.id === value);
                      displayValue = user ? user.name : value;
                      break;
                    case "fileType":
                      label = "Tipo";
                      const type = fileTypes.find(t => t.value === value);
                      displayValue = type ? type.label : value;
                      break;
                    case "dateFrom":
                      label = "Desde";
                      displayValue = format(new Date(value), "dd/MM/yyyy", { locale: ptBR });
                      break;
                    case "dateTo":
                      label = "Até";
                      displayValue = format(new Date(value), "dd/MM/yyyy", { locale: ptBR });
                      break;
                    case "minSize":
                      label = "Min";
                      displayValue = `${value} KB`;
                      break;
                    case "maxSize":
                      label = "Max";
                      displayValue = `${value} KB`;
                      break;
                    default:
                      return null;
                  }
                  
                  return (
                    <span
                      key={key}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs rounded-full"
                    >
                      <span className="font-medium">{label}:</span>
                      <span>{displayValue}</span>
                      <button
                        onClick={() => handleFilterChange(key, "")}
                        className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DocumentsAdvancedFilters;
