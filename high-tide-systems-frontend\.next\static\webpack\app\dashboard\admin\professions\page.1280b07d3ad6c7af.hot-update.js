"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/professions/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js":
/*!**************************************************************!*\
  !*** ./src/app/modules/admin/professions/ProfessionsPage.js ***!
  \**************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-round.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Briefcase,Building,CheckCircle,Edit,Filter,Layers,Plus,RefreshCw,Search,Tag,Trash,UserRound,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/ExportMenu */ \"(app-pages-browser)/./src/components/ui/ExportMenu.js\");\n/* harmony import */ var _components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/permissions/Protected */ \"(app-pages-browser)/./src/components/permissions/Protected.js\");\n/* harmony import */ var _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/modules/admin/services/professionsService */ \"(app-pages-browser)/./src/app/modules/admin/services/professionsService.js\");\n/* harmony import */ var _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/modules/admin/services/companyService */ \"(app-pages-browser)/./src/app/modules/admin/services/companyService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.js\");\n/* harmony import */ var _components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/admin/ProfessionFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/admin/ProfessionGroupFormModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupFormModal.js\");\n/* harmony import */ var _components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/admin/ProfessionUsersModal */ \"(app-pages-browser)/./src/components/admin/ProfessionUsersModal.js\");\n/* harmony import */ var _components_admin_ProfessionGroupProfessionsModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/admin/ProfessionGroupProfessionsModal */ \"(app-pages-browser)/./src/components/admin/ProfessionGroupProfessionsModal.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/admin/OccupationFilters */ \"(app-pages-browser)/./src/components/admin/OccupationFilters.js\");\n/* harmony import */ var _components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/admin/GroupsFilters */ \"(app-pages-browser)/./src/components/admin/GroupsFilters.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ProfessionsPage = ()=>{\n    _s();\n    var _s1 = $RefreshSig$();\n    const { user: currentUser } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { toast_success, toast_error } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [professions, setProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allProfessions, setAllProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todas as profissões para paginação manual\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allGroups, setAllGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]); // Armazena todos os grupos para paginação manual\n    const [filteredGroups, setFilteredGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingGroups, setIsLoadingGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isLoadingCompanies, setIsLoadingCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [search, setSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [groupFilter, setGroupFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [companyFilter, setCompanyFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [professionsFilter, setProfessionsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [professionOptions, setProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingProfessionOptions, setIsLoadingProfessionOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupsFilter, setGroupsFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [groupOptions, setGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingGroupOptions, setIsLoadingGroupOptions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [professionFormOpen, setProfessionFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupFormOpen, setGroupFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [usersModalOpen, setUsersModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [groupProfessionsModalOpen, setGroupProfessionsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedProfession, setSelectedProfession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [confirmationDialogOpen, setConfirmationDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [actionToConfirm, setActionToConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"professions\"); // \"professions\" ou \"groups\"\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedIds, setSelectedIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroupIds, setSelectedGroupIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Estados para o novo sistema de filtros\n    const [occupationFilters, setOccupationFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        professionGroups: [],\n        professions: [],\n        status: \"\"\n    });\n    // Estados para paginação de profissões\n    const [currentProfessionsPage, setCurrentProfessionsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessionsPages, setTotalProfessionsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalProfessions, setTotalProfessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estados para paginação de grupos\n    const [currentGroupsPage, setCurrentGroupsPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroupsPages, setTotalGroupsPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalGroups, setTotalGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Estado para controlar itens por página\n    const [itemsPerPage, setItemsPerPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    // Funções para seleção múltipla\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedIds(professions.map((p)=>p.id));\n        } else {\n            setSelectedIds([]);\n        }\n    };\n    const handleSelectOne = (id, checked)=>{\n        setSelectedIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Funções para seleção múltipla de grupos\n    const handleSelectAllGroups = (checked)=>{\n        if (checked) {\n            setSelectedGroupIds(filteredGroups.map((g)=>g.id));\n        } else {\n            setSelectedGroupIds([]);\n        }\n    };\n    const handleSelectOneGroup = (id, checked)=>{\n        setSelectedGroupIds((prev)=>checked ? [\n                ...prev,\n                id\n            ] : prev.filter((i)=>i !== id));\n    };\n    // Verificar se o usuário é administrador do sistema\n    const isSystemAdmin = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.role) === \"SYSTEM_ADMIN\";\n    // Função para carregar opções de profissões para o multi-select\n    const loadProfessionOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadProfessionOptions]\": async ()=>{\n            setIsLoadingProfessionOptions(true);\n            try {\n                // Carregar todas as profissões para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                    active: true // Apenas profissões ativas por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadProfessionOptions].options\": (profession)=>({\n                            value: profession.id,\n                            label: profession.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadProfessionOptions].options\"]);\n                setProfessionOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de profissões:\", error);\n            } finally{\n                setIsLoadingProfessionOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadProfessionOptions]\"], []);\n    // Função para carregar opções de grupos para o multi-select\n    const loadGroupOptions = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProfessionsPage.useCallback[loadGroupOptions]\": async ()=>{\n            setIsLoadingGroupOptions(true);\n            try {\n                // Carregar todos os grupos para o multi-select\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    active: true // Apenas grupos ativos por padrão\n                });\n                // Transformar os dados para o formato esperado pelo MultiSelect\n                const options = data.map({\n                    \"ProfessionsPage.useCallback[loadGroupOptions].options\": (group)=>({\n                            value: group.id,\n                            label: group.name\n                        })\n                }[\"ProfessionsPage.useCallback[loadGroupOptions].options\"]);\n                setGroupOptions(options);\n            } catch (error) {\n                console.error(\"Erro ao carregar opções de grupos:\", error);\n            } finally{\n                setIsLoadingGroupOptions(false);\n            }\n        }\n    }[\"ProfessionsPage.useCallback[loadGroupOptions]\"], []);\n    // Carregar profissões\n    const loadProfessions = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentProfessionsPage, searchQuery = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : search, groupId = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupFilter, status = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : statusFilter, company = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : companyFilter, professionIds = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : professionsFilter, sortField = arguments.length > 6 && arguments[6] !== void 0 ? arguments[6] : \"name\", sortDirection = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"asc\", perPage = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : itemsPerPage;\n        setIsLoading(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentProfessionsPage(pageNumber);\n            // Buscar todas as profissões\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessions({\n                search: searchQuery || undefined,\n                groupId: groupId || undefined,\n                active: status === \"\" ? undefined : status === \"active\",\n                companyId: company || undefined,\n                professionIds: professionIds.length > 0 ? professionIds : undefined,\n                sortField: sortField,\n                sortDirection: sortDirection\n            });\n            // Armazenar todas as profissões para paginação manual\n            setAllProfessions(data);\n            // Calcular o total de itens e páginas\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            // Aplicar paginação manual\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedProfessions = data.slice(startIndex, endIndex);\n            // Atualizar o estado com os dados paginados manualmente\n            setProfessions(paginatedProfessions);\n            setTotalProfessions(total);\n            setTotalProfessionsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar profissões:\", error);\n            toast_error(\"Erro ao carregar profissões\");\n            setProfessions([]);\n            setTotalProfessions(0);\n            setTotalProfessionsPages(1);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Função para carregar grupos com ordenação\n    const loadGroupsWithSort = async function() {\n        let sortField = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'name', sortDirection = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'asc', page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : currentGroupsPage, perPage = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            const pageNumber = parseInt(page, 10);\n            setCurrentGroupsPage(pageNumber);\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                search: search || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                sortField,\n                sortDirection\n            });\n            setAllGroups(data);\n            const total = data.length;\n            const pages = Math.ceil(total / perPage) || 1;\n            const startIndex = (pageNumber - 1) * perPage;\n            const endIndex = startIndex + perPage;\n            const paginatedGroups = data.slice(startIndex, endIndex);\n            setFilteredGroups(paginatedGroups);\n            setTotalGroups(total);\n            setTotalGroupsPages(pages);\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos com ordenação:\", error);\n            toast_error(\"Erro ao carregar grupos\");\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Carregar grupos de profissões\n    const loadGroups = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : currentGroupsPage, perPage = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : itemsPerPage;\n        setIsLoadingGroups(true);\n        try {\n            // Garantir que a página é um número\n            const pageNumber = parseInt(page, 10);\n            // Atualizar o estado da página atual\n            setCurrentGroupsPage(pageNumber);\n            // Buscar todos os grupos\n            const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                active: activeTab === \"professions\" ? true : undefined // Na tab de profissões, só carrega os ativos\n            });\n            // Armazenar todos os grupos para paginação manual\n            setAllGroups(data);\n            if (activeTab === \"professions\") {\n                // Na tab de profissões, não aplicamos paginação aos grupos (são usados apenas no filtro)\n                setGroups(data);\n            } else {\n                // Na tab de grupos, aplicamos paginação\n                // Calcular o total de itens e páginas\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                // Aplicar paginação manual\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                // Atualizar o estado com os dados paginados manualmente\n                setGroups(data); // Mantemos todos os grupos para o filtro\n                setFilteredGroups(paginatedGroups); // Apenas os 10 itens da página atual\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar grupos de profissões:\", error);\n            toast_error(\"Erro ao carregar grupos de profissões\");\n            setGroups([]);\n            setFilteredGroups([]);\n            setTotalGroups(0);\n            setTotalGroupsPages(1);\n        } finally{\n            setIsLoadingGroups(false);\n        }\n    };\n    // Filtrar grupos quando o usuário submeter o formulário\n    const filterGroups = function(searchTerm) {\n        let page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : currentGroupsPage, groupIds = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : groupsFilter, sortField = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 'name', sortDirection = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 'asc', perPage = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : itemsPerPage;\n        const pageNumber = parseInt(page, 10);\n        setCurrentGroupsPage(pageNumber);\n        const loadFilteredGroups = async ()=>{\n            setIsLoadingGroups(true);\n            try {\n                const data = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroups({\n                    search: searchTerm || undefined,\n                    active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                    companyId: companyFilter || undefined,\n                    groupIds: groupIds.length > 0 ? groupIds : undefined,\n                    sortField,\n                    sortDirection\n                });\n                setAllGroups(data);\n                const total = data.length;\n                const pages = Math.ceil(total / perPage) || 1;\n                const startIndex = (pageNumber - 1) * perPage;\n                const endIndex = startIndex + perPage;\n                const paginatedGroups = data.slice(startIndex, endIndex);\n                setFilteredGroups(paginatedGroups);\n                setTotalGroups(total);\n                setTotalGroupsPages(pages);\n            } catch (error) {\n                console.error(\"Erro ao filtrar grupos:\", error);\n                toast_error(\"Erro ao filtrar grupos\");\n                setFilteredGroups([]);\n                setTotalGroups(0);\n                setTotalGroupsPages(1);\n            } finally{\n                setIsLoadingGroups(false);\n            }\n        };\n        loadFilteredGroups();\n    };\n    // Carregar empresas (apenas para system admin)\n    const loadCompanies = async ()=>{\n        if (!isSystemAdmin) return;\n        setIsLoadingCompanies(true);\n        try {\n            const companies = await _app_modules_admin_services_companyService__WEBPACK_IMPORTED_MODULE_5__.companyService.getCompaniesForSelect();\n            setCompanies(companies || []);\n        } catch (error) {\n            console.error(\"Erro ao carregar empresas:\", error);\n        } finally{\n            setIsLoadingCompanies(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            loadProfessions();\n            loadGroups();\n            if (isSystemAdmin) {\n                loadCompanies();\n            }\n            // Carregar opções para os multi-selects\n            loadProfessionOptions();\n            loadGroupOptions();\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        isSystemAdmin,\n        loadProfessionOptions,\n        loadGroupOptions\n    ]);\n    // Recarregar dados quando a tab mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionsPage.useEffect\": ()=>{\n            setSearch(\"\"); // Limpar a busca ao trocar de tab\n            setGroupFilter(\"\");\n            setStatusFilter(\"\");\n            setCompanyFilter(\"\");\n            setProfessionsFilter([]);\n            setGroupsFilter([]);\n            // Resetar os filtros do novo sistema\n            if (activeTab === \"professions\") {\n                setOccupationFilters({\n                    search: \"\",\n                    companies: [],\n                    professionGroups: [],\n                    professions: [],\n                    status: \"\"\n                });\n            }\n            // Resetar para a primeira página\n            if (activeTab === \"professions\") {\n                setCurrentProfessionsPage(1);\n                loadProfessions(1);\n            } else {\n                setCurrentGroupsPage(1);\n                loadGroups(1); // Recarregar grupos com filtro diferente dependendo da tab\n            }\n        }\n    }[\"ProfessionsPage.useEffect\"], [\n        activeTab\n    ]);\n    // Função de busca removida, agora usamos estados locais nos componentes de filtro\n    const handleGroupFilterChange = (value)=>{\n        setGroupFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um grupo\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, value, statusFilter, companyFilter, professionsFilter);\n    };\n    const handleStatusFilterChange = (value)=>{\n        setStatusFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar um status\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, value, companyFilter, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleCompanyFilterChange = (value)=>{\n        setCompanyFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar uma empresa\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, search, groupFilter, statusFilter, value, professionsFilter);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(search, 1, groupsFilter);\n        }\n    };\n    const handleProfessionsFilterChange = (value)=>{\n        setProfessionsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover uma profissão\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, value);\n    };\n    const handleGroupsFilterChange = (value)=>{\n        setGroupsFilter(value);\n        // Acionar a busca automaticamente quando o usuário selecionar ou remover um grupo\n        setCurrentGroupsPage(1);\n        filterGroups(search, 1, value);\n    };\n    // Funções para o novo sistema de filtros\n    const handleOccupationFiltersChange = (newFilters)=>{\n        setOccupationFilters(newFilters);\n    };\n    const handleOccupationSearch = (filters)=>{\n        setCurrentProfessionsPage(1);\n        // Converter os filtros para o formato esperado pela API\n        const searchQuery = filters.search || \"\";\n        const groupIds = filters.professionGroups || [];\n        const professionIds = filters.professions || [];\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        // Mapear os filtros para os filtros existentes\n        setSearch(searchQuery);\n        setGroupsFilter(groupIds.length > 0 ? groupIds[0] : \"\");\n        setProfessionsFilter(professionIds);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        // Carregar profissões com os novos filtros\n        loadProfessions(1, searchQuery, groupIds.length > 0 ? groupIds[0] : \"\", status, companyIds.length > 0 ? companyIds[0] : \"\", professionIds);\n    };\n    const handleOccupationClearFilters = ()=>{\n        const clearedFilters = {\n            search: \"\",\n            companies: [],\n            professionGroups: [],\n            professions: [],\n            status: \"\"\n        };\n        setOccupationFilters(clearedFilters);\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        setCurrentProfessionsPage(1);\n        loadProfessions(1, \"\", \"\", \"\", \"\", []);\n    };\n    const handleResetFilters = ()=>{\n        setSearch(\"\");\n        setGroupFilter(\"\");\n        setStatusFilter(\"\");\n        setCompanyFilter(\"\");\n        setProfessionsFilter([]);\n        setGroupsFilter([]);\n        // Resetar para a primeira página\n        if (activeTab === \"professions\") {\n            setCurrentProfessionsPage(1);\n            loadProfessions(1, \"\", \"\", \"\", \"\", []);\n        } else {\n            setCurrentGroupsPage(1);\n            filterGroups(\"\", 1, []); // Chamada direta para resetar os filtros\n        }\n        // Forçar a re-renderização dos componentes de filtro\n        setTimeout(()=>{\n            const event = new Event('reset');\n            document.dispatchEvent(event);\n        }, 0);\n    };\n    // Função para lidar com a mudança de página de profissões\n    const handleProfessionsPageChange = (page)=>{\n        loadProfessions(page, search, groupFilter, statusFilter, companyFilter, professionsFilter);\n    };\n    // Função para lidar com a mudança de página de grupos\n    const handleGroupsPageChange = (page)=>{\n        filterGroups(search, page, groupsFilter); // Chamada direta para mudança de página\n    };\n    const handleEditProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setProfessionFormOpen(true);\n    };\n    const handleEditGroup = (group)=>{\n        setSelectedGroup(group); // Se group for null, será criação de novo grupo\n        setGroupFormOpen(true);\n    };\n    const handleDeleteProfession = (profession)=>{\n        setSelectedProfession(profession);\n        setActionToConfirm({\n            type: \"delete-profession\",\n            message: 'Tem certeza que deseja excluir a profiss\\xe3o \"'.concat(profession.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    const handleViewUsers = (profession)=>{\n        setSelectedProfession(profession);\n        setUsersModalOpen(true);\n    };\n    const handleViewGroupProfessions = (group)=>{\n        setSelectedGroup(group);\n        setGroupProfessionsModalOpen(true);\n    };\n    const handleDeleteGroup = (group)=>{\n        setSelectedGroup(group);\n        setActionToConfirm({\n            type: \"delete-group\",\n            message: 'Tem certeza que deseja excluir o grupo \"'.concat(group.name, '\"? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.')\n        });\n        setConfirmationDialogOpen(true);\n    };\n    // Função para exportar profissões\n    const handleExportProfessions = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessions({\n                search: search || undefined,\n                professionIds: professionsFilter.length > 0 ? professionsFilter : undefined,\n                groupId: groupFilter || undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    // Função para exportar grupos de profissões\n    const handleExportGroups = async (format)=>{\n        setIsExporting(true);\n        try {\n            // Exportar usando os mesmos filtros da tabela atual\n            await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.exportProfessionGroups({\n                search: search || undefined,\n                groupIds: groupsFilter.length > 0 ? groupsFilter : undefined,\n                active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\n                companyId: companyFilter || undefined\n            }, format);\n        } catch (error) {\n            console.error(\"Erro ao exportar grupos de profissões:\", error);\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const confirmAction = async ()=>{\n        try {\n            if (actionToConfirm.type === \"delete-profession\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfession(selectedProfession.id);\n                toast_success(\"Profissão excluída com sucesso\");\n                loadProfessions();\n            } else if (actionToConfirm.type === \"delete-group\") {\n                await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.deleteProfessionGroup(selectedGroup.id);\n                toast_success(\"Grupo excluído com sucesso\");\n                setSelectedGroup(null); // Limpar o grupo selecionado após exclusão\n                loadGroups();\n                loadProfessions(); // Recarregar profissões para atualizar os grupos\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Erro ao executar ação:\", error);\n            toast_error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Erro ao executar ação\");\n        } finally{\n            setConfirmationDialogOpen(false);\n        }\n    };\n    // Configuração das tabs para o ModuleTabs\n    const tabsConfig = [\n        {\n            id: \"professions\",\n            label: \"Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 645,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.professions.view\"\n        },\n        {\n            id: \"groups\",\n            label: \"Grupos de Profissões\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                size: 18\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 651,\n                columnNumber: 13\n            }, undefined),\n            permission: \"admin.profession-groups.view\"\n        }\n    ];\n    // Filtrar tabs com base nas permissões do usuário\n    const filteredTabs = tabsConfig.filter((tab)=>{\n        if (!tab.permission) return true;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n            permission: tab.permission,\n            showFallback: false,\n            children: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 659,\n            columnNumber: 12\n        }, undefined);\n    });\n    // Componente de filtros para profissões\n    const ProfessionsFilters = ()=>{\n        _s1();\n        // Estado local para o campo de busca\n        const [localSearch, setLocalSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(search);\n        // Atualizar o estado local quando o estado global mudar\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                setLocalSearch(search);\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], [\n            search\n        ]);\n        // Ouvir o evento de reset\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                const handleReset = {\n                    \"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\": ()=>{\n                        setLocalSearch(\"\");\n                    }\n                }[\"ProfessionsPage.ProfessionsFilters.useEffect.handleReset\"];\n                document.addEventListener('reset', handleReset);\n                return ({\n                    \"ProfessionsPage.ProfessionsFilters.useEffect\": ()=>{\n                        document.removeEventListener('reset', handleReset);\n                    }\n                })[\"ProfessionsPage.ProfessionsFilters.useEffect\"];\n            }\n        }[\"ProfessionsPage.ProfessionsFilters.useEffect\"], []);\n        // Função para atualizar o estado local sem afetar o estado global\n        const handleLocalSearchChange = (e)=>{\n            setLocalSearch(e.target.value);\n        };\n        // Função para aplicar o filtro quando o botão for clicado\n        const applyFilter = ()=>{\n            setSearch(localSearch);\n            setCurrentProfessionsPage(1);\n            // Log para debug\n            console.log(\"Aplicando filtros de profissões:\", {\n                search: localSearch,\n                groupFilter,\n                statusFilter,\n                companyFilter,\n                professionsFilter\n            });\n            loadProfessions(1, localSearch, groupFilter, statusFilter, companyFilter, professionsFilter);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col gap-4 mt-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 711,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    placeholder: \"Buscar por nome ou descri\\xe7\\xe3o...\",\n                                    value: localSearch,\n                                    onChange: handleLocalSearchChange,\n                                    className: \"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\",\n                                    onKeyDown: (e)=>{\n                                        if (e.key === 'Enter') {\n                                            e.preventDefault();\n                                            applyFilter();\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 712,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 710,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: groupFilter,\n                                    onChange: (e)=>handleGroupFilterChange(e.target.value),\n                                    disabled: isLoadingGroups,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os grupos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 734,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"null\",\n                                            children: \"Sem grupo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 735,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: group.id,\n                                                children: group.name\n                                            }, group.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 737,\n                                                columnNumber: 17\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 728,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: statusFilter,\n                                    onChange: (e)=>handleStatusFilterChange(e.target.value),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todos os status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 748,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"active\",\n                                            children: \"Ativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"inactive\",\n                                            children: \"Inativas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 750,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 743,\n                                    columnNumber: 13\n                                }, undefined),\n                                isSystemAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleSelect, {\n                                    moduleColor: \"admin\",\n                                    value: companyFilter,\n                                    onChange: (e)=>handleCompanyFilterChange(e.target.value),\n                                    disabled: isLoadingCompanies,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Todas as empresas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 761,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: company.id,\n                                                children: company.name\n                                            }, company.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 763,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 755,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: applyFilter,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 775,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Filtrar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 770,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModalButton, {\n                                    moduleColor: \"admin\",\n                                    type: \"button\",\n                                    onClick: handleResetFilters,\n                                    variant: \"secondary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            size: 16,\n                                            className: \"sm:hidden\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 785,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"Limpar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 786,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 779,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 709,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.MultiSelect, {\n                        label: \"Filtrar por Profiss\\xf5es\",\n                        value: professionsFilter,\n                        onChange: handleProfessionsFilterChange,\n                        options: professionOptions,\n                        placeholder: \"Selecione uma ou mais profiss\\xf5es pelo nome...\",\n                        loading: isLoadingProfessionOptions,\n                        moduleOverride: \"admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 793,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 792,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n            lineNumber: 708,\n            columnNumber: 7\n        }, undefined);\n    };\n    _s1(ProfessionsFilters, \"DTahFDESZ+ESPZXHJ71BoOPyhTc=\");\n    // Estados para o novo sistema de filtros de grupos\n    const [groupFilters, setGroupFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        search: \"\",\n        companies: [],\n        status: \"\",\n        groups: []\n    });\n    const handleGroupFiltersChange = (newFilters)=>{\n        setGroupFilters(newFilters);\n    };\n    const handleGroupSearch = (filters)=>{\n        setCurrentGroupsPage(1);\n        const searchQuery = filters.search || \"\";\n        const companyIds = filters.companies || [];\n        const status = filters.status || \"\";\n        const groupIds = filters.groups || [];\n        setSearch(searchQuery);\n        setCompanyFilter(companyIds.length > 0 ? companyIds[0] : \"\");\n        setStatusFilter(status);\n        setGroupsFilter(groupIds);\n        filterGroups(searchQuery, 1, groupIds);\n    };\n    // Import tutorial steps from tutorialMapping\n    const professionsGroupsTutorialSteps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\": ()=>{\n            // Import dynamically to avoid circular dependencies\n            const tutorialMap = (__webpack_require__(/*! @/tutorials/tutorialMapping */ \"(app-pages-browser)/./src/tutorials/tutorialMapping.js\")[\"default\"]);\n            return tutorialMap['/dashboard/admin/professions'] || [];\n        }\n    }[\"ProfessionsPage.useMemo[professionsGroupsTutorialSteps]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-slate-800 dark:text-white flex items-center\",\n                        children: [\n                            activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 846,\n                                columnNumber: 13\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                size: 24,\n                                className: \"mr-2 text-slate-600 dark:text-slate-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 847,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" ? \"Profissões\" : \"Grupos de Profissões\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            activeTab === \"professions\" && selectedIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>console.log('Excluir selecionados:', selectedIds),\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 859,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 860,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 854,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportProfessions,\n                                isExporting: isExporting,\n                                disabled: isLoading || professions.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 865,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && selectedGroupIds.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                // TODO: Implementar exclusão em massa de grupos\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-red-600 to-rose-400 dark:from-red-700 dark:to-rose-600 text-white rounded-lg hover:from-red-700 hover:to-rose-500 dark:hover:from-red-800 dark:hover:to-rose-700 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                title: \"Excluir selecionados\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Excluir Selecionados (\",\n                                            selectedGroupIds.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 882,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 874,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ExportMenu__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                onExport: handleExportGroups,\n                                isExporting: isExporting,\n                                disabled: isLoadingGroups || filteredGroups.length === 0,\n                                className: \"text-slate-700 dark:text-slate-300\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 886,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedProfession(null);\n                                    setProfessionFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 903,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Nova Profiss\\xe3o\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 904,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 896,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    setSelectedGroup(null);\n                                    setGroupFormOpen(true);\n                                },\n                                className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white rounded-lg hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800 shadow-md transition-all duration-300 hover:scale-105 hover:shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        size: 18\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 915,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Novo Grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 908,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 851,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 843,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleHeader, {\n                title: \"Filtros e Busca\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    size: 22,\n                    className: \"text-module-admin-icon dark:text-module-admin-icon-dark\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 924,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\",\n                tutorialSteps: professionsGroupsTutorialSteps,\n                tutorialName: \"admin-professions-groups-overview\",\n                filters: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTabs, {\n                            tabs: filteredTabs,\n                            activeTab: activeTab,\n                            onTabChange: setActiveTab,\n                            moduleColor: \"admin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 930,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: activeTab === \"professions\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_OccupationFilters__WEBPACK_IMPORTED_MODULE_14__.OccupationFilters, {\n                                filters: occupationFilters,\n                                onFiltersChange: handleOccupationFiltersChange,\n                                onSearch: handleOccupationSearch,\n                                onClearFilters: handleOccupationClearFilters\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 938,\n                                columnNumber: 17\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_GroupsFilters__WEBPACK_IMPORTED_MODULE_15__.GroupsFilters, {\n                                filters: groupFilters,\n                                onFiltersChange: handleGroupFiltersChange,\n                                onSearch: handleGroupSearch\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 945,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 936,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 922,\n                columnNumber: 7\n            }, undefined),\n            activeTab === \"professions\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.professions.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Profiss\\xf5es\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>loadProfessions(),\n                            className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                            title: \"Atualizar lista\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                size: 18\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                lineNumber: 969,\n                                columnNumber: 19\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 964,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 963,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Profissão',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'group',\n                            width: '15%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Usuários',\n                            field: 'users',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '10%',\n                            sortable: false\n                        }\n                    ],\n                    data: professions,\n                    isLoading: isLoading,\n                    emptyMessage: \"Nenhuma profiss\\xe3o encontrada\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 986,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-professions-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentProfessionsPage,\n                    totalPages: totalProfessionsPages,\n                    totalItems: totalProfessions,\n                    onPageChange: handleProfessionsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar as profissões com os novos parâmetros de ordenação\n                        loadProfessions(currentProfessionsPage, search, groupFilter, statusFilter, companyFilter, professionsFilter, field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        loadProfessions(1, search, groupFilter, statusFilter, companyFilter, professionsFilter, \"name\", \"asc\", newItemsPerPage);\n                    },\n                    selectedIds: selectedGroupIds,\n                    onSelectAll: handleSelectAllGroups,\n                    renderRow: (profession, index, moduleColors, visibleColumns)=>{\n                        var _profession__count, _profession__count1, _profession__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedIds.includes(profession.id),\n                                        onChange: (e)=>handleSelectOne(profession.id, e.target.checked),\n                                        name: \"select-profession-\".concat(profession.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1020,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1019,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1032,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1031,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: profession.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1035,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1034,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1030,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1029,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('group') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.group ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-400\",\n                                        children: profession.group.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1046,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem grupo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1050,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1044,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: profession.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1061,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: profession.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1062,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1060,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1067,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1058,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: profession.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1078,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1076,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1075,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('users') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1089,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_profession__count = profession._count) === null || _profession__count === void 0 ? void 0 : _profession__count.users) || 0,\n                                                    \" usu\\xe1rios\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1090,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1088,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1087,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(profession.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: profession.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1108,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1112,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativa\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1113,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1099,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1098,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewUsers(profession),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver usu\\xe1rios com esta profiss\\xe3o\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1128,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1123,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar profiss\\xe3o\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1130,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.professions.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteProfession(profession),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir profiss\\xe3o\",\n                                                    disabled: ((_profession__count1 = profession._count) === null || _profession__count1 === void 0 ? void 0 : _profession__count1.users) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_profession__count2 = profession._count) === null || _profession__count2 === void 0 ? void 0 : _profession__count2.users) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1146,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1140,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1139,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1122,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1121,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, profession.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1017,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 959,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 958,\n                columnNumber: 9\n            }, undefined),\n            activeTab === \"groups\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                permission: \"admin.profession-groups.view\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleTable, {\n                    moduleColor: \"admin\",\n                    title: \"Lista de Grupos\",\n                    headerContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>loadGroups(),\n                        className: \"p-2 text-gray-600 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-all duration-200 hover:scale-110 hover:shadow-md rounded-md\",\n                        title: \"Atualizar lista\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            size: 18\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1170,\n                            columnNumber: 17\n                        }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1165,\n                        columnNumber: 15\n                    }, void 0),\n                    columns: [\n                        {\n                            header: '',\n                            field: 'select',\n                            width: '50px',\n                            sortable: false\n                        },\n                        {\n                            header: 'Grupo',\n                            field: 'name',\n                            width: '20%'\n                        },\n                        {\n                            header: 'Descrição',\n                            field: 'description',\n                            width: '25%'\n                        },\n                        ...isSystemAdmin ? [\n                            {\n                                header: 'Empresa',\n                                field: 'company',\n                                width: '15%'\n                            }\n                        ] : [],\n                        {\n                            header: 'Profissões',\n                            field: 'professions',\n                            width: '15%'\n                        },\n                        {\n                            header: 'Status',\n                            field: 'active',\n                            width: '10%'\n                        },\n                        {\n                            header: 'Ações',\n                            field: 'actions',\n                            className: 'text-right',\n                            width: '15%',\n                            sortable: false\n                        }\n                    ],\n                    data: filteredGroups,\n                    isLoading: isLoadingGroups,\n                    emptyMessage: \"Nenhum grupo encontrado\",\n                    emptyIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                        lineNumber: 1185,\n                        columnNumber: 24\n                    }, void 0),\n                    tableId: \"admin-profession-groups-table\",\n                    enableColumnToggle: true,\n                    defaultSortField: \"name\",\n                    defaultSortDirection: \"asc\",\n                    currentPage: currentGroupsPage,\n                    totalPages: totalGroupsPages,\n                    totalItems: totalGroups,\n                    onPageChange: handleGroupsPageChange,\n                    onSort: (field, direction)=>{\n                        // Quando a ordenação mudar, recarregar os grupos com os novos parâmetros de ordenação\n                        loadGroupsWithSort(field, direction);\n                    },\n                    showPagination: true,\n                    itemsPerPage: itemsPerPage,\n                    onItemsPerPageChange: (newItemsPerPage)=>{\n                        setItemsPerPage(newItemsPerPage);\n                        filterGroups(search, 1, groupsFilter, newItemsPerPage);\n                    },\n                    renderRow: (group, index, moduleColors, visibleColumns)=>{\n                        var _group__count, _group__count1, _group__count2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                            className: moduleColors.hoverBg,\n                            children: [\n                                visibleColumns.includes('select') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_13__.ModuleCheckbox, {\n                                        moduleColor: \"admin\",\n                                        checked: selectedGroupIds.includes(group.id),\n                                        onChange: (e)=>handleSelectOneGroup(group.id, e.target.checked),\n                                        name: \"select-group-\".concat(group.id)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1208,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1207,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('name') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 h-10 w-10 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    size: 20\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1220,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1219,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-medium text-neutral-900 dark:text-neutral-100\",\n                                                    children: group.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1223,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1222,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1218,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1217,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('description') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-neutral-600 dark:text-neutral-300 line-clamp-2\",\n                                        children: group.description || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                            children: \"Sem descri\\xe7\\xe3o\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                            lineNumber: 1235,\n                                            columnNumber: 25\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1233,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1232,\n                                    columnNumber: 19\n                                }, void 0),\n                                isSystemAdmin && visibleColumns.includes('company') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: group.company ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                size: 14,\n                                                className: \"text-neutral-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1247,\n                                                columnNumber: 25\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: group.company.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1248,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1246,\n                                        columnNumber: 23\n                                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-neutral-400 dark:text-neutral-500 italic\",\n                                        children: \"Sem empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1253,\n                                        columnNumber: 23\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1244,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('professions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                size: 16,\n                                                className: \"text-neutral-400 dark:text-neutral-500 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1263,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-neutral-600 dark:text-neutral-300\",\n                                                children: [\n                                                    ((_group__count = group._count) === null || _group__count === void 0 ? void 0 : _group__count.professions) || 0,\n                                                    \" profiss\\xf5es\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1264,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1262,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1261,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('active') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit \".concat(group.active ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\" : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"),\n                                        children: group.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Ativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1282,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                    size: 12\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1286,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Inativo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1287,\n                                                    columnNumber: 27\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1273,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1272,\n                                    columnNumber: 19\n                                }, void 0),\n                                visibleColumns.includes('actions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                    className: \"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-end gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleViewGroupProfessions(group),\n                                                className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-indigo-500 dark:hover:text-indigo-400 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                title: \"Ver profiss\\xf5es deste grupo\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    size: 18\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1302,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1297,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.edit\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleEditGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-primary-50 dark:hover:bg-primary-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Editar grupo\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        size: 18\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1310,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1305,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1304,\n                                                columnNumber: 23\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_permissions_Protected__WEBPACK_IMPORTED_MODULE_3__.Protected, {\n                                                permission: \"admin.profession-groups.delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleDeleteGroup(group),\n                                                    className: \"p-1.5 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-all duration-200 hover:scale-110 hover:shadow-md\",\n                                                    title: \"Excluir grupo\",\n                                                    disabled: ((_group__count1 = group._count) === null || _group__count1 === void 0 ? void 0 : _group__count1.professions) > 0,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Briefcase_Building_CheckCircle_Edit_Filter_Layers_Plus_RefreshCw_Search_Tag_Trash_UserRound_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        size: 18,\n                                                        className: ((_group__count2 = group._count) === null || _group__count2 === void 0 ? void 0 : _group__count2.professions) > 0 ? \"opacity-50 cursor-not-allowed\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                        lineNumber: 1320,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                    lineNumber: 1314,\n                                                    columnNumber: 25\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                                lineNumber: 1313,\n                                                columnNumber: 23\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                        lineNumber: 1296,\n                                        columnNumber: 21\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                                    lineNumber: 1295,\n                                    columnNumber: 19\n                                }, void 0)\n                            ]\n                        }, group.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                            lineNumber: 1205,\n                            columnNumber: 15\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                    lineNumber: 1161,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1160,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionFormModal__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                isOpen: professionFormOpen,\n                onClose: ()=>setProfessionFormOpen(false),\n                profession: selectedProfession,\n                groups: groups,\n                onSuccess: ()=>{\n                    setProfessionFormOpen(false);\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1333,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupFormModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: groupFormOpen,\n                onClose: ()=>setGroupFormOpen(false),\n                group: selectedGroup,\n                onSuccess: async ()=>{\n                    setGroupFormOpen(false);\n                    // Se havia um grupo selecionado, recarregar os dados atualizados dele primeiro\n                    if (selectedGroup) {\n                        try {\n                            const updatedGroup = await _app_modules_admin_services_professionsService__WEBPACK_IMPORTED_MODULE_4__.professionsService.getProfessionGroupById(selectedGroup.id);\n                            setSelectedGroup(updatedGroup);\n                        } catch (error) {\n                            console.error(\"Erro ao recarregar dados do grupo:\", error);\n                        }\n                    }\n                    // Recarregar grupos\n                    await loadGroups();\n                    // Recarregar profissões para atualizar os grupos\n                    loadProfessions();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1344,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionUsersModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: usersModalOpen,\n                onClose: ()=>setUsersModalOpen(false),\n                professionId: selectedProfession === null || selectedProfession === void 0 ? void 0 : selectedProfession.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1369,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ProfessionGroupProfessionsModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                isOpen: groupProfessionsModalOpen,\n                onClose: ()=>setGroupProfessionsModalOpen(false),\n                groupId: selectedGroup === null || selectedGroup === void 0 ? void 0 : selectedGroup.id\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1375,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: confirmationDialogOpen,\n                onClose: ()=>setConfirmationDialogOpen(false),\n                onConfirm: confirmAction,\n                title: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-profession\" ? \"Excluir Profissão\" : (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type) === \"delete-group\" ? \"Excluir Grupo\" : \"Confirmar ação\",\n                message: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.message) || \"\",\n                variant: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"danger\" : \"primary\",\n                moduleColor: \"admin\",\n                confirmText: (actionToConfirm === null || actionToConfirm === void 0 ? void 0 : actionToConfirm.type.includes(\"delete\")) ? \"Excluir\" : \"Confirmar\",\n                cancelText: \"Cancelar\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n                lineNumber: 1381,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projeto X\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\professions\\\\ProfessionsPage.js\",\n        lineNumber: 842,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ProfessionsPage, \"WMIwddPThsKO3kzPAYWJZAESvbk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = ProfessionsPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfessionsPage);\nvar _c;\n$RefreshReg$(_c, \"ProfessionsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbW9kdWxlcy9hZG1pbi9wcm9mZXNzaW9ucy9Qcm9mZXNzaW9uc1BhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUV5RTtBQWdCbkQ7QUFDOEI7QUFDVztBQUNzQjtBQUNSO0FBQzVCO0FBQ0U7QUFDaUI7QUFDSztBQUNVO0FBQ1I7QUFDc0I7QUFDNEM7QUFDcEU7QUFDUjtBQUVqRSxNQUFNd0Msa0JBQWtCOzs7SUFDdEIsTUFBTSxFQUFFQyxNQUFNQyxXQUFXLEVBQUUsR0FBR25CLDhEQUFPQTtJQUNyQyxNQUFNLEVBQUVvQixhQUFhLEVBQUVDLFdBQVcsRUFBRSxHQUFHcEIsZ0VBQVFBO0lBQy9DLE1BQU0sQ0FBQ3FCLGFBQWFDLGVBQWUsR0FBRzdDLCtDQUFRQSxDQUFDLEVBQUU7SUFDakQsTUFBTSxDQUFDOEMsZ0JBQWdCQyxrQkFBa0IsR0FBRy9DLCtDQUFRQSxDQUFDLEVBQUUsR0FBRyxxREFBcUQ7SUFDL0csTUFBTSxDQUFDZ0QsUUFBUUMsVUFBVSxHQUFHakQsK0NBQVFBLENBQUMsRUFBRTtJQUN2QyxNQUFNLENBQUNrRCxXQUFXQyxhQUFhLEdBQUduRCwrQ0FBUUEsQ0FBQyxFQUFFLEdBQUcsaURBQWlEO0lBQ2pHLE1BQU0sQ0FBQ29ELGdCQUFnQkMsa0JBQWtCLEdBQUdyRCwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3ZELE1BQU0sQ0FBQ3NELFdBQVdDLGFBQWEsR0FBR3ZELCtDQUFRQSxDQUFDLEVBQUU7SUFDN0MsTUFBTSxDQUFDd0QsV0FBV0MsYUFBYSxHQUFHekQsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDMEQsaUJBQWlCQyxtQkFBbUIsR0FBRzNELCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQzRELG9CQUFvQkMsc0JBQXNCLEdBQUc3RCwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUM4RCxRQUFRQyxVQUFVLEdBQUcvRCwrQ0FBUUEsQ0FBQztJQUNyQyxNQUFNLENBQUNnRSxhQUFhQyxlQUFlLEdBQUdqRSwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNrRSxjQUFjQyxnQkFBZ0IsR0FBR25FLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ29FLGVBQWVDLGlCQUFpQixHQUFHckUsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDc0UsbUJBQW1CQyxxQkFBcUIsR0FBR3ZFLCtDQUFRQSxDQUFDLEVBQUU7SUFDN0QsTUFBTSxDQUFDd0UsbUJBQW1CQyxxQkFBcUIsR0FBR3pFLCtDQUFRQSxDQUFDLEVBQUU7SUFDN0QsTUFBTSxDQUFDMEUsNEJBQTRCQyw4QkFBOEIsR0FBRzNFLCtDQUFRQSxDQUFDO0lBQzdFLE1BQU0sQ0FBQzRFLGNBQWNDLGdCQUFnQixHQUFHN0UsK0NBQVFBLENBQUMsRUFBRTtJQUNuRCxNQUFNLENBQUM4RSxjQUFjQyxnQkFBZ0IsR0FBRy9FLCtDQUFRQSxDQUFDLEVBQUU7SUFDbkQsTUFBTSxDQUFDZ0YsdUJBQXVCQyx5QkFBeUIsR0FBR2pGLCtDQUFRQSxDQUFDO0lBQ25FLE1BQU0sQ0FBQ2tGLG9CQUFvQkMsc0JBQXNCLEdBQUduRiwrQ0FBUUEsQ0FBQztJQUM3RCxNQUFNLENBQUNvRixlQUFlQyxpQkFBaUIsR0FBR3JGLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3NGLGdCQUFnQkMsa0JBQWtCLEdBQUd2RiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN3RiwyQkFBMkJDLDZCQUE2QixHQUFHekYsK0NBQVFBLENBQUM7SUFDM0UsTUFBTSxDQUFDMEYsb0JBQW9CQyxzQkFBc0IsR0FBRzNGLCtDQUFRQSxDQUFDO0lBQzdELE1BQU0sQ0FBQzRGLGVBQWVDLGlCQUFpQixHQUFHN0YsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDOEYsd0JBQXdCQywwQkFBMEIsR0FBRy9GLCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ2dHLGlCQUFpQkMsbUJBQW1CLEdBQUdqRywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUNrRyxXQUFXQyxhQUFhLEdBQUduRywrQ0FBUUEsQ0FBQyxnQkFBZ0IsNEJBQTRCO0lBQ3ZGLE1BQU0sQ0FBQ29HLGFBQWFDLGVBQWUsR0FBR3JHLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3NHLGFBQWFDLGVBQWUsR0FBR3ZHLCtDQUFRQSxDQUFDLEVBQUU7SUFDakQsTUFBTSxDQUFDd0csa0JBQWtCQyxvQkFBb0IsR0FBR3pHLCtDQUFRQSxDQUFDLEVBQUU7SUFFM0QseUNBQXlDO0lBQ3pDLE1BQU0sQ0FBQzBHLG1CQUFtQkMscUJBQXFCLEdBQUczRywrQ0FBUUEsQ0FBQztRQUN6RDhELFFBQVE7UUFDUlIsV0FBVyxFQUFFO1FBQ2JzRCxrQkFBa0IsRUFBRTtRQUNwQmhFLGFBQWEsRUFBRTtRQUNmaUUsUUFBUTtJQUNWO0lBRUEsdUNBQXVDO0lBQ3ZDLE1BQU0sQ0FBQ0Msd0JBQXdCQywwQkFBMEIsR0FBRy9HLCtDQUFRQSxDQUFDO0lBQ3JFLE1BQU0sQ0FBQ2dILHVCQUF1QkMseUJBQXlCLEdBQUdqSCwrQ0FBUUEsQ0FBQztJQUNuRSxNQUFNLENBQUNrSCxrQkFBa0JDLG9CQUFvQixHQUFHbkgsK0NBQVFBLENBQUM7SUFFekQsbUNBQW1DO0lBQ25DLE1BQU0sQ0FBQ29ILG1CQUFtQkMscUJBQXFCLEdBQUdySCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNzSCxrQkFBa0JDLG9CQUFvQixHQUFHdkgsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDd0gsYUFBYUMsZUFBZSxHQUFHekgsK0NBQVFBLENBQUM7SUFFL0MseUNBQXlDO0lBQ3pDLE1BQU0sQ0FBQzBILGNBQWNDLGdCQUFnQixHQUFHM0gsK0NBQVFBLENBQUM7SUFFakQsZ0NBQWdDO0lBQ2hDLE1BQU00SCxrQkFBa0IsQ0FBQ0M7UUFDdkIsSUFBSUEsU0FBUztZQUNYdEIsZUFBZTNELFlBQVlrRixHQUFHLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLEVBQUU7UUFDMUMsT0FBTztZQUNMekIsZUFBZSxFQUFFO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNMEIsa0JBQWtCLENBQUNELElBQUlIO1FBQzNCdEIsZUFBZTJCLENBQUFBLE9BQVFMLFVBQVU7bUJBQUlLO2dCQUFNRjthQUFHLEdBQUdFLEtBQUtDLE1BQU0sQ0FBQ0MsQ0FBQUEsSUFBS0EsTUFBTUo7SUFDMUU7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUssd0JBQXdCLENBQUNSO1FBQzdCLElBQUlBLFNBQVM7WUFDWHBCLG9CQUFvQnJELGVBQWUwRSxHQUFHLENBQUNRLENBQUFBLElBQUtBLEVBQUVOLEVBQUU7UUFDbEQsT0FBTztZQUNMdkIsb0JBQW9CLEVBQUU7UUFDeEI7SUFDRjtJQUVBLE1BQU04Qix1QkFBdUIsQ0FBQ1AsSUFBSUg7UUFDaENwQixvQkFBb0J5QixDQUFBQSxPQUFRTCxVQUFVO21CQUFJSztnQkFBTUY7YUFBRyxHQUFHRSxLQUFLQyxNQUFNLENBQUNDLENBQUFBLElBQUtBLE1BQU1KO0lBQy9FO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1RLGdCQUFnQi9GLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYWdHLElBQUksTUFBSztJQUU1QyxnRUFBZ0U7SUFDaEUsTUFBTUMsd0JBQXdCdkksa0RBQVdBOzhEQUFDO1lBQ3hDd0UsOEJBQThCO1lBQzlCLElBQUk7Z0JBQ0YsbURBQW1EO2dCQUNuRCxNQUFNZ0UsT0FBTyxNQUFNdkgsOEZBQWtCQSxDQUFDd0gsY0FBYyxDQUFDO29CQUNuREMsUUFBUSxLQUFLLHNDQUFzQztnQkFDckQ7Z0JBRUEsZ0VBQWdFO2dCQUNoRSxNQUFNQyxVQUFVSCxLQUFLYixHQUFHO2tGQUFDaUIsQ0FBQUEsYUFBZTs0QkFDdENDLE9BQU9ELFdBQVdmLEVBQUU7NEJBQ3BCaUIsT0FBT0YsV0FBV0csSUFBSTt3QkFDeEI7O2dCQUVBekUscUJBQXFCcUU7WUFDdkIsRUFBRSxPQUFPSyxPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMENBQTBDQTtZQUMxRCxTQUFVO2dCQUNSeEUsOEJBQThCO1lBQ2hDO1FBQ0Y7NkRBQUcsRUFBRTtJQUVMLDREQUE0RDtJQUM1RCxNQUFNMEUsbUJBQW1CbEosa0RBQVdBO3lEQUFDO1lBQ25DOEUseUJBQXlCO1lBQ3pCLElBQUk7Z0JBQ0YsK0NBQStDO2dCQUMvQyxNQUFNMEQsT0FBTyxNQUFNdkgsOEZBQWtCQSxDQUFDa0ksbUJBQW1CLENBQUM7b0JBQ3hEVCxRQUFRLEtBQUssa0NBQWtDO2dCQUNqRDtnQkFFQSxnRUFBZ0U7Z0JBQ2hFLE1BQU1DLFVBQVVILEtBQUtiLEdBQUc7NkVBQUN5QixDQUFBQSxRQUFVOzRCQUNqQ1AsT0FBT08sTUFBTXZCLEVBQUU7NEJBQ2ZpQixPQUFPTSxNQUFNTCxJQUFJO3dCQUNuQjs7Z0JBRUFuRSxnQkFBZ0IrRDtZQUNsQixFQUFFLE9BQU9LLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQ0FBc0NBO1lBQ3RELFNBQVU7Z0JBQ1JsRSx5QkFBeUI7WUFDM0I7UUFDRjt3REFBRyxFQUFFO0lBRUwsc0JBQXNCO0lBQ3RCLE1BQU11RSxrQkFBa0I7WUFDdEJDLHdFQUFPM0Msd0JBQ1A0QywrRUFBYzVGLFFBQ2Q2RiwyRUFBVTNGLGFBQ1Y2QywwRUFBUzNDLGNBQ1QwRiwyRUFBVXhGLGVBQ1Z5RixpRkFBZ0J2RixtQkFDaEJ3Riw2RUFBWSxRQUNaQyxpRkFBZ0IsT0FDaEJDLDJFQUFVdEM7UUFFVmpFLGFBQWE7UUFDYixJQUFJO1lBQ0Ysb0NBQW9DO1lBQ3BDLE1BQU13RyxhQUFhQyxTQUFTVCxNQUFNO1lBRWxDLHFDQUFxQztZQUNyQzFDLDBCQUEwQmtEO1lBRTFCLDZCQUE2QjtZQUM3QixNQUFNdEIsT0FBTyxNQUFNdkgsOEZBQWtCQSxDQUFDd0gsY0FBYyxDQUFDO2dCQUNuRDlFLFFBQVE0RixlQUFlUztnQkFDdkJSLFNBQVNBLFdBQVdRO2dCQUNwQnRCLFFBQVFoQyxXQUFXLEtBQUtzRCxZQUFZdEQsV0FBVztnQkFDL0N1RCxXQUFXUixXQUFXTztnQkFDdEJOLGVBQWVBLGNBQWNRLE1BQU0sR0FBRyxJQUFJUixnQkFBZ0JNO2dCQUMxREwsV0FBV0E7Z0JBQ1hDLGVBQWVBO1lBQ2pCO1lBRUEsc0RBQXNEO1lBQ3REaEgsa0JBQWtCNEY7WUFFbEIsc0NBQXNDO1lBQ3RDLE1BQU0yQixRQUFRM0IsS0FBSzBCLE1BQU07WUFDekIsTUFBTUUsUUFBUUMsS0FBS0MsSUFBSSxDQUFDSCxRQUFRTixZQUFZO1lBRTVDLDJCQUEyQjtZQUMzQixNQUFNVSxhQUFhLENBQUNULGFBQWEsS0FBS0Q7WUFDdEMsTUFBTVcsV0FBV0QsYUFBYVY7WUFDOUIsTUFBTVksdUJBQXVCakMsS0FBS2tDLEtBQUssQ0FBQ0gsWUFBWUM7WUFFcEQsd0RBQXdEO1lBQ3hEOUgsZUFBZStIO1lBQ2Z6RCxvQkFBb0JtRDtZQUNwQnJELHlCQUF5QnNEO1FBQzNCLEVBQUUsT0FBT3BCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUN4RyxZQUFZO1lBQ1pFLGVBQWUsRUFBRTtZQUNqQnNFLG9CQUFvQjtZQUNwQkYseUJBQXlCO1FBQzNCLFNBQVU7WUFDUnhELGFBQWE7UUFDZjtJQUNGO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1xSCxxQkFBcUI7WUFBT2hCLDZFQUFZLFFBQVFDLGlGQUFnQixPQUFPTix3RUFBT3JDLG1CQUFtQjRDLDJFQUFVdEM7UUFDL0cvRCxtQkFBbUI7UUFDbkIsSUFBSTtZQUNGLE1BQU1zRyxhQUFhQyxTQUFTVCxNQUFNO1lBQ2xDcEMscUJBQXFCNEM7WUFFckIsTUFBTXRCLE9BQU8sTUFBTXZILDhGQUFrQkEsQ0FBQ2tJLG1CQUFtQixDQUFDO2dCQUN4RHhGLFFBQVFBLFVBQVVxRztnQkFDbEJ0QixRQUFRM0UsaUJBQWlCLEtBQUtpRyxZQUFZakcsaUJBQWlCO2dCQUMzRGtHLFdBQVdoRyxpQkFBaUIrRjtnQkFDNUJZLFVBQVVuRyxhQUFheUYsTUFBTSxHQUFHLElBQUl6RixlQUFldUY7Z0JBQ25ETDtnQkFDQUM7WUFDRjtZQUVBNUcsYUFBYXdGO1lBQ2IsTUFBTTJCLFFBQVEzQixLQUFLMEIsTUFBTTtZQUN6QixNQUFNRSxRQUFRQyxLQUFLQyxJQUFJLENBQUNILFFBQVFOLFlBQVk7WUFDNUMsTUFBTVUsYUFBYSxDQUFDVCxhQUFhLEtBQUtEO1lBQ3RDLE1BQU1XLFdBQVdELGFBQWFWO1lBQzlCLE1BQU1nQixrQkFBa0JyQyxLQUFLa0MsS0FBSyxDQUFDSCxZQUFZQztZQUUvQ3RILGtCQUFrQjJIO1lBQ2xCdkQsZUFBZTZDO1lBQ2YvQyxvQkFBb0JnRDtRQUN0QixFQUFFLE9BQU9wQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1lBQ3hEeEcsWUFBWTtZQUNaVSxrQkFBa0IsRUFBRTtZQUNwQm9FLGVBQWU7WUFDZkYsb0JBQW9CO1FBQ3RCLFNBQVU7WUFDUjVELG1CQUFtQjtRQUNyQjtJQUNGO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1zSCxhQUFhO1lBQU94Qix3RUFBT3JDLG1CQUFtQjRDLDJFQUFVdEM7UUFDNUQvRCxtQkFBbUI7UUFDbkIsSUFBSTtZQUNGLG9DQUFvQztZQUNwQyxNQUFNc0csYUFBYUMsU0FBU1QsTUFBTTtZQUVsQyxxQ0FBcUM7WUFDckNwQyxxQkFBcUI0QztZQUVyQix5QkFBeUI7WUFDekIsTUFBTXRCLE9BQU8sTUFBTXZILDhGQUFrQkEsQ0FBQ2tJLG1CQUFtQixDQUFDO2dCQUN4RFQsUUFBUTNDLGNBQWMsZ0JBQWdCLE9BQU9pRSxVQUFVLDZDQUE2QztZQUN0RztZQUVBLGtEQUFrRDtZQUNsRGhILGFBQWF3RjtZQUViLElBQUl6QyxjQUFjLGVBQWU7Z0JBQy9CLHlGQUF5RjtnQkFDekZqRCxVQUFVMEY7WUFDWixPQUFPO2dCQUNMLHdDQUF3QztnQkFDeEMsc0NBQXNDO2dCQUN0QyxNQUFNMkIsUUFBUTNCLEtBQUswQixNQUFNO2dCQUN6QixNQUFNRSxRQUFRQyxLQUFLQyxJQUFJLENBQUNILFFBQVFOLFlBQVk7Z0JBRTVDLDJCQUEyQjtnQkFDM0IsTUFBTVUsYUFBYSxDQUFDVCxhQUFhLEtBQUtEO2dCQUN0QyxNQUFNVyxXQUFXRCxhQUFhVjtnQkFDOUIsTUFBTWdCLGtCQUFrQnJDLEtBQUtrQyxLQUFLLENBQUNILFlBQVlDO2dCQUUvQyx3REFBd0Q7Z0JBQ3hEMUgsVUFBVTBGLE9BQU8seUNBQXlDO2dCQUMxRHRGLGtCQUFrQjJILGtCQUFrQixxQ0FBcUM7Z0JBQ3pFdkQsZUFBZTZDO2dCQUNmL0Msb0JBQW9CZ0Q7WUFDdEI7UUFDRixFQUFFLE9BQU9wQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1lBQ3hEeEcsWUFBWTtZQUNaTSxVQUFVLEVBQUU7WUFDWkksa0JBQWtCLEVBQUU7WUFDcEJvRSxlQUFlO1lBQ2ZGLG9CQUFvQjtRQUN0QixTQUFVO1lBQ1I1RCxtQkFBbUI7UUFDckI7SUFDRjtJQUVBLHdEQUF3RDtJQUN4RCxNQUFNdUgsZUFBZSxTQUNuQkM7WUFDQTFCLHdFQUFPckMsbUJBQ1AyRCw0RUFBV25HLGNBQ1hrRiw2RUFBWSxRQUNaQyxpRkFBZ0IsT0FDaEJDLDJFQUFVdEM7UUFFVixNQUFNdUMsYUFBYUMsU0FBU1QsTUFBTTtRQUNsQ3BDLHFCQUFxQjRDO1FBSXJCLE1BQU1tQixxQkFBcUI7WUFDekJ6SCxtQkFBbUI7WUFDbkIsSUFBSTtnQkFDRixNQUFNZ0YsT0FBTyxNQUFNdkgsOEZBQWtCQSxDQUFDa0ksbUJBQW1CLENBQUM7b0JBQ3hEeEYsUUFBUXFILGNBQWNoQjtvQkFDdEJ0QixRQUFRM0UsaUJBQWlCLEtBQUtpRyxZQUFZakcsaUJBQWlCO29CQUMzRGtHLFdBQVdoRyxpQkFBaUIrRjtvQkFDNUJZLFVBQVVBLFNBQVNWLE1BQU0sR0FBRyxJQUFJVSxXQUFXWjtvQkFDM0NMO29CQUNBQztnQkFDRjtnQkFFQTVHLGFBQWF3RjtnQkFDYixNQUFNMkIsUUFBUTNCLEtBQUswQixNQUFNO2dCQUN6QixNQUFNRSxRQUFRQyxLQUFLQyxJQUFJLENBQUNILFFBQVFOLFlBQVk7Z0JBQzVDLE1BQU1VLGFBQWEsQ0FBQ1QsYUFBYSxLQUFLRDtnQkFDdEMsTUFBTVcsV0FBV0QsYUFBYVY7Z0JBQzlCLE1BQU1nQixrQkFBa0JyQyxLQUFLa0MsS0FBSyxDQUFDSCxZQUFZQztnQkFFL0N0SCxrQkFBa0IySDtnQkFDbEJ2RCxlQUFlNkM7Z0JBQ2YvQyxvQkFBb0JnRDtZQUN0QixFQUFFLE9BQU9wQixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekN4RyxZQUFZO2dCQUNaVSxrQkFBa0IsRUFBRTtnQkFDcEJvRSxlQUFlO2dCQUNmRixvQkFBb0I7WUFDdEIsU0FBVTtnQkFDUjVELG1CQUFtQjtZQUNyQjtRQUNGO1FBRUF5SDtJQUNGO0lBRUEsK0NBQStDO0lBQy9DLE1BQU1DLGdCQUFnQjtRQUNwQixJQUFJLENBQUM3QyxlQUFlO1FBRXBCM0Usc0JBQXNCO1FBQ3RCLElBQUk7WUFDRixNQUFNUCxZQUFZLE1BQU1qQyxzRkFBY0EsQ0FBQ2lLLHFCQUFxQjtZQUM1RC9ILGFBQWFELGFBQWEsRUFBRTtRQUM5QixFQUFFLE9BQU82RixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1FBQzlDLFNBQVU7WUFDUnRGLHNCQUFzQjtRQUN4QjtJQUNGO0lBRUE1RCxnREFBU0E7cUNBQUM7WUFDUnVKO1lBQ0F5QjtZQUNBLElBQUl6QyxlQUFlO2dCQUNqQjZDO1lBQ0Y7WUFDQSx3Q0FBd0M7WUFDeEMzQztZQUNBVztRQUNGO29DQUFHO1FBQUNiO1FBQWVFO1FBQXVCVztLQUFpQjtJQUUzRCxzQ0FBc0M7SUFDdENwSixnREFBU0E7cUNBQUM7WUFDUjhELFVBQVUsS0FBSyxrQ0FBa0M7WUFDakRFLGVBQWU7WUFDZkUsZ0JBQWdCO1lBQ2hCRSxpQkFBaUI7WUFDakJFLHFCQUFxQixFQUFFO1lBQ3ZCTSxnQkFBZ0IsRUFBRTtZQUVsQixxQ0FBcUM7WUFDckMsSUFBSXFCLGNBQWMsZUFBZTtnQkFDL0JTLHFCQUFxQjtvQkFDbkI3QyxRQUFRO29CQUNSUixXQUFXLEVBQUU7b0JBQ2JzRCxrQkFBa0IsRUFBRTtvQkFDcEJoRSxhQUFhLEVBQUU7b0JBQ2ZpRSxRQUFRO2dCQUNWO1lBQ0Y7WUFFQSxpQ0FBaUM7WUFDakMsSUFBSVgsY0FBYyxlQUFlO2dCQUMvQmEsMEJBQTBCO2dCQUMxQnlDLGdCQUFnQjtZQUNsQixPQUFPO2dCQUNMbkMscUJBQXFCO2dCQUNyQjRELFdBQVcsSUFBSSwyREFBMkQ7WUFDNUU7UUFDRjtvQ0FBRztRQUFDL0U7S0FBVTtJQUVkLGtGQUFrRjtJQUVsRixNQUFNcUYsMEJBQTBCLENBQUN2QztRQUMvQi9FLGVBQWUrRTtRQUNmLHVFQUF1RTtRQUN2RWpDLDBCQUEwQjtRQUMxQnlDLGdCQUFnQixHQUFHMUYsUUFBUWtGLE9BQU85RSxjQUFjRSxlQUFlRTtJQUNqRTtJQUVBLE1BQU1rSCwyQkFBMkIsQ0FBQ3hDO1FBQ2hDN0UsZ0JBQWdCNkU7UUFDaEIsd0VBQXdFO1FBQ3hFLElBQUk5QyxjQUFjLGVBQWU7WUFDL0JhLDBCQUEwQjtZQUMxQnlDLGdCQUFnQixHQUFHMUYsUUFBUUUsYUFBYWdGLE9BQU81RSxlQUFlRTtRQUNoRSxPQUFPO1lBQ0wrQyxxQkFBcUI7WUFDckI2RCxhQUFhcEgsUUFBUSxHQUFHYztRQUMxQjtJQUNGO0lBRUEsTUFBTTZHLDRCQUE0QixDQUFDekM7UUFDakMzRSxpQkFBaUIyRTtRQUNqQiwwRUFBMEU7UUFDMUUsSUFBSTlDLGNBQWMsZUFBZTtZQUMvQmEsMEJBQTBCO1lBQzFCeUMsZ0JBQWdCLEdBQUcxRixRQUFRRSxhQUFhRSxjQUFjOEUsT0FBTzFFO1FBQy9ELE9BQU87WUFDTCtDLHFCQUFxQjtZQUNyQjZELGFBQWFwSCxRQUFRLEdBQUdjO1FBQzFCO0lBQ0Y7SUFFQSxNQUFNOEcsZ0NBQWdDLENBQUMxQztRQUNyQ3pFLHFCQUFxQnlFO1FBQ3JCLHVGQUF1RjtRQUN2RmpDLDBCQUEwQjtRQUMxQnlDLGdCQUFnQixHQUFHMUYsUUFBUUUsYUFBYUUsY0FBY0UsZUFBZTRFO0lBQ3ZFO0lBRUEsTUFBTTJDLDJCQUEyQixDQUFDM0M7UUFDaENuRSxnQkFBZ0JtRTtRQUNoQixrRkFBa0Y7UUFDbEYzQixxQkFBcUI7UUFDckI2RCxhQUFhcEgsUUFBUSxHQUFHa0Y7SUFDMUI7SUFFQSx5Q0FBeUM7SUFDekMsTUFBTTRDLGdDQUFnQyxDQUFDQztRQUNyQ2xGLHFCQUFxQmtGO0lBQ3ZCO0lBRUEsTUFBTUMseUJBQXlCLENBQUNDO1FBQzlCaEYsMEJBQTBCO1FBQzFCLHdEQUF3RDtRQUN4RCxNQUFNMkMsY0FBY3FDLFFBQVFqSSxNQUFNLElBQUk7UUFDdEMsTUFBTWlILFdBQVdnQixRQUFRbkYsZ0JBQWdCLElBQUksRUFBRTtRQUMvQyxNQUFNaUQsZ0JBQWdCa0MsUUFBUW5KLFdBQVcsSUFBSSxFQUFFO1FBQy9DLE1BQU1vSixhQUFhRCxRQUFRekksU0FBUyxJQUFJLEVBQUU7UUFDMUMsTUFBTXVELFNBQVNrRixRQUFRbEYsTUFBTSxJQUFJO1FBRWpDLCtDQUErQztRQUMvQzlDLFVBQVUyRjtRQUNWN0UsZ0JBQWdCa0csU0FBU1YsTUFBTSxHQUFHLElBQUlVLFFBQVEsQ0FBQyxFQUFFLEdBQUc7UUFDcER4RyxxQkFBcUJzRjtRQUNyQnhGLGlCQUFpQjJILFdBQVczQixNQUFNLEdBQUcsSUFBSTJCLFVBQVUsQ0FBQyxFQUFFLEdBQUc7UUFDekQ3SCxnQkFBZ0IwQztRQUVoQiwyQ0FBMkM7UUFDM0MyQyxnQkFBZ0IsR0FBR0UsYUFBYXFCLFNBQVNWLE1BQU0sR0FBRyxJQUFJVSxRQUFRLENBQUMsRUFBRSxHQUFHLElBQUlsRSxRQUFRbUYsV0FBVzNCLE1BQU0sR0FBRyxJQUFJMkIsVUFBVSxDQUFDLEVBQUUsR0FBRyxJQUFJbkM7SUFDOUg7SUFFQSxNQUFNb0MsK0JBQStCO1FBQ25DLE1BQU1DLGlCQUFpQjtZQUNyQnBJLFFBQVE7WUFDUlIsV0FBVyxFQUFFO1lBQ2JzRCxrQkFBa0IsRUFBRTtZQUNwQmhFLGFBQWEsRUFBRTtZQUNmaUUsUUFBUTtRQUNWO1FBRUFGLHFCQUFxQnVGO1FBQ3JCbkksVUFBVTtRQUNWRSxlQUFlO1FBQ2ZFLGdCQUFnQjtRQUNoQkUsaUJBQWlCO1FBQ2pCRSxxQkFBcUIsRUFBRTtRQUN2Qk0sZ0JBQWdCLEVBQUU7UUFDbEJrQywwQkFBMEI7UUFDMUJ5QyxnQkFBZ0IsR0FBRyxJQUFJLElBQUksSUFBSSxJQUFJLEVBQUU7SUFDdkM7SUFFQSxNQUFNMkMscUJBQXFCO1FBQ3pCcEksVUFBVTtRQUNWRSxlQUFlO1FBQ2ZFLGdCQUFnQjtRQUNoQkUsaUJBQWlCO1FBQ2pCRSxxQkFBcUIsRUFBRTtRQUN2Qk0sZ0JBQWdCLEVBQUU7UUFFbEIsaUNBQWlDO1FBQ2pDLElBQUlxQixjQUFjLGVBQWU7WUFDL0JhLDBCQUEwQjtZQUMxQnlDLGdCQUFnQixHQUFHLElBQUksSUFBSSxJQUFJLElBQUksRUFBRTtRQUN2QyxPQUFPO1lBQ0xuQyxxQkFBcUI7WUFDckI2RCxhQUFhLElBQUksR0FBRyxFQUFFLEdBQUcseUNBQXlDO1FBQ3BFO1FBRUEscURBQXFEO1FBQ3JEa0IsV0FBVztZQUNULE1BQU1DLFFBQVEsSUFBSUMsTUFBTTtZQUN4QkMsU0FBU0MsYUFBYSxDQUFDSDtRQUN6QixHQUFHO0lBQ0w7SUFFQSwwREFBMEQ7SUFDMUQsTUFBTUksOEJBQThCLENBQUNoRDtRQUNuQ0QsZ0JBQWdCQyxNQUFNM0YsUUFBUUUsYUFBYUUsY0FBY0UsZUFBZUU7SUFDMUU7SUFFQSxzREFBc0Q7SUFDdEQsTUFBTW9JLHlCQUF5QixDQUFDakQ7UUFDOUJ5QixhQUFhcEgsUUFBUTJGLE1BQU03RSxlQUFlLHdDQUF3QztJQUNwRjtJQUVBLE1BQU0rSCx1QkFBdUIsQ0FBQzVEO1FBQzVCcEQsc0JBQXNCb0Q7UUFDdEI1RCxzQkFBc0I7SUFDeEI7SUFFQSxNQUFNeUgsa0JBQWtCLENBQUNyRDtRQUN2QjFELGlCQUFpQjBELFFBQVEsZ0RBQWdEO1FBQ3pFbEUsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTXdILHlCQUF5QixDQUFDOUQ7UUFDOUJwRCxzQkFBc0JvRDtRQUN0QjlDLG1CQUFtQjtZQUNqQjZHLE1BQU07WUFDTkMsU0FBUyxrREFBK0QsT0FBaEJoRSxXQUFXRyxJQUFJLEVBQUM7UUFDMUU7UUFDQW5ELDBCQUEwQjtJQUM1QjtJQUVBLE1BQU1pSCxrQkFBa0IsQ0FBQ2pFO1FBQ3ZCcEQsc0JBQXNCb0Q7UUFDdEJ4RCxrQkFBa0I7SUFDcEI7SUFFQSxNQUFNMEgsNkJBQTZCLENBQUMxRDtRQUNsQzFELGlCQUFpQjBEO1FBQ2pCOUQsNkJBQTZCO0lBQy9CO0lBRUEsTUFBTXlILG9CQUFvQixDQUFDM0Q7UUFDekIxRCxpQkFBaUIwRDtRQUNqQnRELG1CQUFtQjtZQUNqQjZHLE1BQU07WUFDTkMsU0FBUywyQ0FBc0QsT0FBWHhELE1BQU1MLElBQUksRUFBQztRQUNqRTtRQUNBbkQsMEJBQTBCO0lBQzVCO0lBRUEsa0NBQWtDO0lBQ2xDLE1BQU1vSCwwQkFBMEIsT0FBT0M7UUFDckMvRyxlQUFlO1FBQ2YsSUFBSTtZQUNGLG9EQUFvRDtZQUNwRCxNQUFNakYsOEZBQWtCQSxDQUFDaU0saUJBQWlCLENBQUM7Z0JBQ3pDdkosUUFBUUEsVUFBVXFHO2dCQUNsQk4sZUFBZXZGLGtCQUFrQitGLE1BQU0sR0FBRyxJQUFJL0Ysb0JBQW9CNkY7Z0JBQ2xFUixTQUFTM0YsZUFBZW1HO2dCQUN4QnRCLFFBQVEzRSxpQkFBaUIsS0FBS2lHLFlBQVlqRyxpQkFBaUI7Z0JBQzNEa0csV0FBV2hHLGlCQUFpQitGO1lBQzlCLEdBQUdpRDtRQUNMLEVBQUUsT0FBT2pFLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDaEQsU0FBVTtZQUNSOUMsZUFBZTtRQUNqQjtJQUNGO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1pSCxxQkFBcUIsT0FBT0Y7UUFDaEMvRyxlQUFlO1FBQ2YsSUFBSTtZQUNGLG9EQUFvRDtZQUNwRCxNQUFNakYsOEZBQWtCQSxDQUFDbU0sc0JBQXNCLENBQUM7Z0JBQzlDekosUUFBUUEsVUFBVXFHO2dCQUNsQlksVUFBVW5HLGFBQWF5RixNQUFNLEdBQUcsSUFBSXpGLGVBQWV1RjtnQkFDbkR0QixRQUFRM0UsaUJBQWlCLEtBQUtpRyxZQUFZakcsaUJBQWlCO2dCQUMzRGtHLFdBQVdoRyxpQkFBaUIrRjtZQUM5QixHQUFHaUQ7UUFDTCxFQUFFLE9BQU9qRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywwQ0FBMENBO1FBQzFELFNBQVU7WUFDUjlDLGVBQWU7UUFDakI7SUFDRjtJQUVBLE1BQU1tSCxnQkFBZ0I7UUFDcEIsSUFBSTtZQUNGLElBQUl4SCxnQkFBZ0I4RyxJQUFJLEtBQUsscUJBQXFCO2dCQUNoRCxNQUFNMUwsOEZBQWtCQSxDQUFDcU0sZ0JBQWdCLENBQUMvSCxtQkFBbUJzQyxFQUFFO2dCQUMvRHRGLGNBQWM7Z0JBQ2Q4RztZQUNGLE9BQU8sSUFBSXhELGdCQUFnQjhHLElBQUksS0FBSyxnQkFBZ0I7Z0JBQ2xELE1BQU0xTCw4RkFBa0JBLENBQUNzTSxxQkFBcUIsQ0FBQzlILGNBQWNvQyxFQUFFO2dCQUMvRHRGLGNBQWM7Z0JBQ2RtRCxpQkFBaUIsT0FBTywyQ0FBMkM7Z0JBQ25Fb0Y7Z0JBQ0F6QixtQkFBbUIsaURBQWlEO1lBQ3RFO1FBQ0YsRUFBRSxPQUFPTCxPQUFPO2dCQUVGQSxzQkFBQUE7WUFEWkMsUUFBUUQsS0FBSyxDQUFDLDBCQUEwQkE7WUFDeEN4RyxZQUFZd0csRUFBQUEsa0JBQUFBLE1BQU13RSxRQUFRLGNBQWR4RSx1Q0FBQUEsdUJBQUFBLGdCQUFnQlIsSUFBSSxjQUFwQlEsMkNBQUFBLHFCQUFzQjRELE9BQU8sS0FBSTtRQUMvQyxTQUFVO1lBQ1JoSCwwQkFBMEI7UUFDNUI7SUFDRjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNNkgsYUFBYTtRQUNqQjtZQUNFNUYsSUFBSTtZQUNKaUIsT0FBTztZQUNQNEUsb0JBQU0sOERBQUNsTixzTEFBU0E7Z0JBQUNtTixNQUFNOzs7Ozs7WUFDdkJDLFlBQVk7UUFDZDtRQUNBO1lBQ0UvRixJQUFJO1lBQ0ppQixPQUFPO1lBQ1A0RSxvQkFBTSw4REFBQzVNLHNMQUFNQTtnQkFBQzZNLE1BQU07Ozs7OztZQUNwQkMsWUFBWTtRQUNkO0tBQ0Q7SUFFRCxrREFBa0Q7SUFDbEQsTUFBTUMsZUFBZUosV0FBV3pGLE1BQU0sQ0FBQzhGLENBQUFBO1FBQ3JDLElBQUksQ0FBQ0EsSUFBSUYsVUFBVSxFQUFFLE9BQU87UUFDNUIscUJBQU8sOERBQUM1TSx3RUFBU0E7WUFBQzRNLFlBQVlFLElBQUlGLFVBQVU7WUFBRUcsY0FBYztzQkFDekQ7Ozs7OztJQUVMO0lBRUEsd0NBQXdDO0lBQ3hDLE1BQU1DLHFCQUFxQjs7UUFDekIscUNBQXFDO1FBQ3JDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHck8sK0NBQVFBLENBQUM4RDtRQUUvQyx3REFBd0Q7UUFDeEQ3RCxnREFBU0E7NERBQUM7Z0JBQ1JvTyxlQUFldks7WUFDakI7MkRBQUc7WUFBQ0E7U0FBTztRQUVYLDBCQUEwQjtRQUMxQjdELGdEQUFTQTs0REFBQztnQkFDUixNQUFNcU87Z0ZBQWM7d0JBQ2xCRCxlQUFlO29CQUNqQjs7Z0JBQ0E5QixTQUFTZ0MsZ0JBQWdCLENBQUMsU0FBU0Q7Z0JBQ25DO29FQUFPO3dCQUNML0IsU0FBU2lDLG1CQUFtQixDQUFDLFNBQVNGO29CQUN4Qzs7WUFDRjsyREFBRyxFQUFFO1FBRUwsa0VBQWtFO1FBQ2xFLE1BQU1HLDBCQUEwQixDQUFDQztZQUMvQkwsZUFBZUssRUFBRUMsTUFBTSxDQUFDM0YsS0FBSztRQUMvQjtRQUVBLDBEQUEwRDtRQUMxRCxNQUFNNEYsY0FBYztZQUNsQjdLLFVBQVVxSztZQUNWckgsMEJBQTBCO1lBRTFCLGlCQUFpQjtZQUNqQnFDLFFBQVF5RixHQUFHLENBQUMsb0NBQW9DO2dCQUM5Qy9LLFFBQVFzSztnQkFDUnBLO2dCQUNBRTtnQkFDQUU7Z0JBQ0FFO1lBQ0Y7WUFFQWtGLGdCQUFnQixHQUFHNEUsYUFBYXBLLGFBQWFFLGNBQWNFLGVBQWVFO1FBQzVFO1FBRUEscUJBQ0UsOERBQUN3SztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUMxTyxzTEFBTUE7b0NBQUMwTyxXQUFVOzs7Ozs7OENBQ2xCLDhEQUFDQztvQ0FDQ2xDLE1BQUs7b0NBQ0xtQyxhQUFZO29DQUNaakcsT0FBT29GO29DQUNQYyxVQUFVVDtvQ0FDVk0sV0FBVTtvQ0FDVkksV0FBVyxDQUFDVDt3Q0FDVixJQUFJQSxFQUFFVSxHQUFHLEtBQUssU0FBUzs0Q0FDckJWLEVBQUVXLGNBQWM7NENBQ2hCVDt3Q0FDRjtvQ0FDRjs7Ozs7Ozs7Ozs7O3NDQUlKLDhEQUFDRTs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNoTix5REFBWUE7b0NBQ1h1TixhQUFZO29DQUNadEcsT0FBT2hGO29DQUNQa0wsVUFBVSxDQUFDUixJQUFNbkQsd0JBQXdCbUQsRUFBRUMsTUFBTSxDQUFDM0YsS0FBSztvQ0FDdkR1RyxVQUFVN0w7O3NEQUVWLDhEQUFDOEw7NENBQU94RyxPQUFNO3NEQUFHOzs7Ozs7c0RBQ2pCLDhEQUFDd0c7NENBQU94RyxPQUFNO3NEQUFPOzs7Ozs7d0NBQ3BCaEcsT0FBTzhFLEdBQUcsQ0FBQyxDQUFDeUIsc0JBQ1gsOERBQUNpRztnREFBc0J4RyxPQUFPTyxNQUFNdkIsRUFBRTswREFDbkN1QixNQUFNTCxJQUFJOytDQURBSyxNQUFNdkIsRUFBRTs7Ozs7Ozs7Ozs7OENBTXpCLDhEQUFDakcseURBQVlBO29DQUNYdU4sYUFBWTtvQ0FDWnRHLE9BQU85RTtvQ0FDUGdMLFVBQVUsQ0FBQ1IsSUFBTWxELHlCQUF5QmtELEVBQUVDLE1BQU0sQ0FBQzNGLEtBQUs7O3NEQUV4RCw4REFBQ3dHOzRDQUFPeEcsT0FBTTtzREFBRzs7Ozs7O3NEQUNqQiw4REFBQ3dHOzRDQUFPeEcsT0FBTTtzREFBUzs7Ozs7O3NEQUN2Qiw4REFBQ3dHOzRDQUFPeEcsT0FBTTtzREFBVzs7Ozs7Ozs7Ozs7O2dDQUkxQlIsK0JBQ0MsOERBQUN6Ryx5REFBWUE7b0NBQ1h1TixhQUFZO29DQUNadEcsT0FBTzVFO29DQUNQOEssVUFBVSxDQUFDUixJQUFNakQsMEJBQTBCaUQsRUFBRUMsTUFBTSxDQUFDM0YsS0FBSztvQ0FDekR1RyxVQUFVM0w7O3NEQUVWLDhEQUFDNEw7NENBQU94RyxPQUFNO3NEQUFHOzs7Ozs7d0NBQ2hCMUYsVUFBVXdFLEdBQUcsQ0FBQyxDQUFDOEIsd0JBQ2QsOERBQUM0RjtnREFBd0J4RyxPQUFPWSxRQUFRNUIsRUFBRTswREFDdkM0QixRQUFRVixJQUFJOytDQURGVSxRQUFRNUIsRUFBRTs7Ozs7Ozs7Ozs7OENBTzdCLDhEQUFDL0Ysd0RBQVdBO29DQUNWcU4sYUFBWTtvQ0FDWnhDLE1BQUs7b0NBQ0wyQyxTQUFTYjs7c0RBRVQsOERBQUN0TyxzTEFBTUE7NENBQUN3TixNQUFNOzRDQUFJaUIsV0FBVTs7Ozs7O3NEQUM1Qiw4REFBQ1c7NENBQUtYLFdBQVU7c0RBQW1COzs7Ozs7Ozs7Ozs7OENBR3JDLDhEQUFDOU0sd0RBQVdBO29DQUNWcU4sYUFBWTtvQ0FDWnhDLE1BQUs7b0NBQ0wyQyxTQUFTdEQ7b0NBQ1R3RCxTQUFROztzREFFUiw4REFBQ3BQLHNMQUFTQTs0Q0FBQ3VOLE1BQU07NENBQUlpQixXQUFVOzs7Ozs7c0RBQy9CLDhEQUFDVzs0Q0FBS1gsV0FBVTtzREFBbUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFNekMsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDNU0sd0RBQVdBO3dCQUNWOEcsT0FBTTt3QkFDTkQsT0FBTzFFO3dCQUNQNEssVUFBVXhEO3dCQUNWNUMsU0FBU3RFO3dCQUNUeUssYUFBWTt3QkFDWlcsU0FBU2xMO3dCQUNUbUwsZ0JBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3pCO1FBNUlNMUI7SUE4SU4sbURBQW1EO0lBQ25ELE1BQU0sQ0FBQzJCLGNBQWNDLGdCQUFnQixHQUFHL1AsK0NBQVFBLENBQUM7UUFDL0M4RCxRQUFRO1FBQ1JSLFdBQVcsRUFBRTtRQUNidUQsUUFBUTtRQUNSN0QsUUFBUSxFQUFFO0lBQ1o7SUFFQSxNQUFNZ04sMkJBQTJCLENBQUNuRTtRQUNoQ2tFLGdCQUFnQmxFO0lBQ2xCO0lBRUEsTUFBTW9FLG9CQUFvQixDQUFDbEU7UUFDekIxRSxxQkFBcUI7UUFDckIsTUFBTXFDLGNBQWNxQyxRQUFRakksTUFBTSxJQUFJO1FBQ3RDLE1BQU1rSSxhQUFhRCxRQUFRekksU0FBUyxJQUFJLEVBQUU7UUFDMUMsTUFBTXVELFNBQVNrRixRQUFRbEYsTUFBTSxJQUFJO1FBQ2pDLE1BQU1rRSxXQUFXZ0IsUUFBUS9JLE1BQU0sSUFBSSxFQUFFO1FBRXJDZSxVQUFVMkY7UUFDVnJGLGlCQUFpQjJILFdBQVczQixNQUFNLEdBQUcsSUFBSTJCLFVBQVUsQ0FBQyxFQUFFLEdBQUc7UUFDekQ3SCxnQkFBZ0IwQztRQUNoQmhDLGdCQUFnQmtHO1FBRWhCRyxhQUFheEIsYUFBYSxHQUFHcUI7SUFDL0I7SUFFQSw2Q0FBNkM7SUFDN0MsTUFBTW1GLGlDQUFpQ2hRLDhDQUFPQTttRUFBQztZQUM3QyxvREFBb0Q7WUFDcEQsTUFBTWlRLGNBQWNDLDZIQUE4QztZQUNsRSxPQUFPRCxXQUFXLENBQUMsK0JBQStCLElBQUksRUFBRTtRQUMxRDtrRUFBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNyQjtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDdUI7d0JBQUd2QixXQUFVOzs0QkFDWDdJLGNBQWMsOEJBQ2IsOERBQUN2RixzTEFBU0E7Z0NBQUNtTixNQUFNO2dDQUFJaUIsV0FBVTs7Ozs7MERBQy9CLDhEQUFDOU4sc0xBQU1BO2dDQUFDNk0sTUFBTTtnQ0FBSWlCLFdBQVU7Ozs7Ozs0QkFDN0I3SSxjQUFjLGdCQUFnQixlQUFlOzs7Ozs7O2tDQUdoRCw4REFBQzRJO3dCQUFJQyxXQUFVOzs0QkFFWjdJLGNBQWMsaUJBQWlCSSxZQUFZK0QsTUFBTSxHQUFHLG1CQUNuRCw4REFBQ2tHO2dDQUNDZCxTQUFTLElBQU1yRyxRQUFReUYsR0FBRyxDQUFDLHlCQUF5QnZJO2dDQUNwRHlJLFdBQVU7Z0NBQ1Z5QixPQUFNOztrREFFTiw4REFBQy9QLHNMQUFLQTt3Q0FBQ3FOLE1BQU07Ozs7OztrREFDYiw4REFBQzRCO3dDQUFLWCxXQUFVOzs0Q0FBYzs0Q0FBdUJ6SSxZQUFZK0QsTUFBTTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs0QkFJM0VuRSxjQUFjLCtCQUNiLDhEQUFDaEYsaUVBQVVBO2dDQUNUdVAsVUFBVXREO2dDQUNWL0csYUFBYUE7Z0NBQ2JtSixVQUFVL0wsYUFBYVosWUFBWXlILE1BQU0sS0FBSztnQ0FDOUMwRSxXQUFVOzs7Ozs7NEJBSWI3SSxjQUFjLFlBQVlNLGlCQUFpQjZELE1BQU0sR0FBRyxtQkFDbkQsOERBQUNrRztnQ0FDQ2QsU0FBUztnQ0FDUCxnREFBZ0Q7Z0NBQ2xEO2dDQUNBVixXQUFVO2dDQUNWeUIsT0FBTTs7a0RBRU4sOERBQUMvUCxzTEFBS0E7d0NBQUNxTixNQUFNOzs7Ozs7a0RBQ2IsOERBQUM0Qjt3Q0FBS1gsV0FBVTs7NENBQWM7NENBQXVCdkksaUJBQWlCNkQsTUFBTTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs0QkFHaEZuRSxjQUFjLDBCQUNiLDhEQUFDaEYsaUVBQVVBO2dDQUNUdVAsVUFBVW5EO2dDQUNWbEgsYUFBYUE7Z0NBQ2JtSixVQUFVN0wsbUJBQW1CTixlQUFlaUgsTUFBTSxLQUFLO2dDQUN2RDBFLFdBQVU7Ozs7Ozs0QkFLYjdJLGNBQWMsK0JBQ2IsOERBQUNxSztnQ0FDQ2QsU0FBUztvQ0FDUDlKLHNCQUFzQjtvQ0FDdEJSLHNCQUFzQjtnQ0FDeEI7Z0NBQ0E0SixXQUFVOztrREFFViw4REFBQzNPLHNMQUFJQTt3Q0FBQzBOLE1BQU07Ozs7OztrREFDWiw4REFBQzRCO3dDQUFLWCxXQUFVO2tEQUFjOzs7Ozs7Ozs7Ozs7NEJBR2pDN0ksY0FBYywwQkFDYiw4REFBQ3FLO2dDQUNDZCxTQUFTO29DQUNQNUosaUJBQWlCO29DQUNqQlIsaUJBQWlCO2dDQUNuQjtnQ0FDQTBKLFdBQVU7O2tEQUVWLDhEQUFDM08sc0xBQUlBO3dDQUFDME4sTUFBTTs7Ozs7O2tEQUNaLDhEQUFDNEI7d0NBQUtYLFdBQVU7a0RBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNdEMsOERBQUNsTix5REFBWUE7Z0JBQ1gyTyxPQUFNO2dCQUNOM0Msb0JBQU0sOERBQUN2TixzTEFBTUE7b0JBQUN3TixNQUFNO29CQUFJaUIsV0FBVTs7Ozs7O2dCQUNsQ08sYUFBWTtnQkFDWm9CLGVBQWVSO2dCQUNmUyxjQUFhO2dCQUNiNUUsdUJBQ0U7O3NDQUNFLDhEQUFDN0osdURBQVVBOzRCQUNUME8sTUFBTTVDOzRCQUNOOUgsV0FBV0E7NEJBQ1gySyxhQUFhMUs7NEJBQ2JtSixhQUFZOzs7Ozs7c0NBRWQsOERBQUNSOzRCQUFJQyxXQUFVO3NDQUNaN0ksY0FBYyw4QkFDYiw4REFBQzdELG1GQUFpQkE7Z0NBQ2hCMEosU0FBU3JGO2dDQUNUb0ssaUJBQWlCbEY7Z0NBQ2pCbUYsVUFBVWpGO2dDQUNWa0YsZ0JBQWdCL0U7Ozs7O3VEQUdsQiw4REFBQzNKLDJFQUFhQTtnQ0FDWnlKLFNBQVMrRDtnQ0FDVGdCLGlCQUFpQmQ7Z0NBQ2pCZSxVQUFVZDs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBU3JCL0osY0FBYywrQkFDYiw4REFBQy9FLHdFQUFTQTtnQkFBQzRNLFlBQVc7MEJBQ3BCLDRFQUFDL0wsd0RBQVdBO29CQUNWc04sYUFBWTtvQkFDWmtCLE9BQU07b0JBQ05TLDZCQUNFLDhEQUFDbkM7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUN3Qjs0QkFDQ2QsU0FBUyxJQUFNakc7NEJBQ2Z1RixXQUFVOzRCQUNWeUIsT0FBTTtzQ0FFTiw0RUFBQ2pRLHNMQUFTQTtnQ0FBQ3VOLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7b0JBSXZCb0QsU0FBUzt3QkFDUDs0QkFBRUMsUUFBUTs0QkFBSUMsT0FBTzs0QkFBVUMsT0FBTzs0QkFBUUMsVUFBVTt3QkFBTTt3QkFDOUQ7NEJBQUVILFFBQVE7NEJBQWFDLE9BQU87NEJBQVFDLE9BQU87d0JBQU07d0JBQ25EOzRCQUFFRixRQUFROzRCQUFTQyxPQUFPOzRCQUFTQyxPQUFPO3dCQUFNOzJCQUM1QzdJLGdCQUFnQjs0QkFBQztnQ0FBRTJJLFFBQVE7Z0NBQVdDLE9BQU87Z0NBQVdDLE9BQU87NEJBQU07eUJBQUUsR0FBRyxFQUFFO3dCQUNoRjs0QkFBRUYsUUFBUTs0QkFBYUMsT0FBTzs0QkFBZUMsT0FBTzt3QkFBTTt3QkFDMUQ7NEJBQUVGLFFBQVE7NEJBQVlDLE9BQU87NEJBQVNDLE9BQU87d0JBQU07d0JBQ25EOzRCQUFFRixRQUFROzRCQUFVQyxPQUFPOzRCQUFVQyxPQUFPO3dCQUFNO3dCQUNsRDs0QkFBRUYsUUFBUTs0QkFBU0MsT0FBTzs0QkFBV3JDLFdBQVc7NEJBQWNzQyxPQUFPOzRCQUFPQyxVQUFVO3dCQUFNO3FCQUM3RjtvQkFDRDNJLE1BQU0vRjtvQkFDTlksV0FBV0E7b0JBQ1grTixjQUFhO29CQUNiQyx5QkFBVyw4REFBQzdRLHNMQUFTQTt3QkFBQ21OLE1BQU07Ozs7OztvQkFDNUIyRCxTQUFRO29CQUNSQyxvQkFBb0I7b0JBQ3BCQyxrQkFBaUI7b0JBQ2pCQyxzQkFBcUI7b0JBQ3JCQyxhQUFhL0s7b0JBQ2JnTCxZQUFZOUs7b0JBQ1orSyxZQUFZN0s7b0JBQ1o4SyxjQUFjdkY7b0JBQ2R3RixRQUFRLENBQUNiLE9BQU9jO3dCQUNkLDBGQUEwRjt3QkFDMUYxSSxnQkFDRTFDLHdCQUNBaEQsUUFDQUUsYUFDQUUsY0FDQUUsZUFDQUUsbUJBQ0E4TSxPQUNBYztvQkFFSjtvQkFDQUMsZ0JBQWdCO29CQUNoQnpLLGNBQWNBO29CQUNkMEssc0JBQXNCLENBQUNDO3dCQUNyQjFLLGdCQUFnQjBLO3dCQUNoQjdJLGdCQUFnQixHQUFHMUYsUUFBUUUsYUFBYUUsY0FBY0UsZUFBZUUsbUJBQW1CLFFBQVEsT0FBTytOO29CQUN6RztvQkFDQS9MLGFBQWFFO29CQUNiOEwsYUFBYWpLO29CQUNia0ssV0FBVyxDQUFDeEosWUFBWXlKLE9BQU9DLGNBQWNDOzRCQTJFaEMzSixvQkFxRFdBLHFCQUVrQkE7NkNBakl4Qyw4REFBQzRKOzRCQUF1QjVELFdBQVcwRCxhQUFhRyxPQUFPOztnQ0FDcERGLGVBQWVHLFFBQVEsQ0FBQywyQkFDdkIsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWiw0RUFBQzNNLDJEQUFjQTt3Q0FDYmtOLGFBQVk7d0NBQ1p6SCxTQUFTdkIsWUFBWXVNLFFBQVEsQ0FBQzlKLFdBQVdmLEVBQUU7d0NBQzNDa0gsVUFBVSxDQUFDUixJQUFNekcsZ0JBQWdCYyxXQUFXZixFQUFFLEVBQUUwRyxFQUFFQyxNQUFNLENBQUM5RyxPQUFPO3dDQUNoRXFCLE1BQU0scUJBQW1DLE9BQWRILFdBQVdmLEVBQUU7Ozs7Ozs7Ozs7O2dDQUk3QzBLLGVBQWVHLFFBQVEsQ0FBQyx5QkFDdkIsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ3BPLHNMQUFTQTtvREFBQ21OLE1BQU07Ozs7Ozs7Ozs7OzBEQUVuQiw4REFBQ2dCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDWmhHLFdBQVdHLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBT3pCd0osZUFBZUcsUUFBUSxDQUFDLDBCQUN2Qiw4REFBQ0M7b0NBQUcvRCxXQUFVOzhDQUNYaEcsV0FBV1EsS0FBSyxpQkFDZiw4REFBQ21HO3dDQUFLWCxXQUFVO2tEQUNiaEcsV0FBV1EsS0FBSyxDQUFDTCxJQUFJOzs7OzsrREFHeEIsOERBQUN3Rzt3Q0FBS1gsV0FBVTtrREFBZ0Q7Ozs7Ozs7Ozs7O2dDQU9yRXZHLGlCQUFpQmtLLGVBQWVHLFFBQVEsQ0FBQyw0QkFDeEMsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWGhHLFdBQVdhLE9BQU8saUJBQ2pCLDhEQUFDa0Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDL04sc0xBQVFBO2dEQUFDOE0sTUFBTTtnREFBSWlCLFdBQVU7Ozs7OzswREFDOUIsOERBQUNXO2dEQUFLWCxXQUFVOzBEQUNiaEcsV0FBV2EsT0FBTyxDQUFDVixJQUFJOzs7Ozs7Ozs7OzsrREFJNUIsOERBQUN3Rzt3Q0FBS1gsV0FBVTtrREFBZ0Q7Ozs7Ozs7Ozs7O2dDQU9yRTJELGVBQWVHLFFBQVEsQ0FBQyxnQ0FDdkIsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWiw0RUFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1poRyxXQUFXZ0ssV0FBVyxrQkFDckIsOERBQUNyRDs0Q0FBS1gsV0FBVTtzREFBZ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBUXZFMkQsZUFBZUcsUUFBUSxDQUFDLDBCQUN2Qiw4REFBQ0M7b0NBQUcvRCxXQUFVOzhDQUNaLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNqTyxzTEFBS0E7Z0RBQUNnTixNQUFNO2dEQUFJaUIsV0FBVTs7Ozs7OzBEQUMzQiw4REFBQ1c7Z0RBQUtYLFdBQVU7O29EQUNiaEcsRUFBQUEscUJBQUFBLFdBQVdpSyxNQUFNLGNBQWpCaksseUNBQUFBLG1CQUFtQmtLLEtBQUssS0FBSTtvREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQU10Q1AsZUFBZUcsUUFBUSxDQUFDLDJCQUN2Qiw4REFBQ0M7b0NBQUcvRCxXQUFVOzhDQUNaLDRFQUFDVzt3Q0FDQ1gsV0FBVyxnRUFHUixPQUh3RWhHLFdBQVdGLE1BQU0sR0FDdEYseUVBQ0E7a0RBR0xFLFdBQVdGLE1BQU0saUJBQ2hCOzs4REFDRSw4REFBQ2pJLHNMQUFXQTtvREFBQ2tOLE1BQU07Ozs7Ozs4REFDbkIsOERBQUM0Qjs4REFBSzs7Ozs7Ozt5RUFHUjs7OERBQ0UsOERBQUM3TyxzTEFBT0E7b0RBQUNpTixNQUFNOzs7Ozs7OERBQ2YsOERBQUM0Qjs4REFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQU9mZ0QsZUFBZUcsUUFBUSxDQUFDLDRCQUN2Qiw4REFBQ0M7b0NBQUcvRCxXQUFVOzhDQUNaLDRFQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN3QjtnREFDQ2QsU0FBUyxJQUFNekMsZ0JBQWdCakU7Z0RBQy9CZ0csV0FBVTtnREFDVnlCLE9BQU07MERBRU4sNEVBQUN6UCxzTEFBU0E7b0RBQUMrTSxNQUFNOzs7Ozs7Ozs7OzswREFFbkIsOERBQUMzTSx3RUFBU0E7Z0RBQUM0TSxZQUFXOzBEQUNwQiw0RUFBQ3dDO29EQUNDZCxTQUFTLElBQU05QyxxQkFBcUI1RDtvREFDcENnRyxXQUFVO29EQUNWeUIsT0FBTTs4REFFTiw0RUFBQ2hRLHNMQUFJQTt3REFBQ3NOLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7MERBR2hCLDhEQUFDM00sd0VBQVNBO2dEQUFDNE0sWUFBVzswREFDcEIsNEVBQUN3QztvREFDQ2QsU0FBUyxJQUFNNUMsdUJBQXVCOUQ7b0RBQ3RDZ0csV0FBVTtvREFDVnlCLE9BQU07b0RBQ05qQixVQUFVeEcsRUFBQUEsc0JBQUFBLFdBQVdpSyxNQUFNLGNBQWpCakssMENBQUFBLG9CQUFtQmtLLEtBQUssSUFBRzs4REFFckMsNEVBQUN4UyxzTEFBS0E7d0RBQUNxTixNQUFNO3dEQUFJaUIsV0FBV2hHLEVBQUFBLHNCQUFBQSxXQUFXaUssTUFBTSxjQUFqQmpLLDBDQUFBQSxvQkFBbUJrSyxLQUFLLElBQUcsSUFBSSxrQ0FBa0M7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MkJBakloR2xLLFdBQVdmLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7WUE4STdCOUIsY0FBYywwQkFDYiw4REFBQy9FLHdFQUFTQTtnQkFBQzRNLFlBQVc7MEJBQ3BCLDRFQUFDL0wsd0RBQVdBO29CQUNWc04sYUFBWTtvQkFDWmtCLE9BQU07b0JBQ05TLDZCQUNFLDhEQUFDVjt3QkFDQ2QsU0FBUyxJQUFNeEU7d0JBQ2Y4RCxXQUFVO3dCQUNWeUIsT0FBTTtrQ0FFTiw0RUFBQ2pRLHNMQUFTQTs0QkFBQ3VOLE1BQU07Ozs7Ozs7Ozs7O29CQUdyQm9ELFNBQVM7d0JBQ1A7NEJBQUVDLFFBQVE7NEJBQUlDLE9BQU87NEJBQVVDLE9BQU87NEJBQVFDLFVBQVU7d0JBQU07d0JBQzlEOzRCQUFFSCxRQUFROzRCQUFTQyxPQUFPOzRCQUFRQyxPQUFPO3dCQUFNO3dCQUMvQzs0QkFBRUYsUUFBUTs0QkFBYUMsT0FBTzs0QkFBZUMsT0FBTzt3QkFBTTsyQkFDdEQ3SSxnQkFBZ0I7NEJBQUM7Z0NBQUUySSxRQUFRO2dDQUFXQyxPQUFPO2dDQUFXQyxPQUFPOzRCQUFNO3lCQUFFLEdBQUcsRUFBRTt3QkFDaEY7NEJBQUVGLFFBQVE7NEJBQWNDLE9BQU87NEJBQWVDLE9BQU87d0JBQU07d0JBQzNEOzRCQUFFRixRQUFROzRCQUFVQyxPQUFPOzRCQUFVQyxPQUFPO3dCQUFNO3dCQUNsRDs0QkFBRUYsUUFBUTs0QkFBU0MsT0FBTzs0QkFBV3JDLFdBQVc7NEJBQWNzQyxPQUFPOzRCQUFPQyxVQUFVO3dCQUFNO3FCQUM3RjtvQkFDRDNJLE1BQU12RjtvQkFDTkksV0FBV0U7b0JBQ1g2TixjQUFhO29CQUNiQyx5QkFBVyw4REFBQzlRLHNMQUFHQTt3QkFBQ29OLE1BQU07Ozs7OztvQkFDdEIyRCxTQUFRO29CQUNSQyxvQkFBb0I7b0JBQ3BCQyxrQkFBaUI7b0JBQ2pCQyxzQkFBcUI7b0JBQ3JCQyxhQUFheks7b0JBQ2IwSyxZQUFZeEs7b0JBQ1p5SyxZQUFZdks7b0JBQ1p3SyxjQUFjdEY7b0JBQ2R1RixRQUFRLENBQUNiLE9BQU9jO3dCQUNkLHNGQUFzRjt3QkFDdEZwSCxtQkFBbUJzRyxPQUFPYztvQkFDNUI7b0JBQ0FDLGdCQUFnQjtvQkFDaEJ6SyxjQUFjQTtvQkFDZDBLLHNCQUFzQixDQUFDQzt3QkFDckIxSyxnQkFBZ0IwSzt3QkFDaEJuSCxhQUFhcEgsUUFBUSxHQUFHYyxjQUFjeU47b0JBQ3hDO29CQUNBRSxXQUFXLENBQUNoSixPQUFPaUosT0FBT0MsY0FBY0M7NEJBNkQzQm5KLGVBcURXQSxnQkFFa0JBOzZDQW5IeEMsOERBQUNvSjs0QkFBa0I1RCxXQUFXMEQsYUFBYUcsT0FBTzs7Z0NBQy9DRixlQUFlRyxRQUFRLENBQUMsMkJBQ3ZCLDhEQUFDQztvQ0FBRy9ELFdBQVU7OENBQ1osNEVBQUMzTSwyREFBY0E7d0NBQ2JrTixhQUFZO3dDQUNaekgsU0FBU3JCLGlCQUFpQnFNLFFBQVEsQ0FBQ3RKLE1BQU12QixFQUFFO3dDQUMzQ2tILFVBQVUsQ0FBQ1IsSUFBTW5HLHFCQUFxQmdCLE1BQU12QixFQUFFLEVBQUUwRyxFQUFFQyxNQUFNLENBQUM5RyxPQUFPO3dDQUNoRXFCLE1BQU0sZ0JBQXlCLE9BQVRLLE1BQU12QixFQUFFOzs7Ozs7Ozs7OztnQ0FJbkMwSyxlQUFlRyxRQUFRLENBQUMseUJBQ3ZCLDhEQUFDQztvQ0FBRy9ELFdBQVU7OENBQ1osNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNyTyxzTEFBR0E7b0RBQUNvTixNQUFNOzs7Ozs7Ozs7OzswREFFYiw4REFBQ2dCO2dEQUFJQyxXQUFVOzBEQUNiLDRFQUFDRDtvREFBSUMsV0FBVTs4REFDWnhGLE1BQU1MLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBT3BCd0osZUFBZUcsUUFBUSxDQUFDLGdDQUN2Qiw4REFBQ0M7b0NBQUcvRCxXQUFVOzhDQUNaLDRFQUFDRDt3Q0FBSUMsV0FBVTtrREFDWnhGLE1BQU13SixXQUFXLGtCQUNoQiw4REFBQ3JEOzRDQUFLWCxXQUFVO3NEQUFnRDs7Ozs7Ozs7Ozs7Ozs7OztnQ0FRdkV2RyxpQkFBaUJrSyxlQUFlRyxRQUFRLENBQUMsNEJBQ3hDLDhEQUFDQztvQ0FBRy9ELFdBQVU7OENBQ1h4RixNQUFNSyxPQUFPLGlCQUNaLDhEQUFDa0Y7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDL04sc0xBQVFBO2dEQUFDOE0sTUFBTTtnREFBSWlCLFdBQVU7Ozs7OzswREFDOUIsOERBQUNXO2dEQUFLWCxXQUFVOzBEQUNieEYsTUFBTUssT0FBTyxDQUFDVixJQUFJOzs7Ozs7Ozs7OzsrREFJdkIsOERBQUN3Rzt3Q0FBS1gsV0FBVTtrREFBZ0Q7Ozs7Ozs7Ozs7O2dDQU9yRTJELGVBQWVHLFFBQVEsQ0FBQyxnQ0FDdkIsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDcE8sc0xBQVNBO2dEQUFDbU4sTUFBTTtnREFBSWlCLFdBQVU7Ozs7OzswREFDL0IsOERBQUNXO2dEQUFLWCxXQUFVOztvREFDYnhGLEVBQUFBLGdCQUFBQSxNQUFNeUosTUFBTSxjQUFaekosb0NBQUFBLGNBQWMzRyxXQUFXLEtBQUk7b0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FNdkM4UCxlQUFlRyxRQUFRLENBQUMsMkJBQ3ZCLDhEQUFDQztvQ0FBRy9ELFdBQVU7OENBQ1osNEVBQUNXO3dDQUNDWCxXQUFXLGdFQUdSLE9BSHdFeEYsTUFBTVYsTUFBTSxHQUNqRix5RUFDQTtrREFHTFUsTUFBTVYsTUFBTSxpQkFDWDs7OERBQ0UsOERBQUNqSSxzTEFBV0E7b0RBQUNrTixNQUFNOzs7Ozs7OERBQ25CLDhEQUFDNEI7OERBQUs7Ozs7Ozs7eUVBR1I7OzhEQUNFLDhEQUFDN08sc0xBQU9BO29EQUFDaU4sTUFBTTs7Ozs7OzhEQUNmLDhEQUFDNEI7OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FPZmdELGVBQWVHLFFBQVEsQ0FBQyw0QkFDdkIsOERBQUNDO29DQUFHL0QsV0FBVTs4Q0FDWiw0RUFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDd0I7Z0RBQ0NkLFNBQVMsSUFBTXhDLDJCQUEyQjFEO2dEQUMxQ3dGLFdBQVU7Z0RBQ1Z5QixPQUFNOzBEQUVOLDRFQUFDN1Asc0xBQVNBO29EQUFDbU4sTUFBTTs7Ozs7Ozs7Ozs7MERBRW5CLDhEQUFDM00sd0VBQVNBO2dEQUFDNE0sWUFBVzswREFDcEIsNEVBQUN3QztvREFDQ2QsU0FBUyxJQUFNN0MsZ0JBQWdCckQ7b0RBQy9Cd0YsV0FBVTtvREFDVnlCLE9BQU07OERBRU4sNEVBQUNoUSxzTEFBSUE7d0RBQUNzTixNQUFNOzs7Ozs7Ozs7Ozs7Ozs7OzBEQUdoQiw4REFBQzNNLHdFQUFTQTtnREFBQzRNLFlBQVc7MERBQ3BCLDRFQUFDd0M7b0RBQ0NkLFNBQVMsSUFBTXZDLGtCQUFrQjNEO29EQUNqQ3dGLFdBQVU7b0RBQ1Z5QixPQUFNO29EQUNOakIsVUFBVWhHLEVBQUFBLGlCQUFBQSxNQUFNeUosTUFBTSxjQUFaekoscUNBQUFBLGVBQWMzRyxXQUFXLElBQUc7OERBRXRDLDRFQUFDbkMsc0xBQUtBO3dEQUFDcU4sTUFBTTt3REFBSWlCLFdBQVd4RixFQUFBQSxpQkFBQUEsTUFBTXlKLE1BQU0sY0FBWnpKLHFDQUFBQSxlQUFjM0csV0FBVyxJQUFHLElBQUksa0NBQWtDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJCQW5IakcyRyxNQUFNdkIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OzswQkFnSXpCLDhEQUFDdkcsNkVBQW1CQTtnQkFDbEJ5UixRQUFRaE87Z0JBQ1JpTyxTQUFTLElBQU1oTyxzQkFBc0I7Z0JBQ3JDNEQsWUFBWXJEO2dCQUNaMUMsUUFBUUE7Z0JBQ1JvUSxXQUFXO29CQUNUak8sc0JBQXNCO29CQUN0QnFFO2dCQUNGOzs7Ozs7MEJBR0YsOERBQUM5SCxtRkFBd0JBO2dCQUN2QndSLFFBQVE5TjtnQkFDUitOLFNBQVMsSUFBTTlOLGlCQUFpQjtnQkFDaENrRSxPQUFPM0Q7Z0JBQ1B3TixXQUFXO29CQUNUL04saUJBQWlCO29CQUVqQiwrRUFBK0U7b0JBQy9FLElBQUlPLGVBQWU7d0JBQ2pCLElBQUk7NEJBQ0YsTUFBTXlOLGVBQWUsTUFBTWpTLDhGQUFrQkEsQ0FBQ2tTLHNCQUFzQixDQUFDMU4sY0FBY29DLEVBQUU7NEJBQ3JGbkMsaUJBQWlCd047d0JBQ25CLEVBQUUsT0FBT2xLLE9BQU87NEJBQ2RDLFFBQVFELEtBQUssQ0FBQyxzQ0FBc0NBO3dCQUN0RDtvQkFDRjtvQkFFQSxvQkFBb0I7b0JBQ3BCLE1BQU04QjtvQkFFTixpREFBaUQ7b0JBQ2pEekI7Z0JBQ0Y7Ozs7OzswQkFHRiw4REFBQzdILCtFQUFvQkE7Z0JBQ25CdVIsUUFBUTVOO2dCQUNSNk4sU0FBUyxJQUFNNU4sa0JBQWtCO2dCQUNqQ2dPLFlBQVksRUFBRTdOLCtCQUFBQSx5Q0FBQUEsbUJBQW9Cc0MsRUFBRTs7Ozs7OzBCQUd0Qyw4REFBQ3BHLDBGQUErQkE7Z0JBQzlCc1IsUUFBUTFOO2dCQUNSMk4sU0FBUyxJQUFNMU4sNkJBQTZCO2dCQUM1Q2tFLE9BQU8sRUFBRS9ELDBCQUFBQSxvQ0FBQUEsY0FBZW9DLEVBQUU7Ozs7OzswQkFHNUIsOERBQUN4Ryx5RUFBa0JBO2dCQUNqQjBSLFFBQVFwTjtnQkFDUnFOLFNBQVMsSUFBTXBOLDBCQUEwQjtnQkFDekN5TixXQUFXaEc7Z0JBQ1hnRCxPQUNFeEssQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUI4RyxJQUFJLE1BQUssc0JBQXNCLHNCQUNoRDlHLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCOEcsSUFBSSxNQUFLLGlCQUFpQixrQkFDM0M7Z0JBRUZDLFNBQVMvRyxDQUFBQSw0QkFBQUEsc0NBQUFBLGdCQUFpQitHLE9BQU8sS0FBSTtnQkFDckM0QyxTQUFTM0osQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUI4RyxJQUFJLENBQUMrRixRQUFRLENBQUMsYUFBWSxXQUFXO2dCQUMvRHZELGFBQVk7Z0JBQ1ptRSxhQUFhek4sQ0FBQUEsNEJBQUFBLHNDQUFBQSxnQkFBaUI4RyxJQUFJLENBQUMrRixRQUFRLENBQUMsYUFBWSxZQUFZO2dCQUNwRWEsWUFBVzs7Ozs7Ozs7Ozs7O0FBTW5CO0dBcjFDTW5SOztRQUMwQmpCLDBEQUFPQTtRQUNFQyw0REFBUUE7OztLQUYzQ2dCO0FBdTFDTixpRUFBZUEsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhcm1hblxcRGVza3RvcFxcUHJvamV0byBYXFxoaWdoLXRpZGUtc3lzdGVtcy1mcm9udGVuZFxcc3JjXFxhcHBcXG1vZHVsZXNcXGFkbWluXFxwcm9mZXNzaW9uc1xcUHJvZmVzc2lvbnNQYWdlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7XHJcbiAgUGx1cyxcclxuICBTZWFyY2gsXHJcbiAgRmlsdGVyLFxyXG4gIFJlZnJlc2hDdyxcclxuICBFZGl0LFxyXG4gIFRyYXNoLFxyXG4gIFRhZyxcclxuICBCcmllZmNhc2UsXHJcbiAgQ2hlY2tDaXJjbGUsXHJcbiAgWENpcmNsZSxcclxuICBVc2VycyxcclxuICBVc2VyUm91bmQsXHJcbiAgQnVpbGRpbmcsXHJcbiAgTGF5ZXJzXHJcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xyXG5pbXBvcnQgRXhwb3J0TWVudSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL0V4cG9ydE1lbnVcIjtcclxuaW1wb3J0IHsgUHJvdGVjdGVkIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wZXJtaXNzaW9ucy9Qcm90ZWN0ZWRcIjtcclxuaW1wb3J0IHsgcHJvZmVzc2lvbnNTZXJ2aWNlIH0gZnJvbSBcIkAvYXBwL21vZHVsZXMvYWRtaW4vc2VydmljZXMvcHJvZmVzc2lvbnNTZXJ2aWNlXCI7XHJcbmltcG9ydCB7IGNvbXBhbnlTZXJ2aWNlIH0gZnJvbSBcIkAvYXBwL21vZHVsZXMvYWRtaW4vc2VydmljZXMvY29tcGFueVNlcnZpY2VcIjtcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gXCJAL2NvbnRleHRzL0F1dGhDb250ZXh0XCI7XHJcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSBcIkAvY29udGV4dHMvVG9hc3RDb250ZXh0XCI7XHJcbmltcG9ydCBDb25maXJtYXRpb25EaWFsb2cgZnJvbSBcIkAvY29tcG9uZW50cy91aS9Db25maXJtYXRpb25EaWFsb2dcIjtcclxuaW1wb3J0IFByb2Zlc3Npb25Gb3JtTW9kYWwgZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi9Qcm9mZXNzaW9uRm9ybU1vZGFsXCI7XHJcbmltcG9ydCBQcm9mZXNzaW9uR3JvdXBGb3JtTW9kYWwgZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi9Qcm9mZXNzaW9uR3JvdXBGb3JtTW9kYWxcIjtcclxuaW1wb3J0IFByb2Zlc3Npb25Vc2Vyc01vZGFsIGZyb20gXCJAL2NvbXBvbmVudHMvYWRtaW4vUHJvZmVzc2lvblVzZXJzTW9kYWxcIjtcclxuaW1wb3J0IFByb2Zlc3Npb25Hcm91cFByb2Zlc3Npb25zTW9kYWwgZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi9Qcm9mZXNzaW9uR3JvdXBQcm9mZXNzaW9uc01vZGFsXCI7XHJcbmltcG9ydCB7IE1vZHVsZUhlYWRlciwgTW9kdWxlSW5wdXQsIE1vZHVsZVNlbGVjdCwgTW9kdWxlVGFibGUsIE1vZGFsQnV0dG9uLCBNb2R1bGVUYWJzLCBNdWx0aVNlbGVjdCwgTW9kdWxlQ2hlY2tib3ggfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpXCI7XHJcbmltcG9ydCB7IE9jY3VwYXRpb25GaWx0ZXJzIH0gZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi9PY2N1cGF0aW9uRmlsdGVyc1wiO1xyXG5pbXBvcnQgeyBHcm91cHNGaWx0ZXJzIH0gZnJvbSBcIkAvY29tcG9uZW50cy9hZG1pbi9Hcm91cHNGaWx0ZXJzXCI7XHJcblxyXG5jb25zdCBQcm9mZXNzaW9uc1BhZ2UgPSAoKSA9PiB7XHJcbiAgY29uc3QgeyB1c2VyOiBjdXJyZW50VXNlciB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IHsgdG9hc3Rfc3VjY2VzcywgdG9hc3RfZXJyb3IgfSA9IHVzZVRvYXN0KCk7XHJcbiAgY29uc3QgW3Byb2Zlc3Npb25zLCBzZXRQcm9mZXNzaW9uc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2FsbFByb2Zlc3Npb25zLCBzZXRBbGxQcm9mZXNzaW9uc10gPSB1c2VTdGF0ZShbXSk7IC8vIEFybWF6ZW5hIHRvZGFzIGFzIHByb2Zpc3PDtWVzIHBhcmEgcGFnaW5hw6fDo28gbWFudWFsXHJcbiAgY29uc3QgW2dyb3Vwcywgc2V0R3JvdXBzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbYWxsR3JvdXBzLCBzZXRBbGxHcm91cHNdID0gdXNlU3RhdGUoW10pOyAvLyBBcm1hemVuYSB0b2RvcyBvcyBncnVwb3MgcGFyYSBwYWdpbmHDp8OjbyBtYW51YWxcclxuICBjb25zdCBbZmlsdGVyZWRHcm91cHMsIHNldEZpbHRlcmVkR3JvdXBzXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbY29tcGFuaWVzLCBzZXRDb21wYW5pZXNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbaXNMb2FkaW5nR3JvdXBzLCBzZXRJc0xvYWRpbmdHcm91cHNdID0gdXNlU3RhdGUodHJ1ZSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZ0NvbXBhbmllcywgc2V0SXNMb2FkaW5nQ29tcGFuaWVzXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbc2VhcmNoLCBzZXRTZWFyY2hdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2dyb3VwRmlsdGVyLCBzZXRHcm91cEZpbHRlcl0gPSB1c2VTdGF0ZShcIlwiKTtcclxuICBjb25zdCBbc3RhdHVzRmlsdGVyLCBzZXRTdGF0dXNGaWx0ZXJdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2NvbXBhbnlGaWx0ZXIsIHNldENvbXBhbnlGaWx0ZXJdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW3Byb2Zlc3Npb25zRmlsdGVyLCBzZXRQcm9mZXNzaW9uc0ZpbHRlcl0gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW3Byb2Zlc3Npb25PcHRpb25zLCBzZXRQcm9mZXNzaW9uT3B0aW9uc10gPSB1c2VTdGF0ZShbXSk7XHJcbiAgY29uc3QgW2lzTG9hZGluZ1Byb2Zlc3Npb25PcHRpb25zLCBzZXRJc0xvYWRpbmdQcm9mZXNzaW9uT3B0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2dyb3Vwc0ZpbHRlciwgc2V0R3JvdXBzRmlsdGVyXSA9IHVzZVN0YXRlKFtdKTtcclxuICBjb25zdCBbZ3JvdXBPcHRpb25zLCBzZXRHcm91cE9wdGlvbnNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtpc0xvYWRpbmdHcm91cE9wdGlvbnMsIHNldElzTG9hZGluZ0dyb3VwT3B0aW9uc10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3Byb2Zlc3Npb25Gb3JtT3Blbiwgc2V0UHJvZmVzc2lvbkZvcm1PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbZ3JvdXBGb3JtT3Blbiwgc2V0R3JvdXBGb3JtT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3VzZXJzTW9kYWxPcGVuLCBzZXRVc2Vyc01vZGFsT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2dyb3VwUHJvZmVzc2lvbnNNb2RhbE9wZW4sIHNldEdyb3VwUHJvZmVzc2lvbnNNb2RhbE9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZFByb2Zlc3Npb24sIHNldFNlbGVjdGVkUHJvZmVzc2lvbl0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbc2VsZWN0ZWRHcm91cCwgc2V0U2VsZWN0ZWRHcm91cF0gPSB1c2VTdGF0ZShudWxsKTtcclxuICBjb25zdCBbY29uZmlybWF0aW9uRGlhbG9nT3Blbiwgc2V0Q29uZmlybWF0aW9uRGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2FjdGlvblRvQ29uZmlybSwgc2V0QWN0aW9uVG9Db25maXJtXSA9IHVzZVN0YXRlKG51bGwpO1xyXG4gIGNvbnN0IFthY3RpdmVUYWIsIHNldEFjdGl2ZVRhYl0gPSB1c2VTdGF0ZShcInByb2Zlc3Npb25zXCIpOyAvLyBcInByb2Zlc3Npb25zXCIgb3UgXCJncm91cHNcIlxyXG4gIGNvbnN0IFtpc0V4cG9ydGluZywgc2V0SXNFeHBvcnRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtzZWxlY3RlZElkcywgc2V0U2VsZWN0ZWRJZHNdID0gdXNlU3RhdGUoW10pO1xyXG4gIGNvbnN0IFtzZWxlY3RlZEdyb3VwSWRzLCBzZXRTZWxlY3RlZEdyb3VwSWRzXSA9IHVzZVN0YXRlKFtdKTtcclxuXHJcbiAgLy8gRXN0YWRvcyBwYXJhIG8gbm92byBzaXN0ZW1hIGRlIGZpbHRyb3NcclxuICBjb25zdCBbb2NjdXBhdGlvbkZpbHRlcnMsIHNldE9jY3VwYXRpb25GaWx0ZXJzXSA9IHVzZVN0YXRlKHtcclxuICAgIHNlYXJjaDogXCJcIixcclxuICAgIGNvbXBhbmllczogW10sXHJcbiAgICBwcm9mZXNzaW9uR3JvdXBzOiBbXSxcclxuICAgIHByb2Zlc3Npb25zOiBbXSxcclxuICAgIHN0YXR1czogXCJcIlxyXG4gIH0pO1xyXG5cclxuICAvLyBFc3RhZG9zIHBhcmEgcGFnaW5hw6fDo28gZGUgcHJvZmlzc8O1ZXNcclxuICBjb25zdCBbY3VycmVudFByb2Zlc3Npb25zUGFnZSwgc2V0Q3VycmVudFByb2Zlc3Npb25zUGFnZV0gPSB1c2VTdGF0ZSgxKTtcclxuICBjb25zdCBbdG90YWxQcm9mZXNzaW9uc1BhZ2VzLCBzZXRUb3RhbFByb2Zlc3Npb25zUGFnZXNdID0gdXNlU3RhdGUoMSk7XHJcbiAgY29uc3QgW3RvdGFsUHJvZmVzc2lvbnMsIHNldFRvdGFsUHJvZmVzc2lvbnNdID0gdXNlU3RhdGUoMCk7XHJcblxyXG4gIC8vIEVzdGFkb3MgcGFyYSBwYWdpbmHDp8OjbyBkZSBncnVwb3NcclxuICBjb25zdCBbY3VycmVudEdyb3Vwc1BhZ2UsIHNldEN1cnJlbnRHcm91cHNQYWdlXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFt0b3RhbEdyb3Vwc1BhZ2VzLCBzZXRUb3RhbEdyb3Vwc1BhZ2VzXSA9IHVzZVN0YXRlKDEpO1xyXG4gIGNvbnN0IFt0b3RhbEdyb3Vwcywgc2V0VG90YWxHcm91cHNdID0gdXNlU3RhdGUoMCk7XHJcblxyXG4gIC8vIEVzdGFkbyBwYXJhIGNvbnRyb2xhciBpdGVucyBwb3IgcMOhZ2luYVxyXG4gIGNvbnN0IFtpdGVtc1BlclBhZ2UsIHNldEl0ZW1zUGVyUGFnZV0gPSB1c2VTdGF0ZSgxMCk7XHJcblxyXG4gIC8vIEZ1bsOnw7VlcyBwYXJhIHNlbGXDp8OjbyBtw7psdGlwbGFcclxuICBjb25zdCBoYW5kbGVTZWxlY3RBbGwgPSAoY2hlY2tlZCkgPT4ge1xyXG4gICAgaWYgKGNoZWNrZWQpIHtcclxuICAgICAgc2V0U2VsZWN0ZWRJZHMocHJvZmVzc2lvbnMubWFwKHAgPT4gcC5pZCkpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0U2VsZWN0ZWRJZHMoW10pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlbGVjdE9uZSA9IChpZCwgY2hlY2tlZCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRJZHMocHJldiA9PiBjaGVja2VkID8gWy4uLnByZXYsIGlkXSA6IHByZXYuZmlsdGVyKGkgPT4gaSAhPT0gaWQpKTtcclxuICB9O1xyXG5cclxuICAvLyBGdW7Dp8O1ZXMgcGFyYSBzZWxlw6fDo28gbcO6bHRpcGxhIGRlIGdydXBvc1xyXG4gIGNvbnN0IGhhbmRsZVNlbGVjdEFsbEdyb3VwcyA9IChjaGVja2VkKSA9PiB7XHJcbiAgICBpZiAoY2hlY2tlZCkge1xyXG4gICAgICBzZXRTZWxlY3RlZEdyb3VwSWRzKGZpbHRlcmVkR3JvdXBzLm1hcChnID0+IGcuaWQpKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldFNlbGVjdGVkR3JvdXBJZHMoW10pO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVNlbGVjdE9uZUdyb3VwID0gKGlkLCBjaGVja2VkKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZEdyb3VwSWRzKHByZXYgPT4gY2hlY2tlZCA/IFsuLi5wcmV2LCBpZF0gOiBwcmV2LmZpbHRlcihpID0+IGkgIT09IGlkKSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gVmVyaWZpY2FyIHNlIG8gdXN1w6FyaW8gw6kgYWRtaW5pc3RyYWRvciBkbyBzaXN0ZW1hXHJcbiAgY29uc3QgaXNTeXN0ZW1BZG1pbiA9IGN1cnJlbnRVc2VyPy5yb2xlID09PSBcIlNZU1RFTV9BRE1JTlwiO1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG9ww6fDtWVzIGRlIHByb2Zpc3PDtWVzIHBhcmEgbyBtdWx0aS1zZWxlY3RcclxuICBjb25zdCBsb2FkUHJvZmVzc2lvbk9wdGlvbnMgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XHJcbiAgICBzZXRJc0xvYWRpbmdQcm9mZXNzaW9uT3B0aW9ucyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENhcnJlZ2FyIHRvZGFzIGFzIHByb2Zpc3PDtWVzIHBhcmEgbyBtdWx0aS1zZWxlY3RcclxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHByb2Zlc3Npb25zU2VydmljZS5nZXRQcm9mZXNzaW9ucyh7XHJcbiAgICAgICAgYWN0aXZlOiB0cnVlIC8vIEFwZW5hcyBwcm9maXNzw7VlcyBhdGl2YXMgcG9yIHBhZHLDo29cclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBUcmFuc2Zvcm1hciBvcyBkYWRvcyBwYXJhIG8gZm9ybWF0byBlc3BlcmFkbyBwZWxvIE11bHRpU2VsZWN0XHJcbiAgICAgIGNvbnN0IG9wdGlvbnMgPSBkYXRhLm1hcChwcm9mZXNzaW9uID0+ICh7XHJcbiAgICAgICAgdmFsdWU6IHByb2Zlc3Npb24uaWQsXHJcbiAgICAgICAgbGFiZWw6IHByb2Zlc3Npb24ubmFtZVxyXG4gICAgICB9KSk7XHJcblxyXG4gICAgICBzZXRQcm9mZXNzaW9uT3B0aW9ucyhvcHRpb25zKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIGNhcnJlZ2FyIG9ww6fDtWVzIGRlIHByb2Zpc3PDtWVzOlwiLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmdQcm9mZXNzaW9uT3B0aW9ucyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIG9ww6fDtWVzIGRlIGdydXBvcyBwYXJhIG8gbXVsdGktc2VsZWN0XHJcbiAgY29uc3QgbG9hZEdyb3VwT3B0aW9ucyA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcclxuICAgIHNldElzTG9hZGluZ0dyb3VwT3B0aW9ucyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIENhcnJlZ2FyIHRvZG9zIG9zIGdydXBvcyBwYXJhIG8gbXVsdGktc2VsZWN0XHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBwcm9mZXNzaW9uc1NlcnZpY2UuZ2V0UHJvZmVzc2lvbkdyb3Vwcyh7XHJcbiAgICAgICAgYWN0aXZlOiB0cnVlIC8vIEFwZW5hcyBncnVwb3MgYXRpdm9zIHBvciBwYWRyw6NvXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gVHJhbnNmb3JtYXIgb3MgZGFkb3MgcGFyYSBvIGZvcm1hdG8gZXNwZXJhZG8gcGVsbyBNdWx0aVNlbGVjdFxyXG4gICAgICBjb25zdCBvcHRpb25zID0gZGF0YS5tYXAoZ3JvdXAgPT4gKHtcclxuICAgICAgICB2YWx1ZTogZ3JvdXAuaWQsXHJcbiAgICAgICAgbGFiZWw6IGdyb3VwLm5hbWVcclxuICAgICAgfSkpO1xyXG5cclxuICAgICAgc2V0R3JvdXBPcHRpb25zKG9wdGlvbnMpO1xyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm8gYW8gY2FycmVnYXIgb3DDp8O1ZXMgZGUgZ3J1cG9zOlwiLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmdHcm91cE9wdGlvbnMoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFtdKTtcclxuXHJcbiAgLy8gQ2FycmVnYXIgcHJvZmlzc8O1ZXNcclxuICBjb25zdCBsb2FkUHJvZmVzc2lvbnMgPSBhc3luYyAoXHJcbiAgICBwYWdlID0gY3VycmVudFByb2Zlc3Npb25zUGFnZSxcclxuICAgIHNlYXJjaFF1ZXJ5ID0gc2VhcmNoLFxyXG4gICAgZ3JvdXBJZCA9IGdyb3VwRmlsdGVyLFxyXG4gICAgc3RhdHVzID0gc3RhdHVzRmlsdGVyLFxyXG4gICAgY29tcGFueSA9IGNvbXBhbnlGaWx0ZXIsXHJcbiAgICBwcm9mZXNzaW9uSWRzID0gcHJvZmVzc2lvbnNGaWx0ZXIsXHJcbiAgICBzb3J0RmllbGQgPSBcIm5hbWVcIixcclxuICAgIHNvcnREaXJlY3Rpb24gPSBcImFzY1wiLFxyXG4gICAgcGVyUGFnZSA9IGl0ZW1zUGVyUGFnZVxyXG4gICkgPT4ge1xyXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gR2FyYW50aXIgcXVlIGEgcMOhZ2luYSDDqSB1bSBuw7ptZXJvXHJcbiAgICAgIGNvbnN0IHBhZ2VOdW1iZXIgPSBwYXJzZUludChwYWdlLCAxMCk7XHJcblxyXG4gICAgICAvLyBBdHVhbGl6YXIgbyBlc3RhZG8gZGEgcMOhZ2luYSBhdHVhbFxyXG4gICAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKHBhZ2VOdW1iZXIpO1xyXG5cclxuICAgICAgLy8gQnVzY2FyIHRvZGFzIGFzIHByb2Zpc3PDtWVzXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBwcm9mZXNzaW9uc1NlcnZpY2UuZ2V0UHJvZmVzc2lvbnMoe1xyXG4gICAgICAgIHNlYXJjaDogc2VhcmNoUXVlcnkgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgIGdyb3VwSWQ6IGdyb3VwSWQgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgIGFjdGl2ZTogc3RhdHVzID09PSBcIlwiID8gdW5kZWZpbmVkIDogc3RhdHVzID09PSBcImFjdGl2ZVwiLFxyXG4gICAgICAgIGNvbXBhbnlJZDogY29tcGFueSB8fCB1bmRlZmluZWQsXHJcbiAgICAgICAgcHJvZmVzc2lvbklkczogcHJvZmVzc2lvbklkcy5sZW5ndGggPiAwID8gcHJvZmVzc2lvbklkcyA6IHVuZGVmaW5lZCxcclxuICAgICAgICBzb3J0RmllbGQ6IHNvcnRGaWVsZCxcclxuICAgICAgICBzb3J0RGlyZWN0aW9uOiBzb3J0RGlyZWN0aW9uXHJcbiAgICAgIH0pO1xyXG5cclxuICAgICAgLy8gQXJtYXplbmFyIHRvZGFzIGFzIHByb2Zpc3PDtWVzIHBhcmEgcGFnaW5hw6fDo28gbWFudWFsXHJcbiAgICAgIHNldEFsbFByb2Zlc3Npb25zKGRhdGEpO1xyXG5cclxuICAgICAgLy8gQ2FsY3VsYXIgbyB0b3RhbCBkZSBpdGVucyBlIHDDoWdpbmFzXHJcbiAgICAgIGNvbnN0IHRvdGFsID0gZGF0YS5sZW5ndGg7XHJcbiAgICAgIGNvbnN0IHBhZ2VzID0gTWF0aC5jZWlsKHRvdGFsIC8gcGVyUGFnZSkgfHwgMTtcclxuXHJcbiAgICAgIC8vIEFwbGljYXIgcGFnaW5hw6fDo28gbWFudWFsXHJcbiAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSAocGFnZU51bWJlciAtIDEpICogcGVyUGFnZTtcclxuICAgICAgY29uc3QgZW5kSW5kZXggPSBzdGFydEluZGV4ICsgcGVyUGFnZTtcclxuICAgICAgY29uc3QgcGFnaW5hdGVkUHJvZmVzc2lvbnMgPSBkYXRhLnNsaWNlKHN0YXJ0SW5kZXgsIGVuZEluZGV4KTtcclxuXHJcbiAgICAgIC8vIEF0dWFsaXphciBvIGVzdGFkbyBjb20gb3MgZGFkb3MgcGFnaW5hZG9zIG1hbnVhbG1lbnRlXHJcbiAgICAgIHNldFByb2Zlc3Npb25zKHBhZ2luYXRlZFByb2Zlc3Npb25zKTtcclxuICAgICAgc2V0VG90YWxQcm9mZXNzaW9ucyh0b3RhbCk7XHJcbiAgICAgIHNldFRvdGFsUHJvZmVzc2lvbnNQYWdlcyhwYWdlcyk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJybyBhbyBjYXJyZWdhciBwcm9maXNzw7VlczpcIiwgZXJyb3IpO1xyXG4gICAgICB0b2FzdF9lcnJvcihcIkVycm8gYW8gY2FycmVnYXIgcHJvZmlzc8O1ZXNcIik7XHJcbiAgICAgIHNldFByb2Zlc3Npb25zKFtdKTtcclxuICAgICAgc2V0VG90YWxQcm9mZXNzaW9ucygwKTtcclxuICAgICAgc2V0VG90YWxQcm9mZXNzaW9uc1BhZ2VzKDEpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGNhcnJlZ2FyIGdydXBvcyBjb20gb3JkZW5hw6fDo29cclxuICBjb25zdCBsb2FkR3JvdXBzV2l0aFNvcnQgPSBhc3luYyAoc29ydEZpZWxkID0gJ25hbWUnLCBzb3J0RGlyZWN0aW9uID0gJ2FzYycsIHBhZ2UgPSBjdXJyZW50R3JvdXBzUGFnZSwgcGVyUGFnZSA9IGl0ZW1zUGVyUGFnZSkgPT4ge1xyXG4gICAgc2V0SXNMb2FkaW5nR3JvdXBzKHRydWUpO1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcGFnZU51bWJlciA9IHBhcnNlSW50KHBhZ2UsIDEwKTtcclxuICAgICAgc2V0Q3VycmVudEdyb3Vwc1BhZ2UocGFnZU51bWJlcik7XHJcblxyXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcHJvZmVzc2lvbnNTZXJ2aWNlLmdldFByb2Zlc3Npb25Hcm91cHMoe1xyXG4gICAgICAgIHNlYXJjaDogc2VhcmNoIHx8IHVuZGVmaW5lZCxcclxuICAgICAgICBhY3RpdmU6IHN0YXR1c0ZpbHRlciA9PT0gXCJcIiA/IHVuZGVmaW5lZCA6IHN0YXR1c0ZpbHRlciA9PT0gXCJhY3RpdmVcIixcclxuICAgICAgICBjb21wYW55SWQ6IGNvbXBhbnlGaWx0ZXIgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgIGdyb3VwSWRzOiBncm91cHNGaWx0ZXIubGVuZ3RoID4gMCA/IGdyb3Vwc0ZpbHRlciA6IHVuZGVmaW5lZCxcclxuICAgICAgICBzb3J0RmllbGQsXHJcbiAgICAgICAgc29ydERpcmVjdGlvblxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIHNldEFsbEdyb3VwcyhkYXRhKTtcclxuICAgICAgY29uc3QgdG90YWwgPSBkYXRhLmxlbmd0aDtcclxuICAgICAgY29uc3QgcGFnZXMgPSBNYXRoLmNlaWwodG90YWwgLyBwZXJQYWdlKSB8fCAxO1xyXG4gICAgICBjb25zdCBzdGFydEluZGV4ID0gKHBhZ2VOdW1iZXIgLSAxKSAqIHBlclBhZ2U7XHJcbiAgICAgIGNvbnN0IGVuZEluZGV4ID0gc3RhcnRJbmRleCArIHBlclBhZ2U7XHJcbiAgICAgIGNvbnN0IHBhZ2luYXRlZEdyb3VwcyA9IGRhdGEuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpO1xyXG5cclxuICAgICAgc2V0RmlsdGVyZWRHcm91cHMocGFnaW5hdGVkR3JvdXBzKTtcclxuICAgICAgc2V0VG90YWxHcm91cHModG90YWwpO1xyXG4gICAgICBzZXRUb3RhbEdyb3Vwc1BhZ2VzKHBhZ2VzKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIGNhcnJlZ2FyIGdydXBvcyBjb20gb3JkZW5hw6fDo286XCIsIGVycm9yKTtcclxuICAgICAgdG9hc3RfZXJyb3IoXCJFcnJvIGFvIGNhcnJlZ2FyIGdydXBvc1wiKTtcclxuICAgICAgc2V0RmlsdGVyZWRHcm91cHMoW10pO1xyXG4gICAgICBzZXRUb3RhbEdyb3VwcygwKTtcclxuICAgICAgc2V0VG90YWxHcm91cHNQYWdlcygxKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZ0dyb3VwcyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gQ2FycmVnYXIgZ3J1cG9zIGRlIHByb2Zpc3PDtWVzXHJcbiAgY29uc3QgbG9hZEdyb3VwcyA9IGFzeW5jIChwYWdlID0gY3VycmVudEdyb3Vwc1BhZ2UsIHBlclBhZ2UgPSBpdGVtc1BlclBhZ2UpID0+IHtcclxuICAgIHNldElzTG9hZGluZ0dyb3Vwcyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEdhcmFudGlyIHF1ZSBhIHDDoWdpbmEgw6kgdW0gbsO6bWVyb1xyXG4gICAgICBjb25zdCBwYWdlTnVtYmVyID0gcGFyc2VJbnQocGFnZSwgMTApO1xyXG5cclxuICAgICAgLy8gQXR1YWxpemFyIG8gZXN0YWRvIGRhIHDDoWdpbmEgYXR1YWxcclxuICAgICAgc2V0Q3VycmVudEdyb3Vwc1BhZ2UocGFnZU51bWJlcik7XHJcblxyXG4gICAgICAvLyBCdXNjYXIgdG9kb3Mgb3MgZ3J1cG9zXHJcbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBwcm9mZXNzaW9uc1NlcnZpY2UuZ2V0UHJvZmVzc2lvbkdyb3Vwcyh7XHJcbiAgICAgICAgYWN0aXZlOiBhY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIiA/IHRydWUgOiB1bmRlZmluZWQgLy8gTmEgdGFiIGRlIHByb2Zpc3PDtWVzLCBzw7MgY2FycmVnYSBvcyBhdGl2b3NcclxuICAgICAgfSk7XHJcblxyXG4gICAgICAvLyBBcm1hemVuYXIgdG9kb3Mgb3MgZ3J1cG9zIHBhcmEgcGFnaW5hw6fDo28gbWFudWFsXHJcbiAgICAgIHNldEFsbEdyb3VwcyhkYXRhKTtcclxuXHJcbiAgICAgIGlmIChhY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIikge1xyXG4gICAgICAgIC8vIE5hIHRhYiBkZSBwcm9maXNzw7VlcywgbsOjbyBhcGxpY2Ftb3MgcGFnaW5hw6fDo28gYW9zIGdydXBvcyAoc8OjbyB1c2Fkb3MgYXBlbmFzIG5vIGZpbHRybylcclxuICAgICAgICBzZXRHcm91cHMoZGF0YSk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gTmEgdGFiIGRlIGdydXBvcywgYXBsaWNhbW9zIHBhZ2luYcOnw6NvXHJcbiAgICAgICAgLy8gQ2FsY3VsYXIgbyB0b3RhbCBkZSBpdGVucyBlIHDDoWdpbmFzXHJcbiAgICAgICAgY29uc3QgdG90YWwgPSBkYXRhLmxlbmd0aDtcclxuICAgICAgICBjb25zdCBwYWdlcyA9IE1hdGguY2VpbCh0b3RhbCAvIHBlclBhZ2UpIHx8IDE7XHJcblxyXG4gICAgICAgIC8vIEFwbGljYXIgcGFnaW5hw6fDo28gbWFudWFsXHJcbiAgICAgICAgY29uc3Qgc3RhcnRJbmRleCA9IChwYWdlTnVtYmVyIC0gMSkgKiBwZXJQYWdlO1xyXG4gICAgICAgIGNvbnN0IGVuZEluZGV4ID0gc3RhcnRJbmRleCArIHBlclBhZ2U7XHJcbiAgICAgICAgY29uc3QgcGFnaW5hdGVkR3JvdXBzID0gZGF0YS5zbGljZShzdGFydEluZGV4LCBlbmRJbmRleCk7XHJcblxyXG4gICAgICAgIC8vIEF0dWFsaXphciBvIGVzdGFkbyBjb20gb3MgZGFkb3MgcGFnaW5hZG9zIG1hbnVhbG1lbnRlXHJcbiAgICAgICAgc2V0R3JvdXBzKGRhdGEpOyAvLyBNYW50ZW1vcyB0b2RvcyBvcyBncnVwb3MgcGFyYSBvIGZpbHRyb1xyXG4gICAgICAgIHNldEZpbHRlcmVkR3JvdXBzKHBhZ2luYXRlZEdyb3Vwcyk7IC8vIEFwZW5hcyBvcyAxMCBpdGVucyBkYSBww6FnaW5hIGF0dWFsXHJcbiAgICAgICAgc2V0VG90YWxHcm91cHModG90YWwpO1xyXG4gICAgICAgIHNldFRvdGFsR3JvdXBzUGFnZXMocGFnZXMpO1xyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJybyBhbyBjYXJyZWdhciBncnVwb3MgZGUgcHJvZmlzc8O1ZXM6XCIsIGVycm9yKTtcclxuICAgICAgdG9hc3RfZXJyb3IoXCJFcnJvIGFvIGNhcnJlZ2FyIGdydXBvcyBkZSBwcm9maXNzw7Vlc1wiKTtcclxuICAgICAgc2V0R3JvdXBzKFtdKTtcclxuICAgICAgc2V0RmlsdGVyZWRHcm91cHMoW10pO1xyXG4gICAgICBzZXRUb3RhbEdyb3VwcygwKTtcclxuICAgICAgc2V0VG90YWxHcm91cHNQYWdlcygxKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldElzTG9hZGluZ0dyb3VwcyhmYWxzZSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgLy8gRmlsdHJhciBncnVwb3MgcXVhbmRvIG8gdXN1w6FyaW8gc3VibWV0ZXIgbyBmb3JtdWzDoXJpb1xyXG4gIGNvbnN0IGZpbHRlckdyb3VwcyA9IChcclxuICAgIHNlYXJjaFRlcm0sXHJcbiAgICBwYWdlID0gY3VycmVudEdyb3Vwc1BhZ2UsXHJcbiAgICBncm91cElkcyA9IGdyb3Vwc0ZpbHRlcixcclxuICAgIHNvcnRGaWVsZCA9ICduYW1lJyxcclxuICAgIHNvcnREaXJlY3Rpb24gPSAnYXNjJyxcclxuICAgIHBlclBhZ2UgPSBpdGVtc1BlclBhZ2VcclxuICApID0+IHtcclxuICAgIGNvbnN0IHBhZ2VOdW1iZXIgPSBwYXJzZUludChwYWdlLCAxMCk7XHJcbiAgICBzZXRDdXJyZW50R3JvdXBzUGFnZShwYWdlTnVtYmVyKTtcclxuXHJcblxyXG5cclxuICAgIGNvbnN0IGxvYWRGaWx0ZXJlZEdyb3VwcyA9IGFzeW5jICgpID0+IHtcclxuICAgICAgc2V0SXNMb2FkaW5nR3JvdXBzKHRydWUpO1xyXG4gICAgICB0cnkge1xyXG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCBwcm9mZXNzaW9uc1NlcnZpY2UuZ2V0UHJvZmVzc2lvbkdyb3Vwcyh7XHJcbiAgICAgICAgICBzZWFyY2g6IHNlYXJjaFRlcm0gfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgICAgYWN0aXZlOiBzdGF0dXNGaWx0ZXIgPT09IFwiXCIgPyB1bmRlZmluZWQgOiBzdGF0dXNGaWx0ZXIgPT09IFwiYWN0aXZlXCIsXHJcbiAgICAgICAgICBjb21wYW55SWQ6IGNvbXBhbnlGaWx0ZXIgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgICAgZ3JvdXBJZHM6IGdyb3VwSWRzLmxlbmd0aCA+IDAgPyBncm91cElkcyA6IHVuZGVmaW5lZCxcclxuICAgICAgICAgIHNvcnRGaWVsZCxcclxuICAgICAgICAgIHNvcnREaXJlY3Rpb25cclxuICAgICAgICB9KTtcclxuXHJcbiAgICAgICAgc2V0QWxsR3JvdXBzKGRhdGEpO1xyXG4gICAgICAgIGNvbnN0IHRvdGFsID0gZGF0YS5sZW5ndGg7XHJcbiAgICAgICAgY29uc3QgcGFnZXMgPSBNYXRoLmNlaWwodG90YWwgLyBwZXJQYWdlKSB8fCAxO1xyXG4gICAgICAgIGNvbnN0IHN0YXJ0SW5kZXggPSAocGFnZU51bWJlciAtIDEpICogcGVyUGFnZTtcclxuICAgICAgICBjb25zdCBlbmRJbmRleCA9IHN0YXJ0SW5kZXggKyBwZXJQYWdlO1xyXG4gICAgICAgIGNvbnN0IHBhZ2luYXRlZEdyb3VwcyA9IGRhdGEuc2xpY2Uoc3RhcnRJbmRleCwgZW5kSW5kZXgpO1xyXG5cclxuICAgICAgICBzZXRGaWx0ZXJlZEdyb3VwcyhwYWdpbmF0ZWRHcm91cHMpO1xyXG4gICAgICAgIHNldFRvdGFsR3JvdXBzKHRvdGFsKTtcclxuICAgICAgICBzZXRUb3RhbEdyb3Vwc1BhZ2VzKHBhZ2VzKTtcclxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJybyBhbyBmaWx0cmFyIGdydXBvczpcIiwgZXJyb3IpO1xyXG4gICAgICAgIHRvYXN0X2Vycm9yKFwiRXJybyBhbyBmaWx0cmFyIGdydXBvc1wiKTtcclxuICAgICAgICBzZXRGaWx0ZXJlZEdyb3VwcyhbXSk7XHJcbiAgICAgICAgc2V0VG90YWxHcm91cHMoMCk7XHJcbiAgICAgICAgc2V0VG90YWxHcm91cHNQYWdlcygxKTtcclxuICAgICAgfSBmaW5hbGx5IHtcclxuICAgICAgICBzZXRJc0xvYWRpbmdHcm91cHMoZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGxvYWRGaWx0ZXJlZEdyb3VwcygpO1xyXG4gIH07XHJcblxyXG4gIC8vIENhcnJlZ2FyIGVtcHJlc2FzIChhcGVuYXMgcGFyYSBzeXN0ZW0gYWRtaW4pXHJcbiAgY29uc3QgbG9hZENvbXBhbmllcyA9IGFzeW5jICgpID0+IHtcclxuICAgIGlmICghaXNTeXN0ZW1BZG1pbikgcmV0dXJuO1xyXG5cclxuICAgIHNldElzTG9hZGluZ0NvbXBhbmllcyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IGNvbXBhbmllcyA9IGF3YWl0IGNvbXBhbnlTZXJ2aWNlLmdldENvbXBhbmllc0ZvclNlbGVjdCgpO1xyXG4gICAgICBzZXRDb21wYW5pZXMoY29tcGFuaWVzIHx8IFtdKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIGNhcnJlZ2FyIGVtcHJlc2FzOlwiLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRJc0xvYWRpbmdDb21wYW5pZXMoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBsb2FkUHJvZmVzc2lvbnMoKTtcclxuICAgIGxvYWRHcm91cHMoKTtcclxuICAgIGlmIChpc1N5c3RlbUFkbWluKSB7XHJcbiAgICAgIGxvYWRDb21wYW5pZXMoKTtcclxuICAgIH1cclxuICAgIC8vIENhcnJlZ2FyIG9ww6fDtWVzIHBhcmEgb3MgbXVsdGktc2VsZWN0c1xyXG4gICAgbG9hZFByb2Zlc3Npb25PcHRpb25zKCk7XHJcbiAgICBsb2FkR3JvdXBPcHRpb25zKCk7XHJcbiAgfSwgW2lzU3lzdGVtQWRtaW4sIGxvYWRQcm9mZXNzaW9uT3B0aW9ucywgbG9hZEdyb3VwT3B0aW9uc10pO1xyXG5cclxuICAvLyBSZWNhcnJlZ2FyIGRhZG9zIHF1YW5kbyBhIHRhYiBtdWRhclxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBzZXRTZWFyY2goXCJcIik7IC8vIExpbXBhciBhIGJ1c2NhIGFvIHRyb2NhciBkZSB0YWJcclxuICAgIHNldEdyb3VwRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0U3RhdHVzRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0Q29tcGFueUZpbHRlcihcIlwiKTtcclxuICAgIHNldFByb2Zlc3Npb25zRmlsdGVyKFtdKTtcclxuICAgIHNldEdyb3Vwc0ZpbHRlcihbXSk7XHJcblxyXG4gICAgLy8gUmVzZXRhciBvcyBmaWx0cm9zIGRvIG5vdm8gc2lzdGVtYVxyXG4gICAgaWYgKGFjdGl2ZVRhYiA9PT0gXCJwcm9mZXNzaW9uc1wiKSB7XHJcbiAgICAgIHNldE9jY3VwYXRpb25GaWx0ZXJzKHtcclxuICAgICAgICBzZWFyY2g6IFwiXCIsXHJcbiAgICAgICAgY29tcGFuaWVzOiBbXSxcclxuICAgICAgICBwcm9mZXNzaW9uR3JvdXBzOiBbXSxcclxuICAgICAgICBwcm9mZXNzaW9uczogW10sXHJcbiAgICAgICAgc3RhdHVzOiBcIlwiXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIFJlc2V0YXIgcGFyYSBhIHByaW1laXJhIHDDoWdpbmFcclxuICAgIGlmIChhY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIikge1xyXG4gICAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG4gICAgICBsb2FkUHJvZmVzc2lvbnMoMSk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBzZXRDdXJyZW50R3JvdXBzUGFnZSgxKTtcclxuICAgICAgbG9hZEdyb3VwcygxKTsgLy8gUmVjYXJyZWdhciBncnVwb3MgY29tIGZpbHRybyBkaWZlcmVudGUgZGVwZW5kZW5kbyBkYSB0YWJcclxuICAgIH1cclxuICB9LCBbYWN0aXZlVGFiXSk7XHJcblxyXG4gIC8vIEZ1bsOnw6NvIGRlIGJ1c2NhIHJlbW92aWRhLCBhZ29yYSB1c2Ftb3MgZXN0YWRvcyBsb2NhaXMgbm9zIGNvbXBvbmVudGVzIGRlIGZpbHRyb1xyXG5cclxuICBjb25zdCBoYW5kbGVHcm91cEZpbHRlckNoYW5nZSA9ICh2YWx1ZSkgPT4ge1xyXG4gICAgc2V0R3JvdXBGaWx0ZXIodmFsdWUpO1xyXG4gICAgLy8gQWNpb25hciBhIGJ1c2NhIGF1dG9tYXRpY2FtZW50ZSBxdWFuZG8gbyB1c3XDoXJpbyBzZWxlY2lvbmFyIHVtIGdydXBvXHJcbiAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG4gICAgbG9hZFByb2Zlc3Npb25zKDEsIHNlYXJjaCwgdmFsdWUsIHN0YXR1c0ZpbHRlciwgY29tcGFueUZpbHRlciwgcHJvZmVzc2lvbnNGaWx0ZXIpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN0YXR1c0ZpbHRlckNoYW5nZSA9ICh2YWx1ZSkgPT4ge1xyXG4gICAgc2V0U3RhdHVzRmlsdGVyKHZhbHVlKTtcclxuICAgIC8vIEFjaW9uYXIgYSBidXNjYSBhdXRvbWF0aWNhbWVudGUgcXVhbmRvIG8gdXN1w6FyaW8gc2VsZWNpb25hciB1bSBzdGF0dXNcclxuICAgIGlmIChhY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIikge1xyXG4gICAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG4gICAgICBsb2FkUHJvZmVzc2lvbnMoMSwgc2VhcmNoLCBncm91cEZpbHRlciwgdmFsdWUsIGNvbXBhbnlGaWx0ZXIsIHByb2Zlc3Npb25zRmlsdGVyKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEN1cnJlbnRHcm91cHNQYWdlKDEpO1xyXG4gICAgICBmaWx0ZXJHcm91cHMoc2VhcmNoLCAxLCBncm91cHNGaWx0ZXIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNvbXBhbnlGaWx0ZXJDaGFuZ2UgPSAodmFsdWUpID0+IHtcclxuICAgIHNldENvbXBhbnlGaWx0ZXIodmFsdWUpO1xyXG4gICAgLy8gQWNpb25hciBhIGJ1c2NhIGF1dG9tYXRpY2FtZW50ZSBxdWFuZG8gbyB1c3XDoXJpbyBzZWxlY2lvbmFyIHVtYSBlbXByZXNhXHJcbiAgICBpZiAoYWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIpIHtcclxuICAgICAgc2V0Q3VycmVudFByb2Zlc3Npb25zUGFnZSgxKTtcclxuICAgICAgbG9hZFByb2Zlc3Npb25zKDEsIHNlYXJjaCwgZ3JvdXBGaWx0ZXIsIHN0YXR1c0ZpbHRlciwgdmFsdWUsIHByb2Zlc3Npb25zRmlsdGVyKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldEN1cnJlbnRHcm91cHNQYWdlKDEpO1xyXG4gICAgICBmaWx0ZXJHcm91cHMoc2VhcmNoLCAxLCBncm91cHNGaWx0ZXIpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVByb2Zlc3Npb25zRmlsdGVyQ2hhbmdlID0gKHZhbHVlKSA9PiB7XHJcbiAgICBzZXRQcm9mZXNzaW9uc0ZpbHRlcih2YWx1ZSk7XHJcbiAgICAvLyBBY2lvbmFyIGEgYnVzY2EgYXV0b21hdGljYW1lbnRlIHF1YW5kbyBvIHVzdcOhcmlvIHNlbGVjaW9uYXIgb3UgcmVtb3ZlciB1bWEgcHJvZmlzc8Ojb1xyXG4gICAgc2V0Q3VycmVudFByb2Zlc3Npb25zUGFnZSgxKTtcclxuICAgIGxvYWRQcm9mZXNzaW9ucygxLCBzZWFyY2gsIGdyb3VwRmlsdGVyLCBzdGF0dXNGaWx0ZXIsIGNvbXBhbnlGaWx0ZXIsIHZhbHVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVHcm91cHNGaWx0ZXJDaGFuZ2UgPSAodmFsdWUpID0+IHtcclxuICAgIHNldEdyb3Vwc0ZpbHRlcih2YWx1ZSk7XHJcbiAgICAvLyBBY2lvbmFyIGEgYnVzY2EgYXV0b21hdGljYW1lbnRlIHF1YW5kbyBvIHVzdcOhcmlvIHNlbGVjaW9uYXIgb3UgcmVtb3ZlciB1bSBncnVwb1xyXG4gICAgc2V0Q3VycmVudEdyb3Vwc1BhZ2UoMSk7XHJcbiAgICBmaWx0ZXJHcm91cHMoc2VhcmNoLCAxLCB2YWx1ZSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gRnVuw6fDtWVzIHBhcmEgbyBub3ZvIHNpc3RlbWEgZGUgZmlsdHJvc1xyXG4gIGNvbnN0IGhhbmRsZU9jY3VwYXRpb25GaWx0ZXJzQ2hhbmdlID0gKG5ld0ZpbHRlcnMpID0+IHtcclxuICAgIHNldE9jY3VwYXRpb25GaWx0ZXJzKG5ld0ZpbHRlcnMpO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZU9jY3VwYXRpb25TZWFyY2ggPSAoZmlsdGVycykgPT4ge1xyXG4gICAgc2V0Q3VycmVudFByb2Zlc3Npb25zUGFnZSgxKTtcclxuICAgIC8vIENvbnZlcnRlciBvcyBmaWx0cm9zIHBhcmEgbyBmb3JtYXRvIGVzcGVyYWRvIHBlbGEgQVBJXHJcbiAgICBjb25zdCBzZWFyY2hRdWVyeSA9IGZpbHRlcnMuc2VhcmNoIHx8IFwiXCI7XHJcbiAgICBjb25zdCBncm91cElkcyA9IGZpbHRlcnMucHJvZmVzc2lvbkdyb3VwcyB8fCBbXTtcclxuICAgIGNvbnN0IHByb2Zlc3Npb25JZHMgPSBmaWx0ZXJzLnByb2Zlc3Npb25zIHx8IFtdO1xyXG4gICAgY29uc3QgY29tcGFueUlkcyA9IGZpbHRlcnMuY29tcGFuaWVzIHx8IFtdO1xyXG4gICAgY29uc3Qgc3RhdHVzID0gZmlsdGVycy5zdGF0dXMgfHwgXCJcIjtcclxuXHJcbiAgICAvLyBNYXBlYXIgb3MgZmlsdHJvcyBwYXJhIG9zIGZpbHRyb3MgZXhpc3RlbnRlc1xyXG4gICAgc2V0U2VhcmNoKHNlYXJjaFF1ZXJ5KTtcclxuICAgIHNldEdyb3Vwc0ZpbHRlcihncm91cElkcy5sZW5ndGggPiAwID8gZ3JvdXBJZHNbMF0gOiBcIlwiKTtcclxuICAgIHNldFByb2Zlc3Npb25zRmlsdGVyKHByb2Zlc3Npb25JZHMpO1xyXG4gICAgc2V0Q29tcGFueUZpbHRlcihjb21wYW55SWRzLmxlbmd0aCA+IDAgPyBjb21wYW55SWRzWzBdIDogXCJcIik7XHJcbiAgICBzZXRTdGF0dXNGaWx0ZXIoc3RhdHVzKTtcclxuXHJcbiAgICAvLyBDYXJyZWdhciBwcm9maXNzw7VlcyBjb20gb3Mgbm92b3MgZmlsdHJvc1xyXG4gICAgbG9hZFByb2Zlc3Npb25zKDEsIHNlYXJjaFF1ZXJ5LCBncm91cElkcy5sZW5ndGggPiAwID8gZ3JvdXBJZHNbMF0gOiBcIlwiLCBzdGF0dXMsIGNvbXBhbnlJZHMubGVuZ3RoID4gMCA/IGNvbXBhbnlJZHNbMF0gOiBcIlwiLCBwcm9mZXNzaW9uSWRzKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVPY2N1cGF0aW9uQ2xlYXJGaWx0ZXJzID0gKCkgPT4ge1xyXG4gICAgY29uc3QgY2xlYXJlZEZpbHRlcnMgPSB7XHJcbiAgICAgIHNlYXJjaDogXCJcIixcclxuICAgICAgY29tcGFuaWVzOiBbXSxcclxuICAgICAgcHJvZmVzc2lvbkdyb3VwczogW10sXHJcbiAgICAgIHByb2Zlc3Npb25zOiBbXSxcclxuICAgICAgc3RhdHVzOiBcIlwiXHJcbiAgICB9O1xyXG5cclxuICAgIHNldE9jY3VwYXRpb25GaWx0ZXJzKGNsZWFyZWRGaWx0ZXJzKTtcclxuICAgIHNldFNlYXJjaChcIlwiKTtcclxuICAgIHNldEdyb3VwRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0U3RhdHVzRmlsdGVyKFwiXCIpO1xyXG4gICAgc2V0Q29tcGFueUZpbHRlcihcIlwiKTtcclxuICAgIHNldFByb2Zlc3Npb25zRmlsdGVyKFtdKTtcclxuICAgIHNldEdyb3Vwc0ZpbHRlcihbXSk7XHJcbiAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG4gICAgbG9hZFByb2Zlc3Npb25zKDEsIFwiXCIsIFwiXCIsIFwiXCIsIFwiXCIsIFtdKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSZXNldEZpbHRlcnMgPSAoKSA9PiB7XHJcbiAgICBzZXRTZWFyY2goXCJcIik7XHJcbiAgICBzZXRHcm91cEZpbHRlcihcIlwiKTtcclxuICAgIHNldFN0YXR1c0ZpbHRlcihcIlwiKTtcclxuICAgIHNldENvbXBhbnlGaWx0ZXIoXCJcIik7XHJcbiAgICBzZXRQcm9mZXNzaW9uc0ZpbHRlcihbXSk7XHJcbiAgICBzZXRHcm91cHNGaWx0ZXIoW10pO1xyXG5cclxuICAgIC8vIFJlc2V0YXIgcGFyYSBhIHByaW1laXJhIHDDoWdpbmFcclxuICAgIGlmIChhY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIikge1xyXG4gICAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG4gICAgICBsb2FkUHJvZmVzc2lvbnMoMSwgXCJcIiwgXCJcIiwgXCJcIiwgXCJcIiwgW10pO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0Q3VycmVudEdyb3Vwc1BhZ2UoMSk7XHJcbiAgICAgIGZpbHRlckdyb3VwcyhcIlwiLCAxLCBbXSk7IC8vIENoYW1hZGEgZGlyZXRhIHBhcmEgcmVzZXRhciBvcyBmaWx0cm9zXHJcbiAgICB9XHJcblxyXG4gICAgLy8gRm9yw6dhciBhIHJlLXJlbmRlcml6YcOnw6NvIGRvcyBjb21wb25lbnRlcyBkZSBmaWx0cm9cclxuICAgIHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICBjb25zdCBldmVudCA9IG5ldyBFdmVudCgncmVzZXQnKTtcclxuICAgICAgZG9jdW1lbnQuZGlzcGF0Y2hFdmVudChldmVudCk7XHJcbiAgICB9LCAwKTtcclxuICB9O1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGxpZGFyIGNvbSBhIG11ZGFuw6dhIGRlIHDDoWdpbmEgZGUgcHJvZmlzc8O1ZXNcclxuICBjb25zdCBoYW5kbGVQcm9mZXNzaW9uc1BhZ2VDaGFuZ2UgPSAocGFnZSkgPT4ge1xyXG4gICAgbG9hZFByb2Zlc3Npb25zKHBhZ2UsIHNlYXJjaCwgZ3JvdXBGaWx0ZXIsIHN0YXR1c0ZpbHRlciwgY29tcGFueUZpbHRlciwgcHJvZmVzc2lvbnNGaWx0ZXIpO1xyXG4gIH07XHJcblxyXG4gIC8vIEZ1bsOnw6NvIHBhcmEgbGlkYXIgY29tIGEgbXVkYW7Dp2EgZGUgcMOhZ2luYSBkZSBncnVwb3NcclxuICBjb25zdCBoYW5kbGVHcm91cHNQYWdlQ2hhbmdlID0gKHBhZ2UpID0+IHtcclxuICAgIGZpbHRlckdyb3VwcyhzZWFyY2gsIHBhZ2UsIGdyb3Vwc0ZpbHRlcik7IC8vIENoYW1hZGEgZGlyZXRhIHBhcmEgbXVkYW7Dp2EgZGUgcMOhZ2luYVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUVkaXRQcm9mZXNzaW9uID0gKHByb2Zlc3Npb24pID0+IHtcclxuICAgIHNldFNlbGVjdGVkUHJvZmVzc2lvbihwcm9mZXNzaW9uKTtcclxuICAgIHNldFByb2Zlc3Npb25Gb3JtT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVFZGl0R3JvdXAgPSAoZ3JvdXApID0+IHtcclxuICAgIHNldFNlbGVjdGVkR3JvdXAoZ3JvdXApOyAvLyBTZSBncm91cCBmb3IgbnVsbCwgc2Vyw6EgY3JpYcOnw6NvIGRlIG5vdm8gZ3J1cG9cclxuICAgIHNldEdyb3VwRm9ybU9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlUHJvZmVzc2lvbiA9IChwcm9mZXNzaW9uKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZFByb2Zlc3Npb24ocHJvZmVzc2lvbik7XHJcbiAgICBzZXRBY3Rpb25Ub0NvbmZpcm0oe1xyXG4gICAgICB0eXBlOiBcImRlbGV0ZS1wcm9mZXNzaW9uXCIsXHJcbiAgICAgIG1lc3NhZ2U6IGBUZW0gY2VydGV6YSBxdWUgZGVzZWphIGV4Y2x1aXIgYSBwcm9maXNzw6NvIFwiJHtwcm9mZXNzaW9uLm5hbWV9XCI/IEVzdGEgYcOnw6NvIG7Do28gcG9kZSBzZXIgZGVzZmVpdGEuYCxcclxuICAgIH0pO1xyXG4gICAgc2V0Q29uZmlybWF0aW9uRGlhbG9nT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVWaWV3VXNlcnMgPSAocHJvZmVzc2lvbikgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRQcm9mZXNzaW9uKHByb2Zlc3Npb24pO1xyXG4gICAgc2V0VXNlcnNNb2RhbE9wZW4odHJ1ZSk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlVmlld0dyb3VwUHJvZmVzc2lvbnMgPSAoZ3JvdXApID0+IHtcclxuICAgIHNldFNlbGVjdGVkR3JvdXAoZ3JvdXApO1xyXG4gICAgc2V0R3JvdXBQcm9mZXNzaW9uc01vZGFsT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVEZWxldGVHcm91cCA9IChncm91cCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRHcm91cChncm91cCk7XHJcbiAgICBzZXRBY3Rpb25Ub0NvbmZpcm0oe1xyXG4gICAgICB0eXBlOiBcImRlbGV0ZS1ncm91cFwiLFxyXG4gICAgICBtZXNzYWdlOiBgVGVtIGNlcnRlemEgcXVlIGRlc2VqYSBleGNsdWlyIG8gZ3J1cG8gXCIke2dyb3VwLm5hbWV9XCI/IEVzdGEgYcOnw6NvIG7Do28gcG9kZSBzZXIgZGVzZmVpdGEuYCxcclxuICAgIH0pO1xyXG4gICAgc2V0Q29uZmlybWF0aW9uRGlhbG9nT3Blbih0cnVlKTtcclxuICB9O1xyXG5cclxuICAvLyBGdW7Dp8OjbyBwYXJhIGV4cG9ydGFyIHByb2Zpc3PDtWVzXHJcbiAgY29uc3QgaGFuZGxlRXhwb3J0UHJvZmVzc2lvbnMgPSBhc3luYyAoZm9ybWF0KSA9PiB7XHJcbiAgICBzZXRJc0V4cG9ydGluZyh0cnVlKTtcclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEV4cG9ydGFyIHVzYW5kbyBvcyBtZXNtb3MgZmlsdHJvcyBkYSB0YWJlbGEgYXR1YWxcclxuICAgICAgYXdhaXQgcHJvZmVzc2lvbnNTZXJ2aWNlLmV4cG9ydFByb2Zlc3Npb25zKHtcclxuICAgICAgICBzZWFyY2g6IHNlYXJjaCB8fCB1bmRlZmluZWQsXHJcbiAgICAgICAgcHJvZmVzc2lvbklkczogcHJvZmVzc2lvbnNGaWx0ZXIubGVuZ3RoID4gMCA/IHByb2Zlc3Npb25zRmlsdGVyIDogdW5kZWZpbmVkLFxyXG4gICAgICAgIGdyb3VwSWQ6IGdyb3VwRmlsdGVyIHx8IHVuZGVmaW5lZCxcclxuICAgICAgICBhY3RpdmU6IHN0YXR1c0ZpbHRlciA9PT0gXCJcIiA/IHVuZGVmaW5lZCA6IHN0YXR1c0ZpbHRlciA9PT0gXCJhY3RpdmVcIixcclxuICAgICAgICBjb21wYW55SWQ6IGNvbXBhbnlGaWx0ZXIgfHwgdW5kZWZpbmVkXHJcbiAgICAgIH0sIGZvcm1hdCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJybyBhbyBleHBvcnRhciBwcm9maXNzw7VlczpcIiwgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNFeHBvcnRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEZ1bsOnw6NvIHBhcmEgZXhwb3J0YXIgZ3J1cG9zIGRlIHByb2Zpc3PDtWVzXHJcbiAgY29uc3QgaGFuZGxlRXhwb3J0R3JvdXBzID0gYXN5bmMgKGZvcm1hdCkgPT4ge1xyXG4gICAgc2V0SXNFeHBvcnRpbmcodHJ1ZSk7XHJcbiAgICB0cnkge1xyXG4gICAgICAvLyBFeHBvcnRhciB1c2FuZG8gb3MgbWVzbW9zIGZpbHRyb3MgZGEgdGFiZWxhIGF0dWFsXHJcbiAgICAgIGF3YWl0IHByb2Zlc3Npb25zU2VydmljZS5leHBvcnRQcm9mZXNzaW9uR3JvdXBzKHtcclxuICAgICAgICBzZWFyY2g6IHNlYXJjaCB8fCB1bmRlZmluZWQsXHJcbiAgICAgICAgZ3JvdXBJZHM6IGdyb3Vwc0ZpbHRlci5sZW5ndGggPiAwID8gZ3JvdXBzRmlsdGVyIDogdW5kZWZpbmVkLFxyXG4gICAgICAgIGFjdGl2ZTogc3RhdHVzRmlsdGVyID09PSBcIlwiID8gdW5kZWZpbmVkIDogc3RhdHVzRmlsdGVyID09PSBcImFjdGl2ZVwiLFxyXG4gICAgICAgIGNvbXBhbnlJZDogY29tcGFueUZpbHRlciB8fCB1bmRlZmluZWRcclxuICAgICAgfSwgZm9ybWF0KTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIGV4cG9ydGFyIGdydXBvcyBkZSBwcm9maXNzw7VlczpcIiwgZXJyb3IpO1xyXG4gICAgfSBmaW5hbGx5IHtcclxuICAgICAgc2V0SXNFeHBvcnRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGNvbmZpcm1BY3Rpb24gPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBpZiAoYWN0aW9uVG9Db25maXJtLnR5cGUgPT09IFwiZGVsZXRlLXByb2Zlc3Npb25cIikge1xyXG4gICAgICAgIGF3YWl0IHByb2Zlc3Npb25zU2VydmljZS5kZWxldGVQcm9mZXNzaW9uKHNlbGVjdGVkUHJvZmVzc2lvbi5pZCk7XHJcbiAgICAgICAgdG9hc3Rfc3VjY2VzcyhcIlByb2Zpc3PDo28gZXhjbHXDrWRhIGNvbSBzdWNlc3NvXCIpO1xyXG4gICAgICAgIGxvYWRQcm9mZXNzaW9ucygpO1xyXG4gICAgICB9IGVsc2UgaWYgKGFjdGlvblRvQ29uZmlybS50eXBlID09PSBcImRlbGV0ZS1ncm91cFwiKSB7XHJcbiAgICAgICAgYXdhaXQgcHJvZmVzc2lvbnNTZXJ2aWNlLmRlbGV0ZVByb2Zlc3Npb25Hcm91cChzZWxlY3RlZEdyb3VwLmlkKTtcclxuICAgICAgICB0b2FzdF9zdWNjZXNzKFwiR3J1cG8gZXhjbHXDrWRvIGNvbSBzdWNlc3NvXCIpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkR3JvdXAobnVsbCk7IC8vIExpbXBhciBvIGdydXBvIHNlbGVjaW9uYWRvIGFww7NzIGV4Y2x1c8Ojb1xyXG4gICAgICAgIGxvYWRHcm91cHMoKTtcclxuICAgICAgICBsb2FkUHJvZmVzc2lvbnMoKTsgLy8gUmVjYXJyZWdhciBwcm9maXNzw7VlcyBwYXJhIGF0dWFsaXphciBvcyBncnVwb3NcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcihcIkVycm8gYW8gZXhlY3V0YXIgYcOnw6NvOlwiLCBlcnJvcik7XHJcbiAgICAgIHRvYXN0X2Vycm9yKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5tZXNzYWdlIHx8IFwiRXJybyBhbyBleGVjdXRhciBhw6fDo29cIik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRDb25maXJtYXRpb25EaWFsb2dPcGVuKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBDb25maWd1cmHDp8OjbyBkYXMgdGFicyBwYXJhIG8gTW9kdWxlVGFic1xyXG4gIGNvbnN0IHRhYnNDb25maWcgPSBbXHJcbiAgICB7XHJcbiAgICAgIGlkOiBcInByb2Zlc3Npb25zXCIsXHJcbiAgICAgIGxhYmVsOiBcIlByb2Zpc3PDtWVzXCIsXHJcbiAgICAgIGljb246IDxCcmllZmNhc2Ugc2l6ZT17MTh9IC8+LFxyXG4gICAgICBwZXJtaXNzaW9uOiBcImFkbWluLnByb2Zlc3Npb25zLnZpZXdcIlxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgaWQ6IFwiZ3JvdXBzXCIsXHJcbiAgICAgIGxhYmVsOiBcIkdydXBvcyBkZSBQcm9maXNzw7Vlc1wiLFxyXG4gICAgICBpY29uOiA8TGF5ZXJzIHNpemU9ezE4fSAvPixcclxuICAgICAgcGVybWlzc2lvbjogXCJhZG1pbi5wcm9mZXNzaW9uLWdyb3Vwcy52aWV3XCJcclxuICAgIH1cclxuICBdO1xyXG5cclxuICAvLyBGaWx0cmFyIHRhYnMgY29tIGJhc2UgbmFzIHBlcm1pc3PDtWVzIGRvIHVzdcOhcmlvXHJcbiAgY29uc3QgZmlsdGVyZWRUYWJzID0gdGFic0NvbmZpZy5maWx0ZXIodGFiID0+IHtcclxuICAgIGlmICghdGFiLnBlcm1pc3Npb24pIHJldHVybiB0cnVlO1xyXG4gICAgcmV0dXJuIDxQcm90ZWN0ZWQgcGVybWlzc2lvbj17dGFiLnBlcm1pc3Npb259IHNob3dGYWxsYmFjaz17ZmFsc2V9PlxyXG4gICAgICB7dHJ1ZX1cclxuICAgIDwvUHJvdGVjdGVkPjtcclxuICB9KTtcclxuXHJcbiAgLy8gQ29tcG9uZW50ZSBkZSBmaWx0cm9zIHBhcmEgcHJvZmlzc8O1ZXNcclxuICBjb25zdCBQcm9mZXNzaW9uc0ZpbHRlcnMgPSAoKSA9PiB7XHJcbiAgICAvLyBFc3RhZG8gbG9jYWwgcGFyYSBvIGNhbXBvIGRlIGJ1c2NhXHJcbiAgICBjb25zdCBbbG9jYWxTZWFyY2gsIHNldExvY2FsU2VhcmNoXSA9IHVzZVN0YXRlKHNlYXJjaCk7XHJcblxyXG4gICAgLy8gQXR1YWxpemFyIG8gZXN0YWRvIGxvY2FsIHF1YW5kbyBvIGVzdGFkbyBnbG9iYWwgbXVkYXJcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgIHNldExvY2FsU2VhcmNoKHNlYXJjaCk7XHJcbiAgICB9LCBbc2VhcmNoXSk7XHJcblxyXG4gICAgLy8gT3V2aXIgbyBldmVudG8gZGUgcmVzZXRcclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgIGNvbnN0IGhhbmRsZVJlc2V0ID0gKCkgPT4ge1xyXG4gICAgICAgIHNldExvY2FsU2VhcmNoKFwiXCIpO1xyXG4gICAgICB9O1xyXG4gICAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdyZXNldCcsIGhhbmRsZVJlc2V0KTtcclxuICAgICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNldCcsIGhhbmRsZVJlc2V0KTtcclxuICAgICAgfTtcclxuICAgIH0sIFtdKTtcclxuXHJcbiAgICAvLyBGdW7Dp8OjbyBwYXJhIGF0dWFsaXphciBvIGVzdGFkbyBsb2NhbCBzZW0gYWZldGFyIG8gZXN0YWRvIGdsb2JhbFxyXG4gICAgY29uc3QgaGFuZGxlTG9jYWxTZWFyY2hDaGFuZ2UgPSAoZSkgPT4ge1xyXG4gICAgICBzZXRMb2NhbFNlYXJjaChlLnRhcmdldC52YWx1ZSk7XHJcbiAgICB9O1xyXG5cclxuICAgIC8vIEZ1bsOnw6NvIHBhcmEgYXBsaWNhciBvIGZpbHRybyBxdWFuZG8gbyBib3TDo28gZm9yIGNsaWNhZG9cclxuICAgIGNvbnN0IGFwcGx5RmlsdGVyID0gKCkgPT4ge1xyXG4gICAgICBzZXRTZWFyY2gobG9jYWxTZWFyY2gpO1xyXG4gICAgICBzZXRDdXJyZW50UHJvZmVzc2lvbnNQYWdlKDEpO1xyXG5cclxuICAgICAgLy8gTG9nIHBhcmEgZGVidWdcclxuICAgICAgY29uc29sZS5sb2coXCJBcGxpY2FuZG8gZmlsdHJvcyBkZSBwcm9maXNzw7VlczpcIiwge1xyXG4gICAgICAgIHNlYXJjaDogbG9jYWxTZWFyY2gsXHJcbiAgICAgICAgZ3JvdXBGaWx0ZXIsXHJcbiAgICAgICAgc3RhdHVzRmlsdGVyLFxyXG4gICAgICAgIGNvbXBhbnlGaWx0ZXIsXHJcbiAgICAgICAgcHJvZmVzc2lvbnNGaWx0ZXJcclxuICAgICAgfSk7XHJcblxyXG4gICAgICBsb2FkUHJvZmVzc2lvbnMoMSwgbG9jYWxTZWFyY2gsIGdyb3VwRmlsdGVyLCBzdGF0dXNGaWx0ZXIsIGNvbXBhbnlGaWx0ZXIsIHByb2Zlc3Npb25zRmlsdGVyKTtcclxuICAgIH07XHJcblxyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC00IG10LTRcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWQ6ZmxleC1yb3cgZ2FwLTRcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgIDxTZWFyY2ggY2xhc3NOYW1lPVwiYWJzb2x1dGUgbGVmdC0zIHRvcC0xLzIgdHJhbnNmb3JtIC10cmFuc2xhdGUteS0xLzIgdGV4dC1zbGF0ZS00MDAgZGFyazp0ZXh0LXNsYXRlLTUwMCBoLTUgdy01XCIgLz5cclxuICAgICAgICAgICAgPGlucHV0XHJcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxyXG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQnVzY2FyIHBvciBub21lIG91IGRlc2NyacOnw6NvLi4uXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17bG9jYWxTZWFyY2h9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUxvY2FsU2VhcmNoQ2hhbmdlfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBwbC0xMCBwci00IHB5LTIgYm9yZGVyIGJvcmRlci1uZXV0cmFsLTIwMCBkYXJrOmJvcmRlci1ncmF5LTYwMCByb3VuZGVkLWxnIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wcmltYXJ5LTMwMCBkYXJrOmZvY3VzOnJpbmctcHJpbWFyeS02MDAgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LW5ldXRyYWwtODAwIGRhcms6dGV4dC1uZXV0cmFsLTEwMFwiXHJcbiAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgaWYgKGUua2V5ID09PSAnRW50ZXInKSB7XHJcbiAgICAgICAgICAgICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgICAgICAgICAgICAgYXBwbHlGaWx0ZXIoKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC0yXCI+XHJcbiAgICAgICAgICAgIDxNb2R1bGVTZWxlY3RcclxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17Z3JvdXBGaWx0ZXJ9XHJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVHcm91cEZpbHRlckNoYW5nZShlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ0dyb3Vwc31cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5Ub2RvcyBvcyBncnVwb3M8L29wdGlvbj5cclxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwibnVsbFwiPlNlbSBncnVwbzwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgIHtncm91cHMubWFwKChncm91cCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2dyb3VwLmlkfSB2YWx1ZT17Z3JvdXAuaWR9PlxyXG4gICAgICAgICAgICAgICAgICB7Z3JvdXAubmFtZX1cclxuICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L01vZHVsZVNlbGVjdD5cclxuXHJcbiAgICAgICAgICAgIDxNb2R1bGVTZWxlY3RcclxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgICB2YWx1ZT17c3RhdHVzRmlsdGVyfVxyXG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU3RhdHVzRmlsdGVyQ2hhbmdlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5Ub2RvcyBvcyBzdGF0dXM8L29wdGlvbj5cclxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiYWN0aXZlXCI+QXRpdmFzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cImluYWN0aXZlXCI+SW5hdGl2YXM8L29wdGlvbj5cclxuICAgICAgICAgICAgPC9Nb2R1bGVTZWxlY3Q+XHJcblxyXG4gICAgICAgICAgICB7LyogRmlsdHJvIGRlIGVtcHJlc2EgKGFwZW5hcyBwYXJhIHN5c3RlbSBhZG1pbikgKi99XHJcbiAgICAgICAgICAgIHtpc1N5c3RlbUFkbWluICYmIChcclxuICAgICAgICAgICAgICA8TW9kdWxlU2VsZWN0XHJcbiAgICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtjb21wYW55RmlsdGVyfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVDb21wYW55RmlsdGVyQ2hhbmdlKGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmdDb21wYW5pZXN9XHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlRvZGFzIGFzIGVtcHJlc2FzPC9vcHRpb24+XHJcbiAgICAgICAgICAgICAgICB7Y29tcGFuaWVzLm1hcCgoY29tcGFueSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17Y29tcGFueS5pZH0gdmFsdWU9e2NvbXBhbnkuaWR9PlxyXG4gICAgICAgICAgICAgICAgICAgIHtjb21wYW55Lm5hbWV9XHJcbiAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9Nb2R1bGVTZWxlY3Q+XHJcbiAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICA8TW9kYWxCdXR0b25cclxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXthcHBseUZpbHRlcn1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxGaWx0ZXIgc2l6ZT17MTZ9IGNsYXNzTmFtZT1cInNtOmhpZGRlblwiIC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIHNtOmlubGluZVwiPkZpbHRyYXI8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvTW9kYWxCdXR0b24+XHJcblxyXG4gICAgICAgICAgICA8TW9kYWxCdXR0b25cclxuICAgICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZXNldEZpbHRlcnN9XHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cInNlY29uZGFyeVwiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UmVmcmVzaEN3IHNpemU9ezE2fSBjbGFzc05hbWU9XCJzbTpoaWRkZW5cIiAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBzbTppbmxpbmVcIj5MaW1wYXI8L3NwYW4+XHJcbiAgICAgICAgICAgIDwvTW9kYWxCdXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgey8qIE11bHRpLXNlbGVjdCBwYXJhIGZpbHRyYXIgcG9yIG3Dumx0aXBsYXMgcHJvZmlzc8O1ZXMgKi99XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgIDxNdWx0aVNlbGVjdFxyXG4gICAgICAgICAgICBsYWJlbD1cIkZpbHRyYXIgcG9yIFByb2Zpc3PDtWVzXCJcclxuICAgICAgICAgICAgdmFsdWU9e3Byb2Zlc3Npb25zRmlsdGVyfVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlUHJvZmVzc2lvbnNGaWx0ZXJDaGFuZ2V9XHJcbiAgICAgICAgICAgIG9wdGlvbnM9e3Byb2Zlc3Npb25PcHRpb25zfVxyXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlbGVjaW9uZSB1bWEgb3UgbWFpcyBwcm9maXNzw7VlcyBwZWxvIG5vbWUuLi5cIlxyXG4gICAgICAgICAgICBsb2FkaW5nPXtpc0xvYWRpbmdQcm9mZXNzaW9uT3B0aW9uc31cclxuICAgICAgICAgICAgbW9kdWxlT3ZlcnJpZGU9XCJhZG1pblwiXHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfTtcclxuXHJcbiAgLy8gRXN0YWRvcyBwYXJhIG8gbm92byBzaXN0ZW1hIGRlIGZpbHRyb3MgZGUgZ3J1cG9zXHJcbiAgY29uc3QgW2dyb3VwRmlsdGVycywgc2V0R3JvdXBGaWx0ZXJzXSA9IHVzZVN0YXRlKHtcclxuICAgIHNlYXJjaDogXCJcIixcclxuICAgIGNvbXBhbmllczogW10sXHJcbiAgICBzdGF0dXM6IFwiXCIsXHJcbiAgICBncm91cHM6IFtdXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUdyb3VwRmlsdGVyc0NoYW5nZSA9IChuZXdGaWx0ZXJzKSA9PiB7XHJcbiAgICBzZXRHcm91cEZpbHRlcnMobmV3RmlsdGVycyk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlR3JvdXBTZWFyY2ggPSAoZmlsdGVycykgPT4ge1xyXG4gICAgc2V0Q3VycmVudEdyb3Vwc1BhZ2UoMSk7XHJcbiAgICBjb25zdCBzZWFyY2hRdWVyeSA9IGZpbHRlcnMuc2VhcmNoIHx8IFwiXCI7XHJcbiAgICBjb25zdCBjb21wYW55SWRzID0gZmlsdGVycy5jb21wYW5pZXMgfHwgW107XHJcbiAgICBjb25zdCBzdGF0dXMgPSBmaWx0ZXJzLnN0YXR1cyB8fCBcIlwiO1xyXG4gICAgY29uc3QgZ3JvdXBJZHMgPSBmaWx0ZXJzLmdyb3VwcyB8fCBbXTtcclxuXHJcbiAgICBzZXRTZWFyY2goc2VhcmNoUXVlcnkpO1xyXG4gICAgc2V0Q29tcGFueUZpbHRlcihjb21wYW55SWRzLmxlbmd0aCA+IDAgPyBjb21wYW55SWRzWzBdIDogXCJcIik7XHJcbiAgICBzZXRTdGF0dXNGaWx0ZXIoc3RhdHVzKTtcclxuICAgIHNldEdyb3Vwc0ZpbHRlcihncm91cElkcyk7XHJcblxyXG4gICAgZmlsdGVyR3JvdXBzKHNlYXJjaFF1ZXJ5LCAxLCBncm91cElkcyk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSW1wb3J0IHR1dG9yaWFsIHN0ZXBzIGZyb20gdHV0b3JpYWxNYXBwaW5nXHJcbiAgY29uc3QgcHJvZmVzc2lvbnNHcm91cHNUdXRvcmlhbFN0ZXBzID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICAvLyBJbXBvcnQgZHluYW1pY2FsbHkgdG8gYXZvaWQgY2lyY3VsYXIgZGVwZW5kZW5jaWVzXHJcbiAgICBjb25zdCB0dXRvcmlhbE1hcCA9IHJlcXVpcmUoJ0AvdHV0b3JpYWxzL3R1dG9yaWFsTWFwcGluZycpLmRlZmF1bHQ7XHJcbiAgICByZXR1cm4gdHV0b3JpYWxNYXBbJy9kYXNoYm9hcmQvYWRtaW4vcHJvZmVzc2lvbnMnXSB8fCBbXTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBtYi00XCI+XHJcbiAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LXNsYXRlLTgwMCBkYXJrOnRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgIHthY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIiA/XHJcbiAgICAgICAgICAgIDxCcmllZmNhc2Ugc2l6ZT17MjR9IGNsYXNzTmFtZT1cIm1yLTIgdGV4dC1zbGF0ZS02MDAgZGFyazp0ZXh0LXNsYXRlLTQwMFwiIC8+IDpcclxuICAgICAgICAgICAgPExheWVycyBzaXplPXsyNH0gY2xhc3NOYW1lPVwibXItMiB0ZXh0LXNsYXRlLTYwMCBkYXJrOnRleHQtc2xhdGUtNDAwXCIgLz59XHJcbiAgICAgICAgICB7YWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIgPyBcIlByb2Zpc3PDtWVzXCIgOiBcIkdydXBvcyBkZSBQcm9maXNzw7Vlc1wifVxyXG4gICAgICAgIDwvaDE+XHJcblxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgIHsvKiBCb3TDo28gZGUgZXhjbHVzw6NvIGVtIG1hc3NhIC0gYXBlbmFzIHBhcmEgcHJvZmlzc8O1ZXMgKi99XHJcbiAgICAgICAgICB7YWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIgJiYgc2VsZWN0ZWRJZHMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBjb25zb2xlLmxvZygnRXhjbHVpciBzZWxlY2lvbmFkb3M6Jywgc2VsZWN0ZWRJZHMpfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTQgcHktMiBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB0by1yb3NlLTQwMCBkYXJrOmZyb20tcmVkLTcwMCBkYXJrOnRvLXJvc2UtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3Zlcjpmcm9tLXJlZC03MDAgaG92ZXI6dG8tcm9zZS01MDAgZGFyazpob3Zlcjpmcm9tLXJlZC04MDAgZGFyazpob3Zlcjp0by1yb3NlLTcwMCBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNSBob3ZlcjpzaGFkb3ctbGdcIlxyXG4gICAgICAgICAgICAgIHRpdGxlPVwiRXhjbHVpciBzZWxlY2lvbmFkb3NcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPFRyYXNoIHNpemU9ezE4fSAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RXhjbHVpciBTZWxlY2lvbmFkb3MgKHtzZWxlY3RlZElkcy5sZW5ndGh9KTwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAgey8qIEJvdMOjbyBkZSBleHBvcnRhciAqL31cclxuICAgICAgICAgIHthY3RpdmVUYWIgPT09IFwicHJvZmVzc2lvbnNcIiAmJiAoXHJcbiAgICAgICAgICAgIDxFeHBvcnRNZW51XHJcbiAgICAgICAgICAgICAgb25FeHBvcnQ9e2hhbmRsZUV4cG9ydFByb2Zlc3Npb25zfVxyXG4gICAgICAgICAgICAgIGlzRXhwb3J0aW5nPXtpc0V4cG9ydGluZ31cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nIHx8IHByb2Zlc3Npb25zLmxlbmd0aCA9PT0gMH1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTcwMCBkYXJrOnRleHQtc2xhdGUtMzAwXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICB7LyogQm90w6NvIGRlIGV4Y2x1c8OjbyBlbSBtYXNzYSAtIGFwZW5hcyBwYXJhIGdydXBvcyAqL31cclxuICAgICAgICAgIHthY3RpdmVUYWIgPT09IFwiZ3JvdXBzXCIgJiYgc2VsZWN0ZWRHcm91cElkcy5sZW5ndGggPiAwICYmIChcclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgIC8vIFRPRE86IEltcGxlbWVudGFyIGV4Y2x1c8OjbyBlbSBtYXNzYSBkZSBncnVwb3NcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTQgcHktMiBiZy1ncmFkaWVudC10by1yIGZyb20tcmVkLTYwMCB0by1yb3NlLTQwMCBkYXJrOmZyb20tcmVkLTcwMCBkYXJrOnRvLXJvc2UtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3Zlcjpmcm9tLXJlZC03MDAgaG92ZXI6dG8tcm9zZS01MDAgZGFyazpob3Zlcjpmcm9tLXJlZC04MDAgZGFyazpob3Zlcjp0by1yb3NlLTcwMCBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNSBob3ZlcjpzaGFkb3ctbGdcIlxyXG4gICAgICAgICAgICAgIHRpdGxlPVwiRXhjbHVpciBzZWxlY2lvbmFkb3NcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPFRyYXNoIHNpemU9ezE4fSAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+RXhjbHVpciBTZWxlY2lvbmFkb3MgKHtzZWxlY3RlZEdyb3VwSWRzLmxlbmd0aH0pPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgICB7YWN0aXZlVGFiID09PSBcImdyb3Vwc1wiICYmIChcclxuICAgICAgICAgICAgPEV4cG9ydE1lbnVcclxuICAgICAgICAgICAgICBvbkV4cG9ydD17aGFuZGxlRXhwb3J0R3JvdXBzfVxyXG4gICAgICAgICAgICAgIGlzRXhwb3J0aW5nPXtpc0V4cG9ydGluZ31cclxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nR3JvdXBzIHx8IGZpbHRlcmVkR3JvdXBzLmxlbmd0aCA9PT0gMH1cclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNsYXRlLTcwMCBkYXJrOnRleHQtc2xhdGUtMzAwXCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgey8qIEJvdMOjbyBkZSBhZGljaW9uYXIgKi99XHJcbiAgICAgICAgICB7YWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIgJiYgKFxyXG4gICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xyXG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRQcm9mZXNzaW9uKG51bGwpO1xyXG4gICAgICAgICAgICAgICAgc2V0UHJvZmVzc2lvbkZvcm1PcGVuKHRydWUpO1xyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNCBweS0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1zbGF0ZS01MDAgdG8tc2xhdGUtNjAwIGRhcms6ZnJvbS1zbGF0ZS02MDAgZGFyazp0by1zbGF0ZS03MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmZyb20tc2xhdGUtNjAwIGhvdmVyOnRvLXNsYXRlLTcwMCBkYXJrOmhvdmVyOmZyb20tc2xhdGUtNzAwIGRhcms6aG92ZXI6dG8tc2xhdGUtODAwIHNoYWRvdy1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2NhbGUtMTA1IGhvdmVyOnNoYWRvdy1sZ1wiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8UGx1cyBzaXplPXsxOH0gLz5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPk5vdmEgUHJvZmlzc8Ojbzwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICApfVxyXG4gICAgICAgICAge2FjdGl2ZVRhYiA9PT0gXCJncm91cHNcIiAmJiAoXHJcbiAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBzZXRTZWxlY3RlZEdyb3VwKG51bGwpO1xyXG4gICAgICAgICAgICAgICAgc2V0R3JvdXBGb3JtT3Blbih0cnVlKTtcclxuICAgICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTQgcHktMiBiZy1ncmFkaWVudC10by1yIGZyb20tc2xhdGUtNTAwIHRvLXNsYXRlLTYwMCBkYXJrOmZyb20tc2xhdGUtNjAwIGRhcms6dG8tc2xhdGUtNzAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3Zlcjpmcm9tLXNsYXRlLTYwMCBob3Zlcjp0by1zbGF0ZS03MDAgZGFyazpob3Zlcjpmcm9tLXNsYXRlLTcwMCBkYXJrOmhvdmVyOnRvLXNsYXRlLTgwMCBzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTEwNSBob3ZlcjpzaGFkb3ctbGdcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPFBsdXMgc2l6ZT17MTh9IC8+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5Ob3ZvIEdydXBvPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgPE1vZHVsZUhlYWRlclxyXG4gICAgICAgIHRpdGxlPVwiRmlsdHJvcyBlIEJ1c2NhXCJcclxuICAgICAgICBpY29uPXs8RmlsdGVyIHNpemU9ezIyfSBjbGFzc05hbWU9XCJ0ZXh0LW1vZHVsZS1hZG1pbi1pY29uIGRhcms6dGV4dC1tb2R1bGUtYWRtaW4taWNvbi1kYXJrXCIgLz59XHJcbiAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXHJcbiAgICAgICAgdHV0b3JpYWxTdGVwcz17cHJvZmVzc2lvbnNHcm91cHNUdXRvcmlhbFN0ZXBzfVxyXG4gICAgICAgIHR1dG9yaWFsTmFtZT1cImFkbWluLXByb2Zlc3Npb25zLWdyb3Vwcy1vdmVydmlld1wiXHJcbiAgICAgICAgZmlsdGVycz17XHJcbiAgICAgICAgICA8PlxyXG4gICAgICAgICAgICA8TW9kdWxlVGFic1xyXG4gICAgICAgICAgICAgIHRhYnM9e2ZpbHRlcmVkVGFic31cclxuICAgICAgICAgICAgICBhY3RpdmVUYWI9e2FjdGl2ZVRhYn1cclxuICAgICAgICAgICAgICBvblRhYkNoYW5nZT17c2V0QWN0aXZlVGFifVxyXG4gICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTZcIj5cclxuICAgICAgICAgICAgICB7YWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIgPyAoXHJcbiAgICAgICAgICAgICAgICA8T2NjdXBhdGlvbkZpbHRlcnNcclxuICAgICAgICAgICAgICAgICAgZmlsdGVycz17b2NjdXBhdGlvbkZpbHRlcnN9XHJcbiAgICAgICAgICAgICAgICAgIG9uRmlsdGVyc0NoYW5nZT17aGFuZGxlT2NjdXBhdGlvbkZpbHRlcnNDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVPY2N1cGF0aW9uU2VhcmNofVxyXG4gICAgICAgICAgICAgICAgICBvbkNsZWFyRmlsdGVycz17aGFuZGxlT2NjdXBhdGlvbkNsZWFyRmlsdGVyc31cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxHcm91cHNGaWx0ZXJzXHJcbiAgICAgICAgICAgICAgICAgIGZpbHRlcnM9e2dyb3VwRmlsdGVyc31cclxuICAgICAgICAgICAgICAgICAgb25GaWx0ZXJzQ2hhbmdlPXtoYW5kbGVHcm91cEZpbHRlcnNDaGFuZ2V9XHJcbiAgICAgICAgICAgICAgICAgIG9uU2VhcmNoPXtoYW5kbGVHcm91cFNlYXJjaH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8Lz5cclxuICAgICAgICB9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7LyogVGFiZWxhIGRlIFByb2Zpc3PDtWVzICovfVxyXG4gICAgICB7YWN0aXZlVGFiID09PSBcInByb2Zlc3Npb25zXCIgJiYgKFxyXG4gICAgICAgIDxQcm90ZWN0ZWQgcGVybWlzc2lvbj1cImFkbWluLnByb2Zlc3Npb25zLnZpZXdcIj5cclxuICAgICAgICAgIDxNb2R1bGVUYWJsZVxyXG4gICAgICAgICAgICBtb2R1bGVDb2xvcj1cImFkbWluXCJcclxuICAgICAgICAgICAgdGl0bGU9XCJMaXN0YSBkZSBQcm9maXNzw7Vlc1wiXHJcbiAgICAgICAgICAgIGhlYWRlckNvbnRlbnQ9e1xyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbG9hZFByb2Zlc3Npb25zKCl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiB0ZXh0LWdyYXktNjAwIGRhcms6dGV4dC1ncmF5LTMwMCBob3Zlcjp0ZXh0LWdyZWVuLTYwMCBkYXJrOmhvdmVyOnRleHQtZ3JlZW4tNDAwIGhvdmVyOmJnLWdyZWVuLTUwIGRhcms6aG92ZXI6YmctZ3JlZW4tOTAwLzIwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMTAgaG92ZXI6c2hhZG93LW1kIHJvdW5kZWQtbWRcIlxyXG4gICAgICAgICAgICAgICAgICB0aXRsZT1cIkF0dWFsaXphciBsaXN0YVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgc2l6ZT17MTh9IC8+XHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjb2x1bW5zPXtbXHJcbiAgICAgICAgICAgICAgeyBoZWFkZXI6ICcnLCBmaWVsZDogJ3NlbGVjdCcsIHdpZHRoOiAnNTBweCcsIHNvcnRhYmxlOiBmYWxzZSB9LFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnUHJvZmlzc8OjbycsIGZpZWxkOiAnbmFtZScsIHdpZHRoOiAnMjAlJyB9LFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnR3J1cG8nLCBmaWVsZDogJ2dyb3VwJywgd2lkdGg6ICcxNSUnIH0sXHJcbiAgICAgICAgICAgICAgLi4uKGlzU3lzdGVtQWRtaW4gPyBbeyBoZWFkZXI6ICdFbXByZXNhJywgZmllbGQ6ICdjb21wYW55Jywgd2lkdGg6ICcxNSUnIH1dIDogW10pLFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnRGVzY3Jpw6fDo28nLCBmaWVsZDogJ2Rlc2NyaXB0aW9uJywgd2lkdGg6ICcyMCUnIH0sXHJcbiAgICAgICAgICAgICAgeyBoZWFkZXI6ICdVc3XDoXJpb3MnLCBmaWVsZDogJ3VzZXJzJywgd2lkdGg6ICcxMCUnIH0sXHJcbiAgICAgICAgICAgICAgeyBoZWFkZXI6ICdTdGF0dXMnLCBmaWVsZDogJ2FjdGl2ZScsIHdpZHRoOiAnMTAlJyB9LFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnQcOnw7VlcycsIGZpZWxkOiAnYWN0aW9ucycsIGNsYXNzTmFtZTogJ3RleHQtcmlnaHQnLCB3aWR0aDogJzEwJScsIHNvcnRhYmxlOiBmYWxzZSB9XHJcbiAgICAgICAgICAgIF19XHJcbiAgICAgICAgICAgIGRhdGE9e3Byb2Zlc3Npb25zfVxyXG4gICAgICAgICAgICBpc0xvYWRpbmc9e2lzTG9hZGluZ31cclxuICAgICAgICAgICAgZW1wdHlNZXNzYWdlPVwiTmVuaHVtYSBwcm9maXNzw6NvIGVuY29udHJhZGFcIlxyXG4gICAgICAgICAgICBlbXB0eUljb249ezxCcmllZmNhc2Ugc2l6ZT17MjR9IC8+fVxyXG4gICAgICAgICAgICB0YWJsZUlkPVwiYWRtaW4tcHJvZmVzc2lvbnMtdGFibGVcIlxyXG4gICAgICAgICAgICBlbmFibGVDb2x1bW5Ub2dnbGU9e3RydWV9XHJcbiAgICAgICAgICAgIGRlZmF1bHRTb3J0RmllbGQ9XCJuYW1lXCJcclxuICAgICAgICAgICAgZGVmYXVsdFNvcnREaXJlY3Rpb249XCJhc2NcIlxyXG4gICAgICAgICAgICBjdXJyZW50UGFnZT17Y3VycmVudFByb2Zlc3Npb25zUGFnZX1cclxuICAgICAgICAgICAgdG90YWxQYWdlcz17dG90YWxQcm9mZXNzaW9uc1BhZ2VzfVxyXG4gICAgICAgICAgICB0b3RhbEl0ZW1zPXt0b3RhbFByb2Zlc3Npb25zfVxyXG4gICAgICAgICAgICBvblBhZ2VDaGFuZ2U9e2hhbmRsZVByb2Zlc3Npb25zUGFnZUNoYW5nZX1cclxuICAgICAgICAgICAgb25Tb3J0PXsoZmllbGQsIGRpcmVjdGlvbikgPT4ge1xyXG4gICAgICAgICAgICAgIC8vIFF1YW5kbyBhIG9yZGVuYcOnw6NvIG11ZGFyLCByZWNhcnJlZ2FyIGFzIHByb2Zpc3PDtWVzIGNvbSBvcyBub3ZvcyBwYXLDom1ldHJvcyBkZSBvcmRlbmHDp8Ojb1xyXG4gICAgICAgICAgICAgIGxvYWRQcm9mZXNzaW9ucyhcclxuICAgICAgICAgICAgICAgIGN1cnJlbnRQcm9mZXNzaW9uc1BhZ2UsXHJcbiAgICAgICAgICAgICAgICBzZWFyY2gsXHJcbiAgICAgICAgICAgICAgICBncm91cEZpbHRlcixcclxuICAgICAgICAgICAgICAgIHN0YXR1c0ZpbHRlcixcclxuICAgICAgICAgICAgICAgIGNvbXBhbnlGaWx0ZXIsXHJcbiAgICAgICAgICAgICAgICBwcm9mZXNzaW9uc0ZpbHRlcixcclxuICAgICAgICAgICAgICAgIGZpZWxkLFxyXG4gICAgICAgICAgICAgICAgZGlyZWN0aW9uXHJcbiAgICAgICAgICAgICAgKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgc2hvd1BhZ2luYXRpb249e3RydWV9XHJcbiAgICAgICAgICAgIGl0ZW1zUGVyUGFnZT17aXRlbXNQZXJQYWdlfVxyXG4gICAgICAgICAgICBvbkl0ZW1zUGVyUGFnZUNoYW5nZT17KG5ld0l0ZW1zUGVyUGFnZSkgPT4ge1xyXG4gICAgICAgICAgICAgIHNldEl0ZW1zUGVyUGFnZShuZXdJdGVtc1BlclBhZ2UpO1xyXG4gICAgICAgICAgICAgIGxvYWRQcm9mZXNzaW9ucygxLCBzZWFyY2gsIGdyb3VwRmlsdGVyLCBzdGF0dXNGaWx0ZXIsIGNvbXBhbnlGaWx0ZXIsIHByb2Zlc3Npb25zRmlsdGVyLCBcIm5hbWVcIiwgXCJhc2NcIiwgbmV3SXRlbXNQZXJQYWdlKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgc2VsZWN0ZWRJZHM9e3NlbGVjdGVkR3JvdXBJZHN9XHJcbiAgICAgICAgICAgIG9uU2VsZWN0QWxsPXtoYW5kbGVTZWxlY3RBbGxHcm91cHN9XHJcbiAgICAgICAgICAgIHJlbmRlclJvdz17KHByb2Zlc3Npb24sIGluZGV4LCBtb2R1bGVDb2xvcnMsIHZpc2libGVDb2x1bW5zKSA9PiAoXHJcbiAgICAgICAgICAgICAgPHRyIGtleT17cHJvZmVzc2lvbi5pZH0gY2xhc3NOYW1lPXttb2R1bGVDb2xvcnMuaG92ZXJCZ30+XHJcbiAgICAgICAgICAgICAgICB7dmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoJ3NlbGVjdCcpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxNb2R1bGVDaGVja2JveFxyXG4gICAgICAgICAgICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtzZWxlY3RlZElkcy5pbmNsdWRlcyhwcm9mZXNzaW9uLmlkKX1cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlU2VsZWN0T25lKHByb2Zlc3Npb24uaWQsIGUudGFyZ2V0LmNoZWNrZWQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgbmFtZT17YHNlbGVjdC1wcm9mZXNzaW9uLSR7cHJvZmVzc2lvbi5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCduYW1lJykgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMTAgdy0xMCBiZy1wcmltYXJ5LTEwMCBkYXJrOmJnLXByaW1hcnktOTAwLzMwIHRleHQtcHJpbWFyeS02MDAgZGFyazp0ZXh0LXByaW1hcnktNDAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnJpZWZjYXNlIHNpemU9ezIwfSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1sLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtbmV1dHJhbC05MDAgZGFyazp0ZXh0LW5ldXRyYWwtMTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2Zlc3Npb24ubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCdncm91cCcpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtwcm9mZXNzaW9uLmdyb3VwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHgtMiBweS0xIHRleHQteHMgcm91bmRlZC1mdWxsIGJnLWluZGlnby0xMDAgZGFyazpiZy1pbmRpZ28tOTAwLzMwIHRleHQtaW5kaWdvLTgwMCBkYXJrOnRleHQtaW5kaWdvLTQwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbi5ncm91cC5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwIGRhcms6dGV4dC1uZXV0cmFsLTUwMCBpdGFsaWNcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgU2VtIGdydXBvXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAge2lzU3lzdGVtQWRtaW4gJiYgdmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoJ2NvbXBhbnknKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbi5jb21wYW55ID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcgc2l6ZT17MTR9IGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC02MDAgZGFyazp0ZXh0LW5ldXRyYWwtMzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Byb2Zlc3Npb24uY29tcGFueS5uYW1lfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCBkYXJrOnRleHQtbmV1dHJhbC01MDAgaXRhbGljXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFNlbSBlbXByZXNhXHJcbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCdkZXNjcmlwdGlvbicpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW5ldXRyYWwtNjAwIGRhcms6dGV4dC1uZXV0cmFsLTMwMCBsaW5lLWNsYW1wLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9mZXNzaW9uLmRlc2NyaXB0aW9uIHx8IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1uZXV0cmFsLTQwMCBkYXJrOnRleHQtbmV1dHJhbC01MDAgaXRhbGljXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgU2VtIGRlc2NyacOnw6NvXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgIHt2aXNpYmxlQ29sdW1ucy5pbmNsdWRlcygndXNlcnMnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgc2l6ZT17MTZ9IGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC00MDAgZGFyazp0ZXh0LW5ldXRyYWwtNTAwIG1yLTJcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW5ldXRyYWwtNjAwIGRhcms6dGV4dC1uZXV0cmFsLTMwMFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZmVzc2lvbi5fY291bnQ/LnVzZXJzIHx8IDB9IHVzdcOhcmlvc1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7dmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoJ2FjdGl2ZScpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0yIHB5LTEgdGV4dC14cyByb3VuZGVkLWZ1bGwgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTEgdy1maXQgJHtwcm9mZXNzaW9uLmFjdGl2ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi0xMDAgZGFyazpiZy1ncmVlbi05MDAvMzAgdGV4dC1ncmVlbi04MDAgZGFyazp0ZXh0LWdyZWVuLTQwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcImJnLXJlZC0xMDAgZGFyazpiZy1yZWQtOTAwLzMwIHRleHQtcmVkLTgwMCBkYXJrOnRleHQtcmVkLTQwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwcm9mZXNzaW9uLmFjdGl2ZSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgc2l6ZT17MTJ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QXRpdmE8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WENpcmNsZSBzaXplPXsxMn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5JbmF0aXZhPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7dmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoJ2FjdGlvbnMnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1yaWdodCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdVc2Vycyhwcm9mZXNzaW9uKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgdGV4dC1uZXV0cmFsLTUwMCBkYXJrOnRleHQtbmV1dHJhbC00MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwIGRhcms6aG92ZXI6dGV4dC1pbmRpZ28tNDAwIGhvdmVyOmJnLWluZGlnby01MCBkYXJrOmhvdmVyOmJnLWluZGlnby05MDAvMjAgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTEwIGhvdmVyOnNoYWRvdy1tZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiVmVyIHVzdcOhcmlvcyBjb20gZXN0YSBwcm9maXNzw6NvXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPFVzZXJSb3VuZCBzaXplPXsxOH0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFByb3RlY3RlZCBwZXJtaXNzaW9uPVwiYWRtaW4ucHJvZmVzc2lvbnMuZWRpdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdFByb2Zlc3Npb24ocHJvZmVzc2lvbil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgdGV4dC1uZXV0cmFsLTUwMCBkYXJrOnRleHQtbmV1dHJhbC00MDAgaG92ZXI6dGV4dC1wcmltYXJ5LTUwMCBkYXJrOmhvdmVyOnRleHQtcHJpbWFyeS00MDAgaG92ZXI6YmctcHJpbWFyeS01MCBkYXJrOmhvdmVyOmJnLXByaW1hcnktOTAwLzIwIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTExMCBob3ZlcjpzaGFkb3ctbWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiRWRpdGFyIHByb2Zpc3PDo29cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgc2l6ZT17MTh9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Qcm90ZWN0ZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8UHJvdGVjdGVkIHBlcm1pc3Npb249XCJhZG1pbi5wcm9mZXNzaW9ucy5kZWxldGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZVByb2Zlc3Npb24ocHJvZmVzc2lvbil9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgdGV4dC1uZXV0cmFsLTUwMCBkYXJrOnRleHQtbmV1dHJhbC00MDAgaG92ZXI6dGV4dC1yZWQtNTAwIGRhcms6aG92ZXI6dGV4dC1yZWQtNDAwIGhvdmVyOmJnLXJlZC01MCBkYXJrOmhvdmVyOmJnLXJlZC05MDAvMjAgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTEwIGhvdmVyOnNoYWRvdy1tZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFeGNsdWlyIHByb2Zpc3PDo29cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9mZXNzaW9uLl9jb3VudD8udXNlcnMgPiAwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoIHNpemU9ezE4fSBjbGFzc05hbWU9e3Byb2Zlc3Npb24uX2NvdW50Py51c2VycyA+IDAgPyBcIm9wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkXCIgOiBcIlwifSAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvUHJvdGVjdGVkPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICA8L3RyPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L1Byb3RlY3RlZD5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBUYWJlbGEgZGUgR3J1cG9zIGRlIFByb2Zpc3PDtWVzICovfVxyXG4gICAgICB7YWN0aXZlVGFiID09PSBcImdyb3Vwc1wiICYmIChcclxuICAgICAgICA8UHJvdGVjdGVkIHBlcm1pc3Npb249XCJhZG1pbi5wcm9mZXNzaW9uLWdyb3Vwcy52aWV3XCI+XHJcbiAgICAgICAgICA8TW9kdWxlVGFibGVcclxuICAgICAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXHJcbiAgICAgICAgICAgIHRpdGxlPVwiTGlzdGEgZGUgR3J1cG9zXCJcclxuICAgICAgICAgICAgaGVhZGVyQ29udGVudD17XHJcbiAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbG9hZEdyb3VwcygpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIHRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtZ3JlZW4tNjAwIGRhcms6aG92ZXI6dGV4dC1ncmVlbi00MDAgaG92ZXI6YmctZ3JlZW4tNTAgZGFyazpob3ZlcjpiZy1ncmVlbi05MDAvMjAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTExMCBob3ZlcjpzaGFkb3ctbWQgcm91bmRlZC1tZFwiXHJcbiAgICAgICAgICAgICAgICB0aXRsZT1cIkF0dWFsaXphciBsaXN0YVwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBzaXplPXsxOH0gLz5cclxuICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjb2x1bW5zPXtbXHJcbiAgICAgICAgICAgICAgeyBoZWFkZXI6ICcnLCBmaWVsZDogJ3NlbGVjdCcsIHdpZHRoOiAnNTBweCcsIHNvcnRhYmxlOiBmYWxzZSB9LFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnR3J1cG8nLCBmaWVsZDogJ25hbWUnLCB3aWR0aDogJzIwJScgfSxcclxuICAgICAgICAgICAgICB7IGhlYWRlcjogJ0Rlc2NyacOnw6NvJywgZmllbGQ6ICdkZXNjcmlwdGlvbicsIHdpZHRoOiAnMjUlJyB9LFxyXG4gICAgICAgICAgICAgIC4uLihpc1N5c3RlbUFkbWluID8gW3sgaGVhZGVyOiAnRW1wcmVzYScsIGZpZWxkOiAnY29tcGFueScsIHdpZHRoOiAnMTUlJyB9XSA6IFtdKSxcclxuICAgICAgICAgICAgICB7IGhlYWRlcjogJ1Byb2Zpc3PDtWVzJywgZmllbGQ6ICdwcm9mZXNzaW9ucycsIHdpZHRoOiAnMTUlJyB9LFxyXG4gICAgICAgICAgICAgIHsgaGVhZGVyOiAnU3RhdHVzJywgZmllbGQ6ICdhY3RpdmUnLCB3aWR0aDogJzEwJScgfSxcclxuICAgICAgICAgICAgICB7IGhlYWRlcjogJ0HDp8O1ZXMnLCBmaWVsZDogJ2FjdGlvbnMnLCBjbGFzc05hbWU6ICd0ZXh0LXJpZ2h0Jywgd2lkdGg6ICcxNSUnLCBzb3J0YWJsZTogZmFsc2UgfVxyXG4gICAgICAgICAgICBdfVxyXG4gICAgICAgICAgICBkYXRhPXtmaWx0ZXJlZEdyb3Vwc31cclxuICAgICAgICAgICAgaXNMb2FkaW5nPXtpc0xvYWRpbmdHcm91cHN9XHJcbiAgICAgICAgICAgIGVtcHR5TWVzc2FnZT1cIk5lbmh1bSBncnVwbyBlbmNvbnRyYWRvXCJcclxuICAgICAgICAgICAgZW1wdHlJY29uPXs8VGFnIHNpemU9ezI0fSAvPn1cclxuICAgICAgICAgICAgdGFibGVJZD1cImFkbWluLXByb2Zlc3Npb24tZ3JvdXBzLXRhYmxlXCJcclxuICAgICAgICAgICAgZW5hYmxlQ29sdW1uVG9nZ2xlPXt0cnVlfVxyXG4gICAgICAgICAgICBkZWZhdWx0U29ydEZpZWxkPVwibmFtZVwiXHJcbiAgICAgICAgICAgIGRlZmF1bHRTb3J0RGlyZWN0aW9uPVwiYXNjXCJcclxuICAgICAgICAgICAgY3VycmVudFBhZ2U9e2N1cnJlbnRHcm91cHNQYWdlfVxyXG4gICAgICAgICAgICB0b3RhbFBhZ2VzPXt0b3RhbEdyb3Vwc1BhZ2VzfVxyXG4gICAgICAgICAgICB0b3RhbEl0ZW1zPXt0b3RhbEdyb3Vwc31cclxuICAgICAgICAgICAgb25QYWdlQ2hhbmdlPXtoYW5kbGVHcm91cHNQYWdlQ2hhbmdlfVxyXG4gICAgICAgICAgICBvblNvcnQ9eyhmaWVsZCwgZGlyZWN0aW9uKSA9PiB7XHJcbiAgICAgICAgICAgICAgLy8gUXVhbmRvIGEgb3JkZW5hw6fDo28gbXVkYXIsIHJlY2FycmVnYXIgb3MgZ3J1cG9zIGNvbSBvcyBub3ZvcyBwYXLDom1ldHJvcyBkZSBvcmRlbmHDp8Ojb1xyXG4gICAgICAgICAgICAgIGxvYWRHcm91cHNXaXRoU29ydChmaWVsZCwgZGlyZWN0aW9uKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgc2hvd1BhZ2luYXRpb249e3RydWV9XHJcbiAgICAgICAgICAgIGl0ZW1zUGVyUGFnZT17aXRlbXNQZXJQYWdlfVxyXG4gICAgICAgICAgICBvbkl0ZW1zUGVyUGFnZUNoYW5nZT17KG5ld0l0ZW1zUGVyUGFnZSkgPT4ge1xyXG4gICAgICAgICAgICAgIHNldEl0ZW1zUGVyUGFnZShuZXdJdGVtc1BlclBhZ2UpO1xyXG4gICAgICAgICAgICAgIGZpbHRlckdyb3VwcyhzZWFyY2gsIDEsIGdyb3Vwc0ZpbHRlciwgbmV3SXRlbXNQZXJQYWdlKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgcmVuZGVyUm93PXsoZ3JvdXAsIGluZGV4LCBtb2R1bGVDb2xvcnMsIHZpc2libGVDb2x1bW5zKSA9PiAoXHJcbiAgICAgICAgICAgICAgPHRyIGtleT17Z3JvdXAuaWR9IGNsYXNzTmFtZT17bW9kdWxlQ29sb3JzLmhvdmVyQmd9PlxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCdzZWxlY3QnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8TW9kdWxlQ2hlY2tib3hcclxuICAgICAgICAgICAgICAgICAgICAgIG1vZHVsZUNvbG9yPVwiYWRtaW5cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2VsZWN0ZWRHcm91cElkcy5pbmNsdWRlcyhncm91cC5pZCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZVNlbGVjdE9uZUdyb3VwKGdyb3VwLmlkLCBlLnRhcmdldC5jaGVja2VkKX1cclxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU9e2BzZWxlY3QtZ3JvdXAtJHtncm91cC5pZH1gfVxyXG4gICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCduYW1lJykgJiYgKFxyXG4gICAgICAgICAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtNiBweS00IHdoaXRlc3BhY2Utbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGgtMTAgdy0xMCBiZy1pbmRpZ28tMTAwIGRhcms6YmctaW5kaWdvLTkwMC8zMCB0ZXh0LWluZGlnby02MDAgZGFyazp0ZXh0LWluZGlnby00MDAgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWcgc2l6ZT17MjB9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1uZXV0cmFsLTkwMCBkYXJrOnRleHQtbmV1dHJhbC0xMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Z3JvdXAubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCdkZXNjcmlwdGlvbicpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW5ldXRyYWwtNjAwIGRhcms6dGV4dC1uZXV0cmFsLTMwMCBsaW5lLWNsYW1wLTJcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtncm91cC5kZXNjcmlwdGlvbiB8fCAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC00MDAgZGFyazp0ZXh0LW5ldXRyYWwtNTAwIGl0YWxpY1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIFNlbSBkZXNjcmnDp8Ojb1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7aXNTeXN0ZW1BZG1pbiAmJiB2aXNpYmxlQ29sdW1ucy5pbmNsdWRlcygnY29tcGFueScpICYmIChcclxuICAgICAgICAgICAgICAgICAgPHRkIGNsYXNzTmFtZT1cInB4LTYgcHktNCB3aGl0ZXNwYWNlLW5vd3JhcFwiPlxyXG4gICAgICAgICAgICAgICAgICAgIHtncm91cC5jb21wYW55ID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8QnVpbGRpbmcgc2l6ZT17MTR9IGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC01MDBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbmV1dHJhbC02MDAgZGFyazp0ZXh0LW5ldXRyYWwtMzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge2dyb3VwLmNvbXBhbnkubmFtZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbmV1dHJhbC00MDAgZGFyazp0ZXh0LW5ldXRyYWwtNTAwIGl0YWxpY1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBTZW0gZW1wcmVzYVxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgICAgIHt2aXNpYmxlQ29sdW1ucy5pbmNsdWRlcygncHJvZmVzc2lvbnMnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8QnJpZWZjYXNlIHNpemU9ezE2fSBjbGFzc05hbWU9XCJ0ZXh0LW5ldXRyYWwtNDAwIGRhcms6dGV4dC1uZXV0cmFsLTUwMCBtci0yXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1uZXV0cmFsLTYwMCBkYXJrOnRleHQtbmV1dHJhbC0zMDBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2dyb3VwLl9jb3VudD8ucHJvZmVzc2lvbnMgfHwgMH0gcHJvZmlzc8O1ZXNcclxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPC90ZD5cclxuICAgICAgICAgICAgICAgICl9XHJcblxyXG4gICAgICAgICAgICAgICAge3Zpc2libGVDb2x1bW5zLmluY2x1ZGVzKCdhY3RpdmUnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8c3BhblxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgcHgtMiBweS0xIHRleHQteHMgcm91bmRlZC1mdWxsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0xIHctZml0ICR7Z3JvdXAuYWN0aXZlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyBcImJnLWdyZWVuLTEwMCBkYXJrOmJnLWdyZWVuLTkwMC8zMCB0ZXh0LWdyZWVuLTgwMCBkYXJrOnRleHQtZ3JlZW4tNDAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiYmctcmVkLTEwMCBkYXJrOmJnLXJlZC05MDAvMzAgdGV4dC1yZWQtODAwIGRhcms6dGV4dC1yZWQtNDAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAge2dyb3VwLmFjdGl2ZSA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgc2l6ZT17MTJ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+QXRpdm88L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8WENpcmNsZSBzaXplPXsxMn0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5JbmF0aXZvPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8Lz5cclxuICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICA8L3RkPlxyXG4gICAgICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgICAgICB7dmlzaWJsZUNvbHVtbnMuaW5jbHVkZXMoJ2FjdGlvbnMnKSAmJiAoXHJcbiAgICAgICAgICAgICAgICAgIDx0ZCBjbGFzc05hbWU9XCJweC02IHB5LTQgd2hpdGVzcGFjZS1ub3dyYXAgdGV4dC1yaWdodCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVZpZXdHcm91cFByb2Zlc3Npb25zKGdyb3VwKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0xLjUgdGV4dC1uZXV0cmFsLTUwMCBkYXJrOnRleHQtbmV1dHJhbC00MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwIGRhcms6aG92ZXI6dGV4dC1pbmRpZ28tNDAwIGhvdmVyOmJnLWluZGlnby01MCBkYXJrOmhvdmVyOmJnLWluZGlnby05MDAvMjAgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTEwIGhvdmVyOnNoYWRvdy1tZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiVmVyIHByb2Zpc3PDtWVzIGRlc3RlIGdydXBvXCJcclxuICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPEJyaWVmY2FzZSBzaXplPXsxOH0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFByb3RlY3RlZCBwZXJtaXNzaW9uPVwiYWRtaW4ucHJvZmVzc2lvbi1ncm91cHMuZWRpdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlRWRpdEdyb3VwKGdyb3VwKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LW5ldXRyYWwtNTAwIGRhcms6dGV4dC1uZXV0cmFsLTQwMCBob3Zlcjp0ZXh0LXByaW1hcnktNTAwIGRhcms6aG92ZXI6dGV4dC1wcmltYXJ5LTQwMCBob3ZlcjpiZy1wcmltYXJ5LTUwIGRhcms6aG92ZXI6YmctcHJpbWFyeS05MDAvMjAgcm91bmRlZC1tZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTEwIGhvdmVyOnNoYWRvdy1tZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJFZGl0YXIgZ3J1cG9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXQgc2l6ZT17MTh9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Qcm90ZWN0ZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8UHJvdGVjdGVkIHBlcm1pc3Npb249XCJhZG1pbi5wcm9mZXNzaW9uLWdyb3Vwcy5kZWxldGVcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZUdyb3VwKGdyb3VwKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LW5ldXRyYWwtNTAwIGRhcms6dGV4dC1uZXV0cmFsLTQwMCBob3Zlcjp0ZXh0LXJlZC01MDAgZGFyazpob3Zlcjp0ZXh0LXJlZC00MDAgaG92ZXI6YmctcmVkLTUwIGRhcms6aG92ZXI6YmctcmVkLTkwMC8yMCByb3VuZGVkLW1kIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBob3ZlcjpzY2FsZS0xMTAgaG92ZXI6c2hhZG93LW1kXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkV4Y2x1aXIgZ3J1cG9cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtncm91cC5fY291bnQ/LnByb2Zlc3Npb25zID4gMH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUcmFzaCBzaXplPXsxOH0gY2xhc3NOYW1lPXtncm91cC5fY291bnQ/LnByb2Zlc3Npb25zID4gMCA/IFwib3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWRcIiA6IFwiXCJ9IC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPC9Qcm90ZWN0ZWQ+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvdGQ+XHJcbiAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgIDwvdHI+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgIDwvUHJvdGVjdGVkPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIE1vZGFpcyAqL31cclxuICAgICAgPFByb2Zlc3Npb25Gb3JtTW9kYWxcclxuICAgICAgICBpc09wZW49e3Byb2Zlc3Npb25Gb3JtT3Blbn1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRQcm9mZXNzaW9uRm9ybU9wZW4oZmFsc2UpfVxyXG4gICAgICAgIHByb2Zlc3Npb249e3NlbGVjdGVkUHJvZmVzc2lvbn1cclxuICAgICAgICBncm91cHM9e2dyb3Vwc31cclxuICAgICAgICBvblN1Y2Nlc3M9eygpID0+IHtcclxuICAgICAgICAgIHNldFByb2Zlc3Npb25Gb3JtT3BlbihmYWxzZSk7XHJcbiAgICAgICAgICBsb2FkUHJvZmVzc2lvbnMoKTtcclxuICAgICAgICB9fVxyXG4gICAgICAvPlxyXG5cclxuICAgICAgPFByb2Zlc3Npb25Hcm91cEZvcm1Nb2RhbFxyXG4gICAgICAgIGlzT3Blbj17Z3JvdXBGb3JtT3Blbn1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRHcm91cEZvcm1PcGVuKGZhbHNlKX1cclxuICAgICAgICBncm91cD17c2VsZWN0ZWRHcm91cH1cclxuICAgICAgICBvblN1Y2Nlc3M9e2FzeW5jICgpID0+IHtcclxuICAgICAgICAgIHNldEdyb3VwRm9ybU9wZW4oZmFsc2UpO1xyXG5cclxuICAgICAgICAgIC8vIFNlIGhhdmlhIHVtIGdydXBvIHNlbGVjaW9uYWRvLCByZWNhcnJlZ2FyIG9zIGRhZG9zIGF0dWFsaXphZG9zIGRlbGUgcHJpbWVpcm9cclxuICAgICAgICAgIGlmIChzZWxlY3RlZEdyb3VwKSB7XHJcbiAgICAgICAgICAgIHRyeSB7XHJcbiAgICAgICAgICAgICAgY29uc3QgdXBkYXRlZEdyb3VwID0gYXdhaXQgcHJvZmVzc2lvbnNTZXJ2aWNlLmdldFByb2Zlc3Npb25Hcm91cEJ5SWQoc2VsZWN0ZWRHcm91cC5pZCk7XHJcbiAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRHcm91cCh1cGRhdGVkR3JvdXApO1xyXG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvIGFvIHJlY2FycmVnYXIgZGFkb3MgZG8gZ3J1cG86XCIsIGVycm9yKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIC8vIFJlY2FycmVnYXIgZ3J1cG9zXHJcbiAgICAgICAgICBhd2FpdCBsb2FkR3JvdXBzKCk7XHJcblxyXG4gICAgICAgICAgLy8gUmVjYXJyZWdhciBwcm9maXNzw7VlcyBwYXJhIGF0dWFsaXphciBvcyBncnVwb3NcclxuICAgICAgICAgIGxvYWRQcm9mZXNzaW9ucygpO1xyXG4gICAgICAgIH19XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICA8UHJvZmVzc2lvblVzZXJzTW9kYWxcclxuICAgICAgICBpc09wZW49e3VzZXJzTW9kYWxPcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFVzZXJzTW9kYWxPcGVuKGZhbHNlKX1cclxuICAgICAgICBwcm9mZXNzaW9uSWQ9e3NlbGVjdGVkUHJvZmVzc2lvbj8uaWR9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICA8UHJvZmVzc2lvbkdyb3VwUHJvZmVzc2lvbnNNb2RhbFxyXG4gICAgICAgIGlzT3Blbj17Z3JvdXBQcm9mZXNzaW9uc01vZGFsT3Blbn1cclxuICAgICAgICBvbkNsb3NlPXsoKSA9PiBzZXRHcm91cFByb2Zlc3Npb25zTW9kYWxPcGVuKGZhbHNlKX1cclxuICAgICAgICBncm91cElkPXtzZWxlY3RlZEdyb3VwPy5pZH1cclxuICAgICAgLz5cclxuXHJcbiAgICAgIDxDb25maXJtYXRpb25EaWFsb2dcclxuICAgICAgICBpc09wZW49e2NvbmZpcm1hdGlvbkRpYWxvZ09wZW59XHJcbiAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0Q29uZmlybWF0aW9uRGlhbG9nT3BlbihmYWxzZSl9XHJcbiAgICAgICAgb25Db25maXJtPXtjb25maXJtQWN0aW9ufVxyXG4gICAgICAgIHRpdGxlPXtcclxuICAgICAgICAgIGFjdGlvblRvQ29uZmlybT8udHlwZSA9PT0gXCJkZWxldGUtcHJvZmVzc2lvblwiID8gXCJFeGNsdWlyIFByb2Zpc3PDo29cIiA6XHJcbiAgICAgICAgICBhY3Rpb25Ub0NvbmZpcm0/LnR5cGUgPT09IFwiZGVsZXRlLWdyb3VwXCIgPyBcIkV4Y2x1aXIgR3J1cG9cIiA6XHJcbiAgICAgICAgICBcIkNvbmZpcm1hciBhw6fDo29cIlxyXG4gICAgICAgIH1cclxuICAgICAgICBtZXNzYWdlPXthY3Rpb25Ub0NvbmZpcm0/Lm1lc3NhZ2UgfHwgXCJcIn1cclxuICAgICAgICB2YXJpYW50PXthY3Rpb25Ub0NvbmZpcm0/LnR5cGUuaW5jbHVkZXMoXCJkZWxldGVcIikgPyBcImRhbmdlclwiIDogXCJwcmltYXJ5XCJ9XHJcbiAgICAgICAgbW9kdWxlQ29sb3I9XCJhZG1pblwiXHJcbiAgICAgICAgY29uZmlybVRleHQ9e2FjdGlvblRvQ29uZmlybT8udHlwZS5pbmNsdWRlcyhcImRlbGV0ZVwiKSA/IFwiRXhjbHVpclwiIDogXCJDb25maXJtYXJcIn1cclxuICAgICAgICBjYW5jZWxUZXh0PVwiQ2FuY2VsYXJcIlxyXG4gICAgICAvPlxyXG5cclxuICAgICAgey8qIEJvdMOjbyBmbHV0dWFudGUgcmVtb3ZpZG8sIG1hbnRlbmRvIGFwZW5hcyBvIGJvdMOjbyBkZSBhanVkYS90dXRvcmlhbCBxdWUgw6kgYWRpY2lvbmFkbyBwZWxvIE1vZHVsZUhlYWRlciAqL31cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQcm9mZXNzaW9uc1BhZ2U7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZUNhbGxiYWNrIiwiUGx1cyIsIlNlYXJjaCIsIkZpbHRlciIsIlJlZnJlc2hDdyIsIkVkaXQiLCJUcmFzaCIsIlRhZyIsIkJyaWVmY2FzZSIsIkNoZWNrQ2lyY2xlIiwiWENpcmNsZSIsIlVzZXJzIiwiVXNlclJvdW5kIiwiQnVpbGRpbmciLCJMYXllcnMiLCJFeHBvcnRNZW51IiwiUHJvdGVjdGVkIiwicHJvZmVzc2lvbnNTZXJ2aWNlIiwiY29tcGFueVNlcnZpY2UiLCJ1c2VBdXRoIiwidXNlVG9hc3QiLCJDb25maXJtYXRpb25EaWFsb2ciLCJQcm9mZXNzaW9uRm9ybU1vZGFsIiwiUHJvZmVzc2lvbkdyb3VwRm9ybU1vZGFsIiwiUHJvZmVzc2lvblVzZXJzTW9kYWwiLCJQcm9mZXNzaW9uR3JvdXBQcm9mZXNzaW9uc01vZGFsIiwiTW9kdWxlSGVhZGVyIiwiTW9kdWxlSW5wdXQiLCJNb2R1bGVTZWxlY3QiLCJNb2R1bGVUYWJsZSIsIk1vZGFsQnV0dG9uIiwiTW9kdWxlVGFicyIsIk11bHRpU2VsZWN0IiwiTW9kdWxlQ2hlY2tib3giLCJPY2N1cGF0aW9uRmlsdGVycyIsIkdyb3Vwc0ZpbHRlcnMiLCJQcm9mZXNzaW9uc1BhZ2UiLCJ1c2VyIiwiY3VycmVudFVzZXIiLCJ0b2FzdF9zdWNjZXNzIiwidG9hc3RfZXJyb3IiLCJwcm9mZXNzaW9ucyIsInNldFByb2Zlc3Npb25zIiwiYWxsUHJvZmVzc2lvbnMiLCJzZXRBbGxQcm9mZXNzaW9ucyIsImdyb3VwcyIsInNldEdyb3VwcyIsImFsbEdyb3VwcyIsInNldEFsbEdyb3VwcyIsImZpbHRlcmVkR3JvdXBzIiwic2V0RmlsdGVyZWRHcm91cHMiLCJjb21wYW5pZXMiLCJzZXRDb21wYW5pZXMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc0xvYWRpbmdHcm91cHMiLCJzZXRJc0xvYWRpbmdHcm91cHMiLCJpc0xvYWRpbmdDb21wYW5pZXMiLCJzZXRJc0xvYWRpbmdDb21wYW5pZXMiLCJzZWFyY2giLCJzZXRTZWFyY2giLCJncm91cEZpbHRlciIsInNldEdyb3VwRmlsdGVyIiwic3RhdHVzRmlsdGVyIiwic2V0U3RhdHVzRmlsdGVyIiwiY29tcGFueUZpbHRlciIsInNldENvbXBhbnlGaWx0ZXIiLCJwcm9mZXNzaW9uc0ZpbHRlciIsInNldFByb2Zlc3Npb25zRmlsdGVyIiwicHJvZmVzc2lvbk9wdGlvbnMiLCJzZXRQcm9mZXNzaW9uT3B0aW9ucyIsImlzTG9hZGluZ1Byb2Zlc3Npb25PcHRpb25zIiwic2V0SXNMb2FkaW5nUHJvZmVzc2lvbk9wdGlvbnMiLCJncm91cHNGaWx0ZXIiLCJzZXRHcm91cHNGaWx0ZXIiLCJncm91cE9wdGlvbnMiLCJzZXRHcm91cE9wdGlvbnMiLCJpc0xvYWRpbmdHcm91cE9wdGlvbnMiLCJzZXRJc0xvYWRpbmdHcm91cE9wdGlvbnMiLCJwcm9mZXNzaW9uRm9ybU9wZW4iLCJzZXRQcm9mZXNzaW9uRm9ybU9wZW4iLCJncm91cEZvcm1PcGVuIiwic2V0R3JvdXBGb3JtT3BlbiIsInVzZXJzTW9kYWxPcGVuIiwic2V0VXNlcnNNb2RhbE9wZW4iLCJncm91cFByb2Zlc3Npb25zTW9kYWxPcGVuIiwic2V0R3JvdXBQcm9mZXNzaW9uc01vZGFsT3BlbiIsInNlbGVjdGVkUHJvZmVzc2lvbiIsInNldFNlbGVjdGVkUHJvZmVzc2lvbiIsInNlbGVjdGVkR3JvdXAiLCJzZXRTZWxlY3RlZEdyb3VwIiwiY29uZmlybWF0aW9uRGlhbG9nT3BlbiIsInNldENvbmZpcm1hdGlvbkRpYWxvZ09wZW4iLCJhY3Rpb25Ub0NvbmZpcm0iLCJzZXRBY3Rpb25Ub0NvbmZpcm0iLCJhY3RpdmVUYWIiLCJzZXRBY3RpdmVUYWIiLCJpc0V4cG9ydGluZyIsInNldElzRXhwb3J0aW5nIiwic2VsZWN0ZWRJZHMiLCJzZXRTZWxlY3RlZElkcyIsInNlbGVjdGVkR3JvdXBJZHMiLCJzZXRTZWxlY3RlZEdyb3VwSWRzIiwib2NjdXBhdGlvbkZpbHRlcnMiLCJzZXRPY2N1cGF0aW9uRmlsdGVycyIsInByb2Zlc3Npb25Hcm91cHMiLCJzdGF0dXMiLCJjdXJyZW50UHJvZmVzc2lvbnNQYWdlIiwic2V0Q3VycmVudFByb2Zlc3Npb25zUGFnZSIsInRvdGFsUHJvZmVzc2lvbnNQYWdlcyIsInNldFRvdGFsUHJvZmVzc2lvbnNQYWdlcyIsInRvdGFsUHJvZmVzc2lvbnMiLCJzZXRUb3RhbFByb2Zlc3Npb25zIiwiY3VycmVudEdyb3Vwc1BhZ2UiLCJzZXRDdXJyZW50R3JvdXBzUGFnZSIsInRvdGFsR3JvdXBzUGFnZXMiLCJzZXRUb3RhbEdyb3Vwc1BhZ2VzIiwidG90YWxHcm91cHMiLCJzZXRUb3RhbEdyb3VwcyIsIml0ZW1zUGVyUGFnZSIsInNldEl0ZW1zUGVyUGFnZSIsImhhbmRsZVNlbGVjdEFsbCIsImNoZWNrZWQiLCJtYXAiLCJwIiwiaWQiLCJoYW5kbGVTZWxlY3RPbmUiLCJwcmV2IiwiZmlsdGVyIiwiaSIsImhhbmRsZVNlbGVjdEFsbEdyb3VwcyIsImciLCJoYW5kbGVTZWxlY3RPbmVHcm91cCIsImlzU3lzdGVtQWRtaW4iLCJyb2xlIiwibG9hZFByb2Zlc3Npb25PcHRpb25zIiwiZGF0YSIsImdldFByb2Zlc3Npb25zIiwiYWN0aXZlIiwib3B0aW9ucyIsInByb2Zlc3Npb24iLCJ2YWx1ZSIsImxhYmVsIiwibmFtZSIsImVycm9yIiwiY29uc29sZSIsImxvYWRHcm91cE9wdGlvbnMiLCJnZXRQcm9mZXNzaW9uR3JvdXBzIiwiZ3JvdXAiLCJsb2FkUHJvZmVzc2lvbnMiLCJwYWdlIiwic2VhcmNoUXVlcnkiLCJncm91cElkIiwiY29tcGFueSIsInByb2Zlc3Npb25JZHMiLCJzb3J0RmllbGQiLCJzb3J0RGlyZWN0aW9uIiwicGVyUGFnZSIsInBhZ2VOdW1iZXIiLCJwYXJzZUludCIsInVuZGVmaW5lZCIsImNvbXBhbnlJZCIsImxlbmd0aCIsInRvdGFsIiwicGFnZXMiLCJNYXRoIiwiY2VpbCIsInN0YXJ0SW5kZXgiLCJlbmRJbmRleCIsInBhZ2luYXRlZFByb2Zlc3Npb25zIiwic2xpY2UiLCJsb2FkR3JvdXBzV2l0aFNvcnQiLCJncm91cElkcyIsInBhZ2luYXRlZEdyb3VwcyIsImxvYWRHcm91cHMiLCJmaWx0ZXJHcm91cHMiLCJzZWFyY2hUZXJtIiwibG9hZEZpbHRlcmVkR3JvdXBzIiwibG9hZENvbXBhbmllcyIsImdldENvbXBhbmllc0ZvclNlbGVjdCIsImhhbmRsZUdyb3VwRmlsdGVyQ2hhbmdlIiwiaGFuZGxlU3RhdHVzRmlsdGVyQ2hhbmdlIiwiaGFuZGxlQ29tcGFueUZpbHRlckNoYW5nZSIsImhhbmRsZVByb2Zlc3Npb25zRmlsdGVyQ2hhbmdlIiwiaGFuZGxlR3JvdXBzRmlsdGVyQ2hhbmdlIiwiaGFuZGxlT2NjdXBhdGlvbkZpbHRlcnNDaGFuZ2UiLCJuZXdGaWx0ZXJzIiwiaGFuZGxlT2NjdXBhdGlvblNlYXJjaCIsImZpbHRlcnMiLCJjb21wYW55SWRzIiwiaGFuZGxlT2NjdXBhdGlvbkNsZWFyRmlsdGVycyIsImNsZWFyZWRGaWx0ZXJzIiwiaGFuZGxlUmVzZXRGaWx0ZXJzIiwic2V0VGltZW91dCIsImV2ZW50IiwiRXZlbnQiLCJkb2N1bWVudCIsImRpc3BhdGNoRXZlbnQiLCJoYW5kbGVQcm9mZXNzaW9uc1BhZ2VDaGFuZ2UiLCJoYW5kbGVHcm91cHNQYWdlQ2hhbmdlIiwiaGFuZGxlRWRpdFByb2Zlc3Npb24iLCJoYW5kbGVFZGl0R3JvdXAiLCJoYW5kbGVEZWxldGVQcm9mZXNzaW9uIiwidHlwZSIsIm1lc3NhZ2UiLCJoYW5kbGVWaWV3VXNlcnMiLCJoYW5kbGVWaWV3R3JvdXBQcm9mZXNzaW9ucyIsImhhbmRsZURlbGV0ZUdyb3VwIiwiaGFuZGxlRXhwb3J0UHJvZmVzc2lvbnMiLCJmb3JtYXQiLCJleHBvcnRQcm9mZXNzaW9ucyIsImhhbmRsZUV4cG9ydEdyb3VwcyIsImV4cG9ydFByb2Zlc3Npb25Hcm91cHMiLCJjb25maXJtQWN0aW9uIiwiZGVsZXRlUHJvZmVzc2lvbiIsImRlbGV0ZVByb2Zlc3Npb25Hcm91cCIsInJlc3BvbnNlIiwidGFic0NvbmZpZyIsImljb24iLCJzaXplIiwicGVybWlzc2lvbiIsImZpbHRlcmVkVGFicyIsInRhYiIsInNob3dGYWxsYmFjayIsIlByb2Zlc3Npb25zRmlsdGVycyIsImxvY2FsU2VhcmNoIiwic2V0TG9jYWxTZWFyY2giLCJoYW5kbGVSZXNldCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGFuZGxlTG9jYWxTZWFyY2hDaGFuZ2UiLCJlIiwidGFyZ2V0IiwiYXBwbHlGaWx0ZXIiLCJsb2ciLCJkaXYiLCJjbGFzc05hbWUiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwib25DaGFuZ2UiLCJvbktleURvd24iLCJrZXkiLCJwcmV2ZW50RGVmYXVsdCIsIm1vZHVsZUNvbG9yIiwiZGlzYWJsZWQiLCJvcHRpb24iLCJvbkNsaWNrIiwic3BhbiIsInZhcmlhbnQiLCJsb2FkaW5nIiwibW9kdWxlT3ZlcnJpZGUiLCJncm91cEZpbHRlcnMiLCJzZXRHcm91cEZpbHRlcnMiLCJoYW5kbGVHcm91cEZpbHRlcnNDaGFuZ2UiLCJoYW5kbGVHcm91cFNlYXJjaCIsInByb2Zlc3Npb25zR3JvdXBzVHV0b3JpYWxTdGVwcyIsInR1dG9yaWFsTWFwIiwicmVxdWlyZSIsImRlZmF1bHQiLCJoMSIsImJ1dHRvbiIsInRpdGxlIiwib25FeHBvcnQiLCJ0dXRvcmlhbFN0ZXBzIiwidHV0b3JpYWxOYW1lIiwidGFicyIsIm9uVGFiQ2hhbmdlIiwib25GaWx0ZXJzQ2hhbmdlIiwib25TZWFyY2giLCJvbkNsZWFyRmlsdGVycyIsImhlYWRlckNvbnRlbnQiLCJjb2x1bW5zIiwiaGVhZGVyIiwiZmllbGQiLCJ3aWR0aCIsInNvcnRhYmxlIiwiZW1wdHlNZXNzYWdlIiwiZW1wdHlJY29uIiwidGFibGVJZCIsImVuYWJsZUNvbHVtblRvZ2dsZSIsImRlZmF1bHRTb3J0RmllbGQiLCJkZWZhdWx0U29ydERpcmVjdGlvbiIsImN1cnJlbnRQYWdlIiwidG90YWxQYWdlcyIsInRvdGFsSXRlbXMiLCJvblBhZ2VDaGFuZ2UiLCJvblNvcnQiLCJkaXJlY3Rpb24iLCJzaG93UGFnaW5hdGlvbiIsIm9uSXRlbXNQZXJQYWdlQ2hhbmdlIiwibmV3SXRlbXNQZXJQYWdlIiwib25TZWxlY3RBbGwiLCJyZW5kZXJSb3ciLCJpbmRleCIsIm1vZHVsZUNvbG9ycyIsInZpc2libGVDb2x1bW5zIiwidHIiLCJob3ZlckJnIiwiaW5jbHVkZXMiLCJ0ZCIsImRlc2NyaXB0aW9uIiwiX2NvdW50IiwidXNlcnMiLCJpc09wZW4iLCJvbkNsb3NlIiwib25TdWNjZXNzIiwidXBkYXRlZEdyb3VwIiwiZ2V0UHJvZmVzc2lvbkdyb3VwQnlJZCIsInByb2Zlc3Npb25JZCIsIm9uQ29uZmlybSIsImNvbmZpcm1UZXh0IiwiY2FuY2VsVGV4dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/professions/ProfessionsPage.js\n"));

/***/ })

});