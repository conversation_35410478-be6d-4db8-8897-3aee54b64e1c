'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  ShieldCheck,
  Calendar,
  Users,
  BarChart4,
  MessageSquare,
  Settings
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogTitle
} from '@/components/ui/dialog';

const modules = [
  {
    id: 'scheduler',
    icon: <Calendar size={24} />,
    title: 'Agendamento',
    description: 'Gerencie consultas, compromissos e disponibilidade dos profissionais com um calendário intuitivo e completo.',
    features: [
      'Visualização diária, semanal e mensal',
      'Agendamentos recorrentes',
      'Detecção de conflitos',
      'Horários de trabalho personalizáveis',
      'Notificações automáticas',
      'Solicitações de clientes com aprovação'
    ],
    color: 'scheduler',
    images: [
      {
        src: '/landing/imagem-agendarconsulta-agendamento.png',
        alt: 'Interface do módulo de agendamento - Agendamento de consultas'
      },
      {
        src: '/landing/imagem-horariostrabalho-agendamento.png',
        alt: 'Interface do módulo de agendamento - Horários de trabalho'
      },
      {
        src: '/landing/imagem-tiposservico-agendamento.png',
        alt: 'Interface do módulo de agendamento - Tipos de serviço'
      },
      {
        src: '/landing/imagem-localizacoes-agendamento.png',
        alt: 'Interface do módulo de agendamento - Localizações'
      },
      {
        src: '/landing/imagem-solicitacoes-agendamento.png',
        alt: 'Interface do módulo de agendamento - Solicitações de agendamento'
      }
    ]
  },
  {
    id: 'people',
    icon: <Users size={24} />,
    title: 'Pessoas',
    description: 'Cadastro completo de pacientes, profissionais e colaboradores com todas as informações necessárias.',
    features: [
      'Cadastro de pacientes e contatos',
      'Histórico de atendimentos',
      'Gestão de convênios',
      'Documentos digitalizados',
      'Relacionamentos familiares'
    ],
    color: 'people',
    images: [
      {
        src: '/landing/imagem-clientes-pessoas.png',
        alt: 'Interface do módulo de pessoas - Cadastro de clientes'
      },
      {
        src: '/landing/imagem-convenios-pessoas.png',
        alt: 'Interface do módulo de pessoas - Gestão de convênios'
      },
      {
        src: '/landing/imagem-limitesconvenio-pessoas.png',
        alt: 'Interface do módulo de pessoas - Limites de convênio'
      }
    ]
  },
  {
    id: 'admin',
    icon: <ShieldCheck size={24} />,
    title: 'Administração',
    description: 'Gerencie usuários, permissões, configurações e mantenha o controle total do sistema.',
    features: [
      'Gestão de usuários e permissões',
      'Configurações do sistema',
      'Logs de atividades',
      'Backup de dados',
      'Segurança e privacidade'
    ],
    color: 'admin',
    images: [
      {
        src: '/landing/imagem-introducao.png',
        alt: 'Interface do módulo de administração - Introdução'
      },
      {
        src: '/landing/imagem-configuracoes-admin.png',
        alt: 'Interface do módulo de administração - Configurações'
      },
      {
        src: '/landing/imagem-usuarios-admin.png',
        alt: 'Interface do módulo de administração - Gestão de usuários'
      },
      {
        src: '/landing/imagem-infosusuarios-admin.png',
        alt: 'Interface do módulo de administração - Informações de usuários'
      },
      {
        src: '/landing/imagem-infosusuarios5-admin.png',
        alt: 'Interface do módulo de administração - Detalhes de usuários'
      },
      {
        src: '/landing/imagem-infosusuarios6-admin.png',
        alt: 'Interface do módulo de administração - Perfil de usuários'
      }
    ]
  },
  {
    id: 'reports',
    icon: <BarChart4 size={24} />,
    title: 'Relatórios e Dashboards',
    description: 'Dashboards e relatórios detalhados para análise de desempenho e tomada de decisões.',
    features: [
      'Dashboard interativo',
      'Relatórios personalizáveis',
      'Análise de ocupação',
      'Indicadores de desempenho',
      'Exportação em diversos formatos'
    ],
    color: 'reports',
    images: [
      {
        src: '/landing/imagem-dashboard.png',
        alt: 'Interface do módulo de relatórios - Dashboard principal'
      },
      {
        src: '/landing/imagem-ocupacao-agendamento.png',
        alt: 'Interface do módulo de relatórios - Análise de ocupação'
      },
      {
        src: '/landing/imagem-relatorio-agendamento.png',
        alt: 'Interface do módulo de relatórios - Relatórios de agendamento'
      }
    ]
  },
  {
    id: 'chat',
    icon: <MessageSquare size={24} />,
    title: 'Chat Integrado',
    description: 'Sistema de comunicação integrado para facilitar a comunicação entre equipe e pacientes.',
    features: [
      'Chat em tempo real',
      'Comunicação interna da equipe',
      'Histórico de conversas',
      'Notificações de mensagens',
      'Interface intuitiva e responsiva'
    ],
    color: 'chat',
    images: [
      {
        src: '/landing/imagem-chat.png',
        alt: 'Interface do módulo de chat - Chat principal'
      },
      {
        src: '/landing/imagem-chat2.png',
        alt: 'Interface do módulo de chat - Conversas em grupo'
      },
      {
        src: '/landing/imagem-chat3.png',
        alt: 'Interface do módulo de chat - Notificações'
      },
      {
        src: '/landing/imagem-chat4.png',
        alt: 'Interface do módulo de chat - Histórico de mensagens'
      }
    ]
  },
  {
    id: 'customization',
    icon: <Settings size={24} />,
    title: 'Personalização',
    description: 'Adapte o sistema às necessidades específicas do seu negócio com opções de personalização.',
    features: [
      'Configurações personalizáveis',
      'Campos customizados',
      'Fluxos de trabalho adaptáveis',
      'Interface configurável',
      'Relatórios sob medida'
    ],
    color: 'customization',
    images: []
  }
];

const Modules = () => {
  const [currentSlides, setCurrentSlides] = useState({});
  const [isHovering, setIsHovering] = useState({});
  const [fade, setFade] = useState({});
  const [slideDir, setSlideDir] = useState({});
  const [modalOpen, setModalOpen] = useState({});
  const [modalSlide, setModalSlide] = useState({});

  const nextSlide = (moduleId) => {
    const module = modules.find(m => m.id === moduleId);
    if (!module || module.images.length <= 1) return;
    setSlideDir(prev => ({ ...prev, [moduleId]: 1 }));
    setFade(prev => ({ ...prev, [moduleId]: false }));
    setTimeout(() => {
      setCurrentSlides(prev => ({
        ...prev,
        [moduleId]: (prev[moduleId] || 0) === module.images.length - 1 ? 0 : (prev[moduleId] || 0) + 1
      }));
      setTimeout(() => setFade(prev => ({ ...prev, [moduleId]: true })), 50);
    }, 400);
  };

  const prevSlide = (moduleId) => {
    const module = modules.find(m => m.id === moduleId);
    if (!module || module.images.length <= 1) return;
    setSlideDir(prev => ({ ...prev, [moduleId]: -1 }));
    setFade(prev => ({ ...prev, [moduleId]: false }));
    setTimeout(() => {
      setCurrentSlides(prev => ({
        ...prev,
        [moduleId]: (prev[moduleId] || 0) === 0 ? module.images.length - 1 : (prev[moduleId] || 0) - 1
      }));
      setTimeout(() => setFade(prev => ({ ...prev, [moduleId]: true })), 50);
    }, 400);
  };

  const goToSlide = (moduleId, index) => {
    setSlideDir(prev => ({ ...prev, [moduleId]: 1 }));
    setFade(prev => ({ ...prev, [moduleId]: false }));
    setTimeout(() => {
      setCurrentSlides(prev => ({ ...prev, [moduleId]: index }));
      setTimeout(() => setFade(prev => ({ ...prev, [moduleId]: true })), 50);
    }, 400);
  };

  useEffect(() => {
    const intervals = {};
    modules.forEach(module => {
      if (module.images.length > 1) {
        intervals[module.id] = setInterval(() => {
          nextSlide(module.id);
        }, 5000);
      }
    });
    return () => {
      Object.values(intervals).forEach(interval => clearInterval(interval));
    };
  }, []);

  useEffect(() => {
    modules.forEach(module => {
      setFade(prev => ({ ...prev, [module.id]: true }));
    });
  }, []);

  const renderImageSlider = (module, index) => {
    const currentSlide = currentSlides[module.id] || 0;
    const hasMultipleImages = module.images.length > 1;
    const currentImage = module.images[currentSlide];
    const isFading = fade[module.id];
    const direction = slideDir[module.id] || 1;
    const [prevImage, setPrevImage] = useState(null);
    const [prevModuleId, setPrevModuleId] = useState(null);

    useEffect(() => {
      setPrevImage(currentImage);
      setPrevModuleId(module.id);
    }, [currentSlide, module.id]);

    const openModal = () => {
      setModalOpen(prev => ({ ...prev, [module.id]: true }));
      setModalSlide(prev => ({ ...prev, [module.id]: currentSlide }));
    };
    const closeModal = () => {
      setModalOpen(prev => ({ ...prev, [module.id]: false }));
    };
    const nextModalSlide = () => {
      setModalSlide(prev => ({
        ...prev,
        [module.id]: (prev[module.id] === module.images.length - 1 ? 0 : prev[module.id] + 1)
      }));
    };
    const prevModalSlide = () => {
      setModalSlide(prev => ({
        ...prev,
        [module.id]: (prev[module.id] === 0 ? module.images.length - 1 : prev[module.id] - 1)
      }));
    };
    const goToModalSlide = (idx) => {
      setModalSlide(prev => ({ ...prev, [module.id]: idx }));
    };

    if (!module.images || module.images.length === 0) {
      return (
        <div className="aspect-video bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
          <div className="text-center p-8">
            <div className={`mx-auto mb-4 ${
              module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' :
              module.id === 'people' ? 'text-amber-600 dark:text-amber-400' :
              module.id === 'admin' ? 'text-slate-600 dark:text-slate-400' :
              module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' :
              module.id === 'chat' ? 'text-green-600 dark:text-green-400' :
              module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' :
              'text-gray-600 dark:text-gray-400'
            }`}>
              {module.icon}
            </div>
            <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Módulo de {module.title}
            </h4>
            <p className="text-gray-500 dark:text-gray-400">
              Interface intuitiva e funcional para gerenciar todas as atividades relacionadas.
            </p>
          </div>
        </div>
      );
    }

    return (
      <>
        <div
          className="aspect-video rounded-lg overflow-hidden relative"
          onMouseEnter={() => setIsHovering(prev => ({ ...prev, [module.id]: true }))}
          onMouseLeave={() => setIsHovering(prev => ({ ...prev, [module.id]: false }))}
        >
          {/* Fundo gradiente */}
          <div className={`absolute inset-0 bg-gradient-to-br ${
            module.id === 'scheduler' ? 'from-purple-50 via-blue-50 to-indigo-50 dark:from-purple-900/20 dark:via-blue-900/20 dark:to-indigo-900/20' :
            module.id === 'people' ? 'from-amber-50 via-orange-50 to-yellow-50 dark:from-amber-900/20 dark:via-orange-900/20 dark:to-yellow-900/20' :
            module.id === 'admin' ? 'from-slate-50 via-gray-50 to-zinc-50 dark:from-slate-900/20 dark:via-gray-900/20 dark:to-zinc-900/20' :
            module.id === 'reports' ? 'from-blue-50 via-cyan-50 to-sky-50 dark:from-blue-900/20 dark:via-cyan-900/20 dark:to-sky-900/20' :
            module.id === 'chat' ? 'from-orange-50 via-red-50 to-pink-50 dark:from-orange-900/20 dark:via-red-900/20 dark:to-pink-900/20' :
            'from-gray-50 via-gray-100 to-gray-200 dark:from-gray-900/20 dark:via-gray-800/20 dark:to-gray-700/20'
          }`}></div>

          {/* Gradiente radial centralizado */}
          <div className={`absolute inset-0 bg-gradient-radial from-transparent via-transparent to-current opacity-10 ${
            module.id === 'scheduler' ? 'text-purple-400 dark:text-purple-600' :
            module.id === 'people' ? 'text-amber-400 dark:text-amber-600' :
            module.id === 'admin' ? 'text-slate-400 dark:text-slate-600' :
            module.id === 'reports' ? 'text-blue-400 dark:text-blue-600' :
            'text-gray-400 dark:text-gray-600'
          }`}></div>

          {/* Círculo translúcido - Top Right */}
          <div className={`absolute top-0 right-0 w-32 h-32 rounded-full opacity-20 ${
            module.id === 'scheduler' ? 'bg-purple-400 dark:bg-purple-600' :
            module.id === 'people' ? 'bg-amber-400 dark:bg-amber-600' :
            module.id === 'admin' ? 'bg-slate-400 dark:bg-slate-600' :
            module.id === 'reports' ? 'bg-blue-400 dark:bg-blue-600' :
            'bg-gray-400 dark:bg-gray-600'
          }`}></div>

          {/* Círculo translúcido - Bottom Left */}
          <div className={`absolute bottom-0 left-0 w-24 h-24 rounded-full opacity-15 ${
            module.id === 'scheduler' ? 'bg-blue-400 dark:bg-blue-600' :
            module.id === 'people' ? 'bg-orange-400 dark:bg-orange-600' :
            module.id === 'admin' ? 'bg-gray-400 dark:bg-gray-600' :
            module.id === 'reports' ? 'bg-cyan-400 dark:bg-cyan-600' :
            'bg-gray-400 dark:bg-gray-600'
          }`}></div>

          {/* Elementos decorativos */}
          <div className={`absolute top-4 right-4 w-16 h-16 rounded-full blur-xl ${
            module.id === 'scheduler' ? 'bg-purple-200/30 dark:bg-purple-800/30' :
            module.id === 'people' ? 'bg-amber-200/30 dark:bg-amber-800/30' :
            module.id === 'admin' ? 'bg-slate-200/30 dark:bg-slate-800/30' :
            module.id === 'reports' ? 'bg-blue-200/30 dark:bg-blue-800/30' :
            'bg-gray-200/30 dark:bg-gray-800/30'
          }`}></div>
          <div className={`absolute bottom-4 left-4 w-12 h-12 rounded-full blur-lg ${
            module.id === 'scheduler' ? 'bg-blue-200/30 dark:bg-blue-800/30' :
            module.id === 'people' ? 'bg-orange-200/30 dark:bg-orange-800/30' :
            module.id === 'admin' ? 'bg-gray-200/30 dark:bg-gray-800/30' :
            module.id === 'reports' ? 'bg-cyan-200/30 dark:bg-cyan-800/30' :
            'bg-gray-200/30 dark:bg-gray-800/30'
          }`}></div>

          {/* Imagem ativa com transição dupla */}
          <div className="relative z-10 w-full h-full flex items-center justify-center p-6 overflow-hidden">
            <div className="bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-white/20 dark:border-gray-700/20 relative overflow-hidden w-full h-full flex items-center justify-center shadow-inner">
              <div className="absolute inset-0 rounded-xl shadow-inner pointer-events-none"></div>
              {/* Imagem antiga saindo */}
              {!isFading && prevImage && (
                <img
                  src={prevImage.src}
                  alt={prevImage.alt}
                  className={`w-full h-full object-contain object-center rounded-lg transition-all duration-500 absolute top-0 left-0
                    opacity-0
                    ${direction === 1 ? '-translate-x-12' : 'translate-x-12'}`}
                  style={{
                    imageRendering: 'crisp-edges',
                    maxWidth: '100%',
                    maxHeight: '100%'
                  }}
                  loading="eager"
                  key={prevImage.src + '-out'}
                />
              )}
              {/* Imagem nova entrando - agora clicável */}
              {currentImage && (
                <img
                  src={currentImage.src}
                  alt={currentImage.alt}
                  className={`w-full h-full object-contain object-center rounded-lg transition-all duration-500 relative cursor-zoom-in
                    ${isFading ? 'opacity-100 translate-x-0' : direction === 1 ? 'opacity-0 translate-x-12' : 'opacity-0 -translate-x-12'}`}
                  style={{
                    imageRendering: 'crisp-edges',
                    maxWidth: '100%',
                    maxHeight: '100%'
                  }}
                  loading="eager"
                  onClick={openModal}
                  tabIndex={0}
                  aria-label="Expandir imagem do módulo"
                  key={currentImage.src}
                />
              )}
            </div>
          </div>

          {/* Botões de navegação */}
          {hasMultipleImages && (
            <>
              <button
                className={`absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full text-white flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 ${
                  module.id === 'scheduler' ? 'bg-purple-500/80 hover:bg-purple-600/90' :
                  module.id === 'people' ? 'bg-amber-500/80 hover:bg-amber-600/90' :
                  module.id === 'admin' ? 'bg-slate-500/80 hover:bg-slate-600/90' :
                  module.id === 'reports' ? 'bg-blue-500/80 hover:bg-blue-600/90' :
                  'bg-gray-500/80 hover:bg-gray-600/90'
                }`}
                onClick={() => prevSlide(module.id)}
                aria-label="Slide anterior"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                className={`absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full text-white flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 ${
                  module.id === 'scheduler' ? 'bg-purple-500/80 hover:bg-purple-600/90' :
                  module.id === 'people' ? 'bg-amber-500/80 hover:bg-amber-600/90' :
                  module.id === 'admin' ? 'bg-slate-500/80 hover:bg-slate-600/90' :
                  module.id === 'reports' ? 'bg-blue-500/80 hover:bg-blue-600/90' :
                  'bg-gray-500/80 hover:bg-gray-600/90'
                }`}
                onClick={() => nextSlide(module.id)}
                aria-label="Próximo slide"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}

          {/* Indicadores */}
          {hasMultipleImages && (
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-20">
              {module.images.map((_, imageIndex) => (
                <button
                  key={imageIndex}
                  className={`w-2 h-2 rounded-full transition-all ${
                    currentSlide === imageIndex
                      ? (module.id === 'scheduler' ? 'bg-purple-500' :
                         module.id === 'people' ? 'bg-amber-500' :
                         module.id === 'admin' ? 'bg-slate-500' :
                         module.id === 'reports' ? 'bg-blue-500' :
                         'bg-gray-500')
                      : (module.id === 'scheduler' ? 'bg-purple-300' :
                         module.id === 'people' ? 'bg-amber-300' :
                         module.id === 'admin' ? 'bg-slate-300' :
                         module.id === 'reports' ? 'bg-blue-300' :
                         'bg-gray-300')
                  }`}
                  onClick={() => goToSlide(module.id, imageIndex)}
                  aria-label={`Ir para slide ${imageIndex + 1}`}
                />
              ))}
            </div>
          )}

          {/* Floating elements */}
          <div className={`absolute top-2 left-2 w-3 h-3 rounded-full animate-pulse ${
            module.id === 'scheduler' ? 'bg-purple-400/60 dark:bg-purple-400/40' :
            module.id === 'people' ? 'bg-amber-400/60 dark:bg-amber-400/40' :
            module.id === 'admin' ? 'bg-slate-400/60 dark:bg-slate-400/40' :
            module.id === 'reports' ? 'bg-blue-400/60 dark:bg-blue-400/40' :
            'bg-gray-400/60 dark:bg-gray-400/40'
          }`}></div>
          <div className={`absolute top-6 right-10 w-2 h-2 rounded-full animate-pulse delay-300 ${
            module.id === 'scheduler' ? 'bg-blue-400/60 dark:bg-blue-400/40' :
            module.id === 'people' ? 'bg-orange-400/60 dark:bg-orange-400/40' :
            module.id === 'admin' ? 'bg-gray-400/60 dark:bg-gray-400/40' :
            module.id === 'reports' ? 'bg-cyan-400/60 dark:bg-cyan-400/40' :
            'bg-gray-400/60 dark:bg-gray-400/40'
          }`}></div>
          <div className={`absolute bottom-6 left-7 w-2 h-2 rounded-full animate-pulse delay-500 ${
            module.id === 'scheduler' ? 'bg-indigo-400/60 dark:bg-indigo-400/40' :
            module.id === 'people' ? 'bg-yellow-400/60 dark:bg-yellow-400/40' :
            module.id === 'admin' ? 'bg-zinc-400/60 dark:bg-zinc-400/40' :
            module.id === 'reports' ? 'bg-sky-400/60 dark:bg-sky-400/40' :
            'bg-gray-400/60 dark:bg-gray-400/40'
          }`}></div>
        </div>
        {/* Modal de imagem expandida */}
        <Dialog open={!!modalOpen[module.id]} onOpenChange={val => val ? openModal() : closeModal()}>
          <DialogContent className="max-w-4xl w-[80vw] max-h-[80vh] flex flex-col items-center justify-center bg-white dark:bg-gray-900 rounded-2xl shadow-2xl p-0 border-0">
            <DialogTitle className="sr-only">{module.title} - {module.images[modalSlide[module.id] || 0]?.alt || 'Visualização de imagem do módulo'}</DialogTitle>
            <div className="flex flex-col items-center justify-center w-full h-full">
              <div className="relative flex flex-row items-center justify-center w-full min-h-[40vh]" style={{minHeight:'40vh'}}>
                {/* Botão de fechar sobre a imagem, canto superior direito */}
                <button
                  onClick={closeModal}
                  className="absolute z-50 top-2 right-2 w-10 h-10 flex items-center justify-center rounded-full bg-black/80 text-white shadow-lg hover:bg-black focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all"
                  style={{boxShadow:'0 4px 24px rgba(0,0,0,0.25)'}}
                  aria-label="Fechar modal"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                {/* Seta esquerda */}
                {module.images.length > 1 && (
                  <button
                    className="z-30 w-10 h-10 rounded-full bg-black/60 text-white flex items-center justify-center hover:bg-black/80 transition-colors mr-2"
                    onClick={prevModalSlide}
                    aria-label="Slide anterior"
                    tabIndex={modalOpen[module.id] ? 0 : -1}
                    style={{flex:'0 0 auto'}}>
                    <ChevronLeft size={24} />
                  </button>
                )}
                {/* Imagem centralizada */}
                <img
                  src={module.images[modalSlide[module.id] || 0]?.src}
                  alt={module.images[modalSlide[module.id] || 0]?.alt}
                  className="rounded-2xl shadow-xl object-contain max-w-[78vw] max-h-[70vh] border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 mx-auto"
                  style={{ boxShadow: '0 8px 32px rgba(0,0,0,0.25)' }}
                />
                {/* Seta direita */}
                {module.images.length > 1 && (
                  <button
                    className="z-30 w-10 h-10 rounded-full bg-black/60 text-white flex items-center justify-center hover:bg-black/80 transition-colors ml-2"
                    onClick={nextModalSlide}
                    aria-label="Próximo slide"
                    tabIndex={modalOpen[module.id] ? 0 : -1}
                    style={{flex:'0 0 auto'}}>
                    <ChevronRight size={24} />
                  </button>
                )}
              </div>
              <div className="mt-4 text-center">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{module.images[modalSlide[module.id] || 0]?.alt}</h3>
              </div>
              {/* Dots navigation no modal */}
              {module.images.length > 1 && (
                <div className="flex space-x-2 mt-4">
                  {module.images.map((_, idx) => (
                    <button
                      key={idx}
                      className={`w-3 h-3 rounded-full transition-all border-2 ${
                        (modalSlide[module.id] || 0) === idx
                          ? "bg-primary-500 border-primary-500"
                          : "bg-gray-200 dark:bg-gray-700 border-gray-300 dark:border-gray-600 hover:bg-primary-300"
                      }`}
                      onClick={() => goToModalSlide(idx)}
                      aria-label={`Ir para slide ${idx + 1}`}
                      tabIndex={modalOpen[module.id] ? 0 : -1}
                    />
                  ))}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </>
    );
  };

  return (
    <section id="modules" className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Módulos integrados
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Cada módulo foi desenvolvido para atender necessidades específicas, mas todos trabalham em perfeita harmonia.
          </p>
        </motion.div>

        <div className="space-y-16">
          {modules.map((module, index) => (
            <motion.div 
              key={module.id}
              className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} gap-8 items-center`}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
            >
              {/* Module Card */}
              <div className="flex-1">
                <div className={`bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700`}>
                  <div className={`p-6 ${
                    module.id === 'scheduler' ? 'bg-purple-50 dark:bg-purple-900/20' :
                    module.id === 'people' ? 'bg-amber-50 dark:bg-amber-900/20' :
                    module.id === 'admin' ? 'bg-slate-50 dark:bg-slate-900/20' :
                    module.id === 'reports' ? 'bg-blue-50 dark:bg-blue-900/20' :
                    module.id === 'chat' ? 'bg-green-50 dark:bg-green-900/20' :
                    module.id === 'customization' ? 'bg-orange-50 dark:bg-orange-900/20' :
                    'bg-gray-50 dark:bg-gray-900/20'
                  }`}>
                    <div className="flex items-center">
                      <div className={`w-12 h-12 rounded-lg ${
                        module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-800/30' :
                        module.id === 'people' ? 'bg-amber-100 dark:bg-amber-800/30' :
                        module.id === 'admin' ? 'bg-slate-100 dark:bg-slate-800/30' :
                        module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-800/30' :
                        module.id === 'chat' ? 'bg-green-100 dark:bg-green-800/30' :
                        module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-800/30' :
                        'bg-gray-100 dark:bg-gray-800/30'
                      } flex items-center justify-center mr-4`}>
                        <div className={
                          module.id === 'scheduler' ? 'text-purple-600 dark:text-purple-400' :
                          module.id === 'people' ? 'text-amber-600 dark:text-amber-400' :
                          module.id === 'admin' ? 'text-slate-600 dark:text-slate-400' :
                          module.id === 'reports' ? 'text-blue-600 dark:text-blue-400' :
                          module.id === 'chat' ? 'text-green-600 dark:text-green-400' :
                          module.id === 'customization' ? 'text-orange-600 dark:text-orange-400' :
                          'text-gray-600 dark:text-gray-400'
                        }>
                          {module.icon}
                        </div>
                      </div>
                      <h3 className={`text-2xl font-bold ${
                        module.id === 'scheduler' ? 'text-purple-800 dark:text-purple-300' :
                        module.id === 'people' ? 'text-amber-800 dark:text-amber-300' :
                        module.id === 'admin' ? 'text-slate-800 dark:text-slate-300' :
                        module.id === 'reports' ? 'text-blue-800 dark:text-blue-300' :
                        module.id === 'chat' ? 'text-green-800 dark:text-green-300' :
                        module.id === 'customization' ? 'text-orange-800 dark:text-orange-300' :
                        'text-gray-800 dark:text-gray-300'
                      }`}>
                        {module.title}
                      </h3>
                    </div>
                  </div>
                  <div className="p-6">
                    <p className="text-gray-600 dark:text-gray-300 mb-6">
                      {module.description}
                    </p>
                    <ul className="space-y-3">
                      {module.features.map((feature, i) => (
                        <li key={i} className="flex items-start">
                          <div className={`mt-1 mr-3 w-5 h-5 rounded-full flex items-center justify-center ${
                            module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                            module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' :
                            module.id === 'admin' ? 'bg-slate-100 dark:bg-slate-900/30 text-slate-600 dark:text-slate-400' :
                            module.id === 'reports' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                            module.id === 'chat' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                            module.id === 'customization' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' :
                            'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400'
                          }`}>
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>

              {/* Module Screenshot/Illustration */}
              <div className="flex-1">
                <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 p-4">
                  {renderImageSlider(module, index)}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Modules;
